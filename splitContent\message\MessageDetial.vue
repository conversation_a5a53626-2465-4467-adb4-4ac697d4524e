<template>
  <view>
    <view class="content">
      <view class="title">{{ newsContent.title }}</view>
      <view class="time">{{ newsContent.createTime }}</view>
      <view class="news_content" @click="jump">{{ newsContent.content }}{{ newsContent.type === 2 ? '请扫描下方二维码进群，谢谢！' : '' }}</view>
      <view v-if="newsContent.type === 2 && newsContent.jumpUrl && newsContent.jumpUrl != ''" class="img_content">
        <view class="tip_text">
          <view class="">想了解更多关于会议的事宜</view>
          <view class="">
            <span class="tip_red">长按识别二维码</span>
            添加鼎校甄选相关
          </view>
          人员企业微信了解
        </view>
        <image class="ewm" :src="newsContent.jumpUrl" :show-menu-by-longpress="true" mode=""></image>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        newsContent: {}
      };
    },
    onLoad(e) {
      console.log(decodeURIComponent(e.item), 'eeee');
      this.newsContent = JSON.parse(decodeURIComponent(e.item));
    },
    methods: {
      // 链接跳转
      jump() {
        if (this.newsContent.type !== 2 && this.newsContent.jumpUrl && this.newsContent.jumpUrl != '') {
          console.log(this.newsContent.jumpUrl, 9999);
          uni.navigateTo({
            url: '/' + this.newsContent.jumpUrl
          });
        }
      }
    }
  };
</script>
<style lang="scss" scoped>
  .content {
    min-height: calc(100vh - 112rpx);
    margin: 24rpx 32rpx;
    padding: 32rpx 24rpx;
    background-color: #ffffff;
    .title {
      height: 50rpx;
      font-size: 36rpx;
      color: #333333;
      line-height: 50rpx;
      text-align: center;
    }
    .time {
      height: 36rpx;
      font-weight: 400;
      font-size: 26rpx;
      color: #aaaaaa;
      line-height: 36rpx;
      margin: 32rpx 0 24rpx 0;
    }
    .news_content {
      font-size: 30rpx;
      color: #333333;
      font-weight: 300;
    }
  }
  .img_content {
    text-align: center;
    margin-top: 68rpx;
  }

  .ewm {
    width: 400rpx;
    height: 400rpx;
  }

  .tip_text {
    font-size: 30rpx;
    line-height: 45rpx;
    color: #333333;
    font-weight: 300;
  }

  .tip_red {
    color: #ea6031;
  }
</style>
