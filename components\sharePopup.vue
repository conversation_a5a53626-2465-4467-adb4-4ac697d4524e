<template>
  <!-- 分享弹窗 -->
  <uni-popup ref="sharePopup" type="bottom" style="padding: 0">
    <view class="shareCard">
      <view class="reviewTitle bold">立即分享给好友</view>
      <view class="review_close" @click="closeDialog">
        <uni-icons type="clear" size="26" color="#B1B1B1"></uni-icons>
      </view>
      <view class="displayflexbetween mt-30 ptb-30" style="justify-content: space-around">
        <view class="t-c" @click="posterShare">
          <image :src="imgHost + 'dxSelect/share_img.png'" class="shareIcon" mode=""></image>
          <view class="f-26 mt-10 color_grey66">海报分享</view>
        </view>
        <!-- #ifdef MP-WEIXIN -->
        <view class="t-c" @click="shareLink">
          <button open-type="share" class="fillButton">
            <image :src="imgHost + 'dxSelect/share_lj.png'" class="shareIcon" mode=""></image>
            <view class="f-26 mt-10 color_grey66">链接分享</view>
          </button>
        </view>
        <!-- #endif -->

        <!-- #ifdef APP-PLUS -->
        <view class="t-c" @click="linkShare">
          <button class="fillButton">
            <image :src="imgHost + 'dxSelect/share_lj.png'" class="shareIcon" mode=""></image>
            <view class="f-26 mt-10 color_grey66">链接分享</view>
          </button>
        </view>
        <!-- #endif -->
      </view>
      <view class="bd-ee"></view>
    </view>
  </uni-popup>
</template>

<script>
  import Config from '@/util/config.js';
  export default {
    data() {
      return {
        imgHost: getApp().globalData.imgsomeHost,
        shareContentInfo: {}
      };
    },
    methods: {
      //关闭弹窗
      closeDialog() {
        // 分享
        this.$refs.sharePopup.close();
      },
      open(info) {
        this.$refs.sharePopup.open();
        this.shareContentInfo = info;
        console.log('🚀 ~ open ~ shareContentInfo:', this.shareContentInfo);
      },
      // 点击海报
      posterShare() {
        this.$refs.sharePopup.close();
        //埋点-分享
        getApp().sensors.track('posterSharingClick', {
          name: '海报分享',
          goodId: this.shareContentInfo.id
        });
        uni.navigateTo({
          url: `/splitContent/poster/index?type=${this.shareContentInfo.type}&id=${this.shareContentInfo.id}`
        });
      },
	  
      shareLink() {
		  console.log('链接分享--------------')
        getApp().sensors.track('linkSharingClick', {
          name: '链接分享',
          goodId: this.shareContentInfo.id,
        });
      },
	  
      ///app链接分享 报错 图片太大
      linkShare() {
        setTimeout(() => {
          this.$refs.sharePopup.close();
        }, 2000);
        uni.share({
          provider: 'weixin',
          scene: 'WXSceneSession',
          type: 5,
          title: this.shareContentInfo.title ? this.shareContentInfo.title : '叮，你的好友敲了你一下，赶紧过来看看',
          imageUrl: this.shareContentInfo.imgurl, //分享封面
          miniProgram: {
            id: Config.miniOriginalId,
            path: '/pages/beingShared/index?scene=' + uni.getStorageSync('user_id') + '&type=' + this
              .shareContentInfo.type + '&id=' + this.shareContentInfo.id,
            type: 0,
            webUrl: Config.webUrl
          },
          success: (ret) => {
            console.log(JSON.stringify(ret));
            //埋点-分享
            getApp().sensors.track('linkSharingClick', {
              name: '链接分享',
              goodId: this.shareContentInfo.id,
            });
            uni.showToast({
              icon: 'none',
              title: '分享成功'
            });
          },
          fail: (ret) => {
            console.log(JSON.stringify(ret));
            uni.showToast({
              icon: 'none',
              title: '分享失败'
            });
          }
        });
      }
    }
  };
</script>