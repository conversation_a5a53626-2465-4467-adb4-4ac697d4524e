<template>
  <view class="reviewBox">
    <view>
      <view class="header">
        <uni-icons v-if="!isShare" type="arrowleft" class="backIcon" size="20" color="#000000" @click="back1"></uni-icons>
        <text class="fuxitext">{{ showData.studyData }}复习报告</text>
      </view>

      <view class="record_body" :style="'height: ' + bodyHeight + 'rpx;'">
        <view class="reviewUp_box">
          <!-- <view class="review_share" @click="shareReviewReport">
						<image src="../../static/images/review_share.png" mode=""></image>
					</view> -->
          <!-- <button open-type="share" class="review_share">
						<image src="../../static/images/review_share.png" mode=""></image>
					</button> -->
          <view class="review_rate">
            <!-- 复习报告图 -->
            <image class="rate_bgImage" src="https://document.dxznjy.com/applet/interesting/review_rate.png" mode=""></image>
            <view class="rate_num">
              {{ showData.rate }}
              <text>%</text>
              <view class="review_rate_text">正确率</view>
            </view>
            <view class="review_task">本场作业完成情况统计</view>
            <view class="student-name-code">
              <view>学生姓名：{{ showData.studentName }}</view>
              <view style="margin-top: 14rpx">学生编号：{{ urlStundentCode }}</view>
            </view>
          </view>

          <view class="over_review_census" v-if="isType == 0">
            <view class="review-box">
              <view>
                <view class="review_litleTitle review_color1" style="margin-right: 20rpx">
                  <!-- 									<view style="color: #F1AC71;font-weight: 600;">{{showData.wordCount}}个</view>
									<view style="font-size: 30rpx;color: #915617;">需复习</view> -->
                  <view style="color: #f1ac71; font-weight: 600">{{ wrongList.length || 0 }}个</view>
                  <view style="font-size: 30rpx; color: #915617">遗忘数</view>
                </view>
              </view>
              <view>
                <view class="review_litleTitle review_color3" style="margin-right: 20rpx">
                  <!-- 									<view style="color: #4AD0FF;font-weight: 600;">{{wrongList.length}}个</view>
									<view style="font-size: 30rpx;color: #1B85A9;">遗忘数</view> -->
                  <view style="color: #4ad0ff; font-weight: 600">{{ showData.wordCount - wrongList.length || 0 }}个</view>
                  <view style="font-size: 30rpx; color: #1b85a9">正确数</view>
                </view>
              </view>
              <view>
                <view class="review_litleTitle review_color2">
                  <!-- 									<view style="color: #07DCB2;font-weight: 600;">{{showData.wordCount}}个</view>
                             		<view style="font-size: 30rpx;color: #0A856D;">已复习</view> -->
                  <view style="color: #07dcb2; font-weight: 600">{{ showData.wordCount || 0 }}个</view>
                  <view style="font-size: 30rpx; color: #0a856d">已复习</view>
                </view>
              </view>
            </view>
          </view>
          <view v-else class="over_review_census">
            <view>
              <view class="review_litleTitle-1 review_color1" style="margin-bottom: 30rpx">
                <view style="margin-left: 20rpx; color: #f1ac71; font-weight: 600">{{ showData.totalWordCount || 0 }}个</view>
                <view style="margin-left: 20rpx; font-size: 30rpx; color: #915617">需复习</view>
              </view>
              <view class="review_litleTitle-1 review_color3">
                <view style="margin-left: 20rpx; color: #4ad0ff; font-weight: 600">{{ wrongList.length || 0 }}个</view>
                <view style="margin-left: 20rpx; font-size: 30rpx; color: #1b85a9">遗忘数</view>
              </view>
            </view>
            <view>
              <view class="review_litleTitle-1 review_color2" style="margin-bottom: 30rpx">
                <view style="margin-left: 20rpx; color: #07dcb2; font-weight: 600">{{ showData.wordCount || 0 }}个</view>
                <view style="margin-left: 20rpx; font-size: 30rpx; color: #0a856d">已复习</view>
              </view>
              <view class="review_litleTitle-1 review_color4">
                <view style="margin-left: 20rpx; color: #7676e9; font-weight: 600">{{ showData.totalWordCount - showData.wordCount || 0 }}个</view>
                <view style="margin-left: 20rpx; font-size: 30rpx; color: #3e3ebf">未复习</view>
              </view>
            </view>
          </view>
        </view>
        <view class="study_records" :style="'min-height: ' + recordHeight + 'rpx;'">
          <view class="study_record_titles pl-40">本次遗忘记录</view>

          <view class="record_listBox">
            <view v-if="wrongList && wrongList.length > 0" class="pl-40" style="padding-right: 40rpx">
              <view class="recordList" @click="sayWord(item.word)" v-for="(item, index) in wrongList" :key="index">
                <view class="recordWord">
                  <view>
                    <text class="recordIcon" :class="item.correct == 0 ? 'recordIcon1' : ''"></text>
                    <text class="record_title">{{ item.word }}</text>
                  </view>
                  <view class="record_right_container">
                      <view class="record_round_info" v-if="item.lastReviewCycle !== undefined && item.lastReviewCycle !== null && item.lastReviewCycle !== '' && item.lastReviewCycle !== 0">
                          <text class="round_text">第{{ item.lastReviewCycle }}轮</text>
                      </view>
                      <image style="width: 44rpx; height: 36rpx" src="https://document.dxznjy.com/applet/newimages/img_laba.png" mode="widthFix"></image>
                  </view>
                </view>

                <view class="recordTranst">
                  {{ item.translation }}
                </view>
              </view>
            </view>
            <view class="lake_page" v-if="wrongList && wrongList.length == 0">
              <image src="https://document.dxznjy.com/applet/newimages/zan.png" mode="widthFix"></image>
              <view class="lake_page-text">
                <view>本次没有遗忘单词哦</view>
                <view>请再接再厉~</view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="reviewBottom" v-if="!isShare">
        <view class="reviewLeft_btn reviewBtn" @click="goUrl(`/antiAmnesia/review/history?studentCode=${showData.studentCode}&reviewType=0`)">往期复习</view>
        <button class="reviewRight_btn_1 reviewBtn" hover-class="none" open-type="share">分享链接给家长</button>
      </view>
    </view>
  </view>
</template>

<script>
  var innerAudioContext;
  export default {
    data() {
      return {
        src: 'http://*************:5000/v/b88b6ff1fa28125e',
        showData: {}, //复习字段
        echartsList: 0, //echarts数据
        wrongList: [],
        reviewId: null,
        postImage: getApp().globalData.postHost,
        recordHeight: 0,
        bodyHeight: 0,
        urlStundentCode: '',
        recordInnerHeight: 250,

        isplay: false,
        isShare: false,
        isType: 0 //1刚复习  0复习报告
      };
    },

    onShareAppMessage() {
      return {
        title: '21天抗遗忘打卡',
        imageUrl: '', //分享封面
        path: '/antiAmnesia/antiForgetting/historyReviewReport?reviewId=' + this.reviewId + '&isShare=true&isType=0'
      };
    },
    onLoad(options) {
      this.reviewId = options.reviewId;
      this.isShare = options.isShare == 'true' ? true : false;
      this.isType = options.isType == '1' ? 1 : 0;
      let nowScreenHeight = this.$util.pxTorpx();
      this.bodyHeight = nowScreenHeight - 240;
      this.recordHeight = nowScreenHeight - 940;
      this.recordInnerHeight = nowScreenHeight - 1000;
      uni.showLoading({
        title: '加载中...'
      });
      this.getReviewReport(this.reviewId);

      let that = this;
      innerAudioContext = uni.createInnerAudioContext();
      innerAudioContext.onPlay(() => {
        ('开始播放');
      });
      innerAudioContext.onStop(function () {
        that.isplay = false;
      });
      innerAudioContext.onPause(function () {
        that.isplay = false;
      });
      innerAudioContext.onError(() => {
        that.isplay = false;
      });
    },
    methods: {
      async getReviewReport(id) {
        let result = await this.$httpUser.get('znyy/review/query/student/word/review/detail?reviewId=' + id);
        if (result) {
          if (result.data.data) {
            this.showData = result.data.data;
            if (this.showData.totalWordCount == 0) {
              this.isType = 0;
            }
            this.showData.totalWordCount = Number(this.showData.totalWordCount) + Number(this.showData.wordCount);
            this.urlStundentCode = result.data.data.studentCode;
            // console.log(this.showData, "this.showData");
            this.echartsList = result.data.data.rate;
          }
          //   console.log(this.echartsList, "echartsList父组件");
          let words = JSON.parse(this.showData.words);
          this.wrongList = words.filter((ele) => ele.correct == 0);
          //   console.log(this.wrongList, "this.wrongList");
        }
        setTimeout(function () {
          uni.hideLoading();
        }, 200);
      },
      //返回上一页
      back1() {
        if (this.isShare) {
          uni.switchTab({
            url: '/pages/index/index'
          });
        } else {
          uni.navigateBack({
            delta: 1
          });
        }
      },
      goUrl(url) {
        uni.navigateTo({
          url: url
        });
      },

      sayWord(word) {
        var that = this;
        that.$httpUser
          .get('znyy/app/query/word/voice', {
            word: word
          })
          .then((result) => {
            if (result.data.success) {
              var w = encodeURIComponent(result.data.data);
              var linkUrl;
              if (w.endsWith('.mp3')) {
                linkUrl = 'https://document.dxznjy.com/' + w;
              } else {
                linkUrl = 'https://document.dxznjy.com/' + w + '.mp3';
              }
              innerAudioContext.obeyMuteSwitch = false;
              innerAudioContext.src = linkUrl;
              innerAudioContext.play();
            } else {
              that.$util.alter(result.data.message);
            }
          });
      },

      //获取海报图片
      shareReviewReport() {
        // console.log("分享海报");
        let that = this;
        uni.request({
          url: that.postImage + 'api/link',
          data: {
            'zt#syh_rightRate': that.showData.rate,
            'aRig_zt#syh_alreadyReview': that.showData.reviewWordVM.length,
            'aRig_zt#syh_noReview': that.showData.wordCount - that.showData.reviewWordVM.length,
            'aRig_zt#syh_needReview': that.showData.wordCount,
            accessKey: 'ApfrIzxCoK1DwNZO',
            secretKey: 'EJCwlrnv6QZ0PCdvrWGi',
            posterId: 7
          },
          method: 'POST',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            // console.log(res.data);
            let urlString = res.data.url;
            that.src = that.postImage + 'v/' + urlString.split('/v/')[1];
            uni.navigateTo({
              url: '/interestModule/playbill?url=' + that.src
            });
          }
        });
      }
    }
    // onShareAppMessage(res) {
    // 	if (res.from === 'button') { // 来自页面内分享按钮
    // 		console.log(res.target)
    // 	}
    // 	return {
    // 		title: '自定义分享标题',
    // 		path: '/pages/test/test?id=123'
    // 	}
    // }
  };
</script>

<style lang="less">
  page {
    height: 100%;
  }

  .student-name-code {
    width: 100%;
    text-align: center;
    position: absolute;
    font-family: 'syhtM';
    bottom: -20rpx;
    font-size: 30rpx;
  }

  .review-box {
    display: flex;
    justify-content: space-between;
  }
  .review_litleTitle-1 {
    width: 290rpx;
    height: 160rpx;
    border-radius: 14rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
  }
  .review_litleTitle {
    width: 190rpx;
    height: 160rpx;
    border-radius: 16rpx;
    font-family: 'syhtM';
    font-size: 38rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
  }

  .study_records {
    width: 658rpx;
    padding-top: 15rpx;
    background: white;
    margin-top: 30rpx;
    box-shadow: 2px 4px 20px 1px rgba(0, 0, 0, 0.12);
    border-radius: 14rpx;
    box-sizing: border-box;
    margin: 0 auto;
  }

  .study_record_titles {
    font-size: 32rpx;
    font-family: AlibabaPuHuiTiM;
    color: #000000;
    font-weight: bold;
    margin-top: 20rpx;
  }

  .header {
    padding: 84rpx 0 20rpx 30rpx;
    box-sizing: border-box;
    text-align: center;
    position: relative;
  }

  .backIcon {
    position: absolute;
    left: 30rpx;
    top: 90rpx;
  }

  .lake_page {
    margin-top: 120rpx;
    display: flex;
    flex-direction: column;
    align-items: center;

    image {
      width: 200rpx;
      height: 160rpx;
      margin-bottom: 30rpx;
    }
  }

  .lake_page-text {
    text-align: center;
    font-size: 30rpx;
    font-family: SourceHanSansSC-Regular, SourceHanSansSC;
    font-weight: 400;
    color: #666666;
    line-height: 45rpx;
  }

  .over_review_census {
    margin: 0 auto;
    width: 610rpx;
    display: flex;
    justify-content: space-between;
  }

  .fuxitext {
    color: #ffffff;
    margin-top: 63rpx;
  }

  .record_right_container {
    display: flex;
    align-items: center;
    position: relative;
  }

  .record_round_info {
    position: absolute;
    right: 30rpx;
    display: flex;
    align-items: center;
  }

  .round_text {
    width: 100rpx;
    color: #808080;
    font-size: 24rpx;
    font-family: 'syhtM';
  }
</style>