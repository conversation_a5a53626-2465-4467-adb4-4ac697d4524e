import Request from './request';
import Config from '../config.js';
import Util from './util.js';

var httpArray = new Array();

const httpUser = new Request();

httpUser.setConfig((config) => {
  /* cxy设置全局配置 */
  config.baseUrl = Config.DXHost; /* 根域名不同 */
  config.header = {
    'content-type': 'application/json;charset=UTF-8',
    'x-www-endorsement': ''
  };
  return config;
});

httpUser.interceptor.request((config, cancel) => {
  /* 请求之前拦截器 */
  Util.checkNetwork();
  var Logintoken = uni.getStorageSync('token');
  // debugger
  // cxy 2021 4.21添加 趣味复习token不同于其它板块（接口调用pad）

  // 门店充值需要的token
  let payToken = uni.getStorageSync('payToken');
  let wxpay = uni.getStorageSync('wxpay');
  // if(payToken){
  // 	Logintoken = payToken
  // }

  var LogintokenReview = uni.getStorageSync('logintokenReview');
  let pages = getCurrentPages();
  let route = pages[pages.length - 1].route;
  var isReview = false; //是否是趣味复习需要znyy token
  if (route.indexOf('review/funReview') != -1 || route.indexOf('pages/interest/') != -1 || route.indexOf('pages/wordCheck') != -1) {
    if (route.indexOf('pages/interest/orderDetail') != -1) {
      isReview = false;
    } else {
      isReview = true;
    }
  }
  config.header = {
    ...config.header
  };
  config.header['www-cid'] = 'dx_alading_resource';
  // config.header['dx-source'] = "ZHEN_XUAN"
  // #ifdef APP-PLUS
  config.header['dx-source'] = 'ZHEN_XUAN##ANDROID##APP';
  // #endif
  // #ifdef MP-WEIXIN
  config.header['dx-source'] = 'ZHEN_XUAN##WX##MINIAPP';
  config.header['temp-dx-source'] = 'ZHEN_XUAN##WX##MINIAPP';
  const appVersion = Config.wxAppVersion;
  if (appVersion) {
    config.header['dx-app-version'] = appVersion;
  }
  // #endif
  let isDyy = uni.getStorageSync('isDyy');
  if (config.url.indexOf('mps/line/collect/order/unified/multi/collect/check') > -1 && isDyy) {
    config.header['dx-source'] = 'ZNYY##WX##MINIAPP';
  }

  if (wxpay || payToken) {
	let bussinessTag = uni.getStorageSync('bussinessTag'); // 时长充值多用户收款接口
    if (config.url.indexOf('mps/line/collect/order/unified/collect') > -1 || bussinessTag == 'STUDENT_LINE_BUY_COURSE_DELIVER') {
      config.header['dx-source'] = 'ZNYY##WX##MINIAPP';
    }
	uni.removeStorageSync('bussinessTag');
  }

  if (config.url.indexOf('zx/exp/sendRedPacket') > -1) {
    config.header['dx-source'] = 'ZHEN_XUAN##WX##MINIAPP';
  }
  if (config.url.indexOf('scrm/qywechat/getQrcodeByCode') > -1) {
    config.header['dx-source'] = 'ZHEN_XUAN##WX##MINIAPP';
    delete config.header['Token'];
    delete config.header['x-www-iap-assertion'];
  }
  if (Logintoken != '' && Logintoken != undefined && Logintoken.length > 5) {
    if (LogintokenReview != '' && LogintokenReview != undefined) {
      //如果趣味复习有pad的token则代表在趣味复习板块
      config.header['x-www-iap-assertion'] = LogintokenReview;
    } else {
      config.header['x-www-iap-assertion'] = Logintoken;
    }
    if (config.url.indexOf('scrm/qywechat/getQrcodeByCode') > -1) {
      // console.log(222222222222222222222)
      config.header['dx-source'] = 'ZHEN_XUAN##WX##MINIAPP';
      delete config.header['Token'];
      delete config.header['x-www-iap-assertion'];
    }
  } else {
    // #ifdef APP-PLUS
    config.header = {
      'www-cid': 'dx_alading_resource',
      'content-type': 'application/json;charset=UTF-8',
      'dx-source': 'ZHEN_XUAN##ANDROID##APP'
    };
    // #endif
    // #ifdef MP-WEIXIN
    config.header = {
      'www-cid': 'dx_alading_resource',
      'content-type': 'application/json;charset=UTF-8',
      'dx-source': 'ZHEN_XUAN##WX##MINIAPP'
    };
    // #endif
  }

  if (config.url.indexOf('znyy/areas/student/charge/expReward/save') > -1) {
    config.header['dx-source'] = 'ZHEN_XUAN##WX##MINIAPP';
    config.header['Token'] = Logintoken;
    config.header['x-www-iap-assertion'] = payToken;
  }
  config.header['mini-app-withdraw'] = 'true';
  return config;
});

httpUser.interceptor.response((response) => {
  // console.log(response, 'response');
  /* 请求之后拦截器 */
  delete httpArray[response.config.hashData];
  if (response.data.status == 1 || response.data.status == 20004 || response.data.code == 20000) {
    return response;
  } else if (response.statusCode == 404 || response.data.status == 10000 || response.data.status == 10003 || response.data.code == 50003) {
    var islogin = uni.getStorageSync('isLogin');
    // uni.setStorageSync('isLogin', false)
    // uni.removeStorageSync("logintoken")
    // if (islogin == ''||islogin==false) {
    // 	uni.navigateTo({
    // 		url: '/pages/login/login'
    // 	});
    // 	uni.setStorageSync('isLogin', 'islogin')
    // }
    return response;
  } else if (response.statusCode == 401 || response.statusCode == 50004) {
    var jump = uni.getStorageSync('jump'); //以下解决多次跳转登录页的重点
    if (!jump) {
      //以下做token失效的操作
      setTimeout(() => {
        uni.navigateTo({
          url: '/Personalcenter/login/login'
        });
      }, 100);
      uni.removeStorage({
        key: 'token'
      });
      uni.setStorageSync('jump', 'true');
    }
  } else {
    if (response.config.url.indexOf('v2/mall/getStudentMerchantList') > -1) {
      if (response.data.message == '需购买趣味复习') {
        console.log('不显示提示');
      } else {
        uni.showToast({
          icon: 'none',
          title: response.data.message,
          duration: 2000
        });
      }
    } else {
      uni.showModal({
        title: '温馨提示',
        content: response.data.message,
        showCancel: false
      });
    }

    return response;
  }
});
export { httpUser };
