<template>
  <view class="funContent">
    <image src="https://document.dxznjy.com/applet/newInteresting/interesting_sccg_bg.png" mode="widthFix" class="bg-image"></image>
    <view class="page-content">
      <interesting-head :title="titleText" @backPage="backPage" :hasTitleBg="true" :closeWhite="false" :hasRight="false"></interesting-head>

      <!-- 趣味复习标题 -->
      <view class="interesting_title" @click="playWord(showListData[qIdIndex].word)">
        <text>{{ showListData[qIdIndex].word }}</text>
        <image class="title_icon"></image>
      </view>

      <!-- 答案列表 -->
      <view class="answerListBox">
        <view
          class="answerList"
          :class="index != chooseAnswerIndex ? 'answerNormal' : isRight ? 'answerRight' : 'answerWrang'"
          v-for="(item, index) in showListData[qIdIndex].answerList"
          :key="index"
          @click="chooseAnswer(item, index)"
        >
          <image class="answerList_icon"></image>
          <text :class="index == chooseAnswerIndex ? 'answerListWhiteText' : 'answerListText'">{{ item }}</text>
          <image
            v-if="index == chooseAnswerIndex"
            class="answerIcon"
            :src="isRight ? imgHost + 'newInteresting/interesting_tyby_right.png' : imgHost + 'newInteresting/interesting_tyby_wrong.png'"
            mode=""
          ></image>
        </view>
      </view>

      <view class="userProgressbg">
        <view class="userProgress">
          <cmd-progress
            :percent="((downTimeCount - showDownTime) * 100) / downTimeCount"
            :showInfo="false"
            status="normal"
            :stroke-width="20"
            stroke-color="#50AE15"
          ></cmd-progress>
        </view>
        <view class="interesting_countdown">
          <text style="">{{ showDownTime == undefine || showDownTime < 0 ? 0 : showDownTime }}s</text>
        </view>
      </view>
    </view>

    <!--结束弹窗 -->
    <uni-popup ref="popopPower" type="center" :maskClick="true" :classBG="''">
      <interesting-dialog
        :gradScreenWheel="3"
        :pageUrl="'interestingSccg'"
        :play="play"
        :scheduleCode="showData.scheduleCode"
        :showData="showData"
        @closeDialog="closeDialog1"
      ></interesting-dialog>
    </uni-popup>

    <!-- 引导 -->
    <uni-popup ref="guideOne" type="bottom" :maskClick="true" :classBG="''">
      <view style="position: relative">
        <image style="width: 100%; height: 100vh" src="https://document.dxznjy.com/applet/newInteresting/interesting_guide_sccg_one.png"></image>
        <image class="guide_btn_next" @click="guideNext()" src="https://document.dxznjy.com/applet/newInteresting/interesting_guide_next.png"></image>
      </view>
    </uni-popup>
    <uni-popup ref="guideTwo" type="bottom" :maskClick="true" :classBG="''">
      <view style="position: relative">
        <image style="width: 100%; height: 100vh" src="https://document.dxznjy.com/applet/newInteresting/interesting_guide_sccg_two.png"></image>
        <image class="guide_btn_close" @click="guideClose()" src="https://document.dxznjy.com/applet/newInteresting/interesting_guide_end.png"></image>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import interestingHead from './components/interesting-head/interestingHead.vue';
  import interestingDialog from './components/interesting-dialog/review.vue';
  import cmdProgress from './components/cmd-progress/cmd-progress.vue';

  export default {
    components: {
      interestingHead,
      interestingDialog,
      cmdProgress
    },
    data() {
      return {
        nowGroup: '',
        nowLevel: '',
        waitGroup: '',
        imgHost: getApp().globalData.imguseHost,
        titleText: '识词冲关',
        courseCode: '', //课程编号
        scheduleCode: '', //课程进度
        showListData: [], //当前课程玩法组所有的单词
        showData: {}, //当前课程所有数据
        distrubList: [], //干扰项列表数据
        qIdIndex: 0, //当前题目编号
        play: '1', //玩法
        otherWord: false, //除当前玩法其它玩法是否还有单词
        chooseAnswerIndex: -1, //选择第几个答案
        successList: [], //正确列表数据存储
        errorList: [], //错误列表数据存储
        correctRate: 0, //正确率
        gradScreenWheel: 1, //1组 2关 3轮 ，没有正确率这些数据的时候给0
        isRight: null,
        wordInfo: {
          roundId: null,
          scheduleCode: null,
          courseCode: null,
          nowGroup: null,
          wordCount: null,
          studentWordId: null,
          play: '1'
        },
        downTimeCount: getApp().globalData.interestDownTime,
        showDownTime: this.downTimeCount,
        countdown: null,

        isEnd: false, //当前玩法是否结束

        isGuide: 0, //0未显示过引导-下一步 1知道了-已完成引导 2已完成引导
        screenHeight: 0,
        screenWidth: 0,

        timbre: 'W', // 音色默认女声 M  W
        pronunciationType: 0, // 1英式  0美式  默认美式
        playType: 2, // 版本
        linkUrl: ''
      };
    },

    onLoad(options) {
      this.getHeight();
      this.showData = JSON.parse(decodeURIComponent(options.params));
      this.getWordversion();
      this.isGuide = uni.getStorageSync('sccgGuide');
      if (!this.isGuide) {
        this.isGuide = 0;
      }
      if (this.isGuide == 0) {
        this.$refs.guideOne.open();
      }
      if (this.isGuide == 1) {
        this.$refs.guideTwo.open();
      }
      if (this.isGuide == 2) {
        this.getShowData();
      }
    },
    methods: {
      getHeight() {
        // 获取系统信息
        const systemInfo = uni.getSystemInfoSync();

        // 获取屏幕高度
        this.screenHeight = systemInfo.windowHeight * 2;
        this.screenWidth = systemInfo.windowWidth * 2;
        // 打印屏幕高度
        console.log(this.screenHeight, 999);
      },
      getShowData() {
        let that = this;
        that.nowGroup = that.showData.levelGroup;
        that.nowLevel = that.showData.nowLevel;
        that.waitGroup = that.showData.levelTotalGroup - that.showData.levelGroup;
        that.wordInfo.nowGroup = that.showData.nowGroup;
        that.wordInfo.wordCount = that.showData.wordCount;
        that.wordInfo.nowLevel = that.showData.nowLevel;
        that.wordInfo.roundId = that.showData.roundId;
        that.wordInfo.scheduleCode = that.showData.scheduleCode;

        that.getNoLevelData();
        //                // 判断除当前组之外其它组是否还有单词
        //                if (that.showData.cos.play2.length == 0 && that.showData.cos.play3.length == 0 && that.showData.cos
        //                    .play4.length ==
        //                    0) {
        //                    that.otherWord = false;
        //                } else {
        //                    that.otherWord = true;
        //                    that.gradScreenWheel = 4;
        //                }
        //                if (!that.otherWord) {
        //                    // 判断这组结束是组还是轮还是关
        //                    if (!that.showData.status) {
        //                        that.gradScreenWheel = 1;
        //                    }
        //                    if (that.showData.status && that.showData.nowLevel != that.showData.totalLevel) {
        //                        that.gradScreenWheel = 2;
        //                    } //当前关数==总关数 代表是最后一关，所以是一轮结束(其它组都为空的时候才可以开启下一轮)
        //                    if (that.showData.status && that.showData.nowLevel == that.showData.totalLevel) {
        //                        that.gradScreenWheel = 3;
        //                    }
        //                }
        //                if (that.showData.groupEnd) {
        //                    that.judgeNewGroup();
        //                }
      },

      // 获取当前学员设置的语音版本
      getWordversion() {
        var that = this;
        that.$httpUser
          .get('znyy/course/info', {
            studentCode: that.showData.studentCode
          })
          .then((res) => {
            if (res.data.success) {
              //  console.log(res.data.data)
              let name = res.data.data.voiceModel.split('#');
              that.pronunciationType = name[1];
              that.timbre = name[2];
              that.playType = name[0];
            } else {
              that.$util.alter(res.data.message);
            }
          });
      },

      getNoLevelData() {
        let that = this;
        that.$httpUser.get('znyy/course/query/fun/words/byType?scheduleCode=' + that.showData.scheduleCode + '&type=1').then((res) => {
          if (res.data.success) {
            if (res.data.data && res.data.data.length != 0) {
              that.showListData = res.data.data;
              that.qIdIndex = 0;
              that.chooseAnswerIndex = -1;
              // 对答案进行操作
              that.wordExecute();
            } else {
              that.endDialog();
              return;
            }
          } else {
            uni.navigateBack({ delta: 1 });
            // uni.redirectTo({
            //     url: '/antiAmnesia/review/funReview?scheduleCode=' + that.showData.scheduleCode
            // })
            return;
          }
        });
      },
      //播放音频
      playWord(word) {
        this.$playVoice(word, false, this.timbre, this.pronunciationType, this.playType, this.showData.studentCode);
      },

      //选择答案
      chooseAnswer(item, index) {
        if (this.isEnd) {
          this.$util.alter('当前玩法已结束');
          return;
        }
        if (this.isRight) {
          return;
        }

        let that = this;
        that.chooseAnswerIndex = index;
        //是否正确
        if (that.showListData[that.qIdIndex].translation == that.showListData[that.qIdIndex].answerList[index]) {
          //  console.log("回答正确")
          that.$playVoice('task_success.mp3', true, that.timbre, that.pronunciationType, that.playType, that.showData.studentCode);
          that.isRight = true;
          that.doItRight();
          return;
        }

        //回答错误
        //  console.log("回答错误")
        that.$playVoice('ino.mp3', true, that.timbre, that.pronunciationType, that.playType, that.showData.studentCode);
        that.isRight = false;

        that.markWrongWord();
      },
      //答对一个单词
      doItRight() {
        let that = this;
        that.newMarkRightWord();
        // that.markRightWord();
        that.nextQuestion();
      },

      // 标记正确单词--old
      markRightWord() {
        //  console.log("标记正确单词")
        let that = this;
        that.wordInfo.studentWordId = that.showListData[that.qIdIndex].id;
        that.$httpUser.post(`znyy/course/fun/play/word`, that.wordInfo).then((res) => {
          if (!res.data.success) {
            that.$util.alter(res.data.message);
          } else {
            var errHas = that.errorList.some((item) => {
              if (that.showListData[that.qIdIndex].translation == item.translation) {
                return true;
              }
            });
            if (!errHas) {
              that.successList.push(that.showListData[that.qIdIndex]);
            }
          }
        });
      },
      // 标记正确单词--new
      newMarkRightWord() {
        //  console.log("标记正确单词")
        let that = this;
        that.wordInfo.studentWordId = that.showListData[that.qIdIndex].id;
        that.$httpUser.post(`znyy/course/noLevel/fun/play/word/finish`, that.wordInfo).then((res) => {
          if (!res.data.success) {
            that.$util.alter(res.data.message);
          } else {
            var errHas = that.errorList.some((item) => {
              if (that.showListData[that.qIdIndex].translation == item.translation) {
                return true;
              }
            });
            if (!errHas) {
              that.successList.push(that.showListData[that.qIdIndex]);
            }
          }
        });
      },

      // 标记错误单词
      markWrongWord() {
        //  console.log("标记错误单词")
        let that = this;
        that.wordInfo.studentWordId = that.showListData[that.qIdIndex].id;
        // that.$httpUser.post("znyy/course/mark/wrong/words", that.wordInfo).then((res) => {
        //     if (res.data.success) {
        //         //  console.log("标记错误成功");
        //         //  console.log(res.data.data)
        var errHas = that.errorList.some((item) => {
          if (that.showListData[that.qIdIndex].word == item.word) {
            return true;
          }
        });
        var succHas = that.successList.some((item) => {
          if (that.showListData[that.qIdIndex].word == item.word) {
            return true;
          }
        });
        if (!errHas && !succHas) {
          that.errorList.push(that.showListData[that.qIdIndex]);
        }
        // 如果倒计时结束就标记玩过
        if (that.chooseAnswerIndex == -1) {
          that.newMarkRightWord();
          // that.markRightWord()
        }
        // } else {
        //     that.$util.alter(res.data.message)
        // }
        // })
      },

      // 下一题
      nextQuestion() {
        let that = this;
        clearInterval(that.countdown);
        that.countdown = null;
        if (that.qIdIndex < that.showListData.length - 1) {
          setTimeout(function () {
            that.qIdIndex++;
            that.chooseAnswerIndex = -1;
            that.wordExecute();
          }, 1000);
        } else {
          //提交趣味复习单词
          setTimeout(function () {
            that.getNoLevelData();
          }, 600);
        }
      },

      // 结束弹窗
      endDialog() {
        let that = this;
        that.isEnd = true;
        clearInterval(that.countdown);
        that.countdown = null;
        if (that.successList.length + that.errorList.length == 0) {
          that.correctRate = 0;
        } else if (that.successList.length != 0 && that.errorList.length == 0) {
          that.correctRate = 100;
        } else {
          that.correctRate = parseInt((that.successList.length * 100) / (that.successList.length + that.errorList.length));
          if (isNaN(that.correctRate)) {
            that.correctRate = 0;
          }
        }
        that.$refs.popopPower.open();
      },

      //题目执行
      wordExecute() {
        let that = this;
        that.downFun();
        that.showDownTime = that.downTimeCount;
        let newArr = [];
        that.isRight = null;
        that.distrubList = that.$util.randomSort(that.$distrubChinese).slice(0, 5);
        let answerListSingle = [];
        let forLen = 3; //干绕项去重
        for (var i = 0; i < forLen; i++) {
          if (that.distrubList[i] == that.showListData[that.qIdIndex].translation) {
            that.distrubList.splice(i, 1);
            forLen++;
          } else {
            answerListSingle.push(that.distrubList[i]);
          }
        }
        answerListSingle.push(that.showListData[that.qIdIndex].translation);
        that.showListData[that.qIdIndex].answerList = answerListSingle.sort();
        // 读单词
        that.$playVoice(that.showListData[that.qIdIndex].word, false, that.timbre, that.pronunciationType, that.playType, that.showData.studentCode);
      },

      // 是否进入下一组
      judgeNewGroup() {
        this.endDialog();
      },

      // 倒计时执行事件
      downFun() {
        let that = this;
        if (that.countdown == null) {
          that.countdown = setInterval(() => {
            this.showDownTime--;
            if (this.showDownTime < 0) {
              clearInterval(that.countdown);
              that.countdown = null;
              return;
            }
            if (this.showDownTime < 1) {
              that.$playVoice('game_over.mp3', true, that.timbre, that.pronunciationType, that.playType, that.showData.studentCode);
              if (that.chooseAnswerIndex == -1) {
                that.markWrongWord();
              }
              that.nextQuestion();
            }
          }, 1000);
        } else {
          that.showDownTime = that.downTimeCount;
        }
      },

      refreshWord() {
        this.courseCode = ''; //课程编号
        this.scheduleCode = ''; //课程进度
        this.showListData = []; //当前课程玩法组所有的单词

        this.distrubList = []; //干扰项列表数据
        this.qIdIndex = 0; //当前题目编号
        this.play = '1'; //玩法
        this.otherWord = false; //除当前玩法其它玩法是否还有单词
        this.chooseAnswerIndex = -1; //选择第几个答案
        this.successList = []; //正确列表数据存储
        this.errorList = []; //错误列表数据存储
        this.correctRate = 0; //正确率
        this.gradScreenWheel = 1; //1组 2关 3轮 ，没有正确率这些数据的时候给0
        this.isRight = null;
        this.wordInfo = {
          roundId: null,
          scheduleCode: null,
          courseCode: null,
          nowGroup: null,
          wordCount: null,
          studentWordId: null,
          play: '1'
        };
        const sc = this.showData.scheduleCode;
        this.$httpUser.get(`znyy/course/query/fun/words?scheduleCode=${sc}`).then((res) => {
          if (res.data.success) {
            (this.showData = {}), //当前课程所有数据
              (this.showData = res.data.data);
            this.showData.scheduleCode = sc;
            this.getShowData();
            this.closeDialog();
          } else {
            this.$util.alter(res.data.message);
          }
        });
      },
      //关闭弹窗
      closeDialog() {
        //  console.log("复习报告quxiao")
        this.$refs.popopPower.close();
      },

      //查看报告弹框关闭
      closeDialog1() {
        // #ifdef APP-PLUS
        this.backPage();
        // #endif
        this.$refs.popopPower.close();
      },

      // 返回上一页
      backPage() {
        clearInterval(this.countdown);
        this.countdown = null;
        //返回按钮
        uni.navigateBack({ delta: 1 });
        // uni.redirectTo({
        //     url: '/antiAmnesia/review/funReview?scheduleCode=' + this.showData.scheduleCode +
        //         '&merchantCode=' + this.showData.merchantCode
        // })
      },
      async guideNext() {
        this.isGuide = 1;
        await uni.setStorageSync('sccgGuide', this.isGuide);
        this.$refs.guideOne.close();
        this.$refs.guideTwo.open();
      },
      async guideClose() {
        this.isGuide = 2;
        await uni.setStorageSync('sccgGuide', this.isGuide);
        this.$refs.guideTwo.close();
        this.getShowData();
      }
    },

    onUnload() {
      //  console.log("--页面关闭后销毁实例--");
      // 页面关闭后销毁实例
      clearInterval(this.countdown);
      this.countdown = null;
    }
  };
</script>

<style>
  page {
    height: 100vh;
    padding: 0;
  }
</style>
<style lang="scss">
  .funContent {
    width: 100%;
    // padding: 0 30rpx;
    box-sizing: border-box;
    height: 100%;
    // background: url('https://document.dxznjy.com/applet/newInteresting/interesting_sccg_bg.png') 100% 100% no-repeat;
    // background-size: 100% 100%;
    position: relative;
  }
  .bg-image {
    width: 100%;
    /* height 不写，让小程序按比例自适应 */
  }

  .page-content {
    position: absolute;
    top: 0;
    left: 0;
    /* 保证和背景图在相同坐标系下 */
    width: 100%;
    height: 100%;
  }

  .interesting_title {
    width: 420rpx;
    height: 100rpx;
    display: grid;
    position: relative;
    z-index: 22;
    margin: 160rpx auto 90rpx auto;
    place-items: center; /* 居中 */
  }

  .interesting_title text {
    text-align: center;
    color: #black;
    font-weight: bold;
    font-size: 34rpx;
    max-width: 410rpx;
    word-wrap: break-word;
  }

  .answerListWhiteText {
    color: white;
    font-size: 35rpx;
    font-weight: bold;
  }

  .answerListText {
    color: #a26a3d;
    font-size: 34rpx;
    font-weight: bold;
  }

  .title_icon {
    top: -90rpx;
    right: -50rpx;
    position: absolute;
    width: 60rpx;
    height: 60rpx;
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_sccg_laba.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  /* 趣味复习倒计时 */
  .userProgressbg {
    position: fixed;
    bottom: 0;
    width: 100%;
    // margin-left: -30rpx;
    height: 118rpx;
    background-color: rgba(233, 255, 195, 1);
  }
  .userProgress {
    position: absolute;
    left: 27rpx;
    bottom: 46rpx;
    width: 610rpx;
    height: 40rpx;
  }
  .userProgress .cmd-progress-line {
    display: block !important;
  }
  .userProgress .cmd-progress-line .cmd-progress-inner {
    border: 4rpx solid rgba(214, 166, 76, 1);
    height: 40rpx;
    background-color: rgba(255, 246, 116, 1);
  }
  /deep/.cmd-progress-anim {
    background-image: none !important;
  }
  .interesting_countdown {
    position: absolute;
    bottom: 35rpx;
    right: 27rpx;
    font-size: 36rpx;
    color: rgba(80, 174, 21, 1);
    text-align: center;
  }

  .guide_btn_next {
    position: absolute;
    bottom: 60rpx;
    right: 64rpx;
    width: 269rpx;
    height: 142rpx;
  }

  .guide_btn_close {
    position: absolute;
    bottom: 456rpx;
    right: 64rpx;
    width: 269rpx;
    height: 142rpx;
  }

  .answerListBox {
    width: 660rpx;
    height: 760rpx;
    display: flex;
    justify-content: space-evenly;
    flex-wrap: wrap;
    margin: 130rpx auto 0 auto;
  }

  .answerRight {
    background-color: #2db702;
  }

  .answerWrang {
    background-color: #e84335;
  }

  .answerNormal {
    background-color: #fff7c7;
  }

  .answerList {
    position: relative;
    width: 260rpx;
    height: 220rpx;
    margin-top: 80rpx;
    text-align: center;
    // line-height: 220rpx;
    box-sizing: border-box;
    border-radius: 24rpx;
    display: grid;
    place-items: center;
  }

  .answerList_icon {
    height: 128rpx;
    width: 113rpx;
    top: -100rpx;
    left: 75rpx;
    position: absolute;
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_sccg_icon.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  .answerIcon {
    top: 20rpx;
    right: 20rpx;
    position: absolute;
    width: 30rpx;
    height: 30rpx;
  }
</style>
