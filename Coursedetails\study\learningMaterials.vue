<template>
  <view class="study_materials_conent">
    <view class="study_materials_tabs">
      <view class="tab_box">
        <view :class="currentComponent === 'LearningMaterials' ? 'tabs_check' : 'tabs_item'" @click="changeTab('LearningMaterials')">学习资料</view>
        <view v-if="currentComponent === 'LearningMaterials'" class="tab_line"></view>
      </view>
      <view class="tab_box">
        <view :class="currentComponent === 'VideoMaterials' ? 'tabs_check' : 'tabs_item'" @click="changeTab('VideoMaterials')">视频资料</view>
        <view v-if="currentComponent === 'VideoMaterials'" class="tab_line"></view>
      </view>
    </view>
    <DownloadProfile v-if="currentComponent == 'LearningMaterials'" :onloadInfo="onloadInfo" ref="downloadProfileRef" />
    <view v-else class="video_main">
      <view v-if="!isSetting">
        <view class="video_css" style="height: 390rpx; width: 100%">
          <!-- 1723c88563aeacd1044215e78b07e449_1 -->
          <!--  :isAllowSeek="audition ? yes : no" -->
          <polyv-player
            v-if="videoInfo.videoUrl"
            :ref="'polyv_player' + videoInfo.videoId"
            :id="'polyv_player' + videoInfo.videoId"
            @timeupdate="onBindtimeupdate"
            :autoplay="false"
            :startTime="startTime"
            :playerId="playerIdcont"
            :vid="videoInfo.videoUrl"
            :width="width"
            :height="height"
            :ts="ts"
            :sign="sign"
            :isAllowSeek="audition ? yes : no"
            :preview="true"
            @pause="bindpause"
            @playing="bindplaying()"
            @loadedmetadata="bindloadedmetadata"
            @statechange="statechange"
            @fullscreenchange="bindfullscreenchange"
          ></polyv-player>
        </view>
        <view class="course_content">
          <view class="study_setting" @click="settingFn">
            <view>请选择你的学习视频</view>
            <view style="width: 39rpx; height: 36rpx">
              <image style="width: 100%; height: 100%" src="https://document.dxznjy.com/dxSelect/xkt/setting.png"></image>
            </view>
          </view>
          <view v-if="courseInfo.length !== 0">
            <learningCatalog v-for="(item, index) in courseInfo" @getVideoUrl="getVideoUrl" :key="item.id" :index="index" :item="item" @changeDown="changeDown"></learningCatalog>
          </view>
        </view>
      </view>
      <!-- 配置页 -->
      <view v-else>
        <view class="course_category_box">
          <view class="category_title">
            你学习的课程大类是
            <text>（支持多选）</text>
          </view>
          <view class="course_category">
            <view
              class="category_item"
              :class="item.indexShow ? 'category_item_check' : ''"
              :style="item.curriculumName.length >= 9 ? 'width:100%' : 'width:300rpx'"
              v-for="(item, index) in configList"
              :key="item.curriculumId"
              @click="curriculumStatusChange(item, index)"
            >
              <view>
                {{ item.curriculumName }}
              </view>
              <view style="width: 36rpx; height: 36rpx" v-if="item.indexShow">
                <image style="width: 100%; height: 100%; margin-left: 10rpx" class="image_css" src="https://document.dxznjy.com/dxSelect/xkt/chose.png"></image>
              </view>
            </view>
          </view>
        </view>
        <view class="course_category_box">
          <view class="category_title">
            你学习的课程名称是
            <text>（支持多选）</text>
          </view>
          <view class="course_category_Video" style="">
            <view v-for="courseItem in videoListAll" style="border-bottom: 1rpx soild #f0f0f0" :key="courseItem.curriculumId">
              <view style="width: 100%; height: 1rpx; background: #f0f0f0"></view>
              <view style="font-size: 26rpx; color: #555555; height: 36rpx; line-height: 36rpx; margin: 24rpx 0; display: flex; align-items: center; margin-top: 24rpx">
                <view style="width: 8rpx; height: 21rpx; background: #3eaa8c; margin: 0 12rpx 0 32rpx"></view>
                <view>{{ courseItem.curriculumName }}</view>
              </view>

              <view class="course_category">
                <view
                  class="category_item"
                  :class="videoItem.indexShow ? 'category_item_check' : ''"
                  :style="videoItem.courseName.length >= 9 ? 'width:100%' : 'width:300rpx'"
                  v-for="(videoItem, index) in courseItem.courseList"
                  :key="videoItem.courseId"
                  @click="courseStatusChange(courseItem, index)"
                >
                  <view>
                    {{ videoItem.courseName }}
                  </view>
                  <view style="width: 36rpx; height: 36rpx" v-if="videoItem.indexShow">
                    <image style="width: 100%; height: 100%; margin-left: 10rpx" class="image_css" src="https://document.dxznjy.com/dxSelect/xkt/chose.png"></image>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view style="display: flex">
          <view style="width: 50%; padding: 0 30rpx 0 10rpx">
            <view class="cancel_btn" @click="cancelSetting">取消</view>
          </view>
          <view style="width: 50%; padding: 0 10rpx 0 30rpx">
            <view class="submit_btn" @click="$noMultipleClicks(settingSubmit)">确定</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import DownloadProfile from './downloadProfile.vue';
  const MD5 = require('../../util/md5.js');
  // 修改下面的 vid 和 secretkey
  let secretkey = 'Jkk4ml1Of8';
  let vid = '';
  let ts = new Date().getTime();
  let sign = '';
  import learningCatalog from '../components/learningCatalog';
  const { $http } = require('@/util/methods.js');
  export default {
    components: {
      learningCatalog,
      DownloadProfile
    },
    data() {
      return {
        noClick: true, //防抖
        currentComponent: 'LearningMaterials', // 默认显示学习资料
        domId: 'polyvPlayer',
        playerIdcont: 'polyvPlayercont',
        vid: vid,
        ts: ts,
        sign: sign,
        width: '100%',
        height: '390rpx',
        timer: null,
        polyvPlayerContext: '',
        courseInfo: [],
        videoInfo: {},
        videoList: [],
        startTime: 0,
        onloadInfo: {},
        wifiShow: true,
        studyNumber: 0,
        audition: false,
        courseType: 1,
        type: 0,
        endTime: 180,
        alreadyWatch: true,
        direction: '',
        fullScreen: false,
        videoIndex: '',
        isBuy: false,
        isSetting: false,
        configList: []
      };
    },
    computed: {
      videoListAll() {
        return this.configList.filter((i) => i.indexShow == 1);
      }
    },
    onLoad(e) {
      this.onloadInfo = e;
      console.log('🚀 ~ onLoad ~ onloadInfo:', this.onloadInfo);
      uni.getNetworkType({
        success: (res) => {
          if (res.networkType == 'wifi') {
            this.wifiShow = false;
          } else {
            this.wifiShow = true;
          }
        }
      });
      this.getUserHasBuyGoods();
      this.getVideoList(e, true);
    },
    onShow() {
      this.$refs.downloadProfileRef && this.$refs.downloadProfileRef.refreshData();
    },

    methods: {
      async getUserHasBuyGoods() {
        const res = await $http({
          url: 'zx/wap/goods/isBuyGoods',
          data: {
            goodsId: this.onloadInfo.goodsId,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        this.isBuy = res.data;
      },
      //监听视频全屏/半屏
      bindfullscreenchange(e) {
        console.log(e);
        this.direction = e.detail.direction;
        this.fullScreen = e.detail.fullScreen;
      },

      getTimeInfo(time) {
        this.$nextTick(function () {
          console.log(time, '时间11111');
          console.log(this.videoInfo, this.videoIndex, 'videoInfo1111111111111111111111');
          let polyvPlayerContext = this.selectComponent('#polyv_player' + this.videoInfo.videoId);
          // console.log('🚀 ~ this.videoInfo.videoUrl:', this.videoInfo[this.videoIndex].videoVid);
          if (polyvPlayerContext) {
            if (this.videoInfo.id) return;
            this.getChangeVideo(polyvPlayerContext, this.videoInfo[this.videoIndex].videoVid, time);
          } else {
            let that = this;
            setTimeout(function () {
              if (!that.isSetting) {
                that.getTimeInfo(time);
              }
            }, 200);
          }
        });
      },
      async getVideoList(e, show) {
        if (this.currentComponent == 'LearningMaterials') {
          return;
        }
        this.videoInfo = {};
        uni.showLoading({
          title: '加载中...',
          mask: true
        });
        this.courseInfo = [];
        let _this = this;
        console.log(e, '进入页面后数据----请求接口');
        console.log(show, '进入页面参数show');
        let mobile = uni.getStorageSync('phone') || '';
        let url = 'zx/wap/course/video/list';
        const res = await $http({
          url: url,
          data: {
            curriculumId: _this.onloadInfo.curriculumId,
            goodsType: _this.onloadInfo.goodsType,
            phoneNum: String(mobile)
          }
        });
        uni.hideLoading();
        if (res) {
          this.courseInfo = res.data;
          this.courseInfo.forEach((iem) => {
            // iem.play = false;
            iem.down = true;
            iem.videoList.forEach((item) => {
              item.play = false;
            });
          });
          // 处理后端返回的新数据
          const newData = this.courseInfo;
          // 将新数据转换为适合子组件的格式
          const convertedData = newData.map((course) => {
            return {
              id: course.courseId,
              catalogueName: course.courseName,
              children: course.videoList.map((video) => {
                return {
                  id: video.videoId,
                  catalogueName: video.videoName,
                  videoUrl: video.videoVid
                };
              })
            };
          });
          this.courseInfo.piUserRecordCourseCatalogueList = convertedData;
          if (show) {
            // console.log(this.courseInfo.piUserRecordCourseCatalogueList[0].catalogueType, 'this.courseInfo.piUserRecordCourseCatalogueList[0].catalogueType');
            if (this.courseInfo.piUserRecordCourseCatalogueList[0].catalogueType == 'FILE') {
              this.videoInfo = {};
              this.videoInfo = { ...this.courseInfo.piUserRecordCourseCatalogueList[0] };
              this.videoInfo.key = 1;

              this.courseInfo.piUserRecordCourseCatalogueList[0].play = true;
              if (this.videoInfo.studyTime != 0) {
                this.getTimeInfo(Number(this.videoInfo.studyTime));
              }
            } else {
              this.videoInfo = {};
              let tempObj = { ...this.courseInfo.piUserRecordCourseCatalogueList[0].children[0] };
              this.videoInfo = JSON.parse(JSON.stringify(tempObj));
              // this.courseInfo[0].videoList[0].play = true
              this.$set(this.courseInfo[0].videoList[0], 'play', true);
              this.videoInfo.key = 2;
              console.log(this.courseInfo[0].videoList, 2222222222);
              this.getTimeInfo(Number(this.videoInfo.studyTime));
            }
          }
        }
      },
      changeStudyTime(videoInfo, time) {
        this.courseInfo.piUserRecordCourseCatalogueList.forEach((item) => {
          if (videoInfo.key == 1) {
            if (item.id == videoInfo.id) {
              this.$set(item, 'studyTime', time);
            }
          }
          item.children.forEach((info, i) => {
            if (videoInfo.key == 2) {
              if (info.id == videoInfo.id) {
                this.$set(info, 'studyTime', time);
              }
            }
          });
        });
      },
      getVideoUrl(infoL) {
        console.log('infoL', infoL);
        this.$nextTick(function () {
          this.videoInfo = {};
          this.videoInfo = { ...infoL.info };
          this.videoIndex = infoL.index;
          this.videoInfo.videoUrl = infoL.info[this.videoIndex].videoVid;
          this.getTimeInfo(Number(0));
          this.videoInfo.key = infoL.key;
          console.log(this.videoInfo, '-----------.videoInfoplayplayplay');
          // for (let i= ) {
          this.courseInfo.forEach((info) => {
            info.play = false;
            info.videoList.forEach((item, i) => {
              if (item.videoId == this.videoInfo[this.videoIndex].videoId) {
                this.$set(item, 'play', true);
                info.play = true;
                // console.log('🚀 ~ this.videoInfo[i].videoId:', this.videoInfo[i].videoId);
              } else {
                this.$set(item, 'play', false);
                // console.log('🚀 ~ this.111111111111111111111111[i].videoId:', this.videoInfo[i].videoId);
              }
            });
          });
        });
      },
      // 切换视频
      getChangeVideo(polyvPlayerContext, vid, time) {
        const ts = new Date().getTime();
        const sign = MD5.md5(`${secretkey}${vid}${ts}`);
        console.log('🚀 ~ 切换视频 ~ vid:', vid, ts, sign);
        this.startTime = time;
        polyvPlayerContext.changeVid({
          vid: vid,
          ts,
          sign
        });
      },
      bindloadedmetadata() {
        let polyvPlayerContext = this.selectComponent('#polyv_player' + this.videoInfo.videoId);
        polyvPlayerContext.pause();
        if (Number(this.startTime) > Number(polyvPlayerContext.rDuration)) {
          this.startTime = Number(polyvPlayerContext.rDuration);
        }
        polyvPlayerContext.seek(Number(this.startTime));
      },
      statechange(e) {
        let id = '';
        let _this = this;
        if (e.detail.newstate == 'playing' && e.detail.oldstate != 'playing') {
          id = e.currentTarget.id;
          clearInterval(this.timer);
          _this.studyNumber = 0;
          _this.timer = setInterval(function () {
            _this.studyNumber += 1;
          }, 1000);
        }
        console.log(e.detail.vid, 99999999999);
        // _this.courseInfo[0].videoList.forEach((itemVid, index) => {
        //   if (itemVid.videoId == e.detail.videoId) {
        //     _this.$set(itemVid, 'play', true);
        //   } else {
        //     _this.$set(itemVid, 'play', false);
        //   }
        // });
      },

      bindplaying(e) {
        let _this = this;
        let id = '';
        id = e.currentTarget.id;
        _this.courseInfo.forEach((item) => {
          item.videoList.forEach((itemVid, index) => {
            if (itemVid.videoVid == e.detail.vid) {
              console.log(itemVid, 'itemVid');
              _this.$set(itemVid, 'play', true);
            }
          });
        });
      },
      bindpause(e) {
        let _this = this;
        let polyvPlayerContext = _this.selectComponent('#polyv_player' + _this.videoInfo.videoId);
        polyvPlayerContext.pause();
        console.log('bindpause', e);
        clearInterval(_this.timer);
        _this.courseInfo[0].videoList.forEach((itemVid, index) => {
          console.log('itemVid.videoVid == e.detail.vid:', itemVid.videoVid == e.detail.vid);
          if (itemVid.videoVid == e.detail.vid) {
            console.log('🚀 ~ this.courseInfo[0].videoList.forEach ~ itemVid:', itemVid);
            _this.$set(itemVid, 'play', false);
          }
        });
        console.log('this.courseInfo', _this.courseInfo);
      },
      changeDown(index) {
        let page = { ...this.courseInfo.piUserRecordCourseCatalogueList[Number(index)] };
        page.down = !page.down;
        this.courseInfo.piUserRecordCourseCatalogueList[Number(index)] = page;
        console.log(this.courseInfo.piUserRecordCourseCatalogueList[Number(index)]);
        console.log('--------------------------------------------------------------------');
      },
      playVideo() {
        this.wifiShow = false;
        const polyvPlayer = this.$refs['polyv_player' + this.videoInfo.id];
        polyvPlayer.autoplay = true;
        polyvPlayer.play();
      },
      purchaseRecordedLessons() {
        uni.$emit('purchasePop-up'); // 触发自定义事件
        uni.navigateBack({
          delta: 1 // 返回上一个页面
        });
      },
      onBindtimeupdate(currentTime) {
        let _this = this;
        if (_this.courseType == 0) {
          //改为 _this.endTime = 180
          const polyvPlayer = this.$refs['polyv_player' + this.videoInfo.id];

          if (currentTime.target.currentTime >= 180) {
            if (this.fullScreen) {
              polyvPlayer.exitFullScreen();
            }

            _this.wifiShow = false;
            _this.audition = true;
            _this.bindpause();
          }
        }
      },
      // 设置
      async settingFn() {
        let _this = this;
        _this.isSetting = true;
        const res = await $http({
          url: 'zx/wap/course/find/config',
          data: {
            curriculumId: _this.onloadInfo.curriculumId
          }
        });
        if (!res.success) return this.$util.alter(res.message);
        console.log('返回的config', res.data);
        _this.configList = res.data;
      },
      // 取消返回
      cancelSetting() {
        this.isSetting = false;
        this.videoInfo = {};
        this.getUserHasBuyGoods();
        this.getVideoList(this.onloadInfo, true);
      },
      // 保存设置
      async settingSubmit() {
        // let isAllShow = this.configList.find((item) => item.indexShow == 1);
        // if (isAllShow != -1) return this.$util.alter('请至少选择一个课程类别和具体课程');
        console.log('this.configList', this.configList);
        let isChooseArr = [];
        this.configList.forEach((item, index) => {
          isChooseArr[index] = item.courseList.findIndex((info) => {
            return info.indexShow == 1;
          });
        });
        console.log(isChooseArr, 'isChooseArr');
        let isAllShow = isChooseArr.every((item) => item != -1);
        if (!isAllShow) return this.$util.alter('请至少选择一个课程类别和具体课程');
        console.log(isAllShow, 'isAllShow');
        // 调接口
        const res = await $http({
          url: 'zx/wap/course/save/course/config',
          method: 'POST',
          data: this.configList
        });
        console.log(res, 'resrssrsee');
        if (!res.success) return this.$util.alter(res.message);
        this.getUserHasBuyGoods();
        this.getVideoList(this.onloadInfo, true);
        this.isSetting = false;
      },
      courseStatusChange(courseItem, index) {
        console.log('courseStatusChange', courseItem, index);
        courseItem.courseList[index].indexShow === 1 ? (courseItem.courseList[index].indexShow = 0) : (courseItem.courseList[index].indexShow = 1);
      },
      curriculumStatusChange(item, index) {
        console.log('videovideo', item, index);
        if (item.indexShow === 1) {
          item.indexShow = 0;
          item.courseList.forEach((info) => {
            info.indexShow = 0;
          });
        } else {
          item.indexShow = 1;
          item.courseList.forEach((info) => {
            info.indexShow = 1;
          });
        }
      },
      changeTab(tab) {
        this.isSetting = false;
        this.videoInfo = {};
        this.currentComponent = tab;
        this.getUserHasBuyGoods();
        this.getVideoList(this.onloadInfo, true);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .no-data {
    position: relative;
    background-color: #fff;
    width: 710rpx;
    padding-top: 50%;
    text-align: center;
    .no-data-image {
      width: 148rpx;
      height: 152rpx;
      display: block;
      margin: 16rpx auto;
    }
  }
  .video_css {
    height: 422rpx;
    width: 100%;
    background-color: #e4e4e4;
  }
  .video_main {
    background-color: #fff;
    min-height: 100vh;

    .video_title {
      color: #333333;
      font-size: 30rpx;
      padding: 22rpx 32rpx;
      // overflow: hidden;
      // text-overflow: ellipsis;  /* 超出部分省略号 */
      // word-break: break-all;  /* break-all(允许在单词内换行。) */
      // display: -webkit-box; /* 对象作为伸缩盒子模型显示 */
      // -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
      // -webkit-line-clamp: 2; /* 显示的行数 */
    }
    .course_content {
      .title_icon {
        color: #333333;
        font-size: 28rpx;
        padding: 0 32rpx;
        vertical-align: middle;
        margin-bottom: 24rpx;
      }
      .title_icon::before {
        content: ' ';
        display: inline-block;
        width: 4rpx;
        height: 26rpx;
        background-color: #489981;
        border-radius: 2rpx;
        margin-right: 12rpx;
        vertical-align: middle;
      }
      // .video_top_css{
      // 	background-color: #F6F7F9;
      // 	padding:24rpx 32rpx;
      // 	color:#555555 ;
      // 	font-weight: bold;
      // 	font-size: 28rpx;
      // }
      // .image_css{
      // 	width:24rpx;
      // 	margin-right:16rpx;
      // }
      // .flex-space-between{
      // 	display: flex;
      // 	justify-content: space-between;
      // }
      // .course_two_css{
      // 	padding:32rpx;
      // 	.title_css{
      // 		color:#555555 ;
      // 		font-size: 28rpx;
      // 	}
      // 	border-bottom:2rpx solid #F6F7F9 ;
      // }
      // .course_two_css:last-child{
      // 	border:none;
      // }
      .study_setting {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 36rpx;
        margin: 42rpx 59rpx 35rpx 32rpx;
        font-size: 30rpx;
        color: #555555;
        line-height: 36rpx;
        font-weight: 500;
      }
    }
    .no_wifi_css {
      width: 100%;
      height: 422rpx;
      background-color: rgba(0, 0, 0, 0.8);
      font-size: 24rpx;
      color: #fff;
      text-align: center;
      position: fixed;
      top: 0;
      left: 0;
      .wifi_title {
        width: 376rpx;
        margin: 0 auto;
        padding-top: 132rpx;
        line-height: 34rpx;
      }
    }
    .ban_css {
      width: 168rpx;
      height: 56rpx;
      border-radius: 8rpx;
      background-color: #45d670;
      margin: 0 auto;
      font-size: 24rpx;
      color: #fff;
      line-height: 55rpx;
      margin-top: 34rpx;
    }
    .popup_content {
      display: felx;
      justify-content: space-around;
      align-items: center;
      flex-direction: column;
      width: 500rpx;
      height: 490rpx;
      background: #ffffff;
      border-radius: 48rpx;
      .bottom_css {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 280rpx;
        height: 80rpx;
        background: #428a6f;
        border-radius: 48rpx;
        font-size: 32rpx;
        color: #ffffff;
      }
      .bottom_btn_text {
        margin-top: -40rpx;
        font-size: 28rpx;
        color: #a4adb3;
      }
    }
  }
  .study_materials_conent {
    box-sizing: border-box;
    padding: 0 30rpx;
    .study_materials_tabs {
      position: relative;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      height: 106rpx;
      font-size: 26rpx;
      margin-bottom: 20rpx;
      // padding: 28rpx 163rpx 10rpx 163rpx;
      padding: 28rpx 202rpx 10rpx 163rpx;
      background-color: #fff;
    }
    .tab_box {
      position: relative;
    }
    .tabs_item {
      box-sizing: border-box;
      height: 42rpx;
      margin-top: 6rpx;
      line-height: 37rpx;
      vertical-align: top;
    }
    .tabs_check {
      height: 42rpx;
      font-weight: 600;
      font-size: 30rpx;
      color: #333333;
      line-height: 42rpx;
    }
    .tab_line {
      position: absolute;
      bottom: 0rpx;
      left: 35rpx;
      width: 50rpx;
      height: 10rpx;
      background: #3eaa8c;
      border-radius: 5rpx;
      text-align: center;
    }
  }
  .course_category_box {
    background: #ffffff;
    padding: 32rpx 30rpx 12rpx 30rpx;
    border-radius: 14rpx;
    margin-bottom: 20rpx;
    .category_title {
      height: 36rpx;
      font-size: 28rpx;
      color: #555555;
      line-height: 36rpx;
      margin-bottom: 31rpx;
      text {
        font-size: 24rpx;
        color: #999999;
      }
    }
  }
  .course_category {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    .category_item {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 300rpx;
      height: 80rpx;
      border-radius: 14rpx;
      margin-bottom: 20rpx;
      font-size: 26rpx;
      color: #333333;
      border: 1rpx solid #e8e8e8;
    }
    .category_item_check {
      color: #3eaa8c;
      background: rgba(62, 170, 140, 0.1);
      border: 1rpx solid #49a784;
    }
  }

  .cancel_btn {
    height: 90rpx;
    text-align: center;
    line-height: 90rpx;
    font-size: 30rpx;
    color: #2e896f;
    border-radius: 45rpx;
    border: 1rpx solid #2e896f;
    margin-bottom: 30rpx;
  }
  .submit_btn {
    height: 90rpx;
    background: #2e896f;
    border-radius: 45rpx;
    text-align: center;
    line-height: 90rpx;
    font-size: 30rpx;
    color: #ffffff;
    margin-bottom: 30rpx;
  }
</style>
