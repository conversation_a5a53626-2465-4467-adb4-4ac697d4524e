<template>
  <view class="m_tutoring_class">
    <scroll-view scroll-y class="scroll-container" refresher-enabled :refresher-triggered="refreshing" @refresherrefresh="onRefresh" @scrolltolower="onLoadMore">
      <view class="tutoring_class_content">
        <!-- 伴学课列表 -->
        <view v-if="displayCourseData.length > 0" class="tutoring_class_list">
          <view v-for="(course, index) in displayCourseData" :key="course.courseId" class="tutoring_class_item">
            <view class="list_header">
              <view class="list_title_content">
                <text class="title">{{ course.courseTimeStr }}</text>
              </view>
              <view class="patrol_bt" @click="handleParentPatrol(course, index)">
                <text class="patrol_text">家长巡课 ></text>
              </view>
            </view>
            <view class="course_info">
              <view class="info_row">
                <text class="info_label">学生姓名：</text>
                <text class="info-value">{{ course.studentName }}</text>
              </view>
            </view>
            <view class="course_time">
              <text class="time_text">复习时间：{{ TimestampToDate2(course.startTime) }}</text>
            </view>
            <view class="course_meeting">
              <text class="meeting_text">会议ID：{{ course.meetingNum }}</text>
            </view>
            <view class="divider_line"></view>
            <view class="course_footer">
              <view class="teacher_info">
                <view class="teacher_avatar">
                  <text class="avatar_text">{{ course.userNickName.charAt(0) }}</text>
                </view>
                <text class="teacher_name">教练：{{ course.userNickName }}</text>
              </view>
              <view class="action_buttons">
                <!-- 继续学习按钮 -->
                <view class="action_btn" @click="goOnStudy(course, index)">
                  <text class="btn_text">立即上课</text>
                </view>
              </view>
            </view>
          </view>
          <!-- 没有更多数据提示 -->
          <view v-if="!canLoadMore && hasLoadedOnce && displayCourseData.length > 0" class="no-more-tips">
            <text class="no-more-text">没有更多了～</text>
          </view>
        </view>
        <view v-else class="empty_container">
          <u-empty mode="data" width="56px" textSize="28rpx" icon="https://document.dxznjy.com/course/ac587707bf314badadb28a158852c77d.png" text="暂无数据" />
        </view>
      </view>
    </scroll-view>
    <!-- 提示框-->
    <tips-download-popup ref="downloadPopup" />
  </view>
</template>

<script>
  import TipsDownloadPopup from './components/tipsDownloadPopup.vue';

  export default {
    name: 'ReviewClass',
    components: { TipsDownloadPopup },
    props: {
      questParams: {
        type: Object,
        default: () => ({
          courseStatus: 0
        })
      }
    },
    data() {
      return {
        displayCourseData: [],
        // 分页相关
        pageNum: 1,
        pageSize: 10,
        canLoadMore: true,
        refreshing: false,
        loadingMore: false,
        hasLoadedOnce: false, // 是否触发过加载
        courseStatus: 0 // 0 未上课 1已上课
      };
    },
    onLoad() {},
    watch: {
      questParams(val) {
        if (val) {
          this.pageNum = 1;
          this.canLoadMore = true;
          this.courseStatus = val.courseStatus || 0;
          this.getDeliverCoursePage(true);
        }
      }
    },
    methods: {
      TimestampToDate2(Timestamp) {
        let now = new Date(Timestamp),
          y = now.getFullYear(),
          m = now.getMonth() + 1,
          d = now.getDate();
        return y + '-' + (m < 10 ? '0' + m : m) + '-' + (d < 10 ? '0' + d : d) + ' ' + now.toTimeString().substr(0, 8);
      },

      // 获取交付课列表
      async getDeliverCoursePage(refresh = false) {
        uni.showLoading({ title: '加载中' });
        const params = {
          courseStatus: this.questParams?.courseStatus,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          studentCode: uni.getStorageSync('currentStudentCode')
        };
        try {
          const res = await this.$httpUser.get('zx/student/course/getReviewCourse', params);
          const apiData = res?.data.data || {};
          const list = apiData.data || [];
          const currentPage = Number(apiData.currentPage || 1);
          const totalPage = Number(apiData.totalPage || 1);
          if (refresh) {
            this.displayCourseData = Array.isArray(list) ? list : [];
          } else {
            this.displayCourseData = [...this.displayCourseData, ...(Array.isArray(list) ? list : [])];
          }
          this.canLoadMore = currentPage < totalPage;
        } catch (e) {
          if (!refresh && this.pageNum > 1) {
            this.pageNum = Math.max(1, this.pageNum - 1);
          }
          uni.showToast({ title: '获取课程列表失败', icon: 'none' });
          // 失败时也要复位加载状态
        } finally {
          uni.hideLoading();
          if (this.refreshing) this.refreshing = false;
          if (this.loadingMore) this.loadingMore = false;
        }
      },
      // 下拉刷新
      async onRefresh() {
        if (this.refreshing) return;
        this.refreshing = true;
        this.pageNum = 1;
        this.canLoadMore = true;
        await this.getDeliverCoursePage(true);
      },

      // 上拉加载更多
      async onLoadMore() {
        if (!this.canLoadMore || this.loadingMore) return;
        this.loadingMore = true;
        this.hasLoadedOnce = true;
        this.pageNum += 1;
        await this.getDeliverCoursePage(false);
      },
      // 家长巡课
      handleParentPatrol(course, index) {
        console.log('家长巡课', course, index);
        this.$nextTick(() => {
          this.$refs.downloadPopup.openPopup();
        });
      },
      goOnStudy(course, index) {
        console.log('立即上课', course, index);
        this.$nextTick(() => {
          this.$refs.downloadPopup.openPopup();
        });
      }
    }
  };
</script>

<style scoped lang="scss">
  .m_tutoring_class {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .scroll-container {
    height: 100%;
  }

  .tutoring_class_content {
    padding: 20rpx;
    min-height: auto;
  }

  .tutoring_class_list {
    width: 100%;
    box-sizing: border-box;
  }

  .tutoring_class_item {
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    padding: 20rpx;
    background: url('https://document.dxznjy.com/course/dcdc10a7c6d044ff831625ced72dd338.png') no-repeat;
    background-size: 100% 100%;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    width: 100%;
    box-sizing: border-box;
  }

  .list_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    width: 100%;

    .list_title_content {
      display: flex;
      align-items: center;
      flex: 1;

      .title {
        font-size: 30rpx;
        color: black;
        margin-right: 8rpx;
        font-weight: 600;
        flex: 1;
      }
    }

    .patrol_bt {
      flex-shrink: 0;
      margin-left: 10rpx;

      .patrol_text {
        font-size: 28rpx;
        color: #2c9e7c;
        font-weight: 600;

        &.disabled {
          color: #e57534;
        }
      }
    }
  }

  .course_info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20rpx;
    width: 100%;

    .info_row {
      display: flex;
      align-items: center;
      flex: 1;

      .info_label {
        font-size: 28rpx;
        color: #555555;
      }

      .info-value {
        font-size: 28rpx;
        color: #2c9e7c;
        margin-left: 8rpx;
      }
    }
  }

  .course_time,
  .course_meeting {
    margin-bottom: 20rpx;
    width: 100%;

    .time_text,
    .meeting_text {
      font-size: 28rpx;
      color: #555555;
    }
  }

  .divider_line {
    height: 1rpx;
    background-color: #f1eeee;
    margin: 20rpx 0;
    width: 100%;
  }

  .course_footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .teacher_info {
      display: flex;
      align-items: center;
      flex: 1;

      .teacher_avatar {
        width: 55rpx;
        height: 55rpx;
        background-color: #339378;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10rpx;
        flex-shrink: 0;

        .avatar_text {
          font-size: 28rpx;
          color: #ffffff;
        }
      }

      .teacher_name {
        font-size: 28rpx;
        color: #333333;
        flex: 1;
      }
    }

    .action_buttons {
      display: flex;
      align-items: center;
      gap: 10rpx;
      flex-shrink: 0;

      .action_btn {
        padding: 6rpx 15rpx;
        border: 2rpx solid #339378;
        border-radius: 15rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .btn_text {
          font-size: 24rpx;
          color: #339378;
        }
      }
    }
  }

  .leave_button_container {
    display: flex;
    align-items: center;
    flex-shrink: 0;
  }

  .base_btn {
    padding: 6rpx 15rpx;
    border-radius: 15rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .leave_btn {
    border: 2rpx solid #e57534;

    .leave_text {
      font-size: 24rpx;
      color: #e57534;
    }
  }

  .leave_status {
    background-color: #f5f5f5;

    .status_text {
      font-size: 24rpx;
      color: #999999;
    }
  }
  .no-more-tips {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20rpx 0;
    color: #999;
    font-size: 28rpx;
    width: 100%;

    .no-more-text {
      text-align: center;
    }
  }
  .empty_container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: calc(100vh - 84rpx);
  }
</style>
