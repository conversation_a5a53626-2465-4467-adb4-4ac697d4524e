<!-- 家长会员开通 -->
<template>
  <page-meta :page-style="'overflow:' + (rollShow ? 'hidden' : 'visible')"></page-meta>
  <view class="positionRelative">
    <view class="topBanner">
      <swiper class="indextop" :autoplay="true" :current="bannerIndex" :interval="3000" @animationfinish="changeBannerCurrent" :duration="1000" circular>
        <block class="" v-for="(item, index) in bannerList" :key="index">
          <swiper-item>
            <image :src="item" class="indextopPic" mode="aspectFill" lazy-load="true" @tap="bannerTab(item)"></image>
          </swiper-item>
        </block>
      </swiper>
    </view>

    <view class="bottom-pic">
      <view class="indexBottom">
        <view class="swiperItem">
          <swiper class="h100" :autoplay="true" :current="bannerIndex" :interval="3000" @animationfinish="changeBannerCurrent" :duration="1000" circular>
            <block class="" v-for="(item, index) in bannerList1" :key="index">
              <swiper-item>
                <image :src="item" class="swiperItemPIc" mode="aspectFill" lazy-load="true" @tap="bannerTab(item)"></image>
              </swiper-item>
            </block>
          </swiper>
        </view>
        <view class="swiperItem">
          <swiper class="h100" :autoplay="true" :current="bannerIndex" :interval="3000" @animationfinish="changeBannerCurrent" :duration="1000" circular>
            <block class="" v-for="(item, index) in bannerList2" :key="index">
              <swiper-item>
                <image :src="item" mode="aspectFill" class="swiperItemPIc" lazy-load="true" @tap="bannerTab(item)"></image>
              </swiper-item>
            </block>
          </swiper>
        </view>
      </view>
    </view>
    <view class="footer-bg"></view>

    <view class="foot-content">
      <view class="tip">
        <label class="radio" style="display: inline-block; transform: scale(0.6)">
          <radio value="r1" :checked="isChecked" color="#1D755C" @click="changeIsChecked" />
        </label>
        我已阅读并同意
        <text @click="goWebUrl('https://document.dxznjy.com/dxSelect/fourthEdition/h5/parentMemberServiceRules.html')">《家长会员服务协议》</text>
        、
        <text @click="goWebUrl('https://document.dxznjy.com/dxSelect/fourthEdition/h5/parentMemberBenefitsRules.html')">《家长会员权益规则》</text>
      </view>
      <view class="join" @click="getMessage('joinNow')">
        <image src="https://document.dxznjy.com/course/2fc507725d1b4ff0a9a0bf12d52b8f7d.png" style="width: 100%; height: 100%"></image>
      </view>
      <view class="join" @click="handleShare">
        <image src="https://document.dxznjy.com/course/e2a1306d62624c84ab8ee8e713740bde.png" style="width: 100%; height: 100%"></image>
      </view>
    </view>

    <u-toast ref="uToast"></u-toast>

    <uni-popup ref="popup" type="bottom" @change="change">
      <view class="content-popup">
        <view class="p-30">
          <view class="icon-clear" @click="closePopup">
            <uni-icons type="clear" size="30" color="#B1B1B1"></uni-icons>
          </view>
          <view class="plr-30 pb-30 bg-ff radius-15 mt-30">
            <view>
              <view class="ptb-30 f-32">订购内容:家长会员套餐</view>
            </view>
            <view>
              <view class="ptb-30 f-32">应付金额:￥{{ cartTotalPrice }}元</view>
            </view>
            <view style="display: flex; align-items: center">
              <view class="ptb-30 f-32">会员开始时间:{{ memberStartTime }}</view>
              <view class="ptb-30 f-32" style="font-size: 24rpx; color: darkgray">(具体以实际付款为准)</view>
            </view>
            <view>
              <view class="ptb-30 f-32">有效期:1年</view>
            </view>

            <view class="plr-30 flex">
              <view class="paybtn f-32 c-ff" @click="onSubmitOrder">
                立即支付
                <text class="ml-10">￥{{ cartTotalPrice }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
    <!-- 非家长会员购买 -->
    <uni-popup ref="buyInfoPopup1" type="bottom" @change="change">
      <view class="buyInfo-popup">
        <view class="buy-icon-clear" @click="closeInfoPopup"></view>
        <view class="title-bg"></view>
        <form id="#nform">
          <view class="cell-item">
            <view style="width: 250rpx" class="c-00">
              <span class="redText">*</span>
              开通会员手机号
            </view>
            <view class="phone-input">
              <input
                type="number"
                placeholder-class="placeholder-style"
                disabled
                v-model="userInfo.mobile"
                name="number"
                placeholder="请输入手机号"
                class="input c-55"
                maxlength="11"
              />
            </view>
          </view>
          <view class="cell-item">
            <view style="width: 250rpx" class="c-00">
              <!-- <span class="redText">*</span> -->
              推荐人姓名
            </view>
            <view class="phone-input">
              <input type="text" placeholder-class="placeholder-style" v-model="recommendInfoName" name="recommendName" placeholder="请输入推荐人姓名" class="input c-55" />
            </view>
          </view>
          <view class="cell-item">
            <view style="width: 250rpx" class="c-00">
              <!-- <span class="redText">*</span> -->
              推荐人手机号
            </view>
            <view class="phone-input">
              <input type="text" placeholder-class="placeholder-style" v-model="recommendInfoMobile" name="recommendMobile" placeholder="请输入推荐人手机号" class="input c-55" />
            </view>
          </view>
        </form>
        <view class="noReferrer">推荐人没有填无</view>
        <view class="footer-btn" @tap="handleSubmit(1)"></view>
      </view>
    </uni-popup>
    <!-- 合伙人代购 -->
    <uni-popup ref="buyInfoPopup" type="bottom" @change="change">
      <view class="buyInfo-popup">
        <view class="buy-icon-clear" @click="closeInfoPopup"></view>
        <view class="title-bg"></view>
        <form id="#nform">
          <view class="cell-item">
            <view style="width: 250rpx" class="c-00">
              <span class="redText">*</span>
              开通会员手机号
            </view>
            <view class="phone-input">
              <input type="number" placeholder-class="placeholder-style" v-model="mobile" name="number" placeholder="请输入手机号" class="input c-55" maxlength="11" />
            </view>
          </view>
          <view class="cell-item">
            <view style="width: 250rpx" class="c-00">
              <span class="redText">*</span>
              推荐人姓名
            </view>
            <view class="phone-input">
              <input
                type="text"
                placeholder-class="placeholder-style"
                v-model="recommendInfo.name"
                name="recommendName"
                disabled
                placeholder="请输入推荐人姓名"
                class="input c-55"
              />
            </view>
          </view>
          <view class="cell-item">
            <view style="width: 250rpx" class="c-00">
              <span class="redText">*</span>
              推荐人手机号
            </view>
            <view class="phone-input">
              <input
                type="text"
                placeholder-class="placeholder-style"
                v-model="recommendInfo.mobile"
                name="recommendMobile"
                disabled
                placeholder="请输入推荐人手机号"
                class="input c-55"
              />
            </view>
          </view>
        </form>
        <view class="footer-btn" @tap="handleSubmit(2)"></view>
      </view>
    </uni-popup>

    <uni-popup ref="servePopup" type="center" @change="change">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="reviewCard">
            <view class="reviewTitle pb-20 mt-40">请先阅读并同意《会员服务协议》</view>
            <view class="flex-s mt-60">
              <view class="close_btn" @click="closeServeDialog">取消</view>
              <view class="review_btn" @click="changeServeType">确定</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <share-popup ref="sharePopup"></share-popup>
  </view>
</template>

<script>
  import SharePopup from '@/components/sharePopup.vue';
  import { isPhoneNumber } from '@/util/util.js';
  const { $http, $showMsg } = require('@/util/methods.js');
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  export default {
    components: {
      SharePopup
    },
    data() {
      return {
        noReferrer: '',
        payInfo: {},
        flag1: false,
        isChecked: false,
        type: '', //type 2 学习超人    3  超人俱乐部  4 超人俱乐部升级
        userId: '', //分享人id
        employId: '',
        weburl: '',
        memberOrder: {},
        clickType: '', //点击  learnMore 了解更多    joinNow 加入学习超人/超人俱乐部  upNow 俱乐部升级
        secCode: '', // 判断用户是否从分享进入
        userInfo: {},
        // 手机号
        mobile: '',
        // 合伙人信息
        recommendInfo: {},
        shareId: '', //分享id
        params: {
          type: 'default',
          message: '支付成功',
          duration: 3000,
          url: '/Personalcenter/my/partnerUnion'
        },
        imgHost: getApp().globalData.imgsomeHost,
        cartTotalPrice: 0,
        memberStartTime: '',
        remark: '', // 订单备注
        code1: '',
        orderId: '',
        renewType: '', // 0继续进行 1未到期，从非上级分享链接点击进入 2已到期，从非上级分享链接点击进入
        renreShow: false,
        rollShow: false, //禁止滚动穿透
        // 是否合伙人跳转过来的
        isPartner: false,
        recommendInfoName: '无',
        recommendInfoMobile: '',
        bannerList: ['https://document.dxznjy.com/course/81ae9052f7f6442d8bf4f4af9af27bbf.jpg'],
        bannerList1: [
          'https://document.dxznjy.com/course/0fb83646b5e5432cb233f3e5801b89e6.jpg',
          'https://document.dxznjy.com/course/9701dead9c964e199063fa997791e662.jpg',
          'https://document.dxznjy.com/course/a5a9a012ae9b4c94b2efd6a5818baf48.jpg'
        ],
        bannerList2: [
          'https://document.dxznjy.com/course/91f8b2e6c87543098218baa8185fad57.jpg',
          'https://document.dxznjy.com/course/429a6c63c9d141d9b7251b18f7b62dc1.jpg',
          'https://document.dxznjy.com/course/b4978d8092034c93a52b61c592090766.jpg'
        ]
      };
    },

    onReady() {
      // #ifdef APP-PLUS
      this.clearMuiBack();
      // #endif
    },
    async onShow() {
      let _this = this;
      // _this.banner();
      if (this.flag1) {
        uni.$tlpayResult(this.paySuccess, this.payFail, this.payInfo.orderId);
      }
      let startTime = new Date();
      let year = startTime.getFullYear();
      let month = (startTime.getMonth() + 1).toString().padStart(2, '0');
      let day = startTime.getDate().toString().padStart(2, '0');
      _this.memberStartTime = `${year}-${month}-${day}`;
      if (uni.getStorageSync('token')) {
        if (uni.getStorageSync('parentShareInfo')) {
          const options = JSON.parse(uni.getStorageSync('parentShareInfo'));
          if (options.scene && !options.userId) {
            this.shareId = options.scene;
          } else {
            this.shareId = options.userId;
          }
          uni.removeStorageSync('parentShareInfo');
        } else if (uni.getStorageSync('shareIdCache')) {
          this.shareId = uni.getStorageSync('shareIdCache');
        }
        uni.showLoading({
          title: '加载中...'
        });
        await _this.homeData();
      }
      _this.disabled = false;
      uni.login({
        success(res) {
          _this.code1 = res.code;
        }
      });
    },

    async onLoad(options) {
      let token = uni.getStorageSync('token');
      if (!token) {
        uni.setStorageSync('parentShareInfo', JSON.stringify(options));
        // uni.navigateTo({
        //   url: `/Personalcenter/login/login`
        // });
        return;
      }
      let parentMemberType = uni.getStorageSync('parentMemberType');
      if (parentMemberType == 5 && !options.expire && options.isPartner != '1') {
        uni.redirectTo({
          url: '/Coursedetails/my/parentEquity'
        });
        return;
      }
      let _this = this;
      _this.noReferrer = options.id;
      _this.type = options.type;
      if (options.scene && !options.userId) {
        console.log('w1');
        this.shareId = options.scene;
        this.shareId && uni.setStorageSync('shareIdCache', this.shareId);
      } else {
        this.shareId = options.userId;
        this.shareId && uni.setStorageSync('shareIdCache', this.shareId);
      }
      this.isPartner = options.isPartner == '1';
    },
    onShareAppMessage() {
      return {
        title: '叮，你的好友敲了你一下，赶紧过来看看',
        imageUrl: 'https://document.dxznjy.com/course/49fdbf70fab64b40a4be6366ab365326.jpg',
        //如果有参数的情况可以写path
        path: `/Personalcenter/my/parentVipEquity?userId=${uni.getStorageSync('user_id')}`
      };
    },
    methods: {
      changeBannerCurrent(val) {
        // console.log(val);
        this.bannerIndex = val.detail.current;
      },
      // 分享
      handleShare() {
        let token = uni.getStorageSync('token');
        if (!token) {
          return uni.navigateTo({
            url: `/Personalcenter/login/login`
          });
        }
        let shareContent = {
          type: '2',
          id: ''
        };
        this.$refs.sharePopup.open(shareContent);
      },
      // 点击海报
      posterShare() {
        this.$refs.sharePopup.close();
        uni.navigateTo({
          url: `/splitContent/poster/index?type=5`
        });
      },
      goWebUrl(url) {
        uni.navigateTo({
          url: `/pages/index/web?url=${url}`
        });
      },
      // 同意已阅读用户服务协议
      changeIsChecked() {
        this.isChecked = !this.isChecked;
      },
      closeServeDialog() {
        this.$refs.servePopup.close();
      },
      changeServeType() {
        this.isChecked = true;
        this.$refs.servePopup.close();
        this.getMessage('joinNow');
      },
      async paySuccess() {
        this.flag1 = false;
        this.redirectToOrderIndex();
      },
      payFail(type) {
        this.flag1 = false;
        this.closePopup();
        // 取消支付跳到支付取消页面
        if (type === 'cancel') {
          uni.navigateTo({
            url: `/splitContent/order/payCancel?orderId=${this.payInfo.sourceOrderId}&cancelType=vip&type=create`
          });
        }
      },

      //解决返回该界报错问题
      clearMuiBack() {
        var currentWebview = this.$scope.$getAppWebview().children()[0];
        //监听注入的js
        currentWebview.addEventListener('loaded', function () {
          currentWebview.evalJS('mui.init({keyEventBind: {backbutton: false }});');
        });
      },

      // 禁止滚动穿透
      change(e) {
        this.rollShow = e.show;
      },

      handleSubmit(id) {
        if (id == 2) {
          if (!this.mobile) {
            $showMsg('请输入手机号');
            return;
          }
          if (!isPhoneNumber(this.mobile)) {
            $showMsg('请输入正确格式的手机号');
            return;
          }
        }
        if (id == 1) {
          if (!this.recommendInfoName) {
            $showMsg('若没有推荐人请填无');
            return;
          }
          // if (!isPhoneNumber(this.recommendInfoMobile)) {
          //   $showMsg('请输入正确格式的手机号');
          //   return;
          // }
        }
        this.handlePay();
      },

      // 立即开通
      async handlePay() {
        //校验推荐人手机号
        if (this.recommendInfoMobile && this.recommendInfoMobile != '无') {
          const res = await $http({
            url: 'zx/meal/referrerPhone/verify',
            method: 'get',
            data: {
              referrerPhone: this.recommendInfoMobile
            }
          });
          if (res) {
            // 家长会员 parentMemberType = 5
            if (this.shareId == undefined) {
              this.employId = res.data;
            }

            let parentMemberType = this.userInfo.parentMemberType;
            let showParentVipBuyMember = uni.getStorageSync('showParentVipBuyMember') ? uni.getStorageSync('showParentVipBuyMember') : '';
            if (parentMemberType == 5 && !showParentVipBuyMember && !this.isPartner) {
              uni.showToast({
                title: '已是家长会员'
              });
              setTimeout(() => {
                uni.switchTab({
                  url: '/pages/home/<USER>/index'
                });
              }, 1500);
              return;
            }
            if (!this.isPartner) {
              this.$refs.popup.open();
            }
          }
        } else {
          let parentMemberType = this.userInfo.parentMemberType;
          let showParentVipBuyMember = uni.getStorageSync('showParentVipBuyMember') ? uni.getStorageSync('showParentVipBuyMember') : '';
          if (parentMemberType == 5 && !showParentVipBuyMember && !this.isPartner) {
            uni.showToast({
              title: '已是家长会员'
            });
            setTimeout(() => {
              uni.switchTab({
                url: '/pages/home/<USER>/index'
              });
            }, 1500);
            return;
          }
          if (!this.isPartner) {
            this.$refs.popup.open();
          }
        }
        await this.studySupermanInit();
      },

      async getMessage(val) {
        this.clickType = val;
        let token = uni.getStorageSync('token');
        if (!token) {
          uni.navigateTo({
            url: `/Personalcenter/login/login`
          });
          return;
        }

        if (!this.isChecked) {
          return this.$refs.servePopup.open();
        }

        //合伙人 +无推荐
        if (this.isPartner && this.shareId == undefined) {
          this.recommendInfo.name = this.userInfo.nickName;
          this.recommendInfo.mobile = this.userInfo.mobile;
          this.$refs.buyInfoPopup.open();
        }
        // 合伙人 + 推荐人
        if (this.isPartner && this.shareId) {
          this.handlePay();
        }
        //非合伙人 + 无推荐人
        if (this.noReferrer == 1 || (this.shareId == undefined && !this.isPartner)) {
          this.$refs.buyInfoPopup1.open();
        }
        // 非合伙人+推荐人
        if (!this.isPartner && this.shareId) {
          this.handlePay();
        }

        // 不是合伙人直接开通，合伙人需要填写购买信息  && this.shareId
        // if (!this.isPartner && this.noReferrer != 1) {
        //   this.handlePay();
        // }
        // if (this.noReferrer == 1 || (this.shareId == undefined && !this.isPartner)) {
        //   this.$refs.buyInfoPopup1.open();
        // } else {
        //   this.recommendInfo.name = this.userInfo.nickName;
        //   this.recommendInfo.mobile = this.userInfo.mobile;
        //   this.$refs.buyInfoPopup.open();
        // }
      },

      closePopup() {
        this.$refs.popup.close();

        if (this.isPartner) {
          this.$refs.buyInfoPopup.open();
        }
      },
      closeInfoPopup() {
        this.$refs.buyInfoPopup.close();
        this.$refs.buyInfoPopup1.close();
      },
      goBack() {
        if (this.shareId) {
          uni.reLaunch({
            url: '/pages/index/index'
          });
        } else {
          uni.navigateBack();
        }
      },

      // 获取首页信息
      async homeData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.userInfo = res.data;
          let date = new Date();
          let year = date.getFullYear();
          let month = (date.getMonth() + 1).toString().padStart(2, '0');
          let day = date.getDate().toString().padStart(2, '0');
          date = `${year}-${month}-${day}`;
          date = new Date(date);

          /** 家长会员过期 */
          if (_this.userInfo.parentMemberEndTime) {
            const vipDate2 = new Date(_this.userInfo.parentMemberEndTime);
            const vipDiffTime = vipDate2 - date;
            const vipDiffDays = vipDiffTime / (1000 * 60 * 60 * 24);
            console.log(vipDiffDays);
            if (vipDiffDays <= 30) {
              this.showParentVipBuyMember = true;
              uni.setStorageSync('showParentVipBuyMember', this.showParentVipBuyMember);
            } else {
              this.showParentVipBuyMember = false;
              uni.setStorageSync('showParentVipBuyMember', this.showParentVipBuyMember);
            }
          }
        }
      },

      goWebUrl(url) {
        uni.navigateTo({
          url: `/pages/index/web?url=${url}`
        });
      },
      // 学习超人初始化
      async studySupermanInit() {
        this.detail();
      },

      changeShareType() {
        this.renreShow = true;
        this.$refs.sharePopup.close();
      },

      closeDialog() {
        this.renreShow = false;
        this.$refs.sharePopup.close();
      },

      shutDialog() {
        uni.reLaunch({
          url: '/pages/index/index'
        });
      },

      // 家长会员获取商品详情
      async detail() {
        let params = {};
        params.buyerName = uni.getStorageSync('nickName') ? uni.getStorageSync('nickName') : '';
        params.buyerPhone = uni.getStorageSync('phone') ? uni.getStorageSync('phone') : '';
        params.shareId = this.shareId || this.employId;

        // 合伙人代购
        if (this.isPartner) {
          params.shareId = this.shareId;
          params.buyerName = this.mobile;
          params.buyerPhone = this.mobile;
          params.proxyShopping = this.isPartner;
          params.referrerName = this.recommendInfo.name;
          params.referrerPhone = this.recommendInfo.mobile;
        }
        // 家长会员id
        params.commissionMealId = '5';
        console.log(this.mobile);
        console.log(params);

        const res = await $http({
          url: 'zx/meal/becomeMemberOrder',
          method: 'post',
          data: params
        });
        if (res) {
          this.$refs.buyInfoPopup.close();
          this.$refs.buyInfoPopup1.close();
          if (this.isPartner) {
            this.$refs.popup.open();
          }
          this.memberOrder = res.data;
          this.cartTotalPrice = res.data.amount / 100;
        }
      },

      // 提交订单
      async onSubmitOrder() {
        if (this.disabled) {
          return;
        }
        uni.showLoading({
          title: '提交中'
        });
        this.disabled = true;

        this.mealPay();
      },

      // 套餐支付
      async mealPay() {
        let result = [
          {
            buyNum: 1,
            courseId: 'd7a4373a089525cb2d195840b3985788'
          }
        ];
        this.id = result[0].courseId;

        this.disabled = false;
        if (this.memberOrder && JSON.stringify(this.memberOrder) !== '{}') {
          this.payBtn(this.memberOrder);
        }
      },

      async payBtn(data) {
        let _this = this;
        let resdata = await httpUser.post('mps/line/collect/order/unified/collect', data);
        let res = resdata.data.data;
        _this.disabled = false;
        console.log(res);
        if (res) {
          if (res.openAllinPayMini) {
            this.flag1 = true;
            this.payInfo = res;
            uni.$payTlian(res);
          } else {
            uni.requestPayment({
              provider: 'wxpay',
              timeStamp: res.payInfo.timeStamp,
              nonceStr: res.payInfo.nonceStr,
              package: res.payInfo.packageX,
              signType: res.payInfo.signType,
              paySign: res.payInfo.paySign,
              success: function (ress) {
                _this.redirectToOrderIndex();
              },
              payFail: function (err) {
                uni.showToast({
                  title: '支付失败'
                });
                setTimeout(function () {
                  uni.redirectTo({
                    url: '/splitContent/order/order'
                  });
                }, 1500);
              }
            });
          }
        }
      },
      async redirectToOrderIndex() {
        // 支付成功跳转添加客服
        uni.redirectTo({
          url: '/Personalcenter/my/customerServiceQRcode'
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .topBanner {
    .indextop {
      width: 100%;
      height: 744rpx;
      background: #d8d8d8;
      // border-radius: 16rpx;
      margin: 0 auto;
      .indextopPic {
        width: 100%;
        height: 744rpx;
      }
    }
  }

  .bottom-pic {
    position: relative;
    z-index: 2;
    height: 1029rpx;
    background-image: url('https://document.dxznjy.com/course/f807ef2572c54409b61fa95005cb0073.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    margin-top: -44rpx;
    padding-top: 82rpx;

    .indexBottom {
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 686rpx;
      height: 444rpx;
      border-radius: 8rpx;
      .swiperItem {
        width: 336rpx;
        height: 444rpx;
        // background: #d8d8d8;
        border-radius: 8rpx;
        .swiperItemPIc {
          width: 336rpx;
          height: 444rpx;
          border-radius: 8rpx;
        }
      }
    }
  }
  .back {
    left: 10rpx;
    top: 106rpx;
  }

  .footer-bg {
    width: 100%;
    height: 170rpx;
    background: #00392f;
  }

  .foot-content {
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 31rpx 54rpx;
    padding-top: 61rpx;
    box-sizing: border-box;
    bottom: 0;
    width: 100%;
    height: 170rpx;
    z-index: 9;
    // padding: 30rpx 0 20rpx;
    justify-content: center;
    background: url('https://document.dxznjy.com/course/9b2639b0408244029ec74a0946ea087c.png') 100% 100% no-repeat;
    background-size: 100% 100%;
    .join:nth-of-type(1) {
      margin-right: 12rpx;
    }

    .tip {
      position: absolute;
      top: 10rpx;
      // left: 50rpx;
      font-size: 24rpx;
      color: #999999;
    }
  }

  .learningmore {
    border: 1px solid #2e896f;
    border-radius: 45rpx;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    width: 250rpx;
    color: #2e896f;
    font-size: 30rpx;
    margin-right: 30rpx;
    box-sizing: border-box;
  }

  .join {
    width: 317rpx;
    height: 78rpx;
    color: #ffffff;
  }

  .title {
    position: fixed;
    top: 0;
    background-color: #fff;
  }

  .img-club {
    width: 100%;
    height: 7024rpx;
    margin-top: 170rpx;
    margin-bottom: 190rpx;
  }

  .img-superman {
    width: 100%;
    height: 2565rpx;
    margin-bottom: 0;
    vertical-align: top;
  }

  .content-popup {
    background-color: #f3f8fc;
    border-top-left-radius: 45rpx;
    border-top-right-radius: 45rpx;
  }

  form {
    margin-top: 36rpx;
  }

  .cell-item {
    padding: 0 32rpx;
    height: 108upx;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ececec;
  }

  .redText {
    color: red;
  }

  .buyInfo-popup {
    width: 100%;
    height: 668rpx;
    padding-top: 58rpx;
    box-sizing: border-box;
    background: url('https://document.dxznjy.com/course/a423feeb0ad144ae91300cc8b33fe13e.png');
    background-size: 100% 100%;

    .footer-btn {
      position: absolute;
      bottom: 24rpx;
      left: 50%;
      width: 368rpx;
      height: 94rpx;
      margin-left: -184rpx;
      background: url('https://document.dxznjy.com/course/ce071d474405464a8356d0d1caae1042.png');
      background-size: 100% 100%;
    }

    .noReferrer {
      position: absolute;
      color: #999999;
      font-size: 24rpx;
      left: 80rpx;
      bottom: 165rpx;
    }

    .buy-icon-clear {
      position: absolute;
      top: 32rpx;
      right: 32rpx;
      width: 48rpx;
      height: 48rpx;
      background: url('https://document.dxznjy.com/course/542f3073a22e4f8b9db710c331f7078f.png');
      background-size: 100% 100%;
    }

    .title-bg {
      width: 592rpx;
      height: 46rpx;
      margin: 0 auto;
      margin-bottom: 36rpx;
      background: url('https://document.dxznjy.com/course/88dc9287a7994bb18176c13a45f6253e.png');
      background-size: 100% 100%;
    }
  }

  .icon_dw {
    width: 30rpx;
    height: 32rpx;
  }

  .remark {
    height: 150rpx;
    width: 100%;
    overflow: scroll;
  }

  .icon-clear {
    display: flex;
    justify-content: flex-end;
  }

  .flex_s {
    display: flex;
    align-items: center;
  }

  .flex_1 {
    flex: 1;
  }

  .paybtn {
    width: 100%;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    border-radius: 45rpx;
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
  }

  // 分享选项弹出层
  .dialogBG {
    width: 100%;
    /* height: 100%; */
  }

  /* 21天结束复习弹窗样式 */
  .reviewCard_box {
    width: 670rpx;
    /* height: 560rpx; */
    position: relative;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }

  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 45upx 65upx;
    box-sizing: border-box;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 145rpx;
    z-index: -1;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }

  .review_btn {
    width: 250upx;
    height: 80upx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 80upx;
    justify-content: center;
    text-align: center;
  }

  .close_btn {
    width: 250upx;
    height: 80upx;
    color: #2e896f;
    font-size: 30upx;
    line-height: 80upx;
    text-align: center;
    border-radius: 45upx;
    box-sizing: border-box;
    border: 1px solid #2e896f;
  }
</style>

<style>
  page {
    background-color: #f3f8fc;
  }
</style>
