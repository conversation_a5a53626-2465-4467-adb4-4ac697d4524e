<template>
  <view class="bg">
    <!-- 趣味复习标题 -->
    <interesting-head :title="titleText" blue @backPage="beforeleave" :hasTitleBg="true" :dark="true" :closeWhite="false" :hasRight="false"></interesting-head>
    <spell-interest v-if="count == 1" @next="next" @goNum="setGoNum" @setTitle="setTitle" :end="submitOk" :word="wordlist[index]"></spell-interest>
    <!-- 低年级划重音 -->
    <stress-interest v-if="!grade && count == 2" @next="next" @goNum="setGoNum" @setTitle="setTitle" :end="submitOk" :word="wordlist[index]"></stress-interest>
    <!-- 高年级划重音 -->
    <stress-interest-high v-if="grade && count == 2" @next="next" @goNum="setGoNum" @setTitle="setTitle" :end="submitOk" :word="wordlist[index]"></stress-interest-high>
    <!-- 高年级划次重音 -->
    <stress-interest-high
      v-if="grade && count == 3"
      @next="next"
      @goNum="setGoNum"
      @setTitle="setTitle"
      :end="submitOk"
      :word="wordlist[index]"
      :isSecond="true"
    ></stress-interest-high>
    <!-- 低年级弱读 -->
    <weak-interest v-if="!grade && count == 4" @next="next" @goNum="setGoNum" @setTitle="setTitle" :end="submitOk" :word="wordlist[index]"></weak-interest>
    <!-- 高年级弱读 -->
    <weak-interest-high v-if="grade && count == 4" @next="next" @goNum="setGoNum" @setTitle="setTitle" :end="submitOk" :word="wordlist[index]"></weak-interest-high>
    <!-- 低年级重音定长短 -->
    <fixed-interest v-if="!grade && count == 5" @next="next" :end="submitOk" @goNum="setGoNum" @setTitle="setTitle" :word="wordlist[index]"></fixed-interest>
    <!-- 高年级重音定长短 -->
    <fixed-interest-high v-if="grade && count == 5" @next="next" :end="submitOk" @goNum="setGoNum" @setTitle="setTitle" :word="wordlist[index]"></fixed-interest-high>
    <!-- 高年级次重音定长短 -->
    <fixed-interest-high
      v-if="grade && count == 6"
      @next="next"
      :end="submitOk"
      @goNum="setGoNum"
      @setTitle="setTitle"
      :word="wordlist[index]"
      :isSecond="true"
    ></fixed-interest-high>
    <page-container :show="showPage" :duration="false" :overlay="false" @beforeleave="beforeleave"></page-container>
    <uni-popup ref="popopPowerExit" type="bottom" :maskClick="false" :classBG="''">
      <pyf-dialog-exit @close="closePopopPowerExit" title="是否退出答题?" @confirm="backPageok"></pyf-dialog-exit>
    </uni-popup>
    <!--结束弹窗 -->
    <uni-popup ref="popopPower" type="bottom" :maskClick="false" :classBG="''">
      <interesting-dialog v-if="data" :data="data" @close="closeDialog" @confirm="onceMore"></interesting-dialog>
    </uni-popup>
  </view>
</template>

<script>
  import pyfDialogExit from '../components/interesting-dialog/pyfDialogExit.vue';
  import interestingHead from '../components/interesting-head/pyfInterestingHead.vue';
  import SpellInterest from './components/spellInterest.vue';
  import StressInterest from './components/stressInterest.vue';
  import StressInterestHigh from './components/stressInterestHigh.vue';
  import FixedInterest from './components/fixedInterest.vue';
  import FixedInterestHigh from './components/fixedInterestHigh.vue';
  import WeakInterest from './components/weakInterest.vue';
  import WeakInterestHigh from './components/weakInterestHigh.vue';
  import interestingDialog from '../components/interesting-dialog/pyfRuleReview.vue';

  export default {
    data() {
      return {
        count: 0,
        index: 0,
        titleText: '划音节规则',
        showPage: true,
        courseCode: '',
        studentCode: '',
        merchantCode: '',
        data: null,
        wordlist: [],
        passStatus: 0,
        type: 0,
        end: false,
        submitFlag: false,
        submitOk: false,
        grade: Number(uni.getStorageSync('pyGrade')) || false //高低年级，默认低年级
      };
    },
    components: {
      SpellInterest,
      StressInterest,
      interestingHead,
      WeakInterest,
      FixedInterest,
      pyfDialogExit,
      interestingDialog,
      WeakInterestHigh,
      FixedInterestHigh,
      StressInterestHigh
    },
    onLoad(e) {
      this.studentCode = e.studentCode;
      this.merchantCode = e.merchantCode ? e.merchantCode : '';
      this.courseCode = e.courseCode;
      this.type = e.studyStatus;
      this.init();
    },
    methods: {
      setTitle(i) {
        if (i == 1) {
          this.titleText = '划音节规则';
        } else if (i == 2) {
          this.titleText = '划重音规则';
        } else if (i == 3) {
          this.titleText = '划次重音规则';
        } else if (i == 4) {
          this.titleText = '弱读规则';
        } else if (i == 5) {
          this.titleText = '重音节长音标识';
        } else {
          this.titleText = '次重音节长音标识';
        }
      },
      async init() {
        let { data } = await this.$httpUser.get(
          `znyy/pd/mobile/funReview/findCourseWord?studentCode=${this.studentCode}&merchantCode=${this.merchantCode}&studyStatus=${this.type}&courseCode=${this.courseCode}&passType=4`
        );
        if (data.success) {
          this.wordlist = data.data;
          let a = this.wordlist.findIndex((e) => e.studyStatus === 0);
          if (a) {
            this.index = a;
            if (this.index == this.wordlist.length - 1) {
              this.end = true;
            }
          }
          this.count = 1;
        }
      },
      // 返回上一页
      // backPage() {
      //   //返回按钮
      //   console.log(22222222222);
      //   uni.navigateBack(1);
      // },
      setGoNum(e) {
        if (this.grade && e == 7 && this.end) {
          this.submitOk = true;
        } else if (!this.grade && e == 6 && this.end) {
          this.submitOk = true;
        }
      },
      onceMore() {
        this.type = 2;
        this.index = 0;
        this.end = false;
        this.submitOk = false;
        this.count = 0;
        this.init();
        this.data = null;
        this.$refs.popopPower.close();
        this.$forceUpdate();
      },
      closeDialog() {
        this.showPage = false;
        this.$refs.popopPower.close();
        // setTimeout(() => {
        uni.navigateBack();
        // }, 20);
      },
      async submit(e) {
        if (this.submitFlag) return;
        this.submitFlag = true;
        uni.showLoading({
          title: '提交中...'
        });
        let obj = {
          studentCode: this.studentCode,
          merchantCode: this.merchantCode,
          courseCode: this.courseCode,
          passType: 4,
          studyStatus: this.type - 0,
          studyStartTime: '2024-08-01 13:56:19',
          passStatus: this.wordlist.find((e) => !e.studyStatus) ? 0 : 1
        };
        obj.pdFunReviewStudyWordSaveDtoList = this.wordlist.map((e) => {
          return {
            word: e.wordSyllable,
            studyStatus: e.studyStatus,
            phoneticAnswerStatus: e.pAnswerStatus || e.pAnswerStatus == 0 ? e.pAnswerStatus : null,
            accentAnswerStatus: e.zAnswerStatus || e.zAnswerStatus == 0 ? e.zAnswerStatus : null,
            softAnswerStatus: e.rAnswerStatus || e.rAnswerStatus == 0 ? e.rAnswerStatus : null,
            lengthAnswerStatus: e.dAnswerStatus || e.dAnswerStatus == 0 ? e.dAnswerStatus : null,
            lowerAccentAnswerStatus: e.cAnswerStatus || e.cAnswerStatus == 0 ? e.cAnswerStatus : null,
            lowerLengthAnswerStatus: e.cdAnswerStatus || e.cdAnswerStatus == 0 ? e.cdAnswerStatus : null
          };
        });
        obj.pdFunReviewStudyWordSaveDtoList = obj.pdFunReviewStudyWordSaveDtoList.filter((e) => e.studyStatus == 1);
        let res = await this.$httpUser.post('znyy/pd/mobile/funReview/studyRecordSave', obj);
        if (res.data.success) {
          uni.hideLoading();
          if (e == 1) {
            this.submitFlag = false;
            this.report(res.data.data);
          } else {
            this.submitFlag = false;
            setTimeout(() => {
              uni.navigateBack(1);
            }, 20);
          }
        } else {
          this.submitFlag = false;
          uni.hideLoading();
          uni.showToast({
            icon: 'none',
            title: '提交失败'
          });
        }
      },
      async report(id) {
        let { data } = await this.$httpUser.get(
          `znyy/pd/mobile/funReview/findFunReviewReport?passType=4&studentCode=${this.studentCode}&merchantCode=${this.merchantCode}&recordId=${id}`
        );
        this.data = data.data;
        this.$refs.popopPower.open();
      },
      backPageok() {
        if (this.wordlist.find((e) => e.studyStatus)) {
          this.$refs.popopPowerExit.close();
          this.submit('2');
        } else {
          setTimeout(() => {
            this.$refs.popopPowerExit.close();
            uni.navigateBack(1);
          }, 20);
        }
      },
      closePopopPowerExit() {
        this.showPage = true;
        this.$refs.popopPowerExit.close();
      },
      beforeleave() {
        this.showPage = false;
        if (this.data) {
          setTimeout(() => {
            uni.navigateBack(1);
          }, 20);
        } else {
          this.$refs.popopPowerExit.open();
        }
      },
      next(isRight, i) {
        console.log(isRight, 'answer');
        if (this.count == 1) {
          this.wordlist[this.index].pAnswerStatus = isRight;
        } else if (this.count == 2) {
          this.wordlist[this.index].zAnswerStatus = isRight;
        } else if (this.count == 3) {
          // 次重音答案
          this.wordlist[this.index].cAnswerStatus = isRight;
        } else if (this.count == 4) {
          // 弱读答案
          this.wordlist[this.index].rAnswerStatus = isRight;
        } else if (this.count == 5) {
          // 重音定长短答案
          this.wordlist[this.index].dAnswerStatus = isRight;
        } else if (this.count == 6) {
          // 次重音定长短答案
          this.wordlist[this.index].cdAnswerStatus = isRight;
        }
        if (this.submitOk) {
          this.passStatus = 1;
          this.wordlist[this.index].studyStatus = 1;
          return this.submit('1');
        }
        if ((this.grade && i == 7) || (!this.grade && i == 6)) {
          this.wordlist[this.index].studyStatus = 1;
          this.index++;
          this.count = 0;
          setTimeout(() => {
            this.count = 1;
          }, 30);
        } else {
          this.count = i;
        }
        if (this.index == this.wordlist.length - 1) {
          this.end = true;
        }
      }
    }
  };
</script>

<style lang="scss">
  .bg {
    width: 100vw;
    height: 100vh;
    overflow: hidden;
    background: url('https://document.dxznjy.com/course/f2594c9b29a14c239d9f32165a9a033e.png') no-repeat;
    background-size: cover;
  }
</style>
