<template>
  <view class="mt-20">
    <view class="bg-ff plr-30 ptb-40 radius-15 f-30">
      <view class="f-32">选择日期时间</view>
      <!-- <view class="example-body mt-40 flex-s">
				<uni-datetime-picker type="date" placeholder="请选择日期" :border="false" :clear-icon="false" v-model="single" @maskClick="maskClick" @change="change" />
				<uni-icons type="right" size="16" color="#c0c4cc"></uni-icons>
			</view> -->
      <!-- <view class="w100 mt-30" @click="show = true">
				<text class="select">选择时间：</text><text class="select_time">{{select.time}}</text>
				<u-picker :show="show" :columns="columns" @cancel="cancel" @confirm="confirm" keyName="time"></u-picker>
			</view> -->
      <!-- 选择日期时间 -->
      <view class="text-content w100 mt-30" @click="open">
        <view class="flex_s select_time">
          <view class="flex_x">
            <view>
              <u-icon name="calendar" size="44" color="#c0c4cc"></u-icon>
            </view>
            <text class="f-28 ml-5" :class="single ? '' : 'timeColor'">{{ single || '请选择日期' }}</text>
          </view>
          <view>
            <uni-icons type="right" size="16" color="#c0c4cc"></uni-icons>
          </view>
        </view>
        <uniCalendar ref="calendar" :insert="false" @confirm="confirmDate" @close="close" />
        <!-- <u-calendar :minDate="1587524800000" :maxDate="1786778555000" :show="dateShow" @confirm="confirmDate" @close="close"  color="#ee7e33"></u-calendar> -->
        <!-- <u-datetime-picker itemHeight="68" :show="dateShow" v-model="value" mode="date" @confirm="confirmDate" @cancel="close"></u-datetime-picker> -->
      </view>
      <view class="text-content w100 mt-30" @click="selectTime">
        <view class="flex_s select_time">
          <view class="flex_x">
            <view class="pl-5">
              <u-icon name="clock" size="36" color="#c0c4cc"></u-icon>
            </view>
            <text class="f-28 ml-5" :class="select.time ? '' : 'timeColor'">{{ select.time || '请选择时间' }}</text>
          </view>
          <view>
            <uni-icons type="right" size="16" color="#c0c4cc"></uni-icons>
          </view>
        </view>
        <u-picker itemHeight="68" confirmColor="#2E896F" :show="show" :columns="columns" @cancel="cancel" @confirm="confirm" keyName="time"></u-picker>
      </view>
    </view>
    <!-- 学习详情 -->
    <view v-if="revirelist != ''">
      <view class="bg-ff plr-30 ptb-40 radius-15 mt-30 f-30">
        <view class="bold f-32">学习详情</view>
        <view class="mt-40">姓名：{{ revirelist.studentName || '' }}</view>
        <view class="mt-40">年级：{{ revirelist.gradeName || '' }}</view>
        <view class="mt-40">复习词汇：{{ revirelist.reviewWords == 0 ? 0 : revirelist.reviewWords || '' }}个</view>
        <view class="mt-40">复习遗忘词汇：{{ revirelist.forgetWords == 0 ? 0 : revirelist.forgetWords || '' }}个</view>
        <view class="mt-40">遗忘率：{{ revirelist.forgetRate == 0 ? 0 : revirelist.forgetRate || '' }}%</view>
      </view>
      <view class="bg-ff plr-30 ptb-40 radius-15 mt-30 f-30" :style="{ height: useHeight + 'rpx' }">
        <view class="flex-s bold f-32">
          <view>教练反馈</view>
          <view>教练：{{ userInfo.teacher || '' }}</view>
        </view>
        <view class="mt-40 p-30 bg-f7 radius-15 comment">教练评语：{{ revirelist.feedback || '' }}</view>
      </view>
      <view class="plr-20 mt-30">
        <u-button type="primary" text="分享" size="large" shape="circle" @click="split" color="linear-gradient(to bottom, #88CFBA, #1D755C)"></u-button>
      </view>
    </view>
    <view v-else :style="{ height: nodataHeight + 'rpx' }" class="t-c flex-col bg-ff radius-15 mt-30">
      <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
      <view style="color: #bdbdbd">暂无数据</view>
    </view>
  </view>
</template>

<script>
  import dayjs from 'dayjs';
  import uniCalendar from '../../components/uni-calendar/components/uni-calendar/uni-calendar.vue';
  export default {
    components: {
      uniCalendar
    },
    data() {
      return {
        single: '', // 选中日期
        value: Number(new Date()),
        dateShow: false, // 选择日期
        show: false, // 选择时间
        columns: [],
        select: {
          time: ''
        }, // 选择的时间
        id: '', // 课程反馈详情id
        revirelist: '', // 反馈详情数据
        swiperHeight: '', //页面剩余高度，用于动态赋值
        showShare: true, // 是否分享
        useHeight: 0, //除头部之外高度
        nodataHeight: 0, // 暂无数据高度
        imgHost: getApp().globalData.imgsomeHost
      };
    },
    props: {
      userInfo: {
        type: Object
      }
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 1310;
          this.nodataHeight = h - 555;
        }
      });
    },
    onShow() {},

    created() {
      this.$set(this.userInfo);
    },

    watch: {
      userInfo: {
        immediate: true,
        handler() {
          console.log(this.userInfo.type);
          if (this.userInfo.type === 2) {
            this.date = this.userInfo.date;
            this.id = this.userInfo.id;
            if (this.date != '' || this.date != undefined || this.date != null) {
              this.single = this.date;
              this.select.time = this.userInfo.startTime + '-' + this.userInfo.endTime;
              this.getFeedback(); // 课程反馈详情
              this.getSubject();
            }
          } else {
            console.log('没有数据');
          }
        }
      }
    },
    methods: {
      // 跳转到分享页面
      split() {
        if (this.showShare) {
          uni.navigateTo({
            url: '/Coursedetails/share/reviewshare?id=' + this.id + '&type=' + 2
          });
        } else {
          uni.showToast({
            icon: 'none',
            title: '没有课程数据',
            duration: 1000
          });
        }
      },

      open() {
        this.$refs.calendar.open();
      },

      close() {
        console.log('弹窗关闭');
      },

      // 选择日期
      // change(e) {
      // 	this.single = e;
      // 	this.select.time='';
      // 	this.revirelist='';
      // 	this.userInfo.teacher='';
      // 	this.getSubject()
      // },
      confirmDate(e) {
        console.log(e);
        this.single = e.fulldate;
        // this.dateShow=false;
        this.select.time = '';
        this.revirelist = '';
        this.userInfo.teacher = '';
        this.getSubject();
      },
      selectTime() {
        if (this.single == '') {
          uni.showToast({
            icon: 'none',
            title: '请选择日期'
          });
          return;
        }
        this.show = true;
      },
      // 选择时间确定按钮
      confirm(e) {
        console.log(e);
        this.select.time = e.value[0].time;
        this.id = e.value[0].id;
        this.show = false;
        let list = [['暂无数据']];
        if (this.single != '') {
          if (list.toString() === this.columns.toString()) {
            this.$util.alter('暂无数据，无法选择');
          } else {
            this.getFeedback(); // 课程反馈详情
          }
        }
      },

      // 选择时间取消按钮
      cancel() {
        this.show = false;
      },
      // 根据年月日获取课程日期列表
      async getSubject() {
        this.columns = [];
        let res = await this.$httpUser.get('deliver/app/parent/getStudyTimeList', {
          date: this.single.split('-').slice(0, 2).join('-'),
          type: 2
        });
        // console.log(res.data.data);
        if (res.data.success) {
          if (res.data.data.length == 0) {
            let noData = ['暂无数据'];
            this.columns.push(noData);
          } else {
            this.columns.push(res.data.data);
          }
        }
      },

      // 课程反馈详情
      async getFeedback() {
        let res = await this.$httpUser.get('deliver/app/parent/getFeedbackInfo', {
          id: this.id,
          type: 2
        });
        if (res.data.success) {
          this.revirelist = res.data.data;
        } else {
          this.showShare = false;
        }
        console.log(this.revirelist);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .example-body {
    padding: 0 30rpx;
    height: 70rpx;
    border: 1px solid #c8c8c8;
    border-radius: 45rpx;
  }

  .flex_s {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .flex_x {
    display: flex;
    align-items: center;
  }

  .select_time {
    padding: 0 35rpx;
    height: 70rpx;
    border: 1px solid #c8c8c8;
    border-radius: 45rpx;
  }

  .timeColor {
    color: #666;
  }

  .img_s {
    width: 160rpx;
  }
</style>
