<template>
  <view class="ctxt plr-30 bg-ff">
    <view class="bg-ff ptb-30">
      <view class="binggo_c">
        <view
          class="view_box"
          :class="{ active: index == activeIndex }"
          v-for="(item, index) in wordList"
          :key="index"
          @click="sayWord(index, item.wordSplitList, item.wordSyllableAudioUrl)"
        >
          <view class="box_m" :class="{ ytxt: item.knowType === 0 }">
            <view class="box_small" v-if="item.wordSplitList.length === 0">
              {{ item.word }}
            </view>
            <view class="box_small" v-else v-for="(char, i) in item.wordSplitList" :key="i">
              <text v-if="char.wordSyllableType == 3">'</text>
              {{ char.wordSyllable }}
            </view>
          </view>
          <view class="box_r">
            <image @click.stop="correctWord(index, item, item.wordSyllableAudioUrl, item.scheduleCode, 0)" :src="errorimage(index)"></image>
            <image @click.stop="correctWord(index, item, item.wordSyllableAudioUrl, item.scheduleCode, 1)" :src="rightimage(index)"></image>
          </view>
        </view>
        <!-- <uni-load-more class="mb-65" :status="loadingType"></uni-load-more> -->
        <view class="zanwu">没有更多数据了~</view>
      </view>
      <view v-if="chunks.length == 0" class="t-c flex-col mt-30 bg-ff radius-15 mlr-30" :style="{ height: useHeight + 'rpx' }">
        <image src="https://document.dxznjy.com/alading/correcting/no_data.png" class="mb-20" style="width: 160rpx" mode="widthFix"></image>
        <view style="color: #bdbdbd">暂无数据</view>
      </view>

      <view class="botBtn">
        <button class="btn_b b_r" @click="submitReport" :disabled="isSubmitting">提 交</button>
      </view>
    </view>
  </view>
</template>

<script>
  var innerAudioContext;
  export default {
    data() {
      return {
        isSubmitting: false,
        activeIndex: null,
        // wordSyllableType:null,//-1辅音 0元音 1单词音 2基础音节 3重音 4单词拼读 5划弱音 6定长短
        chunks: [],
        wordList: [], //单词列表
        wordPages: [], //分页单词列表
        totalPages: [],
        yfyList: [], //元辅音集合
        studentCode: '', //学员Code
        pageindex: 1, //当前页
        pageSize: 20, //页数
        loadingType: 'more', //加载更多状态
        planReviewId: '', //复习计划
        reviewType: 0, // 抗遗忘复习类型，0,全量复习，1元辅音复习，2，拼读单词复习
        errorIndex: [], //错误标记
        rightIndex: [] //正确标记
      };
    },
    async onLoad(options) {
      // console.log('options',options.yfyList)
      var that = this;
      innerAudioContext = uni.createInnerAudioContext();
      // 用于存储获取到的音频播放速度值
      let listenSu;
      try {
        // 获取系统信息，根据不同平台发起网络请求获取音频播放速度
        const res = await wx.getSystemInfo();
        if (res.platform === 'android' || res.platform === 'ios') {
          const result = await this.$httpUser.get('znyy/pd/mobile/queryAudioSpeed', {
            type: 'MiniApp_Mobile'
          });
          if (result.data.success) {
            listenSu = Number(result.data.data.enName);
          } else {
            listenSu = 1.5;
          }
        } else if (res.platform === 'windows' || res.platform === 'mac') {
          const result = await this.$httpUser.get('znyy/pd/mobile/queryAudioSpeed', {
            type: 'MiniApp_PC'
          });
          if (result.data.success) {
            listenSu = Number(result.data.data.enName);
          } else {
            listenSu = 1;
          }
        }
      } catch (err) {
        console.log('err', err);
      }
      innerAudioContext.playbackRate = listenSu;
      that.planReviewId = options.planReviewId;
      this.reviewType = +options.reviewType; // 抗遗忘复习类型，0,全量复习，1元辅音复习，2，拼读单词复习 // +号转数字

      // that.yfyList=JSON.parse(options.yfyList)//获取之前的元辅音数据
      if (options.yfyList) that.yfyList = JSON.parse(decodeURIComponent(options.yfyList)); //获取之前的元辅音数据 decodeURIComponent获取完整转码数据
      innerAudioContext.onPlay(() => {
        // console.log('开始播放')
      });
      innerAudioContext.onStop(function () {
        // console.log('播放结束')
        that.isplay = false;
      });
      innerAudioContext.onPause(function () {
        // console.log('播放暂停')
        that.isplay = false;
      });
      innerAudioContext.onError((res) => {
        // console.log(res.errMsg);
        // console.log(res.errCode);
        that.isplay = false;
      });
      that.getWords();
    },
    onUnload() {
      var that = this;
      // 停止音频播放
      const resetAudioContext = () => {
        innerAudioContext.stop();
        innerAudioContext.offEnded();
        innerAudioContext.offCanplay();
      };
      resetAudioContext(); // 先重置音频上下文，避免事件冲突
    },
    methods: {
      async setAudioPlaybackRate() {
        return new Promise(async (resolve, reject) => {
          wx.getSystemInfo({
            success: async (res) => {
              let listenSu;
              if (res.platform == 'android' || res.platform == 'ios') {
                try {
                  let result = await this.$httpUser.get('znyy/pd/mobile/queryAudioSpeed', {
                    type: 'MiniApp_Mobile'
                  });
                  if (result.data.success) {
                    listenSu = result.data.data.enName;
                  } else {
                    listenSu = 1.5;
                  }
                } catch (err) {
                  console.log('err', err);
                  reject(err);
                }
              } else if (res.platform == 'windows' || res.platform == 'mac') {
                try {
                  let result = await this.$httpUser.get('znyy/pp/mobile/queryAudioSpeed', {
                    type: 'MiniApp_PC'
                  });
                  if (result.data.success) {
                    listenSu = result.data.data.enName;
                  } else {
                    listenSu = 1;
                  }
                } catch (err) {
                  console.log('err', err);
                  reject(err);
                }
              }
              resolve(listenSu);
            }
          });
        });
      },
      errorimage(index) {
        return this.errorIndex.indexOf(index) > -1
          ? 'https://document.dxznjy.com/course/514e9457e3fb414ab67eaf715604e1db.png'
          : 'https://document.dxznjy.com/course/a1b3b1a6db0a4288bcbaa53273e117d2.png';
      },
      rightimage(index) {
        return this.rightIndex.indexOf(index) > -1
          ? 'https://document.dxznjy.com/course/2dd08c3d20b0467f9f5e0b7f23a3e566.png'
          : 'https://document.dxznjy.com/course/7aa8bc20f68245d68692050675f27841.png';
      },
      //区分单词音节类型
      getType(type) {
        switch (type) {
          case -1:
            return '辅音';
          case 0:
            return '元音';
          case 1:
            return '单词音';
          case 2:
            return '基础音节';
          case 3:
            return '重音';
          case 4:
            return '单词拼读';
          case 5:
            return '划弱音';
          case 6:
            return '定长短';
        }
      },
      async submitReport() {
        // console.log('this.wordPages---', this.wordPages, this.errorIndex);
        innerAudioContext.stop();
        if (this.errorIndex.length + this.rightIndex.length < this.wordPages.length) {
          uni.showToast({
            icon: 'none',
            title: '还有单词未标记',
            duration: 2000
          });
        } else {
          this.isSubmitting = true;
          // 显示加载中的提示框
          uni.showLoading({
            title: '提交中...',
            mask: true
          });

          this.wordPages.forEach((item, index) => {
            // knowType 1:学会0:不会
            item.knowType = this.rightIndex.includes(index) ? 0 : 1;
          });

          this.totalPages.detailsVos = this.wordPages;
          this.totalPages.syllableVos = this.yfyList;
          this.totalPages.planReviewVo.reviewSource = 'Android##miniApp##student';
          this.totalPages.planReviewVo.reviewType = this.reviewType; // 抗遗忘复习类型，0,全量复习，1元辅音复习，2，拼读单词复习

          this.$httpUser
            .post('znyy/pd/planReviewDetails/createReviewReport', {
              detailsVos: this.totalPages.detailsVos, // 分页单词列表
              planReviewVo: this.totalPages.planReviewVo,
              studentCode: uni.getStorageSync('pyfStudentCode'),
              syllableVos: this.totalPages.syllableVos // 元辅音集合
            })
            .then((res) => {
              let result = res.data.data;
              // console.log(result);

              // 隐藏加载中的提示框
              uni.hideLoading();
              this.isSubmitting = false;
              uni.redirectTo({
                url: '/PYFforget/reviewReport?planReviewId=' + this.planReviewId
              });
            })
            .catch((err) => {
              // console.log('error', err);

              // 隐藏加载中的提示框
              uni.hideLoading();
              this.isSubmitting = false;
            });
        }
      },
      correctWord(index, val, word, scheduleCode, correct) {
        innerAudioContext.obeyMuteSwitch = false;
        word = '2';
        console.log('word发音', word);
        innerAudioContext.src = word;
        innerAudioContext.stop();
        //单词、标记、对错 0是错 1是对
        if (correct == 0) {
          if (this.errorIndex != null && this.errorIndex.indexOf(index) == -1) {
            this.errorIndex.push(index);
          }
          if (this.rightIndex.indexOf(index) > -1) {
            this.rightIndex.splice(this.rightIndex.indexOf(index), 1);
          }
        }
        if (correct == 1) {
          if (this.rightIndex != null && this.rightIndex.indexOf(index) == -1) {
            this.rightIndex.push(index);
          }
          if (this.errorIndex.indexOf(index) > -1) {
            this.errorIndex.splice(this.errorIndex.indexOf(index), 1);
          }
        }
        // console.log('errorIndex,this.rightIndex',this.errorIndex,this.rightIndex)
      },
      // 点击单词发音
      sayWord(index, wordList, word) {
        var that = this;
        that.activeIndex = index;

        setTimeout(() => {
          this.activeIndex = null;
        }, 2000);

        innerAudioContext.obeyMuteSwitch = false;

        that.currentIndex = 0; // 当前播放的音频索引

        // 重置音频上下文 在每次播放之前先解绑所有事件
        const resetAudioContext = () => {
          innerAudioContext.stop();
          innerAudioContext.offEnded();
          innerAudioContext.offCanplay();
        };

        // 检查 wordList 中是否有音频为空的情况
        const hasEmptyAudioUrl = () => {
          let hasEmpty = false;
          wordList.forEach((item) => {
            if (!item.wordSyllableAudioUrl || item.wordSyllableAudioUrl.length === 0) {
              uni.showToast({
                title: '该单词无音频哦',
                icon: 'error',
                duration: 2000
              });
              hasEmpty = true;
              return;
            }
          });
          return hasEmpty;
        };

        const playNextAudio = () => {
          if (hasEmptyAudioUrl()) {
            resetAudioContext(); // 先重置音频上下文，避免事件冲突
            return;
          }
          resetAudioContext(); // 先重置音频上下文，避免事件冲突
          // console.log('that.currentIndex', that.currentIndex);
          // console.log('wordList', wordList);
          // console.log('wordList.length', wordList.length);

          if (that.currentIndex < wordList.length) {
            innerAudioContext.src = wordList[that.currentIndex].wordSyllableAudioUrl;
            //监听音频进入可以播放状态的事件
            innerAudioContext.onCanplay(() => {
              console.log('音频播放1111');
              innerAudioContext.play();
            });

            innerAudioContext.onEnded(() => {
              that.currentIndex++;
              playNextAudio(); // 播放下一个音频
            });

            innerAudioContext.onError((res) => {
              console.error('音频播放错误', res);
            });

            // 触发一次以检查音频是否可播放
            // innerAudioContext.play();
          } else {
            // 播放 word 的音频
            innerAudioContext.src = word;

            innerAudioContext.onCanplay(() => {
              innerAudioContext.play();
            });

            innerAudioContext.onEnded(() => {
              resetAudioContext();
              that.currentIndex = 0;
            });

            innerAudioContext.onError((res) => {
              console.error('音频播放错误', res);
            });

            // 触发一次以检查音频是否可播放
            innerAudioContext.play();
          }
        };

        playNextAudio();
      },
      loadWordList(type = 'add') {
        uni.showLoading({
          title: '加载中'
        });

        if (type === 'add') {
          this.pageindex++;
          this.getWords();
        }
        if (type === 'refresh') {
          this.pageindex = 1;
          this.wordList = [];
          this.getWords();
        }
        uni.hideLoading();
      },
      async getWords() {
        uni.showLoading({ title: '加载中...' }); // 显示加载提示
        let result = await this.$httpUser.get('znyy/pd/planReviewDetails/getPdPlanReviewDetails', {
          planReviewId: this.planReviewId
          // planReviewId: '1279082733194072064'
        });
        //单词页面分页
        this.totalPages = result.data.data;
        // console.log('this.totalPages---------',this.totalPages);
        this.wordPages = result.data.data.detailsVos; //[]
        this.wordPages.totalItems = this.wordPages.length; //30
        this.wordPages.size = this.pageSize; //7
        this.wordPages.totalPage = Math.ceil(this.wordPages.totalItems / this.wordPages.size);
        const chunks = [];
        let pageNum = 1;
        for (let i = 0; i < this.wordPages.totalItems; i += this.wordPages.size) {
          const chunk = this.wordPages.slice(i, i + this.wordPages.size);
          chunks.push({
            chunk,
            size: chunk.length,
            totalItems: this.wordPages.length,
            totalPage: pageNum
          });
          this.chunks = chunks;
          pageNum++;
        }
        const currentChunk = this.chunks[this.pageindex - 1];
        if (currentChunk) {
          // 如果是首次加载，直接赋值；如果是加载更多，追加数据
          if (this.pageindex === 1) {
            this.wordList = currentChunk.chunk;
            uni.hideLoading();
          } else {
            this.wordList = this.wordList.concat(currentChunk.chunk);
          }
          // console.log('当前显示的数据:', this.wordList);
        }
      }
    },

    onPullDownRefresh() {
      this.loadWordList('refresh');
    },

    onReachBottom() {
      if (this.pageindex < this.chunks.length) {
        this.loadWordList();
        // this.loadingType = 'more';
        console.log('加载更多！！！！');
      } else {
        // this.loadingType === 'no-more'
        console.log('没有更多数据');
      }
    }
  };
</script>

<style scoped>
  .ctxt {
    height: 100vh;
  }

  .binggo_c {
    font-size: 40rpx !important;
    /* color: #555555; */
    color: black !important;
    /* font-size: large !important; */
    margin-top: 10rpx;
  }

  .view_box {
    width: 686rpx;
    /* height: 92rpx; */
    min-height: 92rpx;
    font-size: 28rpx;
    border-radius: 56rpx;
    border: 1px solid #d8d8d8;
    overflow: hidden;
    display: flex;
    align-items: center;
    /* text-align: center; */
    justify-content: space-around;
    margin-bottom: 40rpx;
  }

  .ytxt {
    color: #fea858;
  }

  .view_box:last-of-type {
    margin-bottom: 190rpx;
    /* 最后一项的底部间距设置为 104rpx (40rpx + botBtn 高度) */
  }

  .active {
    background-color: #fbfff9;
    border: 1px solid #3a9483;
  }

  .box_l {
    color: #77d977;
    width: 92rpx;
  }

  .box_m {
    width: 442rpx;
    padding: 10rpx 0;
  }

  .box_small {
    height: 70rpx;
    line-height: 60rpx;
    display: inline-block; /* 使宽度自适应内容 */
    padding: 0 10rpx;
    margin-left: 2rpx;
    border: 1px solid #d8d8d8;
    border-radius: 8rpx;
  }
  .box_r {
    width: 164rpx;
    line-height: 0;
    display: flex;
    justify-content: space-around;
  }

  .box_r image {
    width: 64rpx;
    height: 64rpx;
  }

  .lc_yellow {
    color: #fd9b2a;
    text-align: center;
    width: 116rpx;
    border: 1px solid #ffe1be;
    background-color: #fdf6ed;
    border-radius: 8rpx;
    margin-left: 32rpx;
  }

  .flex-self-s {
    margin-bottom: 40rpx;
  }

  .botBtn {
    position: fixed;
    bottom: 0rpx;
    right: 32rpx;
    padding-bottom: 64rpx;
    box-sizing: border-box;
    background-color: #ffffff;
  }

  .btn_b {
    width: 686rpx;
    height: 74rpx;
    border-radius: 60rpx;
    line-height: 74rpx;
    text-align: center;
  }

  .b_r {
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    color: #ffffff;
    margin-left: 32rpx;
    letter-spacing: 3px;
  }
  .zanwu {
    margin: 0 auto;
    margin-top: 20rpx;
    margin-bottom: 20rpx;
    text-align: center;
    font-size: 36rpx;
    color: #b3b7ba;
    height: 180rpx;
  }
</style>
