<template>
  <view class="reviewCard_box">
    <!-- <view class="review_close" @click="close()">
      <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
    </view> -->
    <view class="interesting_popupCenter_1">
      <view class="reviewTitle bold pb-20">真是太棒啦~ 答题完成！</view>
      <view class="boxs">
        <view class="box" v-for="item in list" :key="item.text">
          <view class="title">{{ item.text }}</view>
          <view class="content">
            <image src="https://document.dxznjy.com/course/0c7edba776204297890e4572fdd2c822.png" style="width: 58rpx; height: 58rpx; margin-right: 20rpx"></image>
            <view class="text">正确率:{{ item.accuracy }}%</view>
          </view>
          <view class="content">
            <image src="https://document.dxznjy.com/course/7ba4f73a00204ef6a1c815c2d713eccc.png" style="width: 58rpx; height: 58rpx; margin-right: 20rpx"></image>
            <view class="text">掌握程度:{{ item.level }}</view>
          </view>
          <view class="content">
            <image src="https://document.dxznjy.com/course/ef4ffc248b534fdaa16ef18531a75ebb.png" style="width: 58rpx; height: 58rpx; margin-right: 20rpx"></image>
            <view class="text">描述： {{ item.remark }}</view>
          </view>
        </view>
      </view>
      <view class="popopPower_bottom">
        <button class="popopPower_exit_comfirm nextBtn right" @click="confirm()">再来一轮</button>
        <button class="popopPower_exit_back" @click="close(true)">返回首页</button>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        list: []
      };
    },
    props: {
      data: Object
    },
    created() {
      this.init();
    },
    methods: {
      init() {
        let res = JSON.parse(this.data.reportInfo);
        if (res.ruleP.remark) {
          this.list.push({ ...res.ruleP, text: '划拼音规则' });
        }
        if (res.ruleZ.remark) {
          this.list.push({ ...res.ruleZ, text: '划重音规则' });
        }
        if (res.ruleR.remark) {
          this.list.push({ ...res.ruleR, text: '弱读规则' });
        }
        if (res.ruleD.remark) {
          this.list.push({ ...res.ruleD, text: '重音节长音标识规则' });
        }
        this.list.forEach((e) => {
          e.accuracy = e.accuracy.toFixed(1);
        });
      },
      //确认
      confirm() {
        this.$emit('confirm');
      },

      //关闭弹窗
      close() {
        this.$emit('close');
      }
    }
  };
</script>

<style lang="scss" scoped>
  page {
    height: 100vh;
    padding: 0;
  }
  .boxs {
    height: 840rpx;
    overflow-y: auto;
    .box {
      width: 664rpx;
      height: 594rpx;
      margin: 0 auto;
      .title {
        color: #555555;
        height: 58rpx;
        line-height: 58rpx;
        font-size: 40rpx;
        font-weight: bold;
      }
      .content {
        height: 58rpx;
        display: flex;
        margin-top: 40rpx;
        align-items: center;
        .text {
          flex: 1;
          height: 44rpx;
          font-size: 32rpx;
          color: #555555;
          line-height: 44rpx;
          text-align: left;
        }
      }
      .describe {
        margin-top: 24rpx;
        text-indent: 2em;
        box-sizing: border-box;
        padding: 22rpx;
        height: 220rpx;
        background: #f7fcf6;
        font-size: 28rpx;
        color: #555555;
        line-height: 42rpx;
        text-align: left;
      }
    }
  }
  .interesting_popupCenter_1 {
    position: absolute;
    top: 560rpx;
    width: 100%;
    height: calc(100% - 560rpx);
    color: #000;
    border-radius: 24upx;
    padding: 10upx 42upx;
    box-sizing: border-box;
  }

  .finishAnd {
    display: inline-block;
    font-size: 32rpx;
    color: #333333;
    text-align: center;
    height: 60rpx;
    line-height: 60rpx;
    margin: 40rpx 0 24rpx;
  }

  .interet_popupTitle1 {
    width: 210rpx;
    height: 230rpx;
    margin-bottom: 0;
  }

  .reviewCard_box {
    width: 100vw;
    /* height: 560rpx; */
    height: 100vh;
    position: relative;

    background: url('https://document.dxznjy.com/course/f860361dc23c450dbebe8e8184719af6.png') 100% 100% no-repeat;
    background-color: #fff;
    background-size: 100% auto;
    background-position: top left;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -265rpx;
    left: 145rpx;
    z-index: -1;
  }

  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }
  .review_close {
    position: absolute;
    top: 418rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 32rpx;
    display: flex;
    justify-content: center;
    position: absolute;
    top: -351rpx;
    left: 0;
    color: #fff;
  }

  .popup_content {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    height: 200rpx;
    padding: 0;

    .popup_content_item:nth-child(2n) {
      margin-top: 30rpx;
    }
  }

  .popup_content_item {
    width: 100%;
    line-height: 80rpx;
    display: flex;
    justify-content: space-between;

    image {
      width: 58rpx;
      height: 58rpx;
      margin-right: 20rpx;
    }
    view {
      display: flex;
      align-items: center;
      font-size: 32rpx;
      color: #555555;
    }
    .popup_content_item_right {
      font-size: 56rpx;
      color: #03db62;
    }
  }
  .errWord {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    flex-direction: column;
    align-items: center;
    max-height: 470rpx;
    margin: 0 0 20rpx;
    box-sizing: border-box;
    .errWord_list:nth-child(2n) {
      background-color: #fff;
    }
  }
  .errWord_list {
    width: 100%;
    height: 96rpx;
    line-height: 96rpx;
    font-size: 28rpx;
    color: #555555;
    background-color: #f7ffee;
    /* border-radius: 50rpx; */
    display: flex;
    justify-content: space-between;
    align-items: center;

    box-sizing: border-box;
    padding: 0 24rpx;
    .errWord_list_play {
      width: 38rpx;
      height: 38rpx;
      background: url('https://document.dxznjy.com/course/449ae84acff6431ab559dd94acb5636b.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }
  }
  .popopPower_bottom {
    position: absolute;
    bottom: 30rpx;
    left: 0;
    width: 100%;
  }
  .popopPower_exit_comfirm {
    width: 632rpx;
    height: 84rpx;
    margin: 0 auto;
    position: initial;
    font-size: 28rpx;
    background: url('https://document.dxznjy.com/course/58b85043e5cd4f89b641476d1b4d975b.png') 100% 100% no-repeat;
    background-size: 100% 100%;

    color: #fff;
    display: grid;
    place-items: center;
  }
  .popopPower_exit_back {
    height: 40rpx;
    font-family: AlibabaPuHuiTi_3_85_Bold;
    font-size: 28rpx;
    color: #ffa332;
    line-height: 40rpx;
    font-style: normal;
    margin-top: 26rpx;
  }
</style>
