<template>
  <page-meta :page-style="'overflow:' + (rollShow ? 'hidden' : 'visible')"></page-meta>
  <view class="funContent">
    <interesting-head :title="titleText" :hasRight="false" @backPage="backPage" :rightText="'选择词库'" :closeWhite="true" @clickHeadRight="clickHeadRight()"></interesting-head>

    <view class="interesting_surplus" :class="isShowAgain ? 'interesting_surplusMargin' : ''" @click="clickHeadRight()">
      <view>
        <text>{{ wordCourse.courseName }}</text>
        <image
          style="height: 24rpx; width: 14rpx; transform: scaleX(-1); margin-left: 20rpx"
          src="https://document.dxznjy.com/applet/newInteresting/interesting_back_white.png"
        ></image>
      </view>
    </view>

    <view v-if="!isShowAgain" class="play_again" style="margin-top: 30rpx" @click="playAgain()">
      <text style="color: white; font-size: 32rpx">再来一轮</text>
    </view>

    <!-- 四种玩法列表 -->
    <view class="interesting_list">
      <view class="interesting_listContent_one" @click="goUrl(interestingList[0], 0)">
        <view :class="interestingList[0].isNoData ? 'interesting_listContent_button_disable' : 'interesting_listContent_button'"></view>
      </view>
      <view class="interesting_listContent_two" @click="goUrl(interestingList[1], 1)">
        <view :class="interestingList[1].isNoData ? 'interesting_listContent_button_disable' : 'interesting_listContent_button'"></view>
      </view>
      <view class="interesting_listContent_three" @click="goUrl(interestingList[2], 2)">
        <view :class="interestingList[2].isNoData ? 'interesting_listContent_button_disable' : 'interesting_listContent_button'"></view>
      </view>
      <view class="interesting_listContent_four" @click="goUrl(interestingList[3], 3)">
        <view :class="interestingList[3].isNoData ? 'interesting_listContent_button_disable' : 'interesting_listContent_button'"></view>
      </view>
    </view>

    <!-- 温馨提示 -->
    <uni-popup ref="popopPower" type="center" :maskClick="true" :classBG="''">
      <interesting-dialog
        :title="'温馨提示'"
        :isRed="true"
        :isReview="false"
        :isSingle="true"
        :textContent="'您还没有开通使用权限'"
        @nowBuy="nowBuy()"
        @closeDialog="closeDialog()"
      ></interesting-dialog>
    </uni-popup>

    <!-- 选择词库弹窗 -->
    <uni-popup ref="popopChooseWord" type="center" @change="changeCourse">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image :src="dialog_iconUrl" mode="widthFix"></image>
            <!-- <image style="width: 316rpx;height: 265rpx;" src="https://document.dxznjy.com/applet/newimages/xiezi.png"></image> -->
          </view>
          <view class="review_close" @click="closeDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold pb-20">选择词库</view>
            <scroll-view :scroll-top="scrollTop" scroll-y="true" class="scroll-Y" @scrolltoupper="upper" @scrolltolower="lower" @scroll="scroll">
              <view
                class="dialogContent"
                v-for="(item, index) in wordList"
                :class="!item.status ? 'boxDefault' : courseWordCurrent == index ? 'addclass' : 'not-selected'"
                @click="chooseWordCourse(item, index)"
              >
                <text>{{ item.courseName }}</text>
              </view>
            </scroll-view>
            <view style="margin: 60rpx auto 0 auto; display: flex">
              <view class="sure_btn" @click="confirmWord()">确定</view>
              <view class="cancel_btn" @click="closeDialog()">取消</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <!--词库初始弹窗一组，关，轮结束 -->
    <uni-popup ref="popopPowerReview" type="center" :maskClick="true" :classBG="''">
      <interesting-review
        :gradScreenWheel="2"
        :isIndex="true"
        :pageUrl="'funReview'"
        :play="play"
        :scheduleCode="wordCourse.scheduleCode"
        :showData="showData"
        :reviewWordNum="reviewWordNum"
        :errorWordNum="reviewWordNum"
        :correctRate="correctRate"
        @closeDialog="closeDialog"
      ></interesting-review>
    </uni-popup>
  </view>
</template>

<script>
import interestingHead from '../components/interesting-head/interestingHead.vue';
import interestingDialog from '../components/interesting-dialog/index.vue';
import interestingReview from '../components/interesting-dialog/review.vue';
import Util from '@/util/util.js';
export default {
  components: {
    interestingHead,
    interestingDialog,
    interestingReview
  },
  data() {
    return {
      imgHost: getApp().globalData.imguseHost,
      titleText: '趣味复习',
      studentCode: '',
      merchantCode: '',
      courseWordCurrent: -1, //点击课程列表的下标
      courseCode: '',
      gradScreenWheel: -1,
      wordCourse: {
        courseName: '请选择词库'
      }, //当前选择课程的信息
      showData: {
        cos: null,
        levelGroup: 0,
        levelTotalGroup: 0,
        noLearnedWords: true,
        nowGroup: 0,
        nowLevel: 0,
        status: false,
        totalLevel: 0
      }, //需要展示的数据
      interestingList: [
        {
          imgUrl: 'newInteresting/interesting_sccg.png',
          text: '识词冲关',
          interestName: 'Sccg',
          ischoose: false,
          isNoData: false
        },
        {
          imgUrl: 'newInteresting/interesting_tyby.png',
          text: '听音辨意',
          interestName: 'Tyby',
          ischoose: false,
          isNoData: false
        },
        {
          imgUrl: 'newInteresting/interesting_llk.png',
          text: '连连看',
          interestName: 'Llk',
          ischoose: false,
          isNoData: false
        },
        {
          imgUrl: 'newInteresting/interesting_ppl.png',
          text: '拼拼乐',
          interestName: 'Ppl',
          ischoose: false,
          isNoData: false
        }
      ],
      wordList: [],
      play: '',
      reviewWordNum: [],
      correctRate: 0,

      isShowAgain: true, //是否显示在玩一轮按钮
      allWordNoData: false, //是否四种玩法都没单词

      scrollTop: '',
      rollShow: false, //禁止滚动穿透,

      isChoose: false,

      dialog_iconUrl: Util.getCachedPic('https://document.dxznjy.com/dxSelect/dialog_icon.png', 'dialog_icon_path')
    };
  },
  onLoad(option) {
    this.$util.fetchFile(1);
    this.$util.fetchFile(0);
    this.studentCode = option.studentCode;
    this.merchantCode = option.merchantCode;
    var temporaryCourse = uni.getStorageSync('wordCourse'); //小程序缓存的课程数据
    this.getWordList();
    if (option.scheduleCode == undefined) {
      //首页进入没有scheduleCode
      if (temporaryCourse.scheduleCode != undefined) {
        //如果之前存过
        this.initCourse(temporaryCourse.scheduleCode);
        return;
      }
    } else {
      //其它页面返回
      this.initCourse(option.scheduleCode);
    }
  },
  onShow() {
    var temporaryCourse = uni.getStorageSync('wordCourse'); //小程序缓存的课程数据
    if (temporaryCourse.scheduleCode != undefined) {
      //如果之前存过
      this.initCourse(temporaryCourse.scheduleCode);
    }
  },
  methods: {
    // 禁止滚动穿透
    changeCourse(e) {
      this.rollShow = e.show;
    },
    scroll: function (e) {
      this.old.scrollTop = e.detail.scrollTop;
    },
    upper: function (e) {},
    lower: function (e) {},
    //选择词库
    clickHeadRight() {
      this.courseWordCurrent = -1;
      this.$refs.popopChooseWord.open();
    },
    initCourse(scheducode) {
      if (scheducode == undefined || scheducode == null || scheducode.length < 1) {
        return;
      }
      let that = this;
      if (that.$refs.popopChooseWord != undefined) {
        that.$refs.popopChooseWord.close();
      }
      that.$httpUser.get(`znyy/course/choose/word/course/schedule/code?scheduleCode=${scheducode}`).then((res) => {
        if (res.data.success) {
          that.wordCourse = res.data.data;
          that.wordCourse.courseCode = that.courseCode;
          uni.setStorageSync('wordCourse', that.wordCourse);
          that.getWordList();
          that.getShowData(that.wordCourse.scheduleCode);
        } else {
          that.$util.alter(res.data.message);
        }
      });
    },

    //获取单词词库列表
    getWordList() {
      let that = this;
      that.$httpUser.get(`znyy/course/getFunReviewScheduleList?merchantCode=${that.merchantCode}`).then((res) => {
        if (res.data.success) {
          if (res.data.data.data == null || res.data.data.data.length < 1) {
            that.$util.alter('您还没有学习过课程哦');
            setTimeout(() => {
              uni.navigateBack();
            }, 3000);
          }
          that.wordList = res.data.data.data;
        } else {
          // that.$util.alter(res.data.message);
          setTimeout(() => {
            uni.navigateBack();
          }, 3000);
        }
      });
    },

    // 单词词库选择
    chooseWordCourse(item, index) {
      if (!item.status) {
        this.$util.alter('先去学习新单词再来吧');
        return;
      }
      this.courseCode = item.courseCode;
      this.courseWordCurrent = index;
    },
    // 单词词库选择后确定
    confirmWord() {
      let that = this;
      if (that.courseWordCurrent < 0) {
        that.$util.alter('请选择单词词库');
        return;
      }

      that.$refs.popopChooseWord.close();
      that.$httpUser.get(`znyy/course/choose/word/course/free?courseCode=${that.courseCode}&merchantCode=${that.merchantCode}`).then((res) => {
        if (res.data.success) {
          that.wordCourse = res.data.data;
          that.wordCourse.courseCode = that.courseCode;
          // 当前选择的课程存到本地
          uni.setStorageSync('wordCourse', that.wordCourse);
          that.getShowData(that.wordCourse.scheduleCode);
        } else {
          that.$util.alter(res.data.message);
        }
      });
    },
    // 获取当前页展示数据
    getShowData(scheduleCode) {
      let that = this;
      that.interestingList.forEach((item) => {
        item.isNoData = false;
      });
      that.$httpUser.get(`znyy/course/query/fun/words?scheduleCode=${scheduleCode}`).then((res) => {
        if (res.data.success) {
          that.showData = res.data.data;
          console.log(that.showData);
          that.showData.scheduleCode = scheduleCode;
          that.$set(that.showData, 'merchantCode', that.merchantCode);
          if (that.showData.cos == null && !that.showData.groupEnd) {
            that.$util.alter('您该词库单词还未学习~,请重新选择词库');
          }
          if (!that.showData.status && !that.showData.groupEnd) {
            if (
              that.showData.cos.play1 != null &&
              that.showData.cos.play1.length == 0 &&
              that.showData.cos.play2 != null &&
              that.showData.cos.play2.length == 0 &&
              that.showData.cos.play3 != null &&
              that.showData.cos.play3.length == 0 &&
              that.showData.cos.play4 != null &&
              that.showData.cos.play4.length == 0
            ) {
              that.$util.alter('您该词库单词还未学习~,请重新选择词库');
            }
          }

          // 四个玩法列表展示
          for (let i = 0; i < that.interestingList.length; i++) {
            if (i == 0) {
              that.interestingList[i].isNoData = !that.showData.playCos.playA;
            } else if (i == 1) {
              that.interestingList[i].isNoData = !that.showData.playCos.playB;
            } else if (i == 2) {
              that.interestingList[i].isNoData = !that.showData.playCos.playC;
            } else if (i == 3) {
              that.interestingList[i].isNoData = !that.showData.playCos.playD;
            }
            // if (that.showData.cos == null || that.showData.cos['play' + (i + 1)].length == 0) {
            //     that.interestingList[i].isNoData = true;
            // }
          }

          if (!that.showData.playCos.playA && !that.showData.playCos.playB && !that.showData.playCos.playC && !that.showData.playCos.playD) {
            //均为false则说明全都玩玩了
            this.allWordNoData = true;
          } else {
            this.allWordNoData = false;
          }
          this.isShowAgain = that.showData.playCos.playA && that.showData.playCos.playB && that.showData.playCos.playC && that.showData.playCos.playD;
          console.log('玩法通关' + this.allWordNoData);

          return;
          ///组关限制逻辑//废弃////
          // 判断这组结束是组还是轮还是关
          if (!that.showData.status) {
            that.gradScreenWheel = 1;
          }
          if (that.showData.status && that.showData.nowLevel != that.showData.totalLevel) {
            that.gradScreenWheel = 2;
          } //当前关数==总关数 代表是最后一关，所以是一轮结束(其它组都为空的时候才可以开启下一轮)
          if (that.showData.status && that.showData.nowLevel == that.showData.totalLevel) {
            that.gradScreenWheel = 3;
          }
          if (that.showData.groupEnd) {
            setTimeout(function () {
              that.$refs.popopPowerReview.open();
            }, 300);
          }
        } else {
          that.$util.alter(res.data.message);
        }
      });
    },

    //识词冲关等页面跳转
    goUrl(ele, index) {
      let that = this;
      if (that.wordCourse.courseCode == undefined) {
        that.$util.alter('请先选择词库哦');
        return;
      }

      if (that.allWordNoData) {
        setTimeout(function () {
          that.$refs.popopPowerReview.open();
        }, 300);
        return;
      }

      if (that.interestingList[index].isNoData) {
        // if (that.showData.cos['play' + (index + 1)].length == 0) {
        that.$util.alter('当前玩法无可复习单词~');
        return;
      }
      if (that.isChoose) {
        that.$util.alter('正在打开中~');
        return;
      }
      that.isChoose = true;
      ele.ischoose = true;
      var url = '/interestModule/interesting' + ele.interestName;
      setTimeout(function () {
        that.showData.studentCode = that.studentCode;
        uni.navigateTo({
          url: url + `?params=` + encodeURIComponent(JSON.stringify(that.showData))
        });
        // uni.redirectTo({
        //     url: url + `?params=` + encodeURIComponent(JSON.stringify(that.showData)),
        // })
        ele.ischoose = false;
        that.isChoose = false;
      }, 1000);
    },

    //查看学情报告
    lookHistory() {
      let that = this;
      if (this.wordCourse.scheduleCode == undefined) {
        this.$util.alter('请先选择词库哦');
        return;
      }

      that.$util.alter('当前暂无相关学情报告');
      return;

      that.$httpUser.get(`znyy/stats/review/getAnalysisList?scheduleCode=${that.scheduleCode}`).then((res) => {
        if (res.data.success) {
          if (res.data.data.length != 0) {
            // uni.navigateTo({
            //     url: "/pages/interest/pupilReport?scheduleCode=" + this.wordCourse
            //         .scheduleCode
            // })
          }
        } else {
          that.$util.alter('当前暂无相关学情报告');
        }
      });
    },

    //再来一轮
    playAgain() {
      let that = this;
      if (this.wordCourse.scheduleCode == undefined) {
        this.$util.alter('请先选择词库哦');
        return;
      }
      that.$httpUser.get('znyy/course/noLevel/play/again?scheduleCode=' + that.wordCourse.scheduleCode).then((res) => {
        if (res.data.success) {
          that.getShowData(that.wordCourse.scheduleCode);
        } else {
          that.$util.alter(res.data.message);
        }
      });
    },

    //关闭弹窗
    closeDialog() {
      this.$refs.popopChooseWord.close();
      this.$refs.popopPowerReview.close();
    },

    // 返回上一页
    backPage() {
      console.log('返回上一页');
      // uni.removeStorageSync('wordCourse');
      // #ifdef MP-WEIXIN
      uni.navigateBack({
        delta: 999
      });
      // #endif

      // #ifdef APP-PLUS
      uni.switchTab({
        url: '/pages/index/index'
      });
      // #endif
    }
  },
  // 可以在这个周期清除数据，但是隐藏再打开还是这一页，所以暂时不需要 只记录一下
  onHide() {
    console.log('隐藏');
  }
};
</script>

<style>
page {
  height: 100vh;
  padding: 0;
}

.funContent {
  width: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
  /* height: 100%; */
  background: url('https://document.dxznjy.com/applet/newInteresting/interesting_bg.png') 100% 100% no-repeat;
  background-size: 100% 100%;
}

.interesting_surplus {
  margin-left: 10rpx;
  text-align: center;
  height: 90rpx;
  width: 668rpx;
  line-height: 90rpx;
  background: url('https://document.dxznjy.com/applet/newInteresting/interesting_course.png') 100% 100% no-repeat;
  background-size: 100% 100%;
}

.interesting_surplusMargin {
  margin-top: 75rpx;
  margin-bottom: 50rpx;
}

.interesting_surplus > view {
  text-align: center;
  height: 90rpx;
  width: 668rpx;
  line-height: 90rpx;
  color: #fff;
  font-size: 34rpx;
}

.interesting_list {
  display: flex;
  flex-wrap: wrap;
  padding-left: 20rpx;
}
.interesting_listContent_one {
  width: 300rpx;
  height: 528rpx;
  margin-top: 40rpx;
  margin-right: 49rpx;
  background: url('https://document.dxznjy.com/applet/newInteresting/interesting_sccg.png') 100% 100% no-repeat;
  background-size: 100% 100%;
  position: relative;
}
.interesting_listContent_two {
  width: 300rpx;
  height: 528rpx;
  margin-top: 40rpx;
  background: url('https://document.dxznjy.com/applet/newInteresting/interesting_tyby.png') 100% 100% no-repeat;
  background-size: 100% 100%;
  position: relative;
}
.interesting_listContent_three {
  width: 300rpx;
  height: 528rpx;
  margin-top: 40rpx;
  margin-right: 49rpx;
  background: url('https://document.dxznjy.com/applet/newInteresting/interesting_llk.png') 100% 100% no-repeat;
  background-size: 100% 100%;
  position: relative;
}
.interesting_listContent_four {
  width: 300rpx;
  height: 528rpx;
  margin-top: 40rpx;
  background: url('https://document.dxznjy.com/applet/newInteresting/interesting_ppl.png') 100% 100% no-repeat;
  background-size: 100% 100%;
  position: relative;
}
.interesting_listContent_button {
  position: absolute;
  left: 50%; /* 水平居中 */
  bottom: 30rpx; /* 距离底部 30rpx */
  transform: translateX(-50%); /* 使元素水平居中 */
  width: 220rpx;
  height: 60rpx;
  background: url('https://document.dxznjy.com/applet/newInteresting/interesting_start.png') 100% 100% no-repeat;
  background-size: 100% 100%;
}
.interesting_listContent_button_disable {
  position: absolute;
  left: 50%; /* 水平居中 */
  bottom: 30rpx; /* 距离底部 30rpx */
  transform: translateX(-50%); /* 使元素水平居中 */
  width: 220rpx;
  height: 60rpx;
  background: url('https://document.dxznjy.com/applet/newInteresting/interesting_start_disable.png') 100% 100% no-repeat;
  background-size: 100% 100%;
}

.play_again {
  margin-left: 10rpx;
  text-align: center;
  height: 90rpx;
  line-height: 90rpx;
  width: 668rpx;
  background: url('https://document.dxznjy.com/applet/newInteresting/interesting_btn_yellow.png') 100% 100% no-repeat;
  background-size: 100% 100%;
}

/* 弹窗样式 */
.dialogBG {
  width: 100%;
  /* height: 100%; */
}

/* 21天结束复习弹窗样式 */
.reviewCard_box {
  width: 670rpx;
  /* height: 560rpx; */
  position: relative;
}

.reviewCard_box image {
  width: 100%;
  height: 100%;
}

.cartoom_image {
  width: 420rpx;
  position: absolute;
  top: -265rpx;
  left: 145rpx;
  z-index: -1;
}

.reviewCard {
  position: relative;
  width: 100%;
  height: 100%;
  background: #ffffff;
  color: #000;
  border-radius: 24upx;
  padding: 50upx 55upx;
  box-sizing: border-box;
}
.review_close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  z-index: 1;
}

.reviewTitle {
  width: 100%;
  text-align: center;
  font-size: 34upx;
  display: flex;
  justify-content: center;
}
.dialogContent {
  box-sizing: border-box;
  font-size: 32upx;
  line-height: 45upx;
  text-align: center;
  margin-top: 40rpx;
}

.sure_btn {
  width: 250rpx;
  height: 80rpx;
  background: #2e896f;
  border-radius: 45rpx;
  font-size: 30rpx;
  color: #ffffff;
  line-height: 80rpx;
  text-align: center;
}

.cancel_btn {
  width: 250rpx;
  height: 80rpx;
  background: #ffffff;
  border-radius: 45rpx;
  border: 1rpx solid #2e896f;
  font-size: 30rpx;
  color: #2e896f;
  line-height: 80rpx;
  text-align: center;
  margin-left: 54rpx;
}

.addclass {
  width: 100%;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 30rpx;
  color: #2e896f;
  border: 1px solid #2e896f;
  border-radius: 35rpx;
}

.not-selected {
  width: 100%;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 30rpx;
  color: #000;
  border: 1px solid #c8c8c8;
  border-radius: 35rpx;
}
.boxDefault {
  width: 100%;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 30rpx;
  border-radius: 35rpx;
  background: #dddddd;
  border: 1px solid #dddddd;
}
.scroll-Y {
  height: 440rpx;
}
</style>
