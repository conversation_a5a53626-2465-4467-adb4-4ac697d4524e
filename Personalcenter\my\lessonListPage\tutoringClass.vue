<template>
  <view class="m_tutoring_class">
    <scroll-view scroll-y class="scroll-container" refresher-enabled :refresher-triggered="refreshing" @refresherrefresh="onRefresh" @scrolltolower="onLoadMore">
      <view class="tutoring_class_content">
        <!-- 伴学课列表 -->
        <view v-if="displayCourseData.length > 0" class="tutoring_class_list">
          <view v-for="(course, index) in displayCourseData" :key="course.courseId" class="tutoring_class_item">
            <view class="list_header">
              <view class="list_title_content">
                <text class="title">{{ course.courseName }}</text>
              </view>
              <view class="patrol_bt" v-if="course.feedback.length === 0" @click="handleParentPatrol(course, index)">
                <text class="patrol_text" :class="{ disabled: course.leaveStatus !== 0 }">
                  {{ course.leaveStatus === 0 ? '家长巡课 >' : '已请假' }}
                </text>
              </view>
            </view>
            <view class="course_info">
              <view class="info_row">
                <text class="info_label">学生姓名：</text>
                <text class="info-value">{{ course.studentName }}</text>
              </view>
              <view class="info_row ml20">
                <text class="info_label">课程类型：</text>
                <text class="info-value">{{ course.courseType }}</text>
              </view>
            </view>
            <view class="course_time">
              <text class="time_text">上课时间：{{ course.courseTime }}</text>
            </view>
            <view class="course_meeting">
              <text class="meeting_text">会议ID：{{ course.meetingNum }}</text>
            </view>
            <view class="divider_line"></view>
            <view class="course_footer">
              <view class="teacher_info">
                <view class="teacher_avatar">
                  <text class="avatar_text">{{ course.teacherName.charAt(0) }}</text>
                </view>
                <text class="teacher_name">教练：{{ course.teacherName }}</text>
              </view>
              <view class="action_buttons">
                <!-- 继续学习按钮 -->
                <view class="action_btn" v-if="courseStatus && course.feedback.length === 0 && course.leaveStatus === 0" @click="goOnStudy(course, index)">
                  <text class="btn_text">继续学习</text>
                </view>
                <!-- 立即上课-->
                <view class="action_btn" v-if="course.leaveStatus === 0 && questParams.courseStatus === 0" @click="clickStartClassBtn(course)">
                  <text class="btn_text">立即上课</text>
                </view>
                <!-- 查看反馈 todo 待完善-->
                <!-- <view class="action_btn" v-if="course.leaveStatus === 0 && course.feedback && course.feedback.length > 0" @click="clickFeedbackBtn(course)">-->
                <!-- <text class="btn_text">查看反馈</text>-->
                <!-- </view>-->
                <!-- 等待反馈-->
                <view
                  class="action_btn"
                  v-if="courseStatus === 1 && course.leaveStatus === 0 && !course.feedback && course.feedback.length === 0"
                  @click="clickWaitFeedbackBtn(course)"
                >
                  <text class="btn_text">等待反馈</text>
                </view>
                <!-- 请假按钮  只有未上课情况下才会有请假操作-->
                <view v-if="courseStatus === 0 && course.oneToManyType == 1" class="leave_button_container">
                  <!-- 请假按钮  leaveStatus (0 未请假 )oneToManyType(1一对多)(1 已请假未设置补课时间) (2 已请假未已设置补课时间)-->
                  <view v-if="course.leaveStatus == 0" class="leave_btn base_btn" @click="handleLeaveClick(course)">
                    <text class="leave_text">请假</text>
                  </view>
                  <!-- 已请假状态 -->
                  <view v-if="course.leaveStatus != 0" class="leave_status base_btn">
                    <text class="status_text">已请假</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <!-- 没有更多数据提示 -->
          <view v-if="!canLoadMore && hasLoadedOnce && displayCourseData.length > 0" class="no-more-tips">
            <text class="no-more-text">没有更多了～</text>
          </view>
        </view>
        <view v-else class="empty_container">
          <u-empty mode="data" width="56px" textSize="28rpx" icon="https://document.dxznjy.com/course/ac587707bf314badadb28a158852c77d.png" text="暂无数据" />
        </view>
      </view>
    </scroll-view>
    <!--请假弹框-->
    <u-popup :show="showLeavePopup" mode="center" :safeAreaInsetBottom="false" :closeable="true" :round="12" @close="showLeavePopup = false">
      <leave-popup-content @cancel="showLeavePopup = false" @submit="getDeliverCoursePage(true)" :columnLeave="columnLeave" :leaveInfo="leaveInfo" />
    </u-popup>
    <!-- 提示框-->
    <tips-download-popup ref="downloadPopup" />
  </view>
</template>

<script>
  import LeavePopupContent from './leavePopupContent.vue';
  import TipsDownloadPopup from '../../../components/tipsDownloadPopup.vue';

  export default {
    name: 'tutoringClass',
    components: { TipsDownloadPopup, LeavePopupContent },
    props: {
      questParams: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        displayCourseData: [],
        // 分页相关
        pageNum: 1,
        pageSize: 10,
        canLoadMore: true,
        refreshing: false,
        loadingMore: false,
        courseStatus: 0, // 0 未上课 1已上课
        columnLeave: null,
        hasLoadedOnce: false, // 是否触发过加载,
        leaveInfo: {},
        showLeavePopup: false // 请假弹框
      };
    },
    onLoad() {},
    watch: {
      questParams(val) {
        if (val) {
          this.pageNum = 1;
          this.canLoadMore = true;
          this.courseStatus = val.courseStatus || 0;
          val.merchantCode && this.getDeliverCoursePage(true);
        }
      }
    },
    methods: {
      // 获取交付课列表
      async getDeliverCoursePage(refresh = false) {
        uni.showLoading({ title: '加载中' });
        if (!this.questParams.merchantCode) return false;
        const params = {
          merchantCode: this.questParams?.merchantCode,
          courseStatus: this.questParams?.courseStatus,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          curriculumTypeId: this.questParams?.curriculumTypeId || '',
          studentCode: uni.getStorageSync('currentStudentCode'),
          startStudyTime: this.questParams?.startStudyTime || ''
        };
        try {
          const res = await this.$httpUser.get('zx/student/course/getDeliverCoursePage', params);
          const apiData = res?.data.data || {};
          // experience true 未 试课  experience false 未 正课
          const list = apiData.data.filter((item) => item.experience === true) || [];
          const currentPage = Number(apiData.currentPage || 1);
          const totalPage = Number(apiData.totalPage || 1);
          if (refresh) {
            this.displayCourseData = Array.isArray(list) ? list : [];
          } else {
            this.displayCourseData = [...this.displayCourseData, ...(Array.isArray(list) ? list : [])];
          }
          this.canLoadMore = currentPage < totalPage;
        } catch (e) {
          if (!refresh && this.pageNum > 1) {
            this.pageNum = Math.max(1, this.pageNum - 1);
          }
        } finally {
          uni.hideLoading();
          this.loadingMore = false;
          if (this.refreshing) this.refreshing = false;
        }
      },
      // 下拉刷新
      async onRefresh() {
        if (this.refreshing) return;
        this.refreshing = true;
        this.pageNum = 1;
        this.canLoadMore = true;
        await this.getDeliverCoursePage(true);
      },

      // 上拉加载更多
      async onLoadMore() {
        if (!this.canLoadMore || this.loadingMore) return;
        this.loadingMore = true;
        this.hasLoadedOnce = true;
        this.pageNum += 1;
        await this.getDeliverCoursePage(false);
      },
      // 点击请假
      async handleLeaveClick(course) {
        const canApplyLeave = await this.getLeaveInfo(course);
        // 后台请假次数为0 则不允许情急
        if (!canApplyLeave) {
          return uni.showToast({ icon: 'none', title: '该课程暂不支持请假' });
        }
        // 请假
        this.columnLeave = course;
        this.showLeavePopup = true;
      },
      // 获取请假回显，可以请假返回 true
      async getLeaveInfo(course) {
        console.log('调用');
        uni.showLoading({ title: '加载中' });
        try {
          const res = await this.$httpUser.get('deliver/class/studentLeave/app/planStudyInfo', {
            planStudyId: course.planStudyId,
            type: 1
          });
          this.leaveInfo = res.data.data || {};
          return +this.leaveInfo.configLeaveNum !== 0;
        } catch (e) {
          return false;
        } finally {
          uni.hideLoading();
        }
      },
      // 家长巡课
      handleParentPatrol(course, index) {
        this.$nextTick(() => {
          this.$refs.downloadPopup.openPopup();
        });
        console.log('家长巡课', course, index);
      },
      goOnStudy(course, index) {
        this.$nextTick(() => {
          this.$refs.downloadPopup.openPopup();
        });
        console.log('继续学习', course, index);
      },
      // 立即上课
      clickStartClassBtn(course) {
        this.$nextTick(() => {
          this.$refs.downloadPopup.openPopup();
        });
      },
      // 处理跳转反馈 todo 暂且写着,误删
      async getCourseById(course) {
        if (!course) {
          uni.showToast({ title: '课程数据不存在', icon: 'none' });
          return;
        }
        // 根据 oneToManyType 决定使用哪个 ID
        const id = course.oneToManyType === '1' ? course.classPlanStudyId || '' : course.courseId || '';
        if (!id) {
          uni.showToast({ title: 'ID不能为空', icon: 'none' });
          return;
        }
        try {
          const res = await this.$httpUser.get('deliver/app/parent/getCourseById', { courseId: id });
          console.log(res.data, '/////////////////////////////');
          const data = res.data.data;
          uni.navigateTo({
            url: '/Coursedetails/feedback/newIndex?data=' + JSON.stringify(data)
          });
        } catch (error) {}
      },
      // 查看反馈
      clickFeedbackBtn(course) {
        console.log(course, '查看反馈');
        if (course.courseType == '鼎英语') {
          this.getCourseById(course);
        } else {
          // uni.navigateTo({
          //   url: '/Coursedetails/feedback/newIndex?data=' + JSON.stringify(course)
          // });
        }
      },
      // 等待反馈
      clickWaitFeedbackBtn(course) {
        console.log(course, '等待反馈');
      }
    }
  };
</script>

<style scoped lang="scss">
  .m_tutoring_class {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .scroll-container {
    height: 100%;
  }

  .tutoring_class_content {
    padding: 20rpx;
    min-height: auto;
  }

  .tutoring_class_list {
    width: 100%;
    box-sizing: border-box;
  }

  .tutoring_class_item {
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    padding: 20rpx;
    background: url('https://document.dxznjy.com/course/dcdc10a7c6d044ff831625ced72dd338.png') no-repeat;
    background-size: 100% 100%;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
    width: 100%;
    box-sizing: border-box;
  }

  .list_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    width: 100%;

    .list_title_content {
      display: flex;
      align-items: center;
      flex: 1;

      .title {
        font-size: 30rpx;
        color: black;
        margin-right: 8rpx;
        font-weight: 600;
        flex: 1;
      }
    }

    .patrol_bt {
      flex-shrink: 0;
      margin-left: 10rpx;

      .patrol_text {
        font-size: 28rpx;
        color: #2c9e7c;
        font-weight: 600;

        &.disabled {
          color: #e57534;
        }
      }
    }
  }

  .course_info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20rpx;
    width: 100%;

    .info_row {
      display: flex;
      align-items: center;
      flex: 1;
      width: 100%;

      .info_label {
        min-width: 140rpx;
        flex-shrink: 0;
        font-size: 28rpx;
        color: #555555;
      }

      .info-value {
        flex: 1;
        font-size: 28rpx;
        color: #2c9e7c;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .ml20 {
      margin-left: 20rpx;
    }
  }

  .course_time,
  .course_meeting {
    margin-bottom: 20rpx;
    width: 100%;

    .time_text,
    .meeting_text {
      font-size: 28rpx;
      color: #555555;
    }
  }

  .divider_line {
    height: 1rpx;
    background-color: #f1eeee;
    margin: 20rpx 0;
    width: 100%;
  }

  .course_footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;

    .teacher_info {
      display: flex;
      align-items: center;
      flex: 1;

      .teacher_avatar {
        width: 55rpx;
        height: 55rpx;
        background-color: #339378;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10rpx;
        flex-shrink: 0;

        .avatar_text {
          font-size: 28rpx;
          color: #ffffff;
        }
      }

      .teacher_name {
        font-size: 28rpx;
        color: #333333;
        flex: 1;
      }
    }

    .action_buttons {
      display: flex;
      align-items: center;
      gap: 10rpx;
      flex-shrink: 0;

      .action_btn {
        padding: 6rpx 15rpx;
        border: 2rpx solid #339378;
        border-radius: 15rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .btn_text {
          font-size: 24rpx;
          color: #339378;
        }
      }
    }
  }

  .leave_button_container {
    display: flex;
    align-items: center;
    flex-shrink: 0;
  }

  .base_btn {
    padding: 6rpx 15rpx;
    border-radius: 15rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .leave_btn {
    border: 2rpx solid #e57534;

    .leave_text {
      font-size: 24rpx;
      color: #e57534;
    }
  }

  .leave_status {
    background-color: #f5f5f5;

    .status_text {
      font-size: 24rpx;
      color: #999999;
    }
  }

  .no-more-tips {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20rpx 0;
    color: #999;
    font-size: 28rpx;
    width: 100%;

    .no-more-text {
      text-align: center;
    }
  }

  .empty_container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: calc(100vh - 84rpx);
  }
</style>
