<template>
  <view class="courseItem radius-20 pb-10 positionRelative" :class="{ itemStyle: itemStyle }" :style="{ width: width }" @tap="onTap">
    <view class="courseimg relative">
      <image :src="item.goodsPicUrl" @load="emitHeight" @error="emitHeight" class="wh100" mode="widthFix"></image>
      <view class="positionAbsolute courseTip">
        <image v-if="item.goodsType == 2" :src="imgHost + 'dxSelect/tip_tyk.png'" class="ty_img" mode="widthFix"></image>
        <image v-else-if="item.goodsType == 3" :src="imgHost + 'dxSelect/tip_zsk.png'" class="ty_img" mode="widthFix"></image>
        <view v-else-if="item.goodsType == 4" class="product_tag">
          <view class="text">录播课</view>
        </view>
      </view>
    </view>
    <view class="plr-25 mtb-20">
      <view class="bold twolist f-32">{{ item.goodsName }}</view>
      <view class="fontWeight lh-32 mt-8">
        <text v-if="item.goodsTagOne" class="label_css radius-8 mr-8 display_inline">{{ item.goodsTagOne }}</text>
        <text v-if="item.goodsTagTwo" class="label_css radius-8 mr-8 display_inline">{{ item.goodsTagTwo }}</text>
        <text v-if="item.goodsTagThree" class="label_css radius-8 mr-8 display_inline">{{ item.goodsTagThree }}</text>
      </view>
      <view v-if="item.goodsType != 6">
        <view class="color_red font12 mtb-16 displayflex displayflexbetween">
          <view class="flex-a-c flex-x-s">
            <view>
              <span>{{ item.goodsType == 2 ? '体验价' : '会员价' }}</span>
              <span>￥</span>
              <span class="bold f-34">{{ item.goodsVipPrice }}</span>
            </view>
          </view>
          <image v-if="share && identityType && identityType == 4" class="productShare" :src="imgHost + 'dxSelect/share_icon.png'" @click.stop="shareVip('6', item)"></image>
        </view>
        <view class="displayflex color_grey mt-8 f-24" style="justify-content: space-between">
          <view>
            原价
            <text style="text-decoration: line-through">￥{{ item.goodsOriginalPrice }}</text>
          </view>
          <view>{{ item.goodsSales }}+人付款</view>
          <image v-if="share && !(identityType && identityType == 4)" class="productShare" :src="imgHost + 'dxSelect/share_icon.png'" @click.stop="shareVip('6', item)"></image>
        </view>
      </view>
      <view v-if="item.goodsType == 6">
        <view class="font12 mtb-16 displayflex displayflexbetween">
          <view>
            <span class="bold f-32 color_red">￥{{ item.goodsShowPrice ? item.goodsShowPrice : item.goodsOriginalPrice }}</span>
            <span class="f-24 cB4B1B1 price_text_css">鼎币</span>
          </view>
          <view class="f-24 cB4B1B1">已兑换{{ item.goodsSales }}件</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    name: 'helang-waterfall',
    props: {
      item: {
        type: Object,
        default() {
          return {};
        }
      },
      width: {
        type: String,
        default: '100%'
      },
      tag: {
        type: String | Number,
        default: ''
      },
      index: {
        type: Number,
        default: -1
      },
      share: {
        type: Boolean,
        default: true
      },
      identityType: {
        type: String | Number,
        default: uni.getStorageSync('identityType')
      },
      itemStyle: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        imgHost: getApp().globalData.imgsomeHost
      };
    },
    methods: {
      // 发出组件高度信息，在此处可以区分正确和错误的加载，给予错误的提示图片
      emitHeight(e) {
        const query = uni.createSelectorQuery().in(this);
        query
          .select('.courseItem')
          .boundingClientRect((data) => {
            let height = Math.floor(data.height);
            this.$emit('height', height, this.$props.tag);
          })
          .exec();
      },
      onTap() {
        this.$emit('click', this.$props.index, this.$props.tag);
      },
      shareVip(type, item) {
        //分享icon埋点
        getApp().sensors.track('shareIconClick', {
          goodsId: item.goodsId
        });
        this.$emit('shareVip', type, item);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .price_text_css {
    display: inline-block;
    margin-left: 8rpx;
  }
  .label_css {
    padding: 1rpx 12rpx;
    background-color: #dfffe4;
    font-size: 21rpx;
  }
  .display_inline {
    display: inline-block;
    color: #339378;
  }
  .member_back {
    width: 166rpx;
    height: 37rpx;
    line-height: 37rpx;
    text-align: center;
    background: url('https://document.dxznjy.com/course/8442f0de8da146798babc4aa04065bc9.png') no-repeat;
    background-size: 100%;
    margin-left: 5rpx;
  }
  .courseItem {
    // width: 330upx;
    border-radius: 20upx;
    background-color: #fff;
    margin-bottom: 30rpx;
    .courseimg {
      width: 100%;
    }
    .courseTip {
      width: 95upx;
      height: 40rpx;
      top: 20upx;
      right: 0;
    }
    .ty_img {
      width: 95rpx;
      height: 50rpx;
    }
    .product_tag {
      display: flex;
      align-items: center;
      width: 92rpx;
      height: 40rpx;
      background: #4095e5;
      border-radius: 19rpx 0rpx 0rpx 19rpx;
      opacity: 0.94;
      .text {
        width: 72rpx;
        height: 34rpx;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        font-size: 24rpx;
        color: #ffffff;
        line-height: 34rpx;
        text-align: right;
        font-style: normal;
        margin-left: 14rpx;
      }
    }
    .productShare {
      width: 30upx;
      height: 30upx;
    }
  }
  .itemStyle {
    background: #f9fcff;
    box-shadow: 0rpx 4rpx 8rpx 2rpx rgba(216, 231, 227, 0.2);
  }
</style>
