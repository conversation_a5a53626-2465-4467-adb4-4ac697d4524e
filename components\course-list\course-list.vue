<template>
	<view class="courseItem radius-20 pb-10 positionRelative" :class="{ itemStyle: itemStyle }"
		:style="{ width: width }" @tap="onTap">
		<view class="courseimg relative">
			<image :src="item.goodsPicUrl" style="width: 326rpx; height: 365rpx"></image>
			<!-- <view class="positionAbsolute courseTip">
				<image v-if="item.goodsType == 2" :src="imgHost + 'dxSelect/tip_tyk.png'" class="ty_img"
					mode="widthFix"></image>
				<image v-else-if="item.goodsType == 3" :src="imgHost + 'dxSelect/tip_zsk.png'" class="ty_img"
					mode="widthFix"></image>
				<view v-else-if="item.goodsType == 4" class="product_tag">
					<view class="text">录播课</view>
				</view>
			</view> -->
		</view>
		<view class="plr-12 mtb-9">
			<view class="flex-row-center">
				<view class="bold f-25 course-title">
					<!-- <u-button type="primary" :plain="true" text="镂空" size="mini" customStyle="width: 120rpx; height: 30rpx; margin-right: 10rpx;"></u-button> -->
					<span class="tags">{{ item.goodsTagOne }}</span>
					{{ item.goodsName }}
				</view>
			</view>
			<view class="fontWeight lh-32" style="height: 30rpx;">
				<!-- <text v-if="item.goodsTagOne" class="radius-8 mr-8 display_inline">{{ item.goodsTagOne }}</text> -->
				<text v-if="item.goodsTagTwo" class="radius-8 mr-8 display_inline">{{ item.goodsTagTwo }}</text>
				<text v-if="item.goodsTagThree" class="radius-8 mr-8 display_inline">{{ item.goodsTagThree }}</text>
			</view>
			<view v-if="item.goodsType != 6">
				<!-- <view class="color_red font12 mtb-16 displayflex displayflexbetween">
          <view class="flex-a-c flex-x-s">
            <view>
              <span>{{ item.goodsType == 2 ? '体验价' : '会员价' }}</span>
              <span>￥</span>
              <span class="bold f-34">{{ item.goodsVipPrice }}</span>
            </view>
          </view>
        </view> -->
				<view class="displayflex color_grey"
					style="justify-content: space-between; margin-bottom: 16rpx; margin-top: 13rpx">
					<view class="color_red mtb-16 displayflex displayflexbetween">
						<view class="flex-a-c flex-x-s">
							<view style="line-height: 30rpx">
								<span style="font-size: 21rpx">{{ item.goodsType == 2 ? '体验价' : '会员价' }}</span>
								<span style="font-size: 21rpx">￥</span>
								<span class="bold f-28">{{ item.goodsVipPrice }}</span>
							</view>
						</view>
					</view>
					<!-- <view>
            原价
            <text style="text-decoration: line-through">￥{{ item.goodsOriginalPrice }}</text>
          </view> -->
					<view style="font-size: 21rpx">{{ formatSalesCount(item.goodsSales) }}</view>
				</view>
			</view>
			<view v-if="item.goodsType == 6">
				<view class="font12 mtb-16 displayflex displayflexbetween">
					<view>
						<span
							class="bold f-32 color_red">￥{{ item.goodsShowPrice ? item.goodsShowPrice : item.goodsOriginalPrice }}</span>
						<span class="f-24 cB4B1B1 price_text_css">鼎币</span>
					</view>
					<view class="f-24 cB4B1B1">已兑换{{ item.goodsSales }}件</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			item: {
				type: Object,
				default () {
					return {};
				}
			},
			width: {
				type: String,
				default: '100%'
			},
			tag: {
				type: String | Number,
				default: ''
			},
			index: {
				type: Number,
				default: -1
			},
			itemStyle: {
				type: Boolean,
				default: false
			}
		},
		created() {
			console.log('[Created] 组件item数据:', this.item);
		},
		data() {
			return {
				imgHost: getApp().globalData.imgsomeHost
			};
		},
		methods: {
			onTap() {
				this.$emit('click', this.$props.index, this.$props.tag);
			},
			formatSalesCount(count) {
				const num = parseInt(count);
				if (num > 10000) {
					const wan = Math.floor(num / 10000);
					return `${wan}万+人付款`;
				} else {
					return `${num}人付款`;
				}
			}
		}
	};
</script>

<style lang="scss" scoped>
	.tags {
		padding: 2rpx 8rpx;
		// line-height: 100%;
		color: #009b55;
		border: 1rpx solid #009b55;
		border-radius: 8rpx;
		font-size: 22rpx;
		margin-right: 10rpx;
		align-items: center;
		// display: inline-block;
	}

	.price_text_css {
		display: inline-block;
		margin-left: 8rpx;
	}

	.flex-row-center {
		// display: flex;
		// align-items: center;
	}

	.display_inline {
		font-size: 22rpx;
		font-weight: bold;
		display: inline-block;
		color: #9595ae;
	}

	.course-title {
		display: -webkit-box;
		color: #555555;
		font-size: 28rpx;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		overflow: hidden;
		text-overflow: ellipsis;
		line-height: 1.4;
		word-break: break-all;
		/* 重置white-space属性，覆盖父容器的nowrap设置 */
		white-space: normal;
		/* 移除固定高度，让内容自适应 */
		min-height: 2.8em;
		max-height: 2.8em;
	}

	.member_back {
		width: 166rpx;
		height: 37rpx;
		line-height: 37rpx;
		text-align: center;
		background: url('https://document.dxznjy.com/course/8442f0de8da146798babc4aa04065bc9.png') no-repeat;
		background-size: 100%;
		margin-left: 5rpx;
	}

	.courseItem {
		// width: 330upx;
		border-radius: 20upx;
		background-color: #fff;
		margin-bottom: 30rpx;
		box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.08);

		.courseimg {
			width: 100%;
		}

		.courseTip {
			width: 95upx;
			height: 40rpx;
			top: 20upx;
			right: 0;
		}

		.ty_img {
			width: 95rpx;
			height: 50rpx;
		}

		.product_tag {
			display: flex;
			align-items: center;
			width: 92rpx;
			height: 40rpx;
			background: #4095e5;
			border-radius: 19rpx 0rpx 0rpx 19rpx;
			opacity: 0.94;

			.text {
				width: 72rpx;
				height: 34rpx;
				font-family: AlibabaPuHuiTi_2_55_Regular;
				font-size: 24rpx;
				color: #ffffff;
				line-height: 34rpx;
				text-align: right;
				font-style: normal;
				margin-left: 14rpx;
			}
		}

		.productShare {
			width: 30upx;
			height: 30upx;
		}
	}

	.itemStyle {
		background: #f9fcff;
		box-shadow: 0rpx 4rpx 8rpx 2rpx rgba(216, 231, 227, 0.2);
	}
</style>