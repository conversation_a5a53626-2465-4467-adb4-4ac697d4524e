<template>
  <div>
    <web-view v-if="showWebview" :src="webUrl" ref="webview" />
  </div>
</template>

<script>
  export default {
    data() {
      return {
        webUrl: '',
        showWebview: true
      };
    },
    onLoad(e) {
      let token = uni.getStorageSync('token');

      // let baseUrl = 'http://*************:8081/#/pages/zx/playVideo';
      let baseUrl = 'https://document.dxznjy.com/qyWechat/#/pages/zx/playVideo';
      this.webUrl = baseUrl + '?sendData=' + e.sendData + '&dataInfo=' + e.dataInfo + '&token=' + token;
      // console.log('🚀 ~ onload ~ e:', this.webUrl);
      // this.$nextTick(() => {
      //   setTimeout(() => {
      //     const webview = this.$refs.webview;

      //     console.log('🚀 ~ onUnload ~ webview:', webview);
      //   }, 10000);
      // });
    },
    // 父页面关闭时向 WebView 发送消息
    onUnload() {
      // const webview = this.$refs.webview;
      // console.log('🚀 ~ onUnload ~ webview:', webview);
      // webview.evalJs('window.parentPageClosing()');
    }
  };
</script>

<style lang="scss" scoped></style>
