<template>
  <view class="invitationBg">
    <!-- 开启图片长按识别二维码 -->
    <view class="article" v-html="contentUrl"></view>
    <!-- <image src="https://document.dxznjy.com/course/437c8feb0bd54ae0b4bb5afe10c0880d.png" class="invitation" mode="scaleToFill" show-menu-by-longpress></image> -->
  </view>
</template>

<script>


export default {
  data() {
    return {
      contentUrl: '<p><img src="https://document.dxznjy.com/course/21a28bc91c7b444aad24f7dac6e864fb.png" style="max-width:100%;" contenteditable="false"/>1111111111111111111111111111111111111111111111111111111111</p>',
    };
  },
  onShow() {

    this.contentUrl = wx.getStorageSync('invitationContent');
    console.log(this.contentUrl)
    console.log('gggggggggggggggggggggggggggggggggggggggggggggggggggggggggg')
  },

}
</script>

<style lang="scss" scoped>
.invitationBg {
  width: 100vw;
  height: 100vh;
  //background: url('https://document.dxznjy.com/course/1cc9f3b9a0d44c44a6ace16456262a29.png') no-repeat;
  //background-size: 100%;
  overflow: hidden;
  //background-color: #edf1fc;
}
.invitation {
  //display: flex;
  //margin: 0 auto;
  width: 100%;
  height: 100%;
}
</style>
