<template>
  <div>
    <view :style="{ marginTop: '200rpx' }" class="t-c flex-col radius-15">
      <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
      <view style="color: #bdbdbd">暂无数据</view>
    </view>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        imgHost: getApp().globalData.imgsomeHost
      };
    }
  };
</script>
<style scoped>
  page {
    height: 100vh;
  }
</style>
<style lang="scss" scoped>
  .img_s {
    width: 160rpx;
  }
</style>
