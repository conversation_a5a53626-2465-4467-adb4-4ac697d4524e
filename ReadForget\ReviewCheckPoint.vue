<template>
  <view class="contain" :style="{ height: useHeight + 'rpx' }">
    <view class="item" v-for="item in list" :key="item.id">
      <view class="">{{ item.checkpointName }}</view>
      <view class="right" @click="goReview(item)">
        <view>
          {{ item.courseName }}
          <uni-icons type="right" size="16" color="#000" style="margin-left: 10rpx"></uni-icons>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        studentCode: '',
        merchantCode: '',
        useHeight: 0,
        list: []
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          this.useHeight = res.windowHeight * (750 / res.windowWidth) - 100;
        }
      });
    },
    onLoad(options) {
      this.studentCode = options.studentCode;
      this.merchantCode = options.merchantCode;
    },
    onShow() {
      this.init();
    },
    methods: {
      async init() {
        let res = await this.$httpUser.get('znyy/superReadReview/getReviewRoundList?status=0&studentCode=' + this.studentCode + '&merchantCode=' + this.merchantCode);
        console.log(res);
        if (res.data.success) {
          if (!res.data.data) {
            return uni.reLaunch({
              url: `/ReadForget/index?studentCode=${this.studentCode}&merchantCode=${this.merchantCode}`
            });
          }
          this.list = res.data.data;
        }
      },
      goReview(item) {
        uni.navigateTo({
          url: `/ReadForget/ReviewBook?studentCode=${this.studentCode}&merchantCode=${this.merchantCode}&courseId=${item.courseId}&articleId=${item.articleId}&forgettingId=${item.id}`
        });
      }
    }
  };
</script>

<style lang="scss">
  .contain {
    margin: 0 32rpx;
    background-color: #fff;
    border-radius: 24rpx;
    overflow-y: auto;
    padding: 32rpx;
    .item {
      display: flex;
      height: 108rpx;
      font-size: 32rpx;
      align-items: center;
      color: #555;
      justify-content: space-between;
      border-bottom: 2rpx dashed #efefef;
      .right {
        font-size: 28rpx;
      }
    }
  }
</style>
