<template>
  <view>
    <view class="plr-30">
      <view class="content">
        <form id="#nform">
          <view class="information">
            <view style="width: 35%" class="f-28 bold">学员姓名：</view>
            <view class="phone-input">
              <input disabled type="text" v-model="trialname" name="trialname" placeholder="请输入学员姓名" class="input c-00" @blur="nameSearch" confirm-type="search" />
            </view>
          </view>
          <view class="information mt-40">
            <view style="width: 35%" class="f-28 bold">联系方式：</view>
            <view class="phone-input">
              <input type="number" v-model="mobile" name="mobile" placeholder="请输入联系方式" class="input c-00" maxlength="11" @blur="nameSearch" />
            </view>
          </view>
          <view class="information mt-40">
            <view style="width: 35%" class="f-28 bold">试课状态：</view>
            <view class="phone-input">
              <uni-data-select class="uni-select w100" v-model="value" :localdata="range" @change="change"></uni-data-select>
            </view>
          </view>
        </form>
      </view>
      <scroll-view scroll-y="true" :style="{ height: svHeight + 'px' }" class="tips">
        <view v-for="item in trialList.list" :key="item.expId">
          <view class="detailed" @click="shopNews(item)">
            <image class="infopic" :src="item.portrait == '' ? avaUrl : item.portrait"></image>
            <view>
              <view class="personl" style="margin-bottom: 10rpx; line-height: 60rpx">昵称：{{ item.name }}</view>
              <view class="personl">联系方式：{{ item.mobile }}</view>
            </view>
            <view style="margin-left: 100rpx">
              <view class="t-c mtb-20">
                <image :src="imgHost + 'dxSelect/image/right.png'" style="width: 14rpx; height: 24rpx"></image>
              </view>
              <image v-if="item.status == 0" :src="imgHost + 'dxSelect/image/submitted.png'" style="width: 90rpx; height: 36rpx"></image>
              <image v-if="item.status == 1" :src="imgHost + 'dxSelect/image/trialclass.png'" style="width: 90rpx; height: 36rpx"></image>
              <image v-if="item.status == 2" :src="imgHost + 'dxSelect/image/testedclass.png'" style="width: 90rpx; height: 36rpx"></image>
            </view>
          </view>
        </view>
        <view v-if="no_more && trialList.list != undefined && trialList.list.length > 0">
          <u-divider text="到底了"></u-divider>
        </view>
        <view v-if="trialList.list != undefined && trialList.list.length == 0">
          <u-divider text="没有更多了"></u-divider>
        </view>
      </scroll-view>
    </view>
  </view>
</template>
<script>
  const { $navigationTo, $getSceneData, $showError, $showMsg, $http } = require('@/util/methods.js');
  import Util from '@/util/util.js';
  export default {
    data() {
      return {
        // 试课状态
        value: '',
        range: [
          {
            value: 0,
            text: '待支付'
          },
          {
            value: 1,
            text: '待试课'
          },
          {
            value: 2,
            text: '已试课'
          }
        ],
        trialname: '', // 昵称
        mobile: '', // 试课人手机号
        // ph: '', // 窗口高度
        svHeight: 0, // 暂无数据距离底边距离
        infolist: [], // 个人信息列表
        resultShow: false,
        show: false,
        no_more: false,
        trialList: {}, // 试课推荐列表
        page: 1,
        expId: '',
        avaUrl: Util.getCachedPic('https://document.dxznjy.com/dxSelect/home_avaUrl.png', 'home_avaUrl_path'),
        imgHost: getApp().globalData.imgsomeHost
      };
    },
    onReady() {
      let that = this;
      uni.getSystemInfo({
        //调用uni-app接口获取屏幕高度
        success(res) {
          that._data.pH = res.windowHeight; //windoHeight为窗口高度
          let titleH = uni.createSelectorQuery().select('.tips'); //想要获取高度的元素名（class/id）
          titleH
            .boundingClientRect((data) => {
              let pH = that._data.pH;
              that._data.svHeight = pH - data.top - 15; //计算高度：元素高度=窗口高度-元素距离顶部的距离（data.top）
            })
            .exec();
        }
      });
    },
    onReachBottom() {
      if (this.page >= this.trialList.totalPage) {
        this.no_more = true;
        return false;
      }
      this.getParentreport(true, ++this.page);
    },
    onShow() {
      this.homeData();
      this.getParentreport();
    },
    onLoad(e) {
      console.log(e.type);
      this.trialname = e.studentName;
    },
    methods: {
      // 试课反馈下拉框
      change(e) {
        console.log('e:', e);
        this.value = e;
        this.getParentreport();
      },
      skintap(url) {
        $navigationTo(url);
      },
      shopNews(item) {
        console.log(item);
        let that = this;
        that.expId = item.expId;
        if (item.status == 0) {
          uni.showToast({
            icon: 'none',
            title: '该订单代提交哦'
          });
        } else {
          uni.navigateTo({
            url: '/Trialclass/result?expId=' + that.expId
          });
        }
      },
      //会员试课推荐列表
      async getTriallist(isPage, page) {
        let _this = this;
        uni.showLoading();
        const res = await $http({
          url: 'zx/exp/getPageList',
          data: {
            name: _this.trialname ? _this.trialname : '',
            mobile: _this.mobile ? _this.mobile : '',
            status: _this.value ? _this.value : ''
          }
        });
        uni.hideLoading();
        if (res) {
          if (isPage) {
            let old = _this.trialList.list;
            _this.trialList.list = [...old, ...res.data.list];
          } else {
            _this.trialList = res.data;
          }
        }
      },
      //查询
      nameSearch(e) {
        if (this.mobile.trim() || this.mobile == '') {
          this.getParentreport();
        }
        if (this.trialname.trim() || this.trialname == '') {
          this.getParentreport();
        }
      },
      async homeData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.infolist = res.data;
        }
      },
      //家长试课报告列表
      async getParentreport(isPage, page) {
        let _this = this;
        uni.showLoading();
        const res = await $http({
          url: 'zx/exp/getMemberPageList',
          data: {
            name: _this.trialname ? _this.trialname : '',
            mobile: _this.mobile ? _this.mobile : '',
            status: _this.value ? _this.value : ''
          }
        });
        uni.hideLoading();
        if (res) {
          if (isPage) {
            let old = _this.trialList.list;
            _this.trialList.list = [...old, ...res.data.list];
          } else {
            _this.trialList = res.data;
          }
        }
      }
    }
  };
</script>
<style lang="scss" scoped>
  .content {
    background-color: #fff;
    padding: 40rpx 30rpx 40rpx;
    border-radius: 14rpx;
    margin-bottom: 20rpx;
  }

  .information {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .phone-input {
    background: #fff;
    border-radius: 8rpx;
    width: 100%;
    height: 70rpx;
    font-size: 28rpx;
    color: #999999;
    display: flex;
    padding-left: 30rpx;
    align-items: center;
    border: 3rpx solid #ececea;

    input {
      font-size: 28rpx;
    }
  }

  .name-input {
    background: #fff;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #999999;
    height: 70rpx;
    display: flex;
    justify-content: space-between;
    padding: 0 30rpx;
    margin-top: 30rpx;
    align-items: center;
    border: 3rpx solid #ececea;
  }

  /deep/.uni-select {
    padding: 0 10rpx 0 0;
    border: 0;
  }

  /deep/.uni-select__input-placeholder {
    font-size: 28rpx;
  }

  .uni-list-cell-db {
    background: #fff;
    border-radius: 8rpx;
    width: 100%;
    height: 70rpx;
    font-size: 28rpx;
    color: #999999;
    display: flex;
    padding-left: 30rpx;
    align-items: center;
    border: 3rpx solid #ececea;
  }

  .tips {
    position: relative;
    width: 100%;
    padding-bottom: 200rpx;
  }

  /deep/.phone-btn {
    width: 100%;
    height: 100rpx;
    line-height: 100rpx;
    border-radius: 90rpx;
    font-size: 30rpx;
    color: #fff;
    background: linear-gradient(to right, rgb(244, 142, 110), rgb(246, 76, 83));
  }

  .detailed {
    display: flex;
    align-items: center;
    background-color: #fff;
    padding: 30rpx;
    border-radius: 14rpx;
    margin-bottom: 20rpx;
  }

  .infopic {
    width: 100rpx;
    height: 100rpx;
    margin-right: 30rpx;
    border-radius: 50%;
    // background-color: pink;
  }

  .personl {
    font-size: 27rpx;
  }

  /deep/.btn {
    padding: 3rpx 10rpx;
    border-radius: 4rpx;
    font-size: 26rpx;
    line-height: 36rpx;
  }

  .stay_btn {
    background-color: #439286;
    color: #fff;
    padding: 3rpx 10rpx;
    border-radius: 4rpx;
    font-size: 26rpx;
    line-height: 36rpx;
  }

  .ptb-20 {
    background-color: #fff;
    padding: 30rpx 30rpx 10rpx;
  }

  .wh100 {
    width: 100rpx;
    height: 100rpx;
    background-color: palegoldenrod;
    border-radius: 50%;
    margin-right: 30rpx;
  }

  // .f-28 {
  //     font-size: 34rpx;
  // }

  .ml-20 {
    margin-top: 3%;
  }

  .mt-30 {
    padding: 0 50rpx;
  }

  .classText0 {
    width: 90rpx;
    height: 36rpx;
    line-height: 36rpx;
    text-align: center;
    border-radius: 15rpx;
    background-color: #e57126;
  }

  .classText1 {
    width: 90rpx;
    height: 36rpx;
    line-height: 36rpx;
    text-align: center;
    border-radius: 15rpx;
    background-color: #439286;
  }
  .classText2 {
    width: 90rpx;
    height: 36rpx;
    line-height: 36rpx;
    text-align: center;
    border-radius: 15rpx;
    background-color: #2dc032;
  }
</style>
