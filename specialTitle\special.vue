<template>
	<view class="">

		<u-navbar  bgColor="transparent" :title="specialName" @leftClick="leftClicks" :borderBottom="false" :titleStyle="{ color: '#FFFFFF', fontWeight: 'bold', fontSize: '36rpx' }" >
			<!-- 左侧自定义返回按钮 -->
			<template #left>
				<div class="custom-left-btn">
					<u-icon name="arrow-left" color="#FFFFFF" size="30rpx"></u-icon>
				</div>
			</template>

			<!-- 右侧自定义图标（适配胶囊位置） -->
			<!-- <template #right>
				<div :style="{ paddingRight: rightPadding + 'px' }">
					<div class="custom-right-icon" @click="rightClick">
						<u-icon name="share-square" color="#FFFFFF" size="30rpx"></u-icon>
					</div>
				</div>
			</template> -->
		</u-navbar>

		<view class="">
			<!-- 专题配图 -->
			<view class="" style="margin: 0; padding: 0; display: block;">
				<image :src="courseData.picUrl" mode="" style="width: 750rpx;  display: block;" mode="widthFix"></image>
			</view>
			<!-- 产品介绍 -->
			<view v-if="courseData.productIntroPicUrl" class="" style=" margin: 0; display: block;">
				<image :src="courseData.productIntroPicUrl" style="width: 750rpx; display: block;" mode="widthFix">
				</image>
				<!-- <rich-text :nodes="courseData.productIntroPicUrl"></rich-text> -->
			</view>

			<!-- 导航栏 -->
			<!-- <view class="nav-bar">
				<view class="nav-item" @click="scrollToSection('courseList')">课程列表</view>
				<view class="nav-item" @click="scrollToSection('chinese')">语文</view>
				<view class="nav-item" @click="scrollToSection('math')">数学</view>
				<view class="nav-item" @click="scrollToSection('english')">英语</view>
			</view> -->

			<view class="center-view">
				<view class="title-center" v-if="courseData.picUrl">
					<!-- <span>课程列表</span> -->
				</view>
				<view class="" v-for="(items,indexs) in courseData.categoryList" :key="indexs"
					style="padding: 0 30rpx;">
					<view v-if="items.goodsList.length > 0" class="categoryNameStyle">{{items.categoryName}}</view>
					<view class="course-container">
						<course-list v-for="(item,index) in items.goodsList" :key="index" :item="item" :index="index"
							:width="'326rpx'"
							@click="skintap('Coursedetails/productDetils?id=' + item.goodsId, item.goodsId)"></course-list>
					</view>
				</view>
			</view>

		</view>
		<!-- 分享弹窗 -->
		<!-- shareContent -->
		<sharePopup ref="sharePopupRefs"></sharePopup>
	</view>
</template>

<script>
	import sharePopup from '@/components/sharePopup.vue';
	const {
		$navigationTo,
		$http
	} = require('@/util/methods.js');
	import courseList from '@/components/course-list/course-list.vue'
	export default {
		components: {
			courseList,
			sharePopup
		},
		data() {
			return {
				richText: "",
				courseData: [],
				titleHeight: 0,
				rightPadding: 0,
				// 分享内容
				shareContent: {},
				specialId: '',
				specialName: '' //专题id

			};
		},
		onLoad(option) {
			console.log(option, '参数传递---------')
			this.specialName = option.specialName
			this.specialId = option.specialId
			this.getSpecial()
		},
		methods: {
			leftClicks() {
				uni.navigateBack()
			},
			// rightClick(item) {
			// 	console.log(this.courseData, '分享------------')

			// 	// this.shareContent.type = type;
			// 	// let id = '',
			// 	// 	imgurl = '';
			// 	// //type 1课程  2学习超人（会员） 3超人俱乐部
			// 	// if (type == '1') {
			// 	// 	id = item.courseId;
			// 	// 	imgurl = item.courseImage;
			// 	// } else {
			// 	// 	id = item.mealId;
			// 	// 	type == '2' ? (imgurl = Config.supermanShareImage) : (imgurl = Config.supermanClubShareImage);
			// 	// }
			// 	// this.shareContent.id = item.goodsId;
			// 	// if (type != 6) {
			// 	// 	this.shareContent.imgurl = imgurl;
			// 	// } else {
			// 	// 	this.shareContent.imgurl = item.goodsSharePoster;
			// 	// 	this.shareContent.title = item.goodsShareTextList[0] ? item.goodsShareTextList[0].shareText : null;
			// 	// }
			// 	//
			// 	this.$refs.sharePopupRefs.open(this.shareContent);
			// }, 
			// getHeight() {
			// 	let res = uni.getMenuButtonBoundingClientRect(); //胶囊按钮
			// 	console.log(res, '高度');
			// 	this.titleHeight = res.top + res.height;
			// 	this.rightPadding = res.width
			// },
			// 滚动到指定区域
			scrollToSection(sectionId) {
				uni.pageScrollTo({
					selector: '#' + sectionId,
					duration: 300
				});
			},
			// 专题详情
			async getSpecial() {
				uni.showLoading({
					title: '加载中'
				});
				const res = await $http({
					url: 'zx/wap/homePage/special/detail',
					data: {
						specialId: this.specialId,
						userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
					}
				});
				console.log('完整返回数据:', res)

				if (res.code == 20000) {
					// uni.setNavigationBarTitle({
					// 	title: res.data.specialName
					// });
					this.courseData = res.data
					uni.hideLoading()
				} else {
					uni.hideLoading()
				}

			},
			skintap(url, goodsId) {
				//埋点
				getApp().sensors.track('specialGoodsClick', {
				  goods_id: goodsId
				});
				$navigationTo(url);
			},
		}
	};
</script>
<style>
	body{
		background-color: #fdfdfd;
	}
</style>

<style lang="scss" scoped>
	
	/deep/.u-navbar--fixed {
		background: linear-gradient(to bottom, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 50%, rgba(0,0,0,0) 100%) !important;
	}
	

	/* .nav-bar {
		display: flex;
		justify-content: space-around;
		padding: 20rpx 0;
		background-color: #f5f5f5;
		position: sticky;
		top: 0;
		z-index: 100;
		margin-bottom: 20rpx;
	}
	
	.nav-item {
		padding: 10rpx 20rpx;
		background-color: #fff;
		border-radius: 30rpx;
		font-size: 28rpx;
		color: #666;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
		transition: all 0.3s;
	}
	
	.nav-item:active {
		background-color: #007aff;
		color: #fff;
		transform: scale(0.95);
	} */
	/* 左侧按钮样式 */
	.custom-left-btn {
		width: 48rpx;
		height: 48rpx;
		border-radius: 50%;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		// transition: background-color 0.2s;
		margin-left: 10px;
		/* 左侧安全距离 */
	}

	.custom-left-btn:active {
		background-color: #ffffff;
	}

	/* 右侧图标样式 */
	.custom-right-icon {
		width: 48rpx;
		height: 48rpx;
		border-radius: 50%;
		background: rgba(0, 0, 0, 0.5);
		/* 垂直居中 */
		display: flex;
		align-items: center;
		justify-content: center;
		/* 优化点击区域 */
		// padding: 8px 0 8px 8px;
		// height: 100%;
		// box-sizing: border-box;
	}

	/* 右侧图标点击效果 */
	.custom-right-icon:active {
		background-color: rgba(0, 0, 0, 0.05);
		border-radius: 6px;
	}

	.center-view {
		margin-top: 20rpx;
		width: 100%;
	}

	.title-center {
		width: 750rpx;
		height: 34rpx;
		// line-height: 34rpx;
		// background: linear-gradient(90deg, #ffffff 0%, #ddf0cc 36%, #ddf0cc 70%, #ffffff 100%);
		background-color: #F6F6F6;
		// text-align: center;
		margin-bottom: 20rpx;
		// font-size: 32rpx;
		// font-weight: bold;
		// color: #009b55;
	}

	.course-container {
		// padding: 10rpx;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		// gap: 24rpx;
		// margin: 0 auto;
		// max-width: 710rpx;
	}

	.categoryNameStyle {
		margin: 30rpx 0;
		font-size: 32rpx;
		color: #333333;
		font-weight: bold;
	}
</style>