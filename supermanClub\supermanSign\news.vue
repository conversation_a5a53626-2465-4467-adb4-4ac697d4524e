<template>
	<view>
		<view class="content">
			<view class="plan" v-for="item in newslist" :key="item.id" @click="reading(item.id)">
				<view >
					<view class="title">
						<view style="display: flex;">
							<view class="icon" v-if="item.status == 0">
								<uni-icons type="smallcircle-filled" color="#2E896F" size="10"></uni-icons>
							</view>
							<view class="head">消息提醒</view>
						</view>
						<view class="title_right">
							{{item.createTime}}
						</view>
					</view>
					<view class="hour c-66 f-30">
						{{item.content}}。
					</view>
				</view>
				
			</view>
		</view>
		<!-- 加载更多 -->
		<view v-if="no_more && (newslist != undefined && newslist.length>0)">
			<u-divider text="到底了"></u-divider>
		</view>
		
		<view v-if="newslist.length==0" class="t-c flex-col bg-ff radius-15 mlr-30" :style="{height: useHeight + 'rpx'}">
		    <image :src="imgHost+'dxSelect/fourthEdition/none-data.png'" class="mb-20 img"></image>
		    <view style="color: #BDBDBD;" class="f-30">暂无数据</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				data: {
					pageNum: 1,
					pageSize: 10,
				},
				array:[],
				newStatus:'', // 消息状态
				newslist: [], // 消息通知列表 
				totalItems: '', // 消息总数量
				ifBottomRefresh:false,
				status: 'more', // 加载更多
				contentText: {
					"contentdown": "加载更多数据",
					"contentrefresh": "加载中...",
					"contentnomore": "暂无更多数据"
				},
				useHeight:0,
				no_more: false,
				imgHost: getApp().globalData.imgsomeHost,
			};
		},
		onLoad() {
			let token = uni.getStorageSync('token')
			console.log(token);
			if (token) {
				this.getNewslist() // 消息通知
			} else {
				console.log('没有token');
			}
		},
		onReady() {
			uni.getSystemInfo({
				success: (res) => {
					// 可使用窗口高度，将px转换rpx
					let h = (res.windowHeight * (750 / res.windowWidth));
					this.useHeight = h - 30;
				}
			})
		},
		// 触底的事件
		// onReachBottom() {
		// 	this.data.pageNum += 1
		// 	this.getNewslist()
		// 	if (this.status == 'no-more') return
		// },
		onReachBottom() {
			if ((this.data.pageNum*10) >= Number(this.totalItems)) {
				this.no_more = true;
				return false;
			}
			this.getNewslist(true, ++this.data.pageNum);
		},
		methods: {
			Back() {
				for (let i = 0; i < this.newslist.length; i++) {
					this.newslist[i].status = 1
				}
				console.log(this.newslist)
				uni.switchTab({
					url: '/pages/home/<USER>/index?list=' + this.totalItems
				})
				return;
			},
			// 已读
			reading(id) {
				this.getReadlist(id);
			},

			// 通知列表
			async getNewslist(isPage, page) {
				let res = await this.$httpUser.get("deliver/app/parent/notifyList", this.data);
				if (res.data.success) {	
					if (isPage) {
						let old = this.newslist;
						this.newslist= [...old, ...res.data.data.data];
					} else {
						this.newslist = res.data.data.data;
					}
					this.totalItems = res.data.data.totalItems;
					// // 为数据赋值：通过展开运算符的形式，进行新旧数据的拼接
					// this.newslist = [...this.newslist, ...res.data.data.data]
					// this.totalItems = res.data.data.totalItems
					// this.status = this.newslist.length < res.data.data.totalItems ? 'more' : 'no-more'
				}	
			},
			// 已读通知
			async getReadlist(id) {
				let res = await this.$httpUser.post("deliver/app/parent/notifyList/" + id);
				if(res.data.success) {
					for (let i = 0; i < this.newslist.length; i++) {
						if(this.newslist[i].id==id){
							this.newslist[i].status=1
						}
					}
				}
			},
		}
	}
</script>
<style lang="scss" scoped>
	.icon_img {
		position: absolute;
		left: 10rpx;
	}
	.content {

		// 学习进度
		.plan {
			margin-top: 20rpx;
			background-color: #fff;
			border-radius: 30rpx;
			margin: 20rpx;
			.title {
				display: flex;
				font-size: 32rpx;
				font-weight: 700;
				padding: 20rpx 0 20rpx 0;
				margin-left: 30rpx;
				border-bottom: 1px dashed #EEEEEE;
				justify-content: space-between;
				line-height: 50upx;
				.title_right{
					font-size: 30upx;
					color: #999999;
					padding-right: 30upx;
					font-weight: normal;
				}
			}
			.icon {
				width: 30rpx;
				line-height: 50rpx;
				text-align: center;
				margin-right: 20rpx;
			}
			.head {
				line-height: 60rpx;
			}
			.right {
				font-weight: 400;
				font-size: 30rpx;
				line-height: 60rpx;
				margin-left: 295rpx;
			}
			.hour {
				padding: 20rpx 30rpx;
				line-height: 50rpx;
				font-size: 30rpx;
				color: #666;
			}
		}
	}
	
	.img{
		width: 160rpx;
		height: 165rpx;
	}
</style>
