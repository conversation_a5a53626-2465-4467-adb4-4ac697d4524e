<template>
  <view class="container">
    <view class="form">
      <view class="form-item">
        <view class="form-item-title">
          {{ '学员：' }}
        </view>
        <div class="select-box">
          <div class="select-input">{{ studentNameStudy }}</div>
        </div>
      </view>
      <view v-for="(item, index) in fromlist" :key="index">
        <view v-if="index != 0" class="form-item">
          <view class="form-item-title">
            {{ item.label + '：' }}
          </view>
          <div class="select-box" @click="openPicker(index)">
            <div :class="{ 'select-input': true, none: !dataInfo[item.prop] }">{{ dataInfoDetail[item.prop].label || `请选择${item.label}` }}</div>
            <div class="select-arrow">
              <u-icon name="arrow-down" color="#999" size="32"></u-icon>
            </div>
          </div>
        </view>
      </view>
    </view>
    <view class="mask-footer">
      <button class="cancel-button" @click="clearData(-1)">重置</button>
      <button class="confirm-button" @click="confirmData">确定</button>
    </view>
    <!-- 选择器 -->
    <uni-popup ref="popopChoose" type="center">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="this.$refs.popopChoose.close()">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold pb-10">选择{{ fromlist[currentIndex].label }}</view>
            <view class="reviewContent">
              <!-- 学员列表 -->
              <!-- <view
                v-if="currentIndex === 0"
                class="dialogContent"
                @click="selectItem = item"
                v-for="(item, index) in studentList"
                key="item.value"
                :class="selectItem.value == item.value ? 'addclass' : 'not-selected'"
              >
                {{ item.label }}
              </view> -->
              <!-- 学段列表 -->
              <view
                v-if="currentIndex === 1"
                class="dialogContent"
                @click="selectItem = item"
                v-for="(item, index) in gradeLevelList"
                key="item.value"
                :class="selectItem.value == item.value ? 'addclass' : 'not-selected'"
              >
                {{ item.label }}
              </view>
              <!-- 课程大类列表 -->
              <view
                v-if="currentIndex === 2"
                class="dialogContent"
                @click="selectItem = item"
                v-for="(item, index) in curriculumIdList"
                key="item.value"
                :class="selectItem.value == item.value ? 'addclass' : 'not-selected'"
              >
                {{ item.label }}
              </view>
              <!-- 课程名称列表 -->
              <view
                v-if="currentIndex === 3"
                class="dialogContent"
                @click="selectItem = item"
                v-for="(item, index) in courseIdList"
                key="item.value"
                :class="selectItem.value == item.value ? 'addclass' : 'not-selected'"
              >
                {{ item.label }}
              </view>
              <!-- 资料类型列表 -->
              <view
                v-if="currentIndex === 4"
                class="dialogContent"
                @click="selectItem = item"
                v-for="(item, index) in dataTypeList"
                key="item.value"
                :class="selectItem.value == item.value ? 'addclass' : 'not-selected'"
              >
                {{ item.label }}
              </view>
            </view>
            <view class="mask-footer2">
              <button class="confirm-button" @click="handlePickerConfirm(selectItem)">确定</button>
              <button class="cancel-button" @click="this.$refs.popopChoose.close()">取消</button>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        fromlist: [
          {
            label: '学员',
            prop: 'studentCode'
          },
          {
            label: '学段',
            prop: 'gradeLevel'
          },
          {
            label: '课程类型',
            prop: 'curriculumId'
          },
          {
            label: '课程名称',
            prop: 'courseId'
          },
          {
            label: '资料类型',
            prop: 'dataType'
          }
        ],
        show: {
          studentCode: false,
          gradeLevel: false,
          curriculumId: false,
          courseId: false,
          dataType: false
        },
        selectItem: {}, // 存放弹窗框当前选择项
        dataInfo: {
          studentCode: '',
          gradeLevel: '',
          curriculumId: '',
          courseId: '',
          dataType: ''
        }, // 存放选中项提交数据
        dataInfoDetail: {
          studentCode: {},
          gradeLevel: {},
          curriculumId: {},
          courseId: {},
          dataType: {}
        }, // 存放选中项的详细数据
        currentIndex: 1, // 当前选择项索引
        studentList: [], // 学员列表
        studentNameStudy: '',
        gradeLevelList: [], // 学段列表
        curriculumIdList: [], // 课程类型列表
        courseIdList: [], // 课程名称列表
        dataTypeList: [
          { label: '学习视频', value: '1' },
          { label: '学习资料', value: '2' }
        ], // 学段列表
        platform: '' // 系统信息
      };
    },
    onLoad(e) {
      console.log('🚀 ~ onload ~ e:', e);
      this.memberId = e.memberId;
      this.studentNameStudy = e.studentName;
      this.dataInfo.studentCode = e.studentCode;
      this.dataInfoDetail.studentCode = e.studentCode;

      const { platform } = uni.getSystemInfoSync();
      this.platform = platform; // 系统信息
    },
    mounted() {
      // this.getStudentList();
      this.getGradeList();
    },
    onUnload() {
      // #ifdef APP-PLUS
      // if (this.app) {
      plus.runtime.quit();
      // }
      // #endif
    },
    methods: {
      openPicker(index) {
        if (index > 0 && !this.dataInfo[this.fromlist[index - 1].prop]) {
          uni.showToast({
            title: `请先选择${this.fromlist[index - 1].label}`,
            icon: 'none',
            duration: 1500
          });
          return;
        }
        this.currentIndex = index;
        this.selectItem = this.dataInfoDetail[this.fromlist[index].prop];
        // this.show[this.fromlist[index].prop] = true;
        this.$refs.popopChoose.open();
      },
      // 选择器确定按钮
      handlePickerConfirm(item) {
        // console.log('🚀 ~ handlePickerConfirm ~ value:', value);
        let index = this.currentIndex;
        let data = item.childrenList || []; // 下级数据
        // console.log('🚀 ~ handlePickerConfirm ~ data:', data);

        this.dataInfo[this.fromlist[index].prop] = item.value; // 存放选中项数据
        this.dataInfoDetail[this.fromlist[index].prop] = item; // 存放选中项名称

        this.clearData(index); // 清除下级数据
        this.requestData(index, data); // 请求数据
        // this.show[this.fromlist[index].prop] = false; // 关闭面板
        this.$refs.popopChoose.close(); // 关闭面板
      },
      // 清除下级数据
      clearData(index) {
        for (let i = index + 1; i < this.fromlist.length; i++) {
          this.dataInfo[this.fromlist[i].prop] = '';
          this.dataInfoDetail[this.fromlist[i].prop] = {};
        }
      },
      // 请求数据
      async requestData(index, data = []) {
        // console.log('🚀 ~ requestData ~ index:', index);
        switch (index) {
          case 0:
            this.getGradeList();
            break;
          case 1:
            this.getCurriculumIdList(data);
            break;
          case 2:
            this.getCourseIdList(data);
            break;
          case 4:
            this.getDataTypeList(data);
            break;
          default:
            break;
        }
      },
      // 获取学员列表
      getStudentList() {
        let memberId = uni.getStorageSync('user_code') || this.memberId;
        if (!memberId) {
          uni.showToast({
            title: '请先登录',
            icon: 'none'
          });
          return;
        }
        this.$httpUser
          .get(`znyy/course/queryStudentList/1/10`, {
            memberId
          })
          .then((res) => {
            if (res.data.data.data) {
              this.studentList = res.data.data.data?.map((item) => {
                return { label: item.realName, value: item.studentCode };
              });
            }
          });
      },
      // 获取已有的学段列表
      async getGradeList() {
        //加载
        uni.showLoading({ title: '加载中', mask: true });
        let res = await this.$httpUser.get(`dyf/web/xktCourse/options`, this.dataInfo);
        uni.hideLoading();

        let data = res.data?.data || null;
        if (data) {
          this.gradeLevelList = data?.map((item) => {
            return { label: item.gradeName, value: item.gradeLevel, childrenList: item.childrenList };
          });
        }
      },
      // 获取课程类型列表
      getCurriculumIdList(data) {
        this.curriculumIdList = data?.map((item) => {
          return { label: item.name, value: item.curriculumId, childrenList: item.childrenList };
        });
      },
      // 获取课程名称列表
      getCourseIdList(data) {
        this.courseIdList = data?.map((item) => {
          return { label: item.name, value: item.id };
        });
      },
      // 获取资料类型列表
      getDataTypeList(data) {
        console.log('🚀 ~ getGradeList ~ this.dataInfo:', uni.$u.queryParams(this.dataInfo), this.dataInfo);
      },
      // 确定按钮
      async confirmData() {
        if (!this.dataInfo[this.fromlist[this.fromlist.length - 1].prop]) {
          uni.showToast({
            title: '请检查输入项',
            icon: 'none',
            duration: 1500
          });
          return;
        }
        if (this.dataInfo.dataType == '2') {
          // 跳转学习资料
          uni.navigateTo({
            url: '/Coursedetails/xktStudy/studyData'
          });
        } else if (this.dataInfo.dataType == '1') {
          uni.showLoading({ title: '加载中', mask: true });
          let { data } = await this.$httpUser.get(`zx/wap/course/study/video/list`, this.dataInfo);
          uni.hideLoading();
          console.log('🚀 ~ confirmData ~ data:', data);

          if (!data.data || (data.data && data.data.length <= 0)) {
            uni.showToast({
              title: '暂无学习视频',
              icon: 'none'
            });
            return;
          }

          if (data.data[0].isExpired) {
            // 跳转结束页
            uni.navigateTo({
              url: '/Coursedetails/xktStudy/trailerPage'
            });
          } else {
            // 跳转学习视频
            let sendData = encodeURIComponent(JSON.stringify(data.data)); // 编码
            if (this.platform == 'ios') {
              uni.navigateTo({
                url: '/Coursedetails/xktStudy/studyVideoIos?sendData=' + sendData + '&dataInfo=' + JSON.stringify(this.dataInfo)
              });
            } else {
              uni.navigateTo({
                url: '/Coursedetails/xktStudy/studyVideo?sendData=' + sendData + '&dataInfo=' + JSON.stringify(this.dataInfo)
              });
            }
          }
        }
      }
    }
  };
</script>

<style>
  page {
    height: 100vh;
  }
</style>
<style lang="scss" scoped>
  .container {
    position: relative;
    background-color: #fff;
    margin: 20rpx;
    border-radius: 10rpx;
    height: calc(100% - 80rpx);
  }

  .form {
    .form-item {
      display: flex;
      align-items: center;
      padding: 20rpx;
    }
    .form-item-title {
      font-size: 32rpx;
      font-weight: 600;
      width: 170rpx !important;
      flex-shrink: 0;
    }
  }

  .select-box {
    position: relative;
    width: 100%;
    min-height: 80rpx;
    border-radius: 40rpx;
    border: 2rpx solid #eee;
    padding: 0 30rpx;
    box-sizing: border-box;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

    .select-input {
      font-size: 30rpx;
      color: #333;
      line-height: 70rpx;
      padding-right: 32rpx;
      &.none {
        color: #ccc;
      }
    }
    .select-arrow {
      position: absolute;
      top: 50%;
      right: 30rpx;
      transform: translateY(-50%);
      font-size: 30rpx;
    }
  }
  .mask-footer {
    position: absolute;
    width: 100%;
    left: -50%;
    transform: translateX(50%);
    bottom: 20rpx;
    display: flex;
    justify-content: space-around;
  }
  .mask-footer2 button,
  .mask-footer button {
    width: 250rpx;
    height: 80rpx;
    font-size: 30rpx;
    border-radius: 45rpx;
    line-height: 80rpx;
  }
  // 确定按钮
  .confirm-button {
    display: flex;
    justify-content: space-around;
    align-items: center;
    /* 文本垂直居中对齐 */
    background: #2e896f;
    color: #ffffff !important;
  }
  // 取消按钮
  .cancel-button {
    display: flex;
    justify-content: space-around;
    align-items: center;
    /* 文本垂直居中对齐 */
    color: #2e896f !important;
    border: 1rpx solid #2e896f !important;
    transform: rotateZ(360deg);
  }
  /* 弹窗样式 */
  .addclass {
    width: 100%;
    min-height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #2e896f;
    border: 1px solid #2e896f;
    border-radius: 35rpx;
  }

  .not-selected {
    width: 100%;
    min-height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #000;
    border: 1px solid #c8c8c8;
    border-radius: 35rpx;
  }

  .scroll-Y {
    height: 440rpx;
  }

  .mask-footer2 {
    margin-top: 20px;
    display: flex;
    justify-content: space-around;
  }
  .dialogBG {
    width: 100%;
    /* height: 100%; */
  }
  /* 21天结束复习弹窗样式 */
  .reviewCard_box {
    width: 670rpx;
    /* height: 560rpx; */
    position: relative;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }

  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }
  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 145rpx;
    z-index: -1;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }
  .reviewContent {
    width: 100%;
    max-height: 400rpx;
    overflow-y: scroll;
  }

  .dialogContent {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 60upx;
    text-align: center;
    margin-top: 40rpx;
  }
</style>
