/*
 * @Date         : 2020-03-20 14:51:18
 * @LastEditors  : zhangyu
 * @LastEditTime : 2020-08-12 15:19:56
 * @Description  :
 */
/**
 * 工具类
 */
module.exports = {
  /**
   * 数组根据指定属性去重 property
   */

  uniqueObjectsByPropertyMap(arr, property) {
    const map = new Map();
    arr.forEach((item) => {
      const key = JSON.stringify(item[property]);
      map.set(key, item);
    });
    return Array.from(map.values());
  },

  /**
   * scene解码
   */
  scene_decode: (e) => {
    if (e === undefined) return {};
    let scene = decodeURIComponent(e),
      params = scene.split(','),
      data = {};
    for (let i in params) {
      var val = params[i].split(':');
      val.length > 0 && val[0] && (data[val[0]] = val[1] || null);
    }
    return data;
  },

  /**
   * 格式化日期格式 (用于兼容ios Date对象)
   */
  format_date: (time) => {
    // 将xxxx-xx-xx的时间格式，转换为 xxxx/xx/xx的格式
    return time.replace(/\-/g, '/');
  },

  format_date_c: (time) => {
    // 将xxxx-xx-xx的时间格式，转换为 xxxx/xx/xx的格式
    return time.replace(/\//g, '-');
  },
  /**
   * 对象转URL
   */
  urlEncode: (data) => {
    var _result = [];
    for (var key in data) {
      var value = data[key];
      if (value.constructor == Array) {
        value.forEach((_value) => {
          _result.push(key + '=' + _value);
        });
      } else {
        _result.push(key + '=' + value);
      }
    }
    return _result.join('&');
  },

  // 追加url参数
  appendQuery: (url, key, value) => {
    var options = key;
    if (typeof options == 'string') {
      options = {};
      options[key] = value;
    }
    options = $.param(options);
    if (url.includes('?')) {
      url += '&' + options;
    } else {
      url += '?' + options;
    }
    return url;
  },

  /**
   * 遍历对象
   */
  objForEach: (obj, callback) => {
    Object.keys(obj).forEach((key) => {
      callback(obj[key], key);
    });
  },

  /**
   * 是否在数组内
   */
  inArray: (search, array) => {
    for (var i in array) {
      if (array[i] == search) {
        return true;
      }
    }
    return false;
  },

  /**
   * 判断是否为正整数
   */
  isPositiveInteger: (value) => {
    return /(^[0-9]\d*$)/.test(value);
  },

  // 匹配integer
  isInteger: (str) => {
    if (str == null || str == '') return false;
    var result = str.match(/^[-\+]?\d+$/);
    if (result == null) return false;
    return true;
  },

  // 匹配double或float
  isDouble: (str) => {
    if (str == null || str == '') return false;
    var result = str.match(/^[-\+]?\d+(\.\d+)?$/);
    if (result == null) return false;
    return true;
  },

  // 切割数组
  sliceArray: (arrs, options) => {
    let result = [];
    let { size } = options;
    let index = 0;
    let len = arrs.length;
    let group = Math.ceil(len / size);
    for (let i = 0; i < group; i++) {
      result.push(arrs.splice(0, size));
    }
    return result;
  },
  // 手机号码
  isMobile: (s) => {
    return /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/.test(s);
  },
  isName: (s) => {
    var reg = /^[\u4E00-\u9FA5\uf900-\ufa2d·s]{2,6}$/;
    return s.match(reg);
  },
  // 严格的身份证校验
  isCardID: (sId) => {
    if (!/(^\d{15}$)|(^\d{17}(\d|X|x)$)/.test(sId)) {
      uni.showToast({
        icon: 'none',
        title: '你输入的身份证长度或格式错误'
      });
      return false;
    }
    //身份证城市
    var aCity = {
      11: '北京',
      12: '天津',
      13: '河北',
      14: '山西',
      15: '内蒙古',
      21: '辽宁',
      22: '吉林',
      23: '黑龙江',
      31: '上海',
      32: '江苏',
      33: '浙江',
      34: '安徽',
      35: '福建',
      36: '江西',
      37: '山东',
      41: '河南',
      42: '湖北',
      43: '湖南',
      44: '广东',
      45: '广西',
      46: '海南',
      50: '重庆',
      51: '四川',
      52: '贵州',
      53: '云南',
      54: '西藏',
      61: '陕西',
      62: '甘肃',
      63: '青海',
      64: '宁夏',
      65: '新疆',
      71: '台湾',
      81: '香港',
      82: '澳门',
      91: '国外'
    };
    if (!aCity[parseInt(sId.substr(0, 2))]) {
      uni.showToast({
        icon: 'none',
        title: '你的身份证地区非法'
      });
      return false;
    }

    // 出生日期验证
    var sBirthday = (sId.substr(6, 4) + '-' + Number(sId.substr(10, 2)) + '-' + Number(sId.substr(12, 2))).replace(/-/g, '/'),
      d = new Date(sBirthday);
    if (sBirthday != d.getFullYear() + '/' + (d.getMonth() + 1) + '/' + d.getDate()) {
      uni.showToast({
        icon: 'none',
        title: '身份证上的出生日期非法'
      });
      return false;
    }

    // 身份证号码校验
    var sum = 0,
      weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2],
      codes = '10X98765432';
    for (var i = 0; i < sId.length - 1; i++) {
      sum += sId[i] * weights[i];
    }
    var last = codes[sum % 11]; //计算出来的最后一位身份证号码
    if (sId[sId.length - 1] != last) {
      uni.showToast({
        icon: 'none',
        title: '你输入的身份证号非法'
      });
      return false;
    }

    return true;
  },

  checkCard(cardNo) {
    if (isNaN(cardNo)) return false;
    if (cardNo.length < 12) {
      return false;
    }
    var nums = cardNo.split('');
    var sum = 0;
    var index = 1;
    for (var i = 0; i < nums.length; i++) {
      if ((i + 1) % 2 == 0) {
        var tmp = Number(nums[nums.length - index]) * 2;
        if (tmp >= 10) {
          var t = tmp + ''.split('');
          tmp = Number(t[0]) + Number(t[1]);
        }
        sum += tmp;
      } else {
        sum += Number(nums[nums.length - index]);
      }
      index++;
    }
    if (sum % 10 != 0) {
      return false;
    }
    return true;
  },

  async GetDateDiff(startTime, endTime, diffType) {
    //将xxxx-xx-xx的时间格式，转换为 xxxx/xx/xx的格式
    startTime = startTime.replace(/\-/g, '/');
    endTime = endTime.replace(/\-/g, '/');
    //将计算间隔类性字符转换为小写
    diffType = diffType.toLowerCase();
    var sTime = new Date(startTime); //开始时间
    var eTime = new Date(endTime); //结束时间
    //作为除数的数字
    var timeType = 1;
    switch (diffType) {
      case 'second':
        timeType = 1000;
        break;
      case 'minute':
        timeType = 1000 * 60;
        break;
      case 'hour':
        timeType = 1000 * 3600;
        break;
      case 'day':
        timeType = 1000 * 3600 * 24;
        break;
      default:
        break;
    }
    return parseInt((eTime.getTime() - sTime.getTime()) / parseInt(timeType));
  },

  // picker 开始时间和结束时间   年月日单位，只展示年月
  getDate(type) {
    const date = new Date();
    let year = date.getFullYear();
    let month = date.getMonth() + 1;
    let day = date.getDate();

    if (type === 'start') {
      year = year - 60;
    } else if (type === 'end') {
      year = year + 2;
    }
    month = month > 9 ? month : '0' + month;
    day = day > 9 ? day : '0' + day;
    return `${year}年${month}月`;
  },

  // 日期  -转年月日
  getDateChinese(dt) {
    const date = new Date(dt);
    let year = date.getFullYear();
    let month = date.getMonth() + 1;
    month = month > 9 ? month : '0' + month;
    return `${year}年${month}月`;
  },

  // 分转元
  Fen2Yuan(num) {
    num = Number(num);
    if (typeof num !== 'number' || isNaN(num)) return null;
    console.log((num / 100).toFixed(2));
    return (num / 100).toFixed(2);
  },

  rpx2px(rpx) {
    return (rpx / 750) * wx.getSystemInfoSync().windowWidth;
  },

  // 相对当前时间处理
  timeago(datetime) {
    //dateTimeStamp是一个时间毫秒，注意时间戳是秒的形式，在这个毫秒的基础上除以1000，就是十位数的时间戳。13位数的都是时间毫秒。
    var dateTimeStamp = Date.parse(datetime.replace(/-/gi, '/'));
    var minute = 1000 * 60; //把分，时，天，周，半个月，一个月用毫秒表示
    var hour = minute * 60;
    var day = hour * 24;
    var week = day * 7;
    var halfamonth = day * 15;
    var month = day * 30;
    var now = new Date().getTime(); //获取当前时间毫秒
    var diffValue = now - dateTimeStamp; //时间差

    if (diffValue < 0) {
      // console.log("diffValue<0",datetime,dateTimeStamp,now,diffValue);
      return '刚刚';
    }
    var minC = diffValue / minute; //计算时间差的分，时，天，周，月
    var hourC = diffValue / hour;
    var dayC = diffValue / day;
    var weekC = diffValue / week;
    var monthC = diffValue / month;
    var result = '2';
    if (monthC >= 1 && monthC <= 3) {
      result = ' ' + parseInt(monthC) + '月前';
    } else if (weekC >= 1 && weekC <= 3) {
      result = ' ' + parseInt(weekC) + '周前';
    } else if (dayC >= 1 && dayC <= 6) {
      result = ' ' + parseInt(dayC) + '天前';
    } else if (hourC >= 1 && hourC <= 23) {
      result = ' ' + parseInt(hourC) + '小时前';
    } else if (minC >= 1 && minC <= 59) {
      result = ' ' + parseInt(minC) + '分钟前';
    } else if (diffValue >= 0 && diffValue <= minute) {
      result = '刚刚';
    } else {
      var datetimes = new Date();
      datetimes.setTime(dateTimeStamp);
      var Nyear = datetimes.getFullYear();
      {
      }
      var Nmonth = datetimes.getMonth() + 1 < 10 ? '0' + (datetimes.getMonth() + 1) : datetimes.getMonth() + 1;
      var Ndate = datetimes.getDate() < 10 ? '0' + datetimes.getDate() : datetimes.getDate();
      var Nhour = datetimes.getHours() < 10 ? '0' + datetimes.getHours() : datetimes.getHours();
      var Nminute = datetimes.getMinutes() < 10 ? '0' + datetimes.getMinutes() : datetimes.getMinutes();
      var Nsecond = datetimes.getSeconds() < 10 ? '0' + datetimes.getSeconds() : datetimes.getSeconds();
      result = Nyear + '-' + Nmonth + '-' + Ndate;
    }
    if (hourC >= 24) {
      // console.log(datetime)
      return datetime.slice(0, 16);
    }
    return result;
  },
  // 防抖函数
  noMultipleClicks(methods, ...args) {
    let that = this;
    if (that.noClick) {
      that.noClick = false;
      if (args && args.length !== 0) {
        methods(...args);
      } else {
        methods();
      }
      setTimeout(() => {
        that.noClick = true;
      }, 1000);
    } else {
      uni.showToast({
        icon: 'none',
        title: '不需要频繁点击'
      });
    }
  },
  getCachedPic(url, storageName) {
    let storagePath = wx.getStorageSync(storageName);
    if (storagePath) {
      // console.log("已下载，使用本地图片");
      return storagePath;
    } else {
      // console.log("需下载，使用网络图片");
      this.downloadPicFile(url, storageName);
      return url;
    }
  },
  downloadPicFile(url, storageName) {
    wx.downloadFile({
      //下载图片资源
      url: url,
      success: (res) => {
        if (res.statusCode === 200) {
          const fs = wx.getFileSystemManager();
          fs.saveFile({
            //临时文件保存到本地
            tempFilePath: res.tempFilePath,
            success: (res) => {
              wx.setStorageSync(storageName, res.savedFilePath);
            }
          });
        }
      }
    });
  },
  isPhoneNumber(data) {
    const regExp = /^[1]([3-9])[0-9]{9}$/;
    return regExp.test(data);
  },
  /**
   * 时间戳转 yyyy-MM-dd
   * @param {number} timestamp - 时间戳（10位或13位）
   * @returns {string} - 格式化后的日期字符串
   */
  formatTimestamp(timestamp) {
    // 处理10位时间戳（秒级）
    if (timestamp.toString().length === 10) {
      timestamp *= 1000;
    }

    const date = new Date(timestamp);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }
};
