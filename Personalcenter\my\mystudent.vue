<template>
	<view class="container plr-30">
		<view class="mystudent">
			<view @click="goCourse(item)" class="mystudent-list clearfix mt-30" v-for="(item, index) in mystudentslist"
				:key="index">
				<view class="f-28 pb-30 border-b flex-s">
					<view class="c-66">创建时间：{{item.regTime.split(' ')[0]}}</view>
					<view class="restore f-30" v-if="item.archived" @click.stop="restoreData(item.studentCode)">恢复学生归档数据
					</view>
				</view>
				<view class="mt-30 flex-a-c">
					<image src="https://document.dxznjy.com/applet/student02.png"></image>
					<view class="ml-20 f-30">
						<view class="studentList_itemTop">
							{{ item.realName }}<text class="c-66">{{'（'+ item.studentCode+'）' }}</text>
						</view>
						<view class="studentList_itemTop mt-15 c-66 f-28">
							{{ item.school }}
						</view>
					</view>
				</view>
				<view class="mystudent_list_itemStatus" v-if="item.usermodule==1">已开通同步课堂</view>
			</view>
		</view>


		<view class="bg-ff radius-20 no_data" v-if="ifshowmore == 1&&mystudentslist.length==0"
			:style="{'height': screenHeight+'rpx'}">
			<view class="t-c flex-col f-30" style="margin-top: 400upx;">
				<image :src="imgHost+'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
				<view style="color: #BDBDBD;">暂无数据，新增学员</view>
			</view>
		</view>

		<view class="determine" v-if="ifshowmore == 1" @click="navToaddmystudent">新增学员</view>

	</view>
</template>

<script>
	import util from '@/util/util.js'
	export default {
		data() {
			return {
				ifshowmore: 1,
				ifshowzanwu: 1,
				mystudentslist: [],
				memberId: '', //当前用户membercode
				screenHeight: 0, //屏幕高度
				imgHost: getApp().globalData.imgsomeHost,
			};
		},
		onLoad(option) {
			var that = this;
			this.memberId = option.memberId;
			console.log(this.memberId);

			// uni.$on('LoginSuccess', function(data) {
			// 	that.loadData();
			// });
			// uni.$on('LoginOut', function(data) {
			// 	that.loginOut();
			// });
			//选择学员
			// uni.$on('addstudent', function(data) {
			// 	that.loadData();
			// });
			// this.loadData();

		},

		onReady() {
			uni.getSystemInfo({
				success: (res) => {
					// 可使用窗口高度，将px转换rpx
					this.screenHeight = (res.windowHeight * (750 / res.windowWidth) - 30)
					console.log(this.phoneHeight)
				}
			})
		},

		onShow() {
			this.loadData();
		},
		onUnload() {
			// uni.$off('addstudent');
		},
		methods: {
			goCourse(item) {
				// uni.navigateTo({
				// 	url: '/Personalcenter/index/index?studentCode=' + item.studentCode
				// })
			},
			dateFormat: function(time) {
				return util.dateFormat(time);
			},
			async loadData() {
				this.mystudentslist = [];
				const that = this;
				// that.$httpUser.get(`v2/mall/Oscc/queryStudentList/1/10`, {
				that.$httpUser.get(`znyy/course/queryStudentList/1/10`, {
					memberId: that.memberId
				}).then((res) => {
					if (res.data.success) {
						let result = res.data.data;
						if (result.data != null) {
							if (result.data.length == 0) {
								that.mystudentslist = [];
							} else {
								if (result.data.length > 0 && result.data.length > 0) {
									that.mystudentslist = that.mystudentslist.concat(result.data);
									if (result.data.length > 2) that.ifshowmore = 0;
									that.ifshowzanwu = 0;
								}
							}
						}
					} else {
						uni.showToast({
							title: res.data.message,
							icon: 'none'
						});
					}
				})
			},

			navToaddmystudent() {
				uni.navigateTo({
					url: '/Personalcenter/my/mystudentAdd?memberId=' + this.memberId
				});
			},

			async restoreData(studentCode) {
				const that = this;
				await that.$httpUser.put('znyy/archive/unarchive?studentCode=' + studentCode).then((res) => {
					if (res.data.success) {
						uni.showToast({
							title: '操作成功',
							icon: 'none'
						});
						that.loadData();
					} else {
						uni.showToast({
							title: res.data.message,
							icon: 'none'
						});
					}
				})
			},
		},

	};
</script>

<style>
	.mystudent {
		border-radius: 10rpx;
		font-size: 30rpx;
	}

	.mystudent-list {
		padding: 30rpx;
		background: #FFFFFF;
		flex-wrap: wrap;
		box-sizing: border-box;
		border-radius: 14upx;
		position: relative;
	}

	.mystudent_list_itemStatus {
		position: absolute;
		width: 200upx;
		height: 36upx;
		background: #E57126;
		border-radius: 18upx 0upx 0upx 18upx;
		border: 1px solid #E57126;
		top: 34upx;
		right: 0;
		color: #FFFFFF;
		font-size: 26upx;
		text-align: center;
		line-height: 36upx;
	}

	.isopened {
		width: 100%;
		font-size: 24rpx;
		color: #ffac38;
		text-align: right;
		margin-top: 20rpx;
	}

	.mystudent-list image {
		width: 100rpx;
		height: 100rpx;
	}

	.right {
		flex-grow: 1;
	}

	.right01 {
		width: 40%;
	}

	.right01 view {
		font-size: 28rpx;
		color: #999;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		margin-top: 10rpx;
	}

	.right02 view {
		font-size: 26rpx;
		color: #999;
		margin-top: 10rpx;
	}

	.right02 {
		width: 40%;
		text-align: right;
	}

	.determine {
		width: 586upx;
		height: 80upx;
		background: linear-gradient(to bottom, #88CFBA, #1D755C);
		border-radius: 45upx;
		position: absolute;
		bottom: 60upx;
		color: #FFFFFF;
		line-height: 80upx;
		font-size: 30upx;
		left: 80upx;
		text-align: center;
	}

	.zanwu {
		display: block;
		width: 224rpx;
		height: 190rpx;
		margin: 0 auto;
		margin-top: 100rpx;
		margin-bottom: 100rpx;
	}

	.lost {
		display: block;
		width: 220rpx;
		height: 220rpx;
		margin: 0 auto;
		margin-top: 100rpx;
	}

	.title-lost {
		font-size: 26rpx;
		color: #999;
		text-align: center;
		display: block;
	}

	.renew {
		width: 160rpx;
		height: 60rpx;
		line-height: 60rpx;
		font-size: 26rpx;
		margin: 0 auto;
		margin-top: 20rpx;
		color: #999;
		text-align: center;
		border: 1px solid #999;
		border-radius: 10rpx;
	}

	.no_data {
		position: relative;
	}

	.img_s {
		width: 160upx;
		height: 170upx;
	}

	.border-b {
		border-bottom: 1px solid #EEEEEE;
	}

	.restore {
		color: #2E896F;
	}
</style>