<template>
  <!-- 拼团大厅弹窗 -->
  <uni-popup ref="groupHallPopup" background-color="transparent" @change="groupHallChange">
    <view class="hall-content">
      <scroll-view v-if="groupData.length > 0" class="line_list" scroll-y="true" @scrolltolower="scrollToBottom">
        <view class="line" v-for="item in groupData" :key="item.groupInstancesId">
          <view class="avatars">
            <image class="avatar" v-if="item.nickName1" :src="item.headPortrait1 || avaUrl" mode="aspectFit"></image>
            <image class="avatar" v-if="item.nickName2" :src="item.headPortrait2 || avaUrl" mode="aspectFit"></image>
            <image class="avatar" v-if="item.nickName3" :src="item.headPortrait3 || avaUrl" mode="aspectFit"></image>
          </view>
          <view class="nicknames">{{ item.nickName1 }}{{ item.nickName2 ? `、${item.nickName2}` : '' }}{{ item.nickName3 ? `、${item.nickName3}` : '' }}</view>
          <view class="btn" @click="joinGroupAhead(item)">立即参团</view>
        </view>
        <view class="load_more">
          <view class="left-line"></view>
          <view class="center-line" v-if="groupData.length < 9"></view>
          <view class="toast-text" v-else>{{ loadMoreText }}</view>
          <view class="right-line"></view>
        </view>
      </scroll-view>
      <!-- 缺省页 -->
      <view v-else class="line_list t-c flex-col">
        <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 wh100" mode="widthFix"></image>
        <view style="color: #bdbdbd">暂无数据</view>
      </view>
      <view class="review_close" @click="closePopup"></view>
    </view>
  </uni-popup>
</template>

<script>
  const { $http } = require('@/util/methods.js');

  export default {
    name: 'groupHallPopup',
    props: {
      groupActivityId: {
        type: String,
        default: ''
      },
      goodsId: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        imgHost: '',
        loadMoreText: '到底了', // 加载中，到底了
        page_data: {
          pageNum: 1,
          pageSize: 20,
          totalPage: 1
        },
        groupData: [],
        avaUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
        currentActivityId: '', // 拼团活动id
        currentGoodsId: '' // 商品id
      };
    },
    destroyed() {
      this.page_data.pageNum = 1;
      this.page_data.totalPage = 1;
      this.groupData = [];
    },
    methods: {
      async getGroupList(isOpen) {
        let _this = this;
        // 打开弹窗时，清除列表数据
        if (isOpen) {
          _this.groupData = [];
        }
        const res = await $http({
          url: 'zx/wap/group/canJoinPage',
          data: {
            pageNum: _this.page_data.pageNum,
            pageSize: _this.page_data.pageSize,
            groupActivityId: this.groupActivityId ? this.groupActivityId : this.currentActivityId,
            goodsId: this.goodsId ? this.goodsId : this.currentGoodsId
          }
        });
        if (res.data) {
          _this.loadMoreText = _this.page_data.pageNum < res.data.totalPage ? '查看更多' : '到底了';
          _this.page_data.totalPage = res.data.totalPage;
          const tempArr = _this.groupData.concat(res.data.data);
          _this.groupData = tempArr.filter((item, index, self) => index === self.findIndex((t) => t.groupInstancesId === item.groupInstancesId));
        }
      },
      async openPopup(groupActivityId, goodsId) {
        this.groupData = [];
        if (groupActivityId) {
          this.currentActivityId = groupActivityId;
        }
        if (goodsId) {
          this.currentGoodsId = goodsId;
        }
        await this.getGroupList(true);
        this.imgHost = getApp().globalData.imgsomeHost;
        this.$refs.groupHallPopup.open();
      },
      groupHallChange(e) {
        if (!e.show) {
          this.groupData = [];
        }
        this.$emit('preventScrollPenetrate', e);
      },
      closePopup() {
        this.$refs.groupHallPopup.close();
      },
      scrollToBottom() {
        if (this.page_data.pageNum < this.page_data.totalPage) {
          this.page_data.pageNum++;
          this.loadMoreText = '加载中';
          this.getGroupList();
        }
      },
      joinGroupAhead(value) {
        this.closePopup();
        this.$emit('joinGroup', value);
      }
    }
  };
</script>

<style scoped lang="scss">
  .hall-content {
    width: 658rpx;
    height: 1016rpx;
    padding-top: 170rpx;
    padding-bottom: 40rpx;
    padding-left: 52rpx;
    padding-right: 56rpx;
    box-sizing: border-box;
    background-image: url('https://document.dxznjy.com/course/c96e95027c1e4a19a1f13280a3ee5712.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    position: relative;
  }
  .line_list {
    width: 100%;
    height: 100%;
    overflow: scroll;
    .load_more {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .left-line,
    .right-line {
      width: 45%;
      height: 2rpx;
      background-color: #dcdfe6;
    }
    .center-line {
      flex: 1;
      height: 2rpx;
      background-color: #dcdfe6;
    }
    .toast-text {
      width: 10%;
      text-align: center;
      color: #909399;
      font-size: 14rpx;
    }
  }
  .review_close {
    position: absolute;
    top: -35rpx;
    right: 0;
    z-index: 1;
    width: 40rpx;
    height: 40rpx;
    background: url('https://document.dxznjy.com/dxSelect/9a407f93-744a-4173-8e69-1b4ba2963064.png');
    background-size: 100% 100%;
  }
  .line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 112rpx;
    border-top: 2rpx solid #d2decf;
    .avatars {
      position: relative;
      width: 96rpx;
      height: 48rpx;
      .avatar {
        position: absolute;
        width: 48rpx;
        height: 48rpx;
        border-radius: 24rpx;
      }
      .avatar:first-child {
        top: 0;
        left: 0;
        z-index: 3;
      }
      .avatar:nth-child(2) {
        top: 0;
        left: 24rpx;
        z-index: 2;
      }
      .avatar:nth-child(3) {
        top: 0;
        left: 48rpx;
        z-index: 1;
      }
    }
    .nicknames {
      width: 174rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 28rpx;
      font-family: AlibabaPuHuiTi_3_55_Regular;
      color: #555;
      text-align: left;
    }
    .btn {
      padding: 0 29rpx;
      box-sizing: border-box;
      height: 48rpx;
      line-height: 48rpx;
      border-radius: 24rpx;
      background-color: #339378;
      color: #ffffff;
      font-size: 24rpx;
      font-family: AlibabaPuHuiTi_3_55_Regular;
    }
  }
  .line:first-of-type {
    border-top: none;
  }
  .line_list image {
    width: 150rpx;
  }
</style>
