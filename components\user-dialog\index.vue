<!-- 后台配置的弹窗功能 -->
<template>
  <div @click="handleOk">
    <uni-popup v-for="(popup, index) in popups" :key="index" ref="userPopup" type="center" :safe-area="false" style="padding: 0">
      <view class="dialog-bg">
        <view class="popup-content">
          <image v-if="popup.popupPicUrl" :src="popup.popupPicUrl" mode="widthFix"></image>
          <view class="close-css" :class="currPagePosition == 3 ? 'close-css-my' : ''" @click.stop="closePop(index)"></view>

          <view class="dialog-btn" :style="{ background: popup.color }" @click.stop="handleOk">确认</view>
          <view class="tips">
            <!-- <label class="radio" style="transform: scale(0.6)">
              <radio value="r1" :checked="isChecked" backgroundColor="transparent" borderColor="#F3F3F3" @click="handleChangeChecked" />
            </label> -->
            <view v-if="isChecked" class="tips-radio tips-select" @click.stop="handleToggle"></view>
            <view v-else class="tips-radio tips-default" @click.stop="handleToggle"></view>
            <text @click.stop="handleToggle">今日不再提醒</text>
          </view>
        </view>
      </view>
    </uni-popup>
  </div>
</template>

<script>
const { $http, $navigationTo } = require('@/util/methods.js');
export default {
  data() {
    return {
      popups: [],
      // 1首页 2甄选 3学习 4我的
      positions: ['pages/index/index', 'pages/selectCourse/selectCourse', 'pages/home/<USER>/index', 'pages/home/<USER>/index'],
      currentPopupIndex: -1, // 当前显示的弹框索引
      isChecked: false,
      popupSettings: {}, // 存储用户选择的弹窗设置
      currPagePosition: 0 // 当前所在页面
    };
  },
  mounted() {
    const currentPages = getCurrentPages();
    const currPagePosition = this.positions.indexOf(currentPages[currentPages.length - 1].route);
    this.currPagePosition = currPagePosition;
    this.getPopupList();
  },
  watch: {
    currentPopupIndex: {
      immediate: true,
      handler(val) {
        // 如果配置了popupInterval则到时自动关闭弹窗
        if (val >= 0) {
          const popup = this.popups[val];
          if (this.popups && this.popups[val] && popup.popupInterval > 0) {
            if (this.popups[val].popupInterval) {
              setTimeout(() => {
                this.closePop(val);
              }, popup.popupInterval * 1000);
            }
          }
        }
      }
    }
  },
  methods: {
    //关闭弹窗
    closePop(index) {
      const popup = this.popups[index];
      const today = new Date().toISOString().split('T')[0];
      const key = `${popup.id}_${this.positions[this.currPagePosition]}`;

      // upType 弹出类型1：首次进入弹出 2：每次进入都会弹出 3：点击关闭后今日不再弹出

      if (this.isChecked) {
        // 点击今日不再弹出-关闭后今日不再弹出
        this.popupSettings[`${key}_today`] = today;
        uni.setStorageSync('popupSettings', JSON.stringify(this.popupSettings));
      }

      if (popup.upType === 1) {
        // 首次进入
        this.popupSettings[`${key}_first`] = true;
        uni.setStorageSync('popupSettings', JSON.stringify(this.popupSettings));
      }

      if (popup.upType === 3) {
        // 点击关闭后不再弹出
        this.popupSettings[key] = true;
        uni.setStorageSync('popupSettings', JSON.stringify(this.popupSettings));
      }

      this.$refs.userPopup[index].close();
      if (index < this.popups.length - 1) {
        this.currentPopupIndex++;
        this.$nextTick(() => {
          getApp().sensors.track('openPopupEv', {
            name: '活动弹框曝光',
            activityId: this.popups[this.currentPopupIndex].id
          });
          this.isChecked = false;
          this.$refs.userPopup[this.currentPopupIndex].open();
        });
      }
    },
    handleOk() {
      const popup = this.popups[this.currentPopupIndex];
      //埋点-弹框确认跳转到活动页
      getApp().sensors.track('popupClickOk', {
        name: '弹框确认按钮点击',
        popupId: popup.id
      });
      this.closePop(this.currentPopupIndex);
      // // 首页弹框埋点
      // if (this.currPagePosition == 0) {
      //   getApp().sensors.track('indexPopupClick', {
      //     name: '首页弹框点击',
      //     activityId: popup.id
      //   });
      // }

      // popupType 1商品 2链接 3页面
      if (popup.goodsId && popup.popupType == 1) {
        $navigationTo(`Coursedetails/productDetils?id=${popup.goodsId}&activityId=${popup.id}`);
      } else {
        $navigationTo(popup.popupLinkUrl);
      }
    },
    handleToggle() {
      this.isChecked = !this.isChecked;
    },
    async getPopupList() {
      this.currentPopupIndex = -1;
      const res = await $http({
        url: 'zx/wap/layout/popup/list/query?loginType=1'
      });
      if (res) {
        if (res.data && res.data.length > 0) {
          const isLogin = uni.getStorageSync('token');

          // needLogin 1需要 0 不需要
          const popups = res.data.filter((i) => (i.needLogin == 1 ? isLogin : true)).filter((item) => item.position.includes(this.currPagePosition + 1));
          this.popups = popups.slice(0, 5);

          // 获取用户的弹窗设置
          this.popupSettings = uni.getStorageSync('popupSettings') ? JSON.parse(uni.getStorageSync('popupSettings')) : {};

          const today = new Date().toISOString().split('T')[0];
          this.popups = this.popups.filter((popup) => {
            const key = `${popup.id}_${this.positions[this.currPagePosition]}`;
            if (this.popupSettings[`${key}_today`] === today) {
              // 今日不再弹出
              return false;
            }
            if (popup.upType === 1) {
              // 首次进入弹出
              return this.popupSettings[`${key}_first`] !== true;
            } else if (popup.upType === 2) {
              // 每次进入都会弹出
              return true;
            } else if (popup.upType === 3) {
              // 点击关闭后不再弹出
              return this.popupSettings[key] !== true;
            }
            return true;
          });

          this.$nextTick(() => {
            if (this.popups.length > 0) {
              this.currentPopupIndex++;
              getApp().sensors.track('openPopupEv', {
                name: '活动弹框曝光',
                activityId: this.popups[this.currentPopupIndex].id
              });
              this.$refs.userPopup[this.currentPopupIndex].open();
            }
          });
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-bg {
  width: 100%;
}
.popup-content {
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow-y: auto;
  background: rgba(0, 0, 0, 0.4);
  & > image {
    display: block;
    width: 90%;
    margin: 0 auto;
    padding: 0;
  }
  .close-css {
    position: absolute;
    top: 200rpx;
    right: 20rpx;
    width: 48rpx;
    height: 48rpx;
    background: url('https://document.dxznjy.com/course/7f5032c7081243deba5dcc8c76209441.png');
    background-size: 100% 100%;
  }

  .close-css-my {
    top: 180rpx;
  }

  .dialog-btn {
    position: absolute;
    left: 50%;
    bottom: 90rpx;
    transform: translate(-50%, 0);
    width: 360rpx;
    height: 80rpx;
    border-radius: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    background: #339378;
  }

  .tips {
    position: absolute;
    left: 50%;
    bottom: 50rpx;
    width: max-content;
    display: flex;
    transform: translateX(-50%);
    align-items: center;
    justify-content: center;
    font-size: 26rpx;
    color: #f3f3f3;
    line-height: 28rpx;

    .tips-radio {
      width: 28rpx;
      height: 28rpx;
      margin-right: 14rpx;
    }

    .tips-default {
      background: url('https://document.dxznjy.com/course/d22cb491901449b681086c5e822c256d.png') no-repeat;
      background-size: 100%;
    }
    .tips-select {
      background: url('https://document.dxznjy.com/course/995b5359c2e84419b09fa759a5acabc1.png') no-repeat;
      background-size: 100%;
    }
  }
}
</style>
