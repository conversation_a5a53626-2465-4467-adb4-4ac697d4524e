<template>
  <view class="container mt-20">
    <!-- 日总切换 -->
    <view v-if="!userInfo.experience" :class="switchstate ? 'switch' : 'day-switch'" class="switch">
      <selectSwitch @change="changeSwitch" checked_bj_color="#2E896F" />
    </view>
    <!-- 学习反馈日总结 -->
    <view class="part c-00" v-if="switchstate">
      <view class="bg-ff radius-15 plr-30 ptb-40 mb-30">
        <view v-if="switchstate" class="mt-25 f-32">选择日期时间</view>
        <!-- <view class="text-content date mt-40">
					<view class="w100 dateStyle flex-s">
						<uni-datetime-picker type="date" :class="single?'blackColor':''" placeholder="请选择日期" @change="change" :border="false" :clear-icon="false" v-model="single" @maskClick="maskClick" />
						<uni-icons type="right" size="16" color="#c0c4cc"></uni-icons>
					</view>
				</view> -->

        <view class="text-content w100 mt-30" @click="open">
          <view class="flex_s select_time">
            <view class="flex_x">
              <view>
                <u-icon name="calendar" size="44" color="#c0c4cc"></u-icon>
              </view>
              <text class="f-28 ml-5" :class="single ? '' : 'timeColor'">{{ single || '请选择日期' }}</text>
            </view>
            <view>
              <uni-icons type="right" size="16" color="#c0c4cc"></uni-icons>
            </view>
          </view>
          <uni-calendar ref="calendar" :insert="false" @confirm="confirmDate" @close="close" />
          <!-- <uni-calendar ref="calendar" :insert="false" :start-date="'2010-1-1'" :end-date="'2050-12-31'" @confirm="confirmDate"/> -->
          <!-- <u-calendar :minDate="1587524800000" :maxDate="1786778555000" :show="dateShow" @confirm="confirmDate" @close="close" color="#ee7e33"></u-calendar> -->
          <!-- <u-datetime-picker itemHeight="68" :show="dateShow" v-model="value" mode="date" @confirm="confirmDate" @cancel="close"></u-datetime-picker> -->
        </view>

        <view class="text-content w100 mt-30" @click="selectTime">
          <view class="flex_s select_time">
            <view class="flex_x">
              <view class="pl-5">
                <u-icon name="clock" size="36" color="#c0c4cc"></u-icon>
              </view>
              <text class="f-28 ml-5" :class="select.time ? '' : 'timeColor'">{{ select.time || '请选择时间' }}</text>
            </view>
            <view>
              <uni-icons type="right" size="16" color="#c0c4cc"></uni-icons>
            </view>
          </view>
          <u-picker itemHeight="68" confirmColor="#2E896F" :show="show" :columns="columns" @cancel="cancel" @confirm="confirm" keyName="time"></u-picker>
        </view>
      </view>

      <!-- 所有反馈 -->
      <view class="uni-margin-wrap" v-if="backdetails == '' && allFeedbackList.length > 0">
        <view v-for="item in allFeedbackList" :key="item.id" class="swiper-item uni-bg-red mb-30 bg-ff radius-15">
          <view class="title_name_css twolist fontWeight">{{ item.curriculumName }}</view>
          <view class="flex_s">
            <view class="title_css">
              <text>
                <text>学员姓名：</text>
                <text>{{ item.studentName }}</text>
              </text>
            </view>
            <view class="title_css">
              <text>
                <text>课程类型：</text>
                <text>{{ item.curriculumName }}</text>
              </text>
            </view>
          </view>
          <view class="title_css">上课时间：{{ item.startStudyTime.slice(0, 11)+ ' ' + item.startStudyTime.slice(11, 20) + '~' + item.endStudyTime.slice(11, 20)}}</view>
          <!-- <view class="title_css">
            <text>
              <text>教练：</text>
              <text>{{ myClassList.nextStudyCourse.teacher || '' }}</text>
            </text>
          </view> -->
          <view class="bottom_css pt-30 pb-15 mt-30">
            <button @click="getFeedbackDetail(item)" class="btn btn_plan btn_left" type="default" plain="true">查看反馈</button>
          </view>
        </view>
      </view>

      <view v-if="!userInfo.experience && backdetails != ''">
        <view class="details bg-ff plr-30 ptb-40 radius-15">
          <view class="f-32 bold">学习详情</view>
          <view class="mt-40" style="padding-top: 6rpx">姓名：{{ backdetails.studentName === 0 ? 0 : backdetails.studentName || '' }}</view>
          <view class="mt-40">年级：{{ backdetails.gradeName === 0 ? 0 : backdetails.gradeName || '' }}</view>
          <view class="mt-40">学习时间：{{ backdetails.studyTime === 0 ? 0 : backdetails.studyTime || '' }}</view>
          <view class="mt-40">学习学时：{{ backdetails.studyHour === 0 ? 0 : backdetails.studyHour || '' }}小时</view>
          <!-- <view class="mt-40">总报学时：{{ backdetails.totalCourse === 0 ? 0 : backdetails.totalCourse || '' }}小时</view> -->
          <view class="mt-40">所学内容：{{ backdetails.studyContent || '' }}</view>
          <view class="mt-40" v-if="backdetails.curriculumName == '鼎英语'">学习效率：{{ backdetails.studyRate === 0 ? 0 : backdetails.studyRate || '' }}%</view>
          <view class="mt-40" v-if="backdetails.isWord">所学词库：{{ backdetails.studyBooks === 0 ? 0 : backdetails.studyBooks || '' }}</view>
          <view class="mt-40" v-if="backdetails.isWord">复习词汇：{{ backdetails.reviewWords === 0 ? 0 : backdetails.reviewWords || '' }}个</view>
          <view class="mt-40" v-if="backdetails.isWord">复习遗忘词汇：{{ backdetails.forgetWords === 0 ? 0 : backdetails.forgetWords || '' }}个</view>
          <view class="mt-40" v-if="backdetails.isWord">复习遗忘率：{{ backdetails.forgetRate === 0 ? 0 : backdetails.forgetRate || '' }}%</view>
          <view class="mt-40" v-if="backdetails.isWord">学新词汇：{{ backdetails.newWords === 0 ? 0 : backdetails.newWords || '' }}个</view>
          <view class="mt-40" v-if="backdetails.isWord">学新遗忘词汇：{{ backdetails.newForget === 0 ? 0 : backdetails.newForget || '' }}个</view>
          <view class="mt-40" v-if="backdetails.isWord">学新遗忘率：{{ backdetails.newForgetRate === 0 ? 0 : backdetails.newForgetRate || '' }}%</view>
          <view class="mt-40" v-if="backdetails.isWord">今日共识记词汇（复习遗忘词汇+学新词汇）:{{ backdetails.todayWords === 0 ? 0 : backdetails.todayWords || '' }}个</view>
          <view class="mt-40" v-if="backdetails.isWord">学习进度：{{ backdetails.learnSchedule === 0 ? 0 : backdetails.learnSchedule || '' }}%</view>
        </view>
        <view class="mt-30 bg-ff plr-30 ptb-40 radius-15">
          <view class="f-32 bold">教练反馈</view>
          <view class="mt-20 p-30 bg-f7 radius-15">教练评语：{{ backdetails.feedback || '' }}</view>
        </view>

        <view class="plr-20 mt-30">
          <u-button type="primary" text="分享" shape="circle" size="large" @click="splits" color="linear-gradient(to bottom, #88CFBA, #1D755C)"></u-button>
        </view>
      </view>
      <view v-if="!userInfo.experience && allFeedbackList.length == 0 && backdetails == ''" :style="{ height: useHeight + 'rpx' }" class="t-c flex-col bg-ff radius-15">
        <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
        <view style="color: #bdbdbd">暂无数据</view>
      </view>

      <view v-if="userInfo.experience && triallist != ''">
        <view class="details bg-ff plr-30 ptb-40 radius-15">
          <view class="f-32 bold">学习详情</view>
          <view class="mt-40">日期：{{ triallist.dateTime || '' }}</view>
          <view class="mt-40">姓名：{{ triallist.studentName || '' }}</view>
          <view class="mt-40">年级：{{ triallist.gradeName || '' }}</view>
          <view class="mt-40">学习时间-结束时间：{{ triallist.studyTime || '' }}</view>
          <view class="mt-40">试学学时：{{ triallist.studyHour == 0 ? 0 : triallist.studyHour || '' }}小时</view>
          <view class="mt-40">词汇测试水平：{{ triallist.expWords == 0 ? 0 : triallist.expWords || '' }}</view>
          <view class="mt-40">首测词汇量：{{ triallist.studyHour == 0 ? 0 : triallist.studyHour || '' }}个</view>
          <view class="mt-40">识记词汇数量：{{ triallist.todayWords || '' }}个</view>
          <!-- <view class="mt-40" style="padding-top: 6rpx;">学习效率：{{backdetails.studyRate === 0 ? 0 : backdetails.studyRate || ''}}%</view> -->
          <view class="mt-40">遗忘数量：{{ triallist.forgetWords == 0 ? 0 : triallist.forgetWords || '' }}个</view>
          <view class="mt-40">记忆率：{{ triallist.wordsRate == 0 ? 0 : triallist.wordsRate || '' }}%</view>
          <view class="mt-40">体验词库：{{ triallist.studyBooks || '' }}</view>
          <view class="mt-40">
            记忆特点：
            <text v-if="triallist.memory == 1">弱</text>
            <text v-if="triallist.memory == 2">正常</text>
            <text v-if="triallist.memory == 3">强</text>
          </view>
          <view class="mt-40">体验后学习意愿：{{ triallist.studyIntention == 0 ? 0 : triallist.studyIntention || '' }}</view>
        </view>
        <view class="bg-ff plr-30 ptb-40 radius-15">
          <view class="f-32 bold">反馈</view>
          <view class="mt-40">学员学习状况反馈：{{ triallist.feedback || '' }}</view>
        </view>

        <view class="plr-20 mt-30">
          <u-button type="primary" text="分享" @click="goTrial" shape="circle" size="large" color="linear-gradient(to bottom, #88CFBA, #1D755C)"></u-button>
        </view>
      </view>

      <view v-if="userInfo.experience && triallist == ''" :style="{ height: useHeight + 'rpx' }" class="t-c flex-col bg-ff radius-15">
        <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
        <view style="color: #bdbdbd">暂无数据</view>
      </view>
    </view>

    <!-- 学习反馈总 -->
    <view v-if="!switchstate && totallist != ''">
      <view class="bg-ff plr-30 ptb-40 radius-15 c-00">
        <view class="f-32 mt-25 bold">学习详情</view>
        <view class="mt-40">姓名：{{ totallist.studentName || '' }}</view>
        <view class="mt-40">年级：{{ totallist.gradeName || '' }}</view>
        <view class="mt-40">所学内容：{{ totallist.studyBooks || '' }}</view>
        <view class="mt-40">学习进度：{{ totallist.learnSchedule === 0 ? 0 : backdetails.learnSchedule || '' }}%</view>
        <view class="mt-40">复习词汇：{{ totallist.reviewWords === 0 ? 0 : backdetails.reviewWords || '' }}个</view>
        <view class="mt-40">复习遗忘词汇：{{ totallist.forgetWords === 0 ? 0 : backdetails.forgetWords || '' }}个</view>
        <view class="mt-40">复习遗忘率：{{ totallist.forgetRate === 0 ? 0 : backdetails.forgetRate || '' }}%</view>
        <view class="mt-40">学新词汇：{{ totallist.newWords === 0 ? 0 : backdetails.newWords || '' }}个</view>
        <view class="mt-40">学新遗忘词汇：{{ totallist.newForget === 0 ? 0 : backdetails.newForget || '' }}个</view>
        <view class="mt-40">学新遗忘率：{{ totallist.newForgetRate === 0 ? 0 : backdetails.newForgetRate || '' }}%</view>
      </view>

      <view class="plr-20 mt-30">
        <u-button @click="splits" type="primary" text="分享" shape="circle" size="large" color="linear-gradient(to bottom, #88CFBA, #1D755C)"></u-button>
      </view>
    </view>

    <view v-if="!switchstate && totallist == ''" :style="{ height: totalHeight + 'rpx' }" class="t-c flex-col bg-ff radius-15">
      <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
      <view style="color: #bdbdbd">暂无数据</view>
    </view>
  </view>
</template>

<script>
  import uniCalendar from '../../components/uni-calendar/components/uni-calendar/uni-calendar.vue';
  import selectSwitch from '../../components/xuan-switch/xuan-switch.vue';
  // import checkboxGroup from "uview-ui/libs/config/props/checkboxGroup";
  export default {
    components: {
      selectSwitch, // 日总切换
      uniCalendar
    },
    props: {
      userInfo: {
        type: Object
      }
    },
    options: {
      styleIsolation: 'shared'
    }, //解决/deep/不生效
    data() {
      return {
        single: '', // 选中日期
        value: Number(new Date()),
        dateShow: false, // 选择日期
        show: false, // 选择时间
        switchstate: true, // 日总切换状态
        date: '', // 返回日期
        swiperHeight: '', //页面剩余高度，用于动态赋值
        columns: [],
        select: {
          time: ''
        }, // 选择的时间
        id: '', // 课程反馈详情id
        backdetails: '', // 反馈详情数据
        planid: '', // 课程反馈总id
        totallist: '', // 课程反馈总
        showShare: true, // 是否分享
        // ph: '',          // 窗口高度
        // svHeight: 0,     // 暂无数据距离底边距离
        experience: false, // 是否试课课程
        triallist: [],
        imgHost: getApp().globalData.imgsomeHost,
        useHeight: 0, //除头部之外高度
        totalHeight: 0, // 总复习高度
        allFeedbackList: []
      };
    },
    onLoad(options) {},
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 580;

          this.getAllFeedback();
        }
      });
    },
    watch: {
      userInfo: {
        immediate: true,
        handler(newInfo, oldInfo) {
          console.log(newInfo);

          if (this.userInfo.type == 1) {
            console.log(this.userInfo);
            this.single = this.userInfo.date;
            this.id = this.userInfo.id;
            this.planid = this.userInfo.planId;
            this.select.time = this.userInfo.startTime + '-' + this.userInfo.endTime;
            this.getSubject();
            if (this.userInfo.experience == false) {
              this.getFeedback(); //反馈详情日
            } else {
              this.trialData(); //试课详情
            }
          } else {
            console.log('复习课程出bug了');
          }
        }
      }
    },
    methods: {
      // 查看反馈
      getFeedbackDetail(item) {
        this.allFeedbackList = [];
        this.id = item.id;
        this.planid =item.planId;
        this.getFeedback();
      },
      changeSwitch(isSwitch) {
        this.switchstate = isSwitch;
        if (this.switchstate == false) {
          this.totalHeight = this.useHeight + 385;
          console.log(this.totalHeight);
          if (this.select.time != '') {
            this.getTotalback(); // 学习反馈总
          }
        } else {
          if (this.select.time != '') {
            this.getFeedback(); // 学习反馈日
          }
        }
      },

      open() {
        // console.log(this.$refs.calendar);
        this.$refs.calendar.open();
      },

      close() {
        console.log('弹窗关闭');
      },

      // 选择日期
      confirmDate(e) {
        this.single = e.fulldate;
        // this.dateShow=false;
        this.select.time = '';
        this.backdetails = '';
        this.getSubject();
      },
      selectTime() {
        if (this.single == '') {
          uni.showToast({
            icon: 'none',
            title: '请选择日期'
          });
          return;
        }
        this.show = true;
      },

      // 选择时间确定按钮
      confirm(e) {
        // debugger;
        console.log(e);
        this.select.time = e.value[0].time;
        this.id = e.value[0].id;
        this.planid = e.value[0].planId;
        this.show = false;
        let list = [['暂无数据']];
        console.log(list.toString() === this.columns.toString()); // true
        if (this.single != '') {
          if (list.toString() === this.columns.toString()) {
            this.$util.alter('暂无数据，无法选择');
          } else {
            this.getFeedback(); //反馈详情日
          }
        }
      },
      // 选择时间取消按钮
      cancel() {
        this.show = false;
      },

      // 根据年月获取课程日期列表
      // async getdatelist() {
      // 	let res = await this.$httpUser.get("deliver/app/parent/getStudyDateList",{
      // 		date:this.date,
      // 		type:1
      // 	});
      // 	console.log(res);
      // },

      // 根据年月日获取课程日期列表
      async getSubject() {
        this.columns = [];
        let res = await this.$httpUser.get('deliver/app/parent/getStudyTimeList', {
          date: this.single.split('-').slice(0, 2).join('-'),
          type: 1
        });
        if (res.data.success) {
          if (res.data.data.length == 0) {
            let noData = ['暂无数据'];
            this.columns.push(noData);
          } else {
            res.data.data.forEach((e) => {
              e.time = e.startTime.slice(11, 20) + '~' + e.endTime.slice(11, 20);
            });
            this.columns.push(res.data.data);
          }
        }
      },
      // 获取所有课程反馈
      async getAllFeedback() {
        let params = {
          type: 1
        };
        if (this.single) {
          params.date = this.single.split('-').slice(0, 2).join('-');
        }
        const res = await this.$httpUser.get('deliver/app/parent/getStudyTimeListNew', params);

        if (res) {
          this.allFeedbackList = res.data.data;
        }
      },

      // 课程反馈详情日
      async getFeedback() {
        let res = await this.$httpUser.get('deliver/app/parent/getFeedbackInfo', {
          id: this.id,
          type: 1
        });
        if (res.data.success) {
          this.backdetails = res.data.data;
        } else {
          this.showShare = false;
        }
      },
      // 课程反馈详情总
      async getTotalback() {
        let res = await this.$httpUser.get('deliver/app/parent/getTotalStatistics', {
          id: this.id,
          planId: this.planid,
          type: 1
        });
        if (res.data.success) {
          this.totallist = res.data.data;
        } else {
          this.showShare = false;
        }
      },
      // 试课反馈详情
      async trialData() {
        let that = this;
        let res = await this.$httpUser.post('deliver/app/teacher/getExperienceInfo/' + this.userInfo.id);
        if (res.data.success) {
          this.triallist = res.data.data;
        } else {
          this.showShare = false;
        }
      },

      // 跳转到分享页面
      splits() {
        uni.navigateTo({
          url: '/Coursedetails/share/NewShare?state=' + this.switchstate + '&id=' + this.id + '&planId=' + this.planid + '&type=' + 1
        });
      },
      goTrial() {
        uni.navigateTo({
          url: '/Coursedetails/share/trial?id=' + this.id + '&planId=' + this.planid + '&type=' + 1
        });
        // if (this.showShare) {
        // 	uni.navigateTo({
        // 		url: "/Coursedetails/share/trial?id="+ this.id +'&planId='+this.planid + '&type='+1
        // 	})
        // } else {
        // 	uni.showToast({
        // 		'icon': 'none',
        // 		'title': "没有课程数据",
        // 		duration: 1500
        // 	})
        // }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .uni-margin-wrap {
    .swiper-item {
      padding: 32rpx 24rpx;
      padding-bottom: 0;
      margin-top: 32rpx;
      .title_name_css {
        font-size: 30rpx;
        color: #333333;
        // overflow: hidden;
        // text-overflow: ellipsis;  /* 超出部分省略号 */
        // word-break: break-all;  /* break-all(允许在单词内换行。) */
        // display: -webkit-box; /* 对象作为伸缩盒子模型显示 */
        // -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
        // -webkit-line-clamp: 2; /* 显示的行数 */
      }
      .title_css {
        color: #555555;
        margin-top: 22rpx;
        font-size: 30rpx;
        .right_css {
          display: inline-block;
          margin-left: 54rpx;
        }
      }
      .bottom_css {
        border-top: 2rpx solid #f6f7f9;
        text-align: right;
        .btn_left {
          margin-right: 20rpx;
        }
      }

      .btn {
        width: 200rpx;
        height: 60rpx;
        line-height: 60rpx;
        box-sizing: border-box;
        border-radius: 30upx;
        font-size: 24upx;
        display: inline-block;
      }
      .btn_plan {
        width: 200rpx;
        color: #4e9f87 !important;
        border-color: #7baea0 !important;
        line-height: 59rpx;
      }
    }
  }
  .container {
    position: relative;
  }

  .switch {
    position: absolute;
    right: 30rpx;
    top: 55rpx;
    z-index: 1;
  }

  .day-switch {
    position: absolute;
    right: 30rpx;
    top: 55rpx;
    z-index: 1;
  }

  .dateStyle {
    padding: 0 30rpx;
    height: 70rpx;
    border: 1px solid #c8c8c8;
    border-radius: 45rpx;
  }

  .select_time {
    width: 100%;
    padding: 0 35rpx;
    height: 70rpx;
    border: 1px solid #c8c8c8;
    border-radius: 45rpx;
  }

  .timeColor {
    color: #666;
  }

  .text-content {
    display: flex;
  }

  .select {
    max-width: 175rpx;
    width: 175rpx;
    line-height: 70rpx;
  }

  .flex_s {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .flex_x {
    display: flex;
    align-items: center;
  }

  .blackColor {
    color: #000 !important;
  }

  .img_s {
    width: 160rpx;
  }
</style>
