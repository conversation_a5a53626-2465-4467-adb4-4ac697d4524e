<template>
  <view class="page-container">
    <view class="tab">
      <view @tap="tabchange(index)" v-for="(item, index) in tabNameList" :key="index">
        <view class="c-c2 t-c radius-8 topTitle" :class="{ active: current === index }">{{ item }}</view>
      </view>
    </view>
    <view class="content">
      <swiper class="swiper-box" :current="currentSwiper" @animationfinish="animationfinish" @transition="transition">
        <swiper-item class="swiper-item-content">
          <scroll-view :scroll-y="true" :style="{ height: '100%' }">
            <view class="flex-c bold">{{ title }}</view>
            <!-- <u-parse :content="content" style="white-space: normal"></u-parse> -->
            <view style="white-space: pre-wrap" v-html="content"></view>
          </scroll-view>
        </swiper-item>

        <swiper-item class="swiper-item-question">
          <scroll-view :scroll-y="true" :style="{ height: 'calc(100% - 116rpx)' }">
            <view class="textNubmerButton" :class="{ 'show-all': showAll }" v-if="type == 0">
              <view class="text-wrapper" :class="item.style" v-for="(item, index) in textButtonData" @click="textItem(item, index)" :key="index">
                <text class="text">{{ item.text }}</text>
              </view>
              <view v-for="i in 10" :key="i" style="height: 0rpx; width: 54rpx"></view>
            </view>
            <view class="textNubmerButton" :class="{ 'show-all': showAll }" v-if="type == 1">
              <view class="text-wrapper" :class="item.style" v-for="(item, index) in textButtonDataList" @click="textItem(item, index)" :key="index">
                <text class="text">{{ item.text }}</text>
              </view>
              <view v-for="i in 10" :key="i" style="height: 0rpx; width: 54rpx"></view>
            </view>
            <view v-if="textButtonData.length > 7" style="margin-left: 46%">
              <u-icon size="54" bold="true" @click="toggleShowAll" v-if="!showAll" name="arrow-down" color="#428A6F"></u-icon>
              <u-icon size="54" bold="true" @click="toggleShowAll" v-else name="arrow-up" color="#428A6F"></u-icon>
            </view>
            <!-- 做题进度 单、多选 提交/重做 -->
            <view class="flex mb-20 mt-20 pr-40 pl-40">
              <view class="f-28">
                <text style="color: #428a6f">{{ currentQuestionIndex + 1 }}</text>
                <text class="ml-5 mr-5">/</text>
                <text>{{ textButtonData.length }}</text>
                <text class="ml-10" style="color: #428a6f">{{ choiceQuestionType }}</text>
              </view>
              <view>
                <text class="iconBox" @click="submitQuestion" v-if="type == 0">提交试题</text>
                <text v-if="type == 1" class="iconBox" @click="resetQuestion">重做试题</text>
              </view>
            </view>
            <optionQuestion
              v-if="currentQuestion.questionType === 1"
              :questionList="questionList"
              :type="type"
              :currentQuestionIndex="currentQuestionIndex"
              @answerSubmitted="handleAnswerSubmitted"
            />
            <blankQuestion
              v-if="currentQuestion.questionType === 2"
              :questionList="questionList"
              :currentQuestionIndex="currentQuestionIndex"
              :type="type"
              @answerSubmitted="handleAnswerSubmitted"
            />
          </scroll-view>
        </swiper-item>
      </swiper>
    </view>
    <view class="bottomButton" v-if="showButton">
      <u-button shape="circle" :plain="true" color="#428A6F" @click="previousQuestion">上一题</u-button>
      <u-button shape="circle" color="#428A6F" @click="nextQuestion">下一题</u-button>
    </view>
  </view>
</template>

<script>
  import optionQuestion from './components/optionQuestion.vue';
  import blankQuestion from './components/blankQuestion.vue';

  export default {
    components: { optionQuestion, blankQuestion },
    data() {
      return {
        value: 1,
        heightvalue: '',
        current: 0, // tabs组件的current值
        currentSwiper: 0, // swiper 组件的current值
        tabNameList: ['文章', '题目'],
        showButton: 0, // 上一题下一题按钮
        content: '', // 文章内容
        studentCode: '', // 学生编号
        courseId: '', // 课程编号
        type: '', // 1 已做 0 未做
        merchantCode: '', // 门店编号
        checkpointId: '', // 关卡编号
        questionList: [], // 题目列表
        questionInfoList: [], // 题目答案数组
        questionSubmitList: '', // 提交 id 数据
        currentQuestionIndex: 0, // 当前题目索引
        textButtonData: [], // 文章题目
        textButtonDataList: [], // 已做文章题目
        showAll: false, // 文章题目是否全部显示
        choiceQuestionType: '', // 0-单选 1 多选
        isRestQuestion: false,
        title: '' // 文章标题
      };
    },
    onLoad(options) {
      uni.setStorageSync('answerDataList', []);
      uni.setStorageSync('answerViewMap', {});
      if (options.type == 1) {
        uni.setNavigationBarTitle({
          title: '已做试题'
        });
      } else {
        uni.setNavigationBarTitle({
          title: '未做试题'
        });
      }
      this.studentCode = options.studentCode;
      this.courseId = options.courseId;
      this.type = options.type;
      this.merchantCode = options.merchantCode;
      this.checkpointId = options.checkpointId;
      this.getListData();
    },
    // 回显的时候更新数据
    onShow() {},
    computed: {
      currentQuestion() {
        return this.questionList[this.currentQuestionIndex]?.questionInfo;
      }
    },
    methods: {
      getListData() {
        this.$httpUser
          .get('znyy/super-read/wrongBook/detail', {
            studentCode: this.studentCode,
            merchantCode: this.merchantCode,
            courseId: this.courseId,
            checkpointId: this.checkpointId,
            type: this.type
          })
          .then((res) => {
            // #ifdef MP-WEIXIN
            this.content = res.data.data.content.replaceAll(/#/g, '_');
            this.content = this.content.replaceAll(/@&/g, '  ');
            this.content = this.content.replaceAll(/@%/g, '\n');
            this.content = this.content.replaceAll('@=', '<span style="text-decoration: underline;">');
            this.content = this.content.replaceAll('@*', '</span>');
            // #endif
            // #ifdef APP-PLUS
            this.content = res.data.data.content.replace(/(#)|(@&)|(@%)|(@=)|(@\*)/g, (match, p1, p2, p3, p4, p5) => {
              if (p1) return '_'; // 匹配 #
              if (p2) return '  '; // 匹配 @&
              if (p3) return '\n'; // 匹配 @%
              if (p4) return '<span style="text-decoration: underline;">'; // 匹配 @=
              if (p5) return '</span>'; // 匹配 @*
            });
            // #endif

            this.title = res.data.data.title;
            this.questionSubmitList = res.data.data;
            this.questionList = res.data.data.questionList;
            const defaultStyle = 'text-wrapper';

            this.questionList.forEach((item, index) => {
              this.textButtonData.push({
                text: item.questionInfo.questionNumber,
                style: defaultStyle
              });
            });
            const styleShow = this.questionList.map((item, index) => {
              const questionInfo = item.questionInfo;
              return questionInfo.correctAnswer == item.studentAnswer;
            });

            this.textButtonDataList = Array.from({ length: this.questionList.length }, (_, i) => ({
              text: this.questionList[i].questionInfo.questionNumber,
              style: styleShow[i] == false ? 'textWrapperError' : 'textWrapperRight'
            }));
            this.updateQuestionType(0);
          });
      },
      updateQuestionType(i) {
        const questionInfoList = this.questionList[i].questionInfo;
        if (questionInfoList.questionType === 1) {
          this.choiceQuestionType = questionInfoList.choiceQuestionType === 0 ? '单选题' : '多选题';
        } else {
          this.choiceQuestionType = questionInfoList.questionTypeRemark;
        }
      },

      toggleShowAll() {
        this.showAll = !this.showAll;
      },
      previousQuestion() {
        if (this.currentQuestionIndex > 0) {
          let answerViewMap = uni.getStorageSync('answerViewMap') || {};
          // 检查对应索引的 answerView 值
          let answerView = answerViewMap[this.currentQuestionIndex];
          if (answerView !== true && this.type == 0)
            return uni.showToast({
              title: '点击查看答案才算做题记录哦~',
              icon: 'none'
            });
          if (this.type == 1) {
            // 将 answerViewMap 中所有值设置为 true
            for (let key in answerViewMap) {
              answerViewMap[key] = true;
            }
            uni.setStorageSync('answerViewMap', answerViewMap);
          }
          this.currentQuestionIndex--;
          this.updateQuestionType(this.currentQuestionIndex);
        } else {
          uni.showToast({
            title: '当前就是第一题哦~',
            icon: 'none'
          });
        }
      },
      checkAnswerCorrectness(studentAnswer, questionIndex) {
        const currentQuestion = this.questionList[questionIndex];
        const correctAnswer = currentQuestion.questionInfo.correctAnswer;
        if (studentAnswer === correctAnswer) {
          return true;
        }
      },
      handleAnswerSubmitted(value) {
        console.log('33333333333333', value);
        this.questionInfoList.push(value);
        // 遍历已提交的答案数据，根据答案情况设置相应题号的样式
        this.questionInfoList.forEach((answerData) => {
          const index = answerData.index;
          console.log('111111111111111', index);
          const isCorrect = this.checkAnswerCorrectness(answerData.studentAnswer, index);
          console.log('222222222222222222', isCorrect);
          if (isCorrect === true) {
            this.textButtonData[index].style = 'textWrapperRight';
          } else if (isCorrect === undefined) {
            this.textButtonData[index].style = 'textWrapperError';
          } else if (isCorrect === undefined && this.type == 0) {
            this.textButtonData[index].style = 'textWrapper';
          }
        });
      },
      nextQuestion() {
        if (this.currentQuestionIndex < this.questionList.length - 1) {
          let answerViewMap = uni.getStorageSync('answerViewMap') || {};

          // 检查对应索引的 answerView 值
          let answerView = answerViewMap[this.currentQuestionIndex + 1];

          if (this.type == 0 && answerView !== true) {
            return uni.showToast({
              title: '点击查看答案才算做题记录哦~',
              icon: 'none'
            });
          }

          if (this.type == 1) {
            // 将 answerViewMap 中所有值设置为 true
            for (let key in answerViewMap) {
              answerViewMap[key] = true;
            }
            uni.setStorageSync('answerViewMap', answerViewMap);
          }

          this.currentQuestionIndex++;
          this.updateQuestionType(this.currentQuestionIndex);
        } else {
          uni.showToast({
            title: '没有下一题了~',
            icon: 'none'
          });
        }
      },
      textItem(item, index) {
        console.log('textItem', item, index);
        let answerViewMap = uni.getStorageSync('answerViewMap') || {};
        // 检查对应索引的 answerView 值
        let answerView = answerViewMap[index];
        if (answerView !== true && this.type == 0) {
          return uni.showToast({
            title: '点击查看答案才算做题记录哦~',
            icon: 'none'
          });
        } else {
          if (this.type == 1) {
            // 将 answerViewMap 中所有值设置为 true
            for (let key in answerViewMap) {
              answerViewMap[key] = true;
            }
            uni.setStorageSync('answerViewMap', answerViewMap);
          }
          this.currentQuestionIndex = index;
          this.updateQuestionType(this.currentQuestionIndex);
        }
      },
      submitQuestion() {
        let that = this;
        let quesLength = that.questionInfoList.length !== that.questionList.length;
        if (that.questionInfoList.length === 0) {
          return uni.showToast({
            title: '请至少做一道题目~',
            icon: 'none'
          });
        }
        let quesContent = quesLength ? '还有未做试题，是否提交' : '是否提交试题';
        uni.showModal({
          content: quesContent,
          confirmText: '是',
          cancelText: '否',
          success: function (res) {
            if (res.confirm) {
              that.$httpUser
                .post('znyy/super-read/wrongBook/submit', {
                  studentCode: that.questionSubmitList.studentCode,
                  merchantCode: that.questionSubmitList.merchantCode,
                  courseId: that.questionSubmitList.courseId,
                  checkpointId: that.questionSubmitList.checkpointId,
                  type: that.isRestQuestion ? 1 : 0,
                  questionList: that.questionInfoList
                })
                .then((res) => {
                  if (!that.isRestQuestion) {
                    uni.showToast({
                      title: '提交成功',
                      icon: 'none',
                      duration: 2000,
                      success: function () {
                        setTimeout(() => {
                          uni.setStorageSync('dataUpdated', true);
                          uni.navigateBack({
                            delta: 1
                          });
                        }, 2000);
                      }
                    });
                  } else {
                    uni.showToast({
                      title: '提交成功',
                      icon: 'none'
                    });
                    that.type = 1;
                    that.getListData();
                    that.questionList = [];
                    that.textButtonData = [];
                    that.questionInfoList = [];
                    uni.setStorageSync('answerViewMap', {});
                    uni.setStorageSync('answerDataList', []);
                  }
                });
            } else if (res.cancel) {
              console.log('用户点击取消');
            }
          }
        });
      },
      resetQuestion() {
        this.type = 0;
        uni.setStorageSync('answerViewMap', {});
        this.isRestQuestion = true;
        this.currentQuestionIndex = 0;
      },
      animationfinish(e) {
        if (e.target.current === 1) {
          this.showButton = 1;
          this.current = 1;
        } else {
          this.current = 0;
        }
      },
      transition(e) {
        if (e.detail.dx < 0) {
          this.showButton = 0;
        }
      },
      tabchange(index) {
        this.current = index;
        setTimeout(() => {
          this.showButton = index;
        }, 80);
        this.currentSwiper = index;
      }
    }
  };
</script>

<style>
  page {
    height: 100%;
  }
</style>

<style lang="scss" scoped>
  .page-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    box-sizing: border-box;
    padding-bottom: var(--safe-area-inset-bottom);

    .tab {
      height: 98rpx;
      flex-shrink: 0;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .iconBox {
      border: 2rpx solid #339378;
      border-radius: 8rpx;
      font-size: 24rpx;
      color: #339378;
      padding: 6rpx;
    }
    .content {
      flex: 1;
      box-sizing: border-box;
      .swiper-box {
        min-height: unset;
        height: 100%;
        .swiper-item-content {
          height: 100%;
          padding: 0rpx 20rpx 20rpx;
          box-sizing: border-box;
        }
        .swiper-item-question {
          height: 100%;
          box-sizing: border-box;
          margin-bottom: 20rpx;
        }
      }
    }
  }

  .topTitle {
    width: 236rpx;
    height: 54rpx;
    line-height: 54rpx;
    font-size: 28rpx;
    background: #f7f7f7 !important;
  }

  .active {
    background: #ffffff !important;
    color: #339378;
  }

  .bottomButton {
    width: 100%;
    display: flex;
    position: fixed;
    bottom: 0;
    margin-top: 15rpx;
    margin-bottom: 15rpx;
    padding-left: 15rpx;
    padding-bottom: var(--safe-area-inset-bottom);
  }

  ::v-deep .u-button {
    margin-right: 25rpx;
    height: 90rpx;
  }
  .textNubmerButton {
    height: 96rpx;
    padding: 22rpx 36rpx 0rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    overflow: hidden;
    column-gap: 50rpx;
    row-gap: 22rpx;
    background-color: #f7f7f7 !important;
    flex-direction: row;
    flex-wrap: wrap;
  }

  .show-all {
    height: auto;
    overflow: visible;
  }
  .text-wrapper {
    width: 54rpx;
    height: 54rpx;
    border-radius: 8rpx;
    border: 2px solid #efeff0;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    .text {
      overflow-wrap: break-word;
      font-size: 28rpx;
      font-family: AlibabaPuHuiTiR;
      font-weight: normal;
      text-align: center;
      white-space: nowrap;
      line-height: 40rpx;
    }
  }

  .textWrapperRight {
    width: 54rpx;
    height: 54rpx;
    background: #effbf6;
    border-radius: 8rpx;
    border: 2rpx solid #94e6c7;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    .text {
      overflow-wrap: break-word;
      color: rgba(49, 207, 147, 1);
      font-size: 28rpx;
      font-family: AlibabaPuHuiTiR;
      font-weight: normal;
      text-align: center;
      white-space: nowrap;
      line-height: 40rpx;
    }
  }
  .textWrapperError {
    width: 54rpx;
    height: 54rpx;
    background: #ffac80;
    border-radius: 8rpx;
    border: 2px solid rgba(255, 175, 133, 1);
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    .text {
      overflow-wrap: break-word;
      color: #f7f7f7;
      font-size: 28rpx;
      font-family: AlibabaPuHuiTiR;
      font-weight: normal;
      text-align: center;
      white-space: nowrap;
      line-height: 40rpx;
    }
  }
</style>
