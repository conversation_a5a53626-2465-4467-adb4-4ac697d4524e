<template>
  <view class="contain" :style="{ height: useHeight + 'rpx' }">
    <view class="content" v-html="data.listeningContent"></view>
    <view class="contentTranslation" v-html="data.listeningContentTranslation"></view>
    <view style="height: 300rpx"></view>
    <my-audio ref="audio" :src="data.listeningAudioUrl"></my-audio>
  </view>
</template>

<script>
  import MyAudio from './components/my-audio.vue';
  export default {
    components: {
      MyAudio
    },
    data() {
      return {
        data: {},
        useHeight: 0
      };
    },
    onLoad(option) {
      let a = decodeURIComponent(option.data);
      this.data = JSON.parse(a);
      uni.setNavigationBarTitle({
        title: option.title
      });
    },
    methods: {},
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          this.useHeight = res.windowHeight * (750 / res.windowWidth);
        }
      });
    }
  };
</script>

<style lang="scss" scoped>
  .contain {
    box-sizing: border-box;
    padding: 32rpx;
    background-color: #fff;
    overflow-y: auto;
    .content {
      white-space: pre-wrap;
      line-height: 64rpx;
      font-weight: 600;
      font-size: 34rpx;
      color: #000;
      // font-size: 24rpx;
    }
    .contentTranslation {
      margin-top: 32rpx;
      white-space: pre-wrap;
      // font-size: 24rpx;
      color: #555555;
      line-height: 64rpx;
    }
  }
</style>
