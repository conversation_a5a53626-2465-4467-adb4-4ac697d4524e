<template>
  <!-- 拼团商品组件 -->
  <view class="goods-item" :style="itemStyle" @click="handleClick">
    <view class="goods-img">
      <image class="wh100" :src="dataSource.piGoodsVo.goodsPicUrl" mode="aspectFill" />
    </view>
    <view class="goods-content">
      <view class="goods-title">{{ dataSource.piGoodsVo.goodsName || '' }}</view>
      <view class="goods-price">
        <view class="group-number">{{ dataSource.piGroupActivityVo.groupSize || '-' }}人团</view>
        <view class="sale-price">{{ '￥' + dataSource.groupPrice }}</view>
        <view class="origin-price">单价￥ {{ dataSource.piGoodsVo.goodsOriginalPrice }}</view>
      </view>

      <view class="goods-time-end" v-if="notStart">未开始</view>
      <view class="goods-time-end" v-else-if="!timeOver">
        距结束：
        <text class="time">{{ countdown.days }}</text>
        <text class="plr">天</text>
        <!-- <text class="time">{{ `${countdown.hours}:${countdown.minutes}:${countdown.seconds}` }}</text> -->
        <text class="time">{{ `${countdown.hours}:${countdown.minutes}` }}</text>
      </view>
      <view class="goods-time-end" v-else>已结束</view>
    </view>
  </view>
</template>

<script>
  const { $http, $navigationTo } = require('@/util/methods.js');
  export default {
    props: {
      dataSource: {
        type: Object,
        default: () => ({})
      },
      itemStyle: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        countdown: {
          days: '00',
          hours: '00',
          minutes: '00',
          seconds: '00'
        },
        // endTime: new Date().getTime() + 24 * 2 * 60 * 60 * 1000,
        endTime: 0,
        timeOver: false,
        notStart: false
      };
    },
    watch: {
      'dataSource.piGroupActivityVo.activityEndTime': {
        handler(newVal, oldVal) {
          if (newVal && newVal !== oldVal) {
            console.log('🚀 ~ dataSource ~ newVal:', newVal);
            console.log('🚀 ~ dataSource ~ oldVal:', oldVal);
            this.endTime = new Date(newVal).getTime();
            if (!this.notStart) {
              this.startCountdown();
            }
          }
        },
        immediate: true,
        deep: true
      },
      'dataSource.piGroupActivityVo.activityStartTime': {
        handler(newVal, oldVal) {
          if (newVal && newVal !== oldVal) {
            const startTime = new Date(newVal).getTime();
            const now = new Date().getTime();
            const timeLeft = now - startTime;
            if (timeLeft <= 0) {
              this.notStart = true;
              return;
            }
          }
        },
        immediate: true,
        deep: true
      }
    },
    methods: {
      startCountdown() {
        const updateCountdown = () => {
          const now = new Date().getTime();
          const timeLeft = this.endTime - now;

          if (timeLeft <= 0) {
            clearInterval(this.countdownTimer);
            this.countdown = {
              days: '00',
              hours: '00',
              minutes: '00',
              seconds: '00',
              status: 'finish'
            };
            this.handleCountdownEnd();
            return;
          }

          const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
          const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
          const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
          const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

          this.countdown = {
            days: String(days).padStart(2, '0'),
            hours: String(hours).padStart(2, '0'),
            minutes: String(minutes).padStart(2, '0'),
            seconds: String(seconds).padStart(2, '0')
          };
        };

        updateCountdown();
        this.countdownTimer = setInterval(updateCountdown, 1000);
      },
      handleCountdownEnd() {
        // 倒计时结束时的处理逻辑
        console.log('倒计时已结束');
        this.timeOver = true;
      },
      handleClick() {
        getApp().sensors.track('clickOnGroupBuying', {
          name: '点击拼团商品',
          goodsId: this.dataSource.goodsId
        });
        $navigationTo(
          `Coursedetails/productDetils?id=${this.dataSource.goodsId}&groupActivityId=${this.dataSource.groupId}&isGroupBuyGood=1&isGroupLeader=1${
            this.notStart ? '&notStart=1' : ''
          }${this.timeOver ? '&hasEnd=1' : ''}`
        );
        // $navigationTo(`Coursedetails/productDetils?id=1287805056553324544&isGroupBuyGood=1&groupActivityId=1359911237798354944`);
        // this.$refs.inviteGroupBuyingRef.open();
      }
    },

    beforeDestroy() {
      clearInterval(this.countdownTimer);
    }
  };
</script>

<style lang="scss" scoped>
  .goods-item {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 224rpx;
    padding: 14rpx 0 16rpx 16rpx;
    margin-bottom: 24rpx;
    background: #f7faf8;
    box-sizing: border-box;

    .goods-img {
      width: 154rpx;
      height: 194rpx;
      margin-right: 16rpx;

      image {
        border-radius: 8rpx;
      }
    }
  }

  .goods-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    height: 100%;

    .goods-title {
      width: 100%;
      max-width: 398rpx;
      // height: 88rpx;
      font-size: 28rpx;
      color: #555555;
      line-height: 44rpx;
    }

    .goods-price {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      width: 100%;

      & > view {
        margin-right: 8rpx;
      }

      .group-number {
        min-width: 100rpx;
        height: 40rpx;
        background: #ed7d4d;
        font-size: 24rpx;
        border-radius: 8rpx;
        color: #ffffff;
        text-align: center;
        line-height: 40rpx;
      }

      .sale-price {
        font-size: 28rpx;
        color: #ed7d4d;
        line-height: 44rpx;
      }

      .origin-price {
        margin-right: 0;
        font-size: 24rpx;
        color: #a1a1a1;
        line-height: 44rpx;
      }
    }

    .goods-time-end {
      width: 100%;
      font-size: 24rpx;
      color: #555555;

      .time {
        font-size: 28rpx;
        color: #ed7d4d;
      }

      .plr {
        padding-left: 12rpx;
        padding-right: 16rpx;
      }
    }
  }
</style>
