import {
    httpUser
} from '@/util/luch-request/indexUser.js' // 全局挂载引入
import CryptoJS from 'crypto-js';
const playVoice = (word, isWord,timbre,pronunciationType,playType,studentCode) => {
    let innerAudioContext = uni.createInnerAudioContext();
    // #ifdef MP-WEIXIN
    innerAudioContext.obeyMuteSwitch = false;
    // #endif
    if (isWord) { //不是单词的音频
        var linkUrl = 'https://document.dxznjy.com/word/local/dflasfa' + word;
        innerAudioContext.src = linkUrl;
        innerAudioContext.play();
        return;
    }
    // 单词播放
	// let userCode = uni.getStorageSync('userCode');
	let data = studentCode+ 'L0anhf';
	let sg = CryptoJS.SHA1(data).toString();
    httpUser.get('znyy/app/query/word/voice', {
        word: word,
		v: playType,
		rp: pronunciationType == 1 ? true : false,
		sex: timbre,
		sg: sg
    }).then(result => {
        if (result.data.success) {
            // var w = encodeURIComponent(result.data.data);
			let linkUrl;
			if (playType == 1) {
				linkUrl = 'https://document.dxznjy.com/' + encodeURIComponent(result.data.data);
			} else {
				linkUrl = result.data.data;
			}
			innerAudioContext.obeyMuteSwitch = false;
			innerAudioContext.src = linkUrl;
			innerAudioContext.play();
        } else {
            uni.showToast({
                title: result.data.message,
                icon: "none"
            })
        }
    });

}

export {
    playVoice
};