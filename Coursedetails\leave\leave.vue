<template>
  <view class="space">
    <!-- 自定义导航栏 -->
    <view class="box-bg">
      <uni-nav-bar height="65px" backgroundColor="#F3F8FC">
        <block slot="left">
          <view class="left-icon" @click="back">
            <uni-icons type="back" color="#666" size="26" />
          </view>
        </block>
        <view class="leave-title">请假</view>
      </uni-nav-bar>
    </view>
    <!-- 自定义导航栏 -->

    <view class="plr-30 f-30">
      <view class="plr-30 ptb-40 bg-ff radius-15">
        <view class="bold f-32">课程详情：</view>
        <view class="mt-40">课程名称：鼎英语</view>
        <view class="mt-40">课程内容：{{ userNews.experience ? '试课课程' : '正式课程' }}</view>
        <view class="mt-40">教练：{{ userNews.teacher }}</view>
        <view class="mt-40">上课时间：{{ userNews.starttime }}-{{ userNews.endtime }}</view>
        <!-- <view class="mt-40" v-if="showTime">调整课时：{{time.selecttime}}</view> -->
        <view class="mt-40">
          <view class="right" style="display: flex" v-if="showTime">
            <text>调整课时：</text>
            <text style="text-decoration: underline" @click="isShow = true">{{ getTime(value) }}</text>
            <u-icon name="edit-pen-fill" size="36" color="#66b39c" @click="isShow = true"></u-icon>
          </view>
        </view>
      </view>
      <view class="plr-30 ptb-40 bg-ff radius-15 mt-30">
        <view class="bold f-32">请假原因：</view>
        <view class="mt-20 bg-f7 p-30 radius-15" :style="{ height: 300 + 'rpx' }">
          <textarea v-model="cause" @input="bindTextAreaBlur" placeholder-style="color:#999" placeholder="请输入请假事由" />
        </view>

        <view class="mt-40">
          <text class="c-f0">*</text>
          <text class="c-99">备注: 请假需提前24小时,如在24小时之内请假则此课程费用依旧扣除。</text>
        </view>

        <view class="mt-40 flex-c">
          <view class="mr-30 w100">
            <u-button shape="circle" @click="cancel" :plain="true">取消</u-button>
          </view>

          <view class="w100">
            <u-button shape="circle" @click="$noMultipleClicks(determine)" color="linear-gradient(to bottom, #88CFBA, #1D755C)">确定</u-button>
          </view>
        </view>
      </view>
    </view>
    <u-datetime-picker ref="datetimePicker" :show="isShow" v-model="value" mode="datetime" @confirm="define" @cancel="remove" :formatter="formatter"></u-datetime-picker>
    <uni-popup ref="loadingPopup" type="center" :is-mask-click="false">
      <view class="bg-ff radius-15 t-c">
        <view class="loadingpadding">
          <image src="https://document.dxznjy.com/Assistant/loading.png" class="loadingImg"></image>
          <view class="mt-30">加载中...</view>
          <view>请耐心等候</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>
<script>
  import dayjs from 'dayjs';
  export default {
    data() {
      return {
        noClick: true, //防抖
        single: '',
        value: Number(new Date()),
        isShow: false,

        cause: '', // 请假原因
        time: {
          selecttime: '' // 请假选择时间
        },
        id: '', // 请假申请id
        type: '', // 请假类型
        userNews: {
          teacher: '', // 教练
          Type: '', // 课程内容
          starttime: '', // 上课开始时间
          endtime: '' // 上课结束时间
        },

        showTime: true, // 显示调整课程

        datetime: '', // 请假选择时间

        leavedetails: {}, // 请假详情
        useHeight: 0 //除头部之外高度
      };
    },
    onLoad(option) {
      let data = option.item;
      if (data == undefined) {
        data = option.data;
      }
      this.userInfo = JSON.parse(decodeURIComponent(data));
      this.id = this.userInfo.id;
      this.type = this.userInfo.type;

      if (this.type === 1) {
        this.showTime = true;
      } else {
        this.showTime = false;
      }
      this.userNews.teacher = this.userInfo.teacher;
      this.userNews.Type = this.userInfo.type;
      this.userNews.experience = this.userInfo.experience;
      console.log(this.userNews.Type);
      if (this.userInfo.starttime == undefined) {
        this.userNews.starttime = this.userInfo.startTime;
      } else {
        this.userNews.starttime = this.userInfo.starttime;
      }
      if (this.userInfo.endtime == undefined) {
        this.userNews.endtime = this.userInfo.endTime;
      } else {
        this.userNews.endtime = this.userInfo.endtime;
      }
    },
    // onShow(options) {
    // 	// 1 获取当前的小程序的页面栈-数组 长度最大是10页面
    // 	let pages = getCurrentPages();
    // 	// 2 数组中 索引最大的页面就是当前页面
    // 	let currentPage = pages[pages.length - 1];
    // 	// 3 获取url上的type参数
    // 	// const { type } = currentPage.options;
    // 	let res = currentPage.options;
    // 	this.id = res.id
    // 	this.type = res.type
    // 	if(this.type === 1) {
    // 		this.show = true
    // 	}else{
    // 		this.show = false
    // 	}
    // 	// this.getDetaails()
    // },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 1050;
        }
      });
      // 微信小程序需要用此写法
      this.$refs.datetimePicker.setFormatter(this.formatter);
    },
    methods: {
      getTime(timestamp) {
        var date = new Date(timestamp);
        var year = date.getFullYear();
        var month = ('0' + (date.getMonth() + 1)).slice(-2);
        var day = ('0' + date.getDate()).slice(-2);
        var hours = ('0' + date.getHours()).slice(-2);
        var minutes = ('0' + date.getMinutes()).slice(-2);
        var datetime = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes;
        return datetime;
      },
      // 左侧按钮返回上一页
      back() {
        uni.navigateBack();
      },

      formatter(type, value) {
        if (type === 'year') {
          return `${value}年`;
        }
        if (type === 'month') {
          return `${value}月`;
        }
        if (type === 'day') {
          return `${value}日`;
        }

        if (type === 'hour') {
          return `${value}时`;
        }

        if (type === 'minute') {
          return `${value}分`;
        }
        return value;
      },

      // 调整课时确定
      define(val) {
        let time = val.value;
        this.time.selecttime = dayjs(time).format('YYYY-MM-DD HH:mm');
        this.datetime = this.time.selecttime;
        this.isShow = false;
      },

      // 调整时间取消按钮
      remove(e) {
        console.log(e);
        this.isShow = false;
      },

      // 文本域
      bindTextAreaBlur: function (e) {
        // console.log(e.detail.value)
        let inputValue = e.detail.value;
        console.log(inputValue.length);
        if (inputValue.length >= 140) {
          uni.showToast({
            title: '输入字数超出限制',
            icon: 'none'
          });
        } else {
          this.cause = inputValue;
        }
      },

      // 请假页面取消按钮
      cancel() {
        uni.navigateBack();
      },

      // 请假页面确定按钮
      determine() {
        this.getLeave();
        // this.cause ='';
        // this.time.selecttime ='';
      },

      // 请假申请
      async getLeave() {
        // this.$refs.loadingPopup.open();
        uni.showLoading({});
        if (!this.time.selecttime) {
          this.datetime = dayjs(this.value).format('YYYY-MM-DD HH:mm');
        }
        let res = await this.$httpUser.post(
          'deliver/app/parent/addVacationApply?cause=' + encodeURI(this.cause) + '&dateTime=' + this.datetime + '&id=' + this.id + '&type=' + this.type
        );
        console.log(res);
        // this.$refs.loadingPopup.close();
        uni.hideLoading();
        if (res.data.success) {
          uni.showToast({
            title: '请假成功',
            duration: 1500
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        }
      },

      // 请假详情
      async getDetaails() {
        let res = await this.$httpUser.get('deliver/app/parent/getVacationApplyInfo?id=' + this.id);
        // console.log(res);
        if (res.data.success) {
          this.leavedetails = res.data.data;
          console.log(this.leavedetails);
          this.cause = this.leavedetails.cause;
          if (this.leavedetails.dateTime === '' || this.leavedetails.dateTime === null || this.leavedetails.dateTime === undefined) {
            this.times.starttimes = '';
            this.times.endtimes = '';
          } else {
            this.times.starttimes = this.leavedetails.dateTime;
            let end = dayjs(this.times.starttimes).add(2, 'hour').format('YYYY-MM-DD HH:mm');
            this.times.endtimes = end.substring(end.length - 5);
          }
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  $nav-height: 30px;

  // 导航栏
  .box-bg {
    height: 140rpx;

    /deep/.uni-navbar__header {
      width: 100%;
      position: fixed;
      z-index: 999;
    }

    .left-icon {
      margin-top: 100rpx;
    }

    .leave-title {
      // text-align: center;
      font-size: 36rpx;
      margin: auto;
      margin-top: 100rpx;
      font-weight: 700;
    }
  }

  /deep/.u-button--info {
    border: 1px solid #1d755c !important;
    color: #1d755c !important;
  }

  .loadingImg {
    width: 120rpx;
    height: 120rpx;
  }

  .loadingpadding {
    padding: 85rpx 250rpx;
  }
</style>
