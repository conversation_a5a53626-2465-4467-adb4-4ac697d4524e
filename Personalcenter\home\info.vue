<template>
  <view class="plr-30">
    <view class="bg-ff radius-15 plr-30" :style="{ height: useHeight + 'rpx' }">
      <view class="flex info ptb-30">
        <view class="label f-30 c-33">头像</view>
        <view class="flex-a-c">
          <button class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
            <image class="header-image" :src="headPortrait"></image>
          </button>
          <uni-icons type="right" size="20" color="#999999"></uni-icons>
        </view>
      </view>
      <view class="flex info">
        <view class="label f-30 c-33">昵称</view>
        <view class="flex-a-c" @click="goUrl">
          <text class="mr-10">{{ userinfo.nickName }}</text>
          <uni-icons type="right" size="20" color="#999999"></uni-icons>
        </view>
        <!-- <input type="nickname" class="t-r flex-box" @blur="getName" @input="inputName" :value="nickName" placeholder="请输入昵称" maxlength='10'/> -->
      </view>

      <view class="flex info">
        <view class="label f-30 c-33">手机号</view>
        <view class="">{{ userinfo.mobile }}</view>
      </view>
      <button class="info_sure" @click="$noMultipleClicks(userEdit)">确定</button>
    </view>
  </view>
</template>

<script>
  const { $http, $showMsg } = require('@/util/methods.js');
  import Config from '@/util/config.js';
  export default {
    name: 'webview',
    data() {
      return {
        useHeight: 0,
        noClick: true, //防抖
        userinfo: {},
        headPortrait: ''
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 30;
        }
      });
    },
    onShow() {
      this.homeData();
      // this.setting();
    },
    methods: {
      async homeData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.userinfo = res.data;
          _this.headPortrait = _this.userinfo.headPortrait;
        }
      },
      async userEdit() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/updateNickName',
          method: 'post',
          data: {
            headPortrait: _this.headPortrait,
            nickName: _this.userinfo.nickName
          }
        });
        uni.setStorageSync('nickName', _this.userinfo.nickName);
        uni.setStorageSync('avaUrl', _this.headPortrait);
        $showMsg(res.message);
        setTimeout(function () {
          uni.navigateBack();
        }, 2000);
      },
      // async setting() {
      // 	let _this = this
      // 	const res = await $http({
      // 		url: 'zx/setting/getSysSetting',
      // 	})
      // 	if (res) {
      // 		_this.pageShow = true;
      // 		// _this.studyCentre = res.data.studyCentre
      // 	}
      // },
      getName(e) {
        console.log(e.detail.value);
        this.userinfo.nickName = e.detail.value;
      },
      inputName(e) {
        console.log(e.detail.value);
        this.userinfo.nickName = e.detail.value;
      },
      onChooseAvatar(e) {
        let _this = this;
        uni.uploadFile({
          url: `${Config.DXHost}zx/common/uploadFile`,
          filePath: e.detail.avatarUrl,
          name: 'file',
          header: {
            Token: uni.getStorageSync('token')
          },
          success: function (res) {
            let data = JSON.parse(res.data);
            console.log(data);
            if (data.status == 1) {
              _this.headPortrait = data.data.fileUrl;
            } else {
              uni.showToast({
                title: data.message,
                icon: 'none'
              });
            }
          },
          fail: function (err) {
            $showMsg(err.errMsg);
          },
          complete: function (res) {
            uni.hideLoading();
          }
        });
      },

      goUrl() {
        uni.navigateTo({
          url: '/Personalcenter/home/<USER>'
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .info_sure {
    width: 600upx;
    height: 80upx;
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    margin: 60upx auto;
    text-align: center;
    line-height: 80upx;
    color: #fff;
    border-radius: 45upx;
  }

  .info {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 120rpx;
    border-bottom: 1rpx solid #eee;
  }

  .avatar-wrapper {
    width: 100rpx;
    height: 100rpx;
    margin-right: 10rpx;
  }

  .header-image {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
  }
</style>
