<template>
  <view class="recent-deliver-course bg-ff radius-15" :style="{ paddingBottom: courseData ? '104rpx' : '20rpx' }">
    <!-- 加载状态 -->
    <view class="loading" v-if="isLoading">
      <text>加载中...</text>
    </view>

    <!-- 内容区域 -->
    <view v-else-if="courseData">
      <view class="header f-28">
        <view class="course-name-container">
          <view v-if="courseData.lessonsFlag === 1" class="lessons-flag">补</view>
          <text class="course-name bold">{{ courseData.courseName }}</text>
        </view>
        <view class="links">
          <text class="more-courses" @click="goToMoreCourses"></text>
          <text v-if="courseData.leaveStatus === 0" class="parent-inspect c-48" @click="goToInspect">家长巡课 ></text>
          <span v-else>
            <text>已请假</text>
          </span>
        </view>
      </view>

      <view class="student-info f-26 mt-20">
        <text class="student-name c-66">学员姓名：{{ courseData.studentName }}</text>
        <text class="course-type c-48">课程类型：{{ courseData.courseType }}</text>
      </view>

      <view class="divider mt-20"></view>

      <view class="class-time f-26 c-66 mt-20">上课时间：{{ courseData.courseTime }}</view>

      <view class="teacher-action mt-20">
        <view class="teacher-info flex-a-c">
          <image class="teacher-avatar" :src="courseData.teacherPhoto || defaultAvatar" mode="aspectFill" alt="教师头像"></image>
          <text class="teacher-name f-26 c-33 ml-10">教练名称：{{ courseData.teacherName }}</text>
        </view>
        <view v-if="courseData.leaveStatus === 0" class="start-class-btn" @click="startClass">立即上课</view>
      </view>
    </view>

    <!-- 无数据 -->
    <view class="no-data" v-else>
      <u-empty mode="data" width="40px" textSize="28rpx" icon="https://document.dxznjy.com/course/ac587707bf314badadb28a158852c77d.png" text="暂无近期课程" />
      <text class="more-courses" @click="goToMoreCourses"></text>
    </view>
    <!-- 提示框-->
    <tips-download-popup ref="downloadPopup" />
  </view>
</template>

<script>
  import TipsDownloadPopup from './tipsDownloadPopup.vue';

  const { $http } = require('@/util/methods.js');

  export default {
    name: 'RecentDeliverCourse',
    components: { TipsDownloadPopup },
    props: {
      merchantCode: {
        type: String,
        default: ''
      },
      studentCode: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        courseData: null,
        isLoading: false,
        defaultAvatar: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png'
      };
    },
    watch: {
      studentCode: {
        immediate: true,
        handler() {
          this.getRecentCourseInfo();
        }
      }
    },
    methods: {
      // 获取最近一节交付课
      async getRecentCourseInfo() {
        if (this.isLoading) return;

        this.isLoading = true;
        try {
          if (!this.studentCode) {
            console.warn('未获取到学生编码');
            this.courseData = null;
            return;
          }

          const res = await $http({
            url: `zx/student/course/getLatestDeliverCourse?studentCode=${this.studentCode}&merchantCode=${this.merchantCode}`
          });
          this.courseData = res.data && res.data.experience ? { ...res.data } : null;
        } catch (error) {
          console.error('获取课程信息失败：', error);
          this.courseData = null;
          uni.showToast({ title: '加载最近一节课程失败', icon: 'none' });
        } finally {
          this.isLoading = false;
        }
      },
      // 家长巡课
      goToInspect() {
        this.$nextTick(() => {
          this.$refs.downloadPopup.openPopup();
        });
      },
      // 更多课程
      goToMoreCourses() {
        uni.navigateTo({
          url: `/Personalcenter/my/lessonListPage/index?merchantCode=${this.merchantCode}`
        });
      },
      // 立即上课
      startClass() {
        this.$nextTick(() => {
          this.$refs.downloadPopup.openPopup();
        });
      }
    }
  };
</script>

<style scoped lang="scss">
  .recent-deliver-course {
    position: relative;
    margin: 30rpx;
    padding: 20rpx 20rpx;
    background-color: #fff;
    .more-courses {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 686rpx;
      height: 104rpx;
      background: url('https://document.dxznjy.com/course/f25ad6eafb9446629cc414ba592db677.png') no-repeat;
      background-size: 100% 100%;
    }

    .loading,
    .no-data {
      padding: 40rpx 0;
      text-align: center;
      color: #999;
      font-size: 26rpx;
    }

    .no-data {
      margin-bottom: 60rpx;
    }

    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .course-name-container {
        display: flex;
        align-items: center;
      }

      .lessons-flag {
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #dbe436;
        color: #ffffff;
        font-size: 20rpx;
        padding: 4rpx 8rpx;
        border-radius: 6rpx;
        margin-right: 10rpx;
        height: 28rpx;
        line-height: 28rpx;
      }

      .course-name {
        color: #333333;
      }

      .links {
        display: flex;
        align-items: center;
      }

      .parent-inspect,
      .more-courses {
        color: #48917d;
        font-weight: 600;
      }
    }

    .student-info {
      display: flex;
      justify-content: space-between;
    }

    .divider {
      height: 1px;
      background-color: #eeeeee;
    }

    .teacher-action {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .teacher-avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
      }

      .start-class-btn {
        padding: 12rpx 30rpx;
        background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
        color: #ffffff;
        font-size: 26rpx;
        border-radius: 30rpx;

        &:active {
          opacity: 0.9;
        }
      }
    }
  }
</style>
