<template>
	<view>
		<view class="plr-32">
			<view class="search-css pl-30">
				<u-icon name="search" class="box-50 search-image" color="#22AC39" size="38"></u-icon>
				<input class="search-input f-28 c-55" v-model="seachValue" type="text" @blue="seachValue=''" @focus="searchFocus" readonly  placeholder="搜索你想要的......" />
			</view>
		</view>
		<view class="shopping_mall plr-32 mt-28">
			<block v-for="(item,index) in dataList" :key="index">
				
				<view class="courseItem radius-20 pb-10 positionRelative"  @tap="goDetails(item)" >
					<view class="shopping_goodsSales_css f-32"  v-if="item.goodsStock==0">
						已抢完…
					</view>
					<view class="courseimg relative">
						<image :src="item.goodsPicUrl" style="width:316rpx;height: 406rpx;"></image>
					</view>
					<view class="plr-15 mt-12">
						<view class="bold f-28 c-55 lh-40 course_name_css">{{ item.goodsName}}</view>
						<view class="font12 mt-24 mtb-16 displayflex displayflexbetween">
							<view>
								<span class="bold f-32 color_red">{{item.goodsOriginalPrice}}</span>
								<span class="f-24 cB4B1B1 price_text_css">鼎币</span>
							</view> 
							<view class="f-24 cB4B1B1">已兑换{{item.goodsSales}}件</view>
						</view>
					</view>
				</view>
			</block>
		</view>
		<view style="width: 100%;text-align: center;">
			<view @click="seeInfo" class="button_bottom_css radius-8 f-24">
				<image class="image_css" src="https://document.dxznjy.com/course/71679d1948c24ff4a95e5ed64d6f87c6.png"></image>
				<span>查看历史兑换记录</span>
			</view>
		</view>
	</view>
</template>

<script>
	const {$http} = require("@/util/methods.js")
	export default {
		data() {
			return {
				seachValue:'',
				no_more:false,
				imgHost: getApp().globalData.imgsomeHost,
				dataList:[],
				goodsTypeListStr:'6',
				infoLists:{}
			}
		},
		onReachBottom() {
			if (this.page >= Number(this.infoLists.totalPage)) {
				this.no_more = true
				return false;
			}
			this.getselection(true, ++this.page);
		},
		onLoad(){
			this.getselection()
		},
		methods: {
			async getselection(isPage, page){
				page = page || 1;
			    // let _this = this
			    const res = await $http({
			    	url: 'zx/wap/goods/select/list',
			    	data: {
			    		goodsTypeListStr: this.goodsTypeListStr,
			    		pageNum:page,
						pageSize:20,
			            userId:uni.getStorageSync('user_id')?uni.getStorageSync('user_id'):'',
			    	}
			    })
				if (res) {
					this.infoLists=res.data
					if (isPage) {
						this.dataList=[...this.dataList,...res.data.data]
					} else {
						this.dataList=res.data.data
					}
					
				}
			},
			seeInfo() {
				getApp().sensors.track('HistoricalRedemptionClick', {
          name: '历史兑换记录'
        });
				uni.navigateTo({
					url: '/shoppingMall/exchangeRecord'
				})
			},
			searchFocus() {
				getApp().sensors.track('indexSearchHandleClick', {
          name: '鼎币商城搜索'
        });
				uni.navigateTo({
					url: '/interestModule/searchPage'
				})
			},
			goDetails(item) {
				getApp().sensors.track('shoppingMallListClick', {
          name: item.goodsName
        });
				uni.navigateTo({
					url: '/shoppingMall/details?id='+item.goodsId
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
/* 搜索框 */
.search-css{
	border:2rpx solid #076E57;
	display: flex;
	margin:0rpx auto;
	border-radius: 40rpx;
	.search-image{
		margin:0 10rpx;
		vertical-align:middle;
	}
	.search-input{
		width:600upx;
		display:inline-block;
		height:80rpx;
		line-height:80rpx;
		vertical-align:middle;
		margin-left:16rpx;
	}
}
.button_bottom_css{
	background-color:rgba(86, 199, 165, 0.1);
	width: 320rpx;
	height: 60rpx;
	margin:24rpx auto;
	color:#56C7A5;
	line-height: 60rpx;
	.image_css{
		width: 30rpx;
		height: 30rpx;
		vertical-align: middle;
	}
	span{
		vertical-align: middle;
		display: inline-block;
		margin-left: 12rpx;
	}
}
.shopping_mall{
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	height: calc(100vh - 220rpx);
	overflow-x: hidden;
	overflow-y: scroll;
	.shopping_goodsSales_css{
		position: absolute;
		top:0;
		left:0;
		width:316rpx;
		height: 406rpx;
		background-color: rgba(0, 0, 0, 0.40);
		text-align: center;
		line-height: 400rpx;
		z-index: 2;
		color:#fff;
	}
	// 
	.cB4B1B1{
		color:#B4B1B1;
	}
	.price_text_css{
		display: inline-block;
		margin-left: 8rpx;
	}
	.course_name_css{
		overflow: hidden;
		text-overflow: ellipsis;  /* 超出部分省略号 */
		word-break: break-all;  /* break-all(允许在单词内换行。) */  
		display: -webkit-box; /* 对象作为伸缩盒子模型显示 */
		-webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
		-webkit-line-clamp: 2; /* 显示的行数 */
	}
	.courseItem {
		width: 316upx;
		border-radius: 20upx;
		background-color: #fff;
		margin-bottom: 30rpx;
		height: 596rpx;
	}
	.courseimg {
		width: 100%;
	}
	.productShare {
		width: 30upx;
		height: 30upx;
	}
	.courseTip {
		width: 95upx;
		height: 40rpx;
		top: 20upx;
		right: 0;
	}
	.ty_img {
		width: 95rpx;
		height: 50rpx;
	}
}
</style>
