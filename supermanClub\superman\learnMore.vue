<!-- 了解更多 -->
<!-- 展示员工企业微信名片，短信链接跳转此界面，本项目暂时没有直接入口 -->
<template>
  <view class="">
    <view class="content">
      <view class="userCard">
        <view class="tip_text">
          <view class="">想了解更多关于{{ type == 'learningSuperman' ? '学习超人' : '超人俱乐部' }}的事宜</view>
          <view class="">
            <span class="tip_red">长按识别二维码</span>
            添加鼎校甄选相关
          </view>
          人员企业微信了解
        </view>
        <image class="ewm" :src="codeImg" :show-menu-by-longpress="true" mode=""></image>
      </view>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        type: 2, //类型，2学习超人  3超人俱乐部
        wxUrl: 'https://open.work.weixin.qq.com/wwopen/userQRCode?vcode=vc4181b8fa39d70214',
        codeImg: '' // 二维码
      };
    },
    onLoad(option) {
      this.type = option.type;
    },
    onShow() {
      this.getQrcode();
    },
    methods: {
      //
      getStudyManageIn() {},
      //获取客服二维码
      async getQrcode() {
        let res = await $http({
          url: 'zx/common/getKfCodeByType',
          data: {
            type: 1
          }
        });
        this.codeImg = res.data.codeUrl;
      }
    }
  };
</script>

<style>
  .content {
    width: 100%;
    height: 100vh;
    text-align: center;
    background-color: rgb(244, 245, 247);
    justify-content: center;
    flex-wrap: wrap;
  }
  .userCard {
    height: 96vh;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    align-items: center;
    margin: 1vh auto;
  }

  .ewm {
    width: 400upx;
    height: 400upx;
    margin-bottom: 460upx;
  }
  .tip_text {
    font-size: 30upx;
    width: 450upx;
    line-height: 45upx;
    color: #333333;
    margin-top: 230upx;
  }
  .tip_red {
    color: #ea6031;
  }
</style>
