<template>
  <view>
    <uni-popup ref="popup" type="center">
      <view class="modal-content">
        <text class="close" @click="close"></text>
        <view class="goods-name">{{ shareDataInfo.couponName }}</view>
        <view class="content">
          <text class="title">兑换码：</text>
          <view class="code-con">
            <view class="code">{{ shareDataInfo.couponCode }}</view>
            <view class="copy-icon" @click="handleCopy"></view>
          </view>
          <button v-if="shareDataInfo.isShow" open-type="share" class="btn" @click="handleShare"></button>
          <button v-else class="btn btn-copy f-28 c-ff t-c" @click="handleGo">点击复制</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        shareDataInfo: {}
      };
    },
    mounted() {},
    methods: {
      copyFn(cb) {
        uni.setClipboardData({
          data: this.shareDataInfo.couponCode,
          success: function (res) {
            uni.getClipboardData({
              success: function (res) {
                uni.showToast({
                  title: '已复制到剪贴板'
                });
                cb && cb();
              }
            });
          }
        });
      },
      handleGo() {
        this.copyFn(() => {
          setTimeout(() => {
            uni.switchTab({
              url: '/pages/index/index'
            });
          }, 500);
        });
      },
      handleCopy() {
        this.copyFn();
      },
      handleShare() {
        this.$refs.popup.close();
      },
      close() {
        this.$refs.popup.close();
      },
      open(info) {
        this.shareDataInfo = info;
        this.$refs.popup.open();
      }
    }
  };
</script>

<style lang="scss" scoped>
  view {
    box-sizing: border-box;
  }
  .modal-content {
    position: relative;
    width: 532rpx;
    height: 460rpx;
    padding-top: 22rpx;

    background-image: url('https://document.dxznjy.com/course/2735fa3e63484970b908119677686285.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;

    .goods-name {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      width: 288rpx;
      height: 112rpx;
      overflow-y: scroll;
      font-family: AlimamaShuHeiTi;
      font-weight: 1000;
      font-size: 40rpx;
      color: #18ae85;
      line-height: 56rpx;
      letter-spacing: 4rpx;
      -webkit-text-stroke: 2rpx #ffffff; /* 文字描边 */
      text-stroke: 2rpx #ffffff; /* 文字描边 */
    }
    .close {
      position: absolute;
      top: -40rpx;
      right: 12rpx;
      width: 40rpx;
      height: 40rpx;
      background-image: url('https://document.dxznjy.com/course/def9654f278c49b6982d86af6d676b2d.png');
      background-size: 100% 100%;
    }

    .content {
      position: absolute;
      top: 184rpx;
      left: 0;
      width: calc(100% - 10rpx);
      padding: 38rpx 32rpx;
      padding-top: 0;
    }

    .title {
      font-family: AlibabaPuHuiTiBold;
      font-size: 32rpx;
      color: #555555;
      line-height: 44rpx;
    }

    .code-con {
      width: 100%;
      height: 76rpx;
      background: #ffffff;
      border-radius: 8rpx;
      display: flex;
      padding: 0 26rpx;
      align-items: center;
      justify-content: space-between;
      margin-top: 16rpx;

      .code {
        font-family: AlibabaPuHuiTiBold;
        font-weight: normal;
        font-size: 28rpx;
        color: #555555;
        line-height: 40rpx;
      }

      .copy-icon {
        width: 32rpx;
        height: 32rpx;
        background-image: url('https://document.dxznjy.com/course/0cd3385c0f1a4aaf87556745646c2854.png');
        background-size: 100% 100%;
      }
    }
    .btn {
      width: 262rpx;
      height: 70rpx;
      margin: 0 auto;
      margin-top: 32rpx;
      background: transparent;
      background-image: url('https://document.dxznjy.com/course/6eec96af93554fd595eda432bc622d49.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    .btn-copy {
      background-image: linear-gradient(to bottom, #88cfba, #1d755c);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 45upx;
    }
  }
  .close {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 20px;
    cursor: pointer;
  }
  .modal-content input {
    margin-right: 10px;
  }
</style>
