<template>
  <page-meta :page-style="'overflow:' + (show ? 'hidden' : 'visible')"></page-meta>
  <view>
    <view class="product_top_css">
      <!-- <image class="image_css w100 h-100"></image> -->
      <swiper :indicator-dots="false" class="w100" style="height: 375rpx" autoplay="true" circular="true" duration="500" indicator-active-color="#ffffff">
        <!-- goodsVideoUrl -->
        <block v-if="shopdetail.goodsVideoUrl">
          <swiper-item class="section_item">
            <video id="myVideo" class="video_css_content" :src="shopdetail.goodsVideoUrl" enable-danmu danmu-btn controls></video>
          </swiper-item>
        </block>
        <block v-for="(item, index) in shopdetail.goodsCarouselList" :key="index">
          <swiper-item class="section_item">
            <image :src="item.picUrl" class="w100 image_css_content" mode="scaleToFill"></image>
          </swiper-item>
        </block>
      </swiper>
      <view class="product_content_css plr-32">
        <view>
          <view class="product_top_style pt-28">
            <view class="f-24 colorRed">鼎币</view>
            <view class="f-48 bold ml-8 colorRed">{{ shopdetail.goodsOriginalPrice }}</view>
            <view class="member_back ml-8 c-ff f-24">已兑换{{ shopdetail.goodsSales || 0 }}件</view>
          </view>
          <view class="f-28 c-33 bold mt-8 lh-44 w686">{{ shopdetail.goodsName }}</view>
          <view class="bg-ff p-25 exchange_css" style="margin-top: 32rpx; border-radius: 24rpx">
            <rich-text :nodes="shopdetail.goodsDesc"></rich-text>
          </view>
        </view>
        <view class="button_bottom_css">
          <button class="botton_style_css f-28 c-ff" :disabled="shopdetail.goodsStock == '0'" @click="changeProduct">我要兑换</button>
        </view>
      </view>
    </view>

    <uni-popup ref="popFramePopup" type="bottom" @change="change">
      <view class="plr-35 popup_content_css relative">
        <view class="header_css">
          <view class="title_css">确定兑换</view>
          <view class="close_css">
            <u-icon @click="closePop(item)" name="close" color="#575757" size="32"></u-icon>
          </view>
          <!-- close -->
        </view>
        <view class="text_tip_css">若您确认兑换，则无法撤销</view>
        <view class="product_center_css mt-40">
          <view class="image_left">
            <image class="w100" mode="widthFix" :src="shopdetail.goodsPicUrl"></image>
          </view>
          <view class="product_right_css">
            <view class="title_top_css f-28 c-55 lh-40">{{ shopdetail.goodsName }}</view>
            <view class="price_css mt-16">
              <span class="f-32 bold colorRed">{{ shopdetail.goodsOriginalPrice }}</span>
              <span class="f-24 ml-8">鼎币</span>
            </view>
          </view>
        </view>
        <button @click="confirmChange" class="change_button lh-40 f-28 c-ff absolute">确认兑换</button>
      </view>
    </uni-popup>
    <uni-popup ref="successPopup" type="center" @change="change">
      <view class="success_popup bg-ff">
        <view class="payment_image_css plr-25">
          <view @click="usageInstruction(exchangeInfo.marketOrderPayDto.usageInstruction)">
            <image class="image_icon" src="https://document.dxznjy.com/course/1ea3945f14f44fb5aa34d76c3e874cbd.png"></image>
            <span class="popup_title_css ml-8">使用说明</span>
          </view>
          <u-icon @click="costClose()" name="close-circle-fill" color="#B1B1B1" size="32"></u-icon>
        </view>
        <view class="text_css f-32 bold lh-42 c-33">
          <view class="mt-30">卡券兑换成功！</view>
        </view>
        <view class="content_css_popup mt-24 c-ff" :style="'background-image: url(https://document.dxznjy.com/course/04f38aff62fb48e3a682c0f64cdd8515.png);'">
          <view class="card_content_css">
            <view class="f-32 lh-44 card_good_name">{{ exchangeInfo.marketOrderPayDto.goodsName }}</view>
            <view class="mt-24 f-28 lh-40">卡号</view>
            <view class="f-40 lh-50 card_exchange_code">{{ exchangeInfo.marketOrderPayDto.exchangeCode }}</view>
            <view @click.stop="copeCardNum(exchangeInfo.marketOrderPayDto.exchangeCode)" class="mt-40 f-28 button_css">点击复制卡号</view>
          </view>
        </view>
        <button @click="costClose()" class="popup_button f-32 lh-44 c-ff mt-16">确定</button>
        <view class="botton_css mt-24 f-28 lh-40" @click="seeInfo">查看历史兑换记录></view>
      </view>
    </uni-popup>
    <uni-popup ref="modalPopup" type="center" @change="change">
      <view class="modal_popup_css bg-ff">
        <view class="modal_popup_close" @click="costClose()">
          <u-icon name="close-circle-fill" color="#B1B1B1" size="36"></u-icon>
        </view>
        <view class="modal_title_css f-32 bold lh-44 c-33">提示</view>
        <view v-if="popupShow == 1" class="modal_center_css f-28">
          该商品为会员用户专享，
          <br />
          请先开通平台会员
        </view>
        <view v-if="popupShow == 2" class="modal_center_css f-28">
          您的鼎币数量不足，
          <br />
          是否前往获取？
        </view>
        <view class="flex-s plr-45">
          <button @click="costClose()" class="cancel_button_css button_modal">取消</button>
          <!-- 2024-11-6 紧急修改 购买超级会员修改成购买家长会员 隐藏 -->
          <button v-if="false" @click="openMember()" class="button_modal determine_button_css">开通会员</button>
          <!-- <button v-if="popupShow == 1" @click="openMember()" class="button_modal determine_button_css">开通会员</button> -->
          <button v-if="popupShow == 2" @click="getDb()" class="button_modal determine_button_css">立即获取</button>
        </view>
      </view>
    </uni-popup>
    <uni-popup ref="costRef" type="center" @change="change">
      <view class="cost_content_css">
        <view class="cost_header_css f-32 bold">使用说明</view>
        <view class="lh-44 plr-32 f-28 c-55 mt-20 cost_text_css" v-if="shopdetail.usageInstruction">
          <p v-html="shopdetail.usageInstruction.replace(/(\r\n|\n|\r)/gm, '<br />')"></p>
        </view>
        <view class="block_circle">
          <u-icon @click="costBlock()" name="arrow-left" color="#555" size="36"></u-icon>
        </view>
        <view class="close_circle" @click="costClose()">
          <u-icon name="close-circle-fill" color="#555" size="38"></u-icon>
        </view>
      </view>
    </uni-popup>
    <uni-popup ref="exchange" type="center" @change="change">
      <view class="plr-35 popup_exChange pb-50 radius-30 relative">
        <view class="header_css">
          <view class="title_css">提示</view>
          <view class="close_css">
            <u-icon @click="closeExPop" name="close" color="#575757" size="32"></u-icon>
          </view>
        </view>
        <view class="f-28 exchange_center_text mt-40">您已经兑换过该商品!</view>
        <view class="flex" style="width: 100%">
          <button @click="seeInfo" class="change_buttonEx buttonList lh-40 f-28 c-ff">查看兑换记录</button>
          <button @click="confirmChangeSecond" class="change_buttonEx lh-40 f-28 c-ff">继续兑换</button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>
<script>
  const { $http, $navigationTo } = require('@/util/methods.js');
  export default {
    data() {
      return {
        imageIndex: 0,
        goodsId: '',
        popupShow: 1,
        shopdetail: {},
        exchangeInfo: {},
        show: false,
        dbNumber: 0,
        activityId: '',
        isFirstExchange: false,
        identityType: uni.getStorageSync('identityType'),
        productDe:
          '<p><img src="https://document.dxznjy.com/course/5f5bf49efb91423abd2a628a8ac2616d.png" style="max-width:100%;" contenteditable="false"/><img src="https://document.dxznjy.com/course/7355f52fdcb2401aa5b6f8c9630664fd.png" style="max-width:100%;" contenteditable="false"/><img src="https://document.dxznjy.com/course/d5ce6968578042c5b1562222ef10c10e.png" style="max-width:100%;" contenteditable="false"/><br/><img src="https://document.dxznjy.com/course/f4045fba7d974db9a648bb596a683204.png" style="max-width:100%;" contenteditable="false" width="100%"/></p>'
      };
    },
    onLoad(e) {
      this.goodsId = e.id;
    },

    onShow() {
      this.detail();
      if (uni.getStorageSync('token')) {
        this.getActivityId();
        this.getExchange();
      }
    },

    methods: {
      getActivityId() {
        this.$httpUser.get('zx/wap/invite/ifShowInvite').then((res) => {
          this.activityId = res.data.data.activityId;
        });
      },
      changeProduct() {
        getApp().sensors.track('mallRedemptionClick', {
          name: this.shopdetail.goodsName
        });
        if (!uni.getStorageSync('token')) {
          uni.navigateTo({
            url: '/Personalcenter/login/login'
          });
          return;
        }
        this.$refs.popFramePopup.open();
      },
      getExchange() {
        this.$httpUser
          .get('zx/wap/order/checkUserPurchase', {
            goodsId: this.goodsId
          })
          .then((res) => {
            this.isFirstExchange = res.data.data;
          });
      },
      seeInfo() {
        uni.navigateTo({
          url: '/shoppingMall/exchangeRecord'
        });
      },
      usageInstruction() {
        if (this.exchangeInfo.marketOrderPayDto.usageInstruction) {
          this.$refs.successPopup.close();
          this.$refs.costRef.open();
        }
      },
      copeCardNum(code) {
        uni.setClipboardData({
          data: code,
          success: function (res) {
            uni.getClipboardData({
              success: function (res) {
                uni.showToast({
                  title: '复制成功'
                });
              }
            });
          }
        });
      },
      costBlock() {
        this.$refs.costRef.close();
        this.$refs.successPopup.open();
      },
      costClose() {
        console.log('===============================================');
        this.$refs.successPopup.close();
        this.$refs.costRef.close();
        this.$refs.modalPopup.close();
      },
      change(e) {
        this.show = e.show;
      },
      async confirmChange() {
        getApp().sensors.track('mallConfirmRedemptionClick', {
          name: this.shopdetail.goodsName
        });
        if (this.identityType != 4) {
          this.popupShow = 1;
          this.$refs.modalPopup.open();
          return;
        }
        if (this.dbNumber < Number(this.shopdetail.goodsOriginalPrice)) {
          this.popupShow = 2;
          this.$refs.modalPopup.open();
          return;
        }
        if (this.isFirstExchange) {
          this.closePop();
          this.$refs.exchange.open();
        } else {
          let _this = this;
          const res = await $http({
            url: 'zx/wap/order/generate/market',
            method: 'POST',
            data: {
              goods: {
                goodsId: this.shopdetail.goodsId,
                purchaseQuantity: 1,
                payAmount: this.shopdetail.goodsOriginalPrice
              },
              parentsMobile: uni.getStorageSync('phone')
            }
          });
          if (res && res.code == 20000) {
            await this.getExchange();
            this.$refs.successPopup.open();
            this.exchangeInfo = res.data;
          }
        }
      },
      async confirmChangeSecond() {
        if (this.identityType != 4) {
          this.popupShow = 1;
          this.$refs.modalPopup.open();
          return;
        }
        if (this.dbNumber < Number(this.shopdetail.goodsOriginalPrice)) {
          this.popupShow = 2;
          this.$refs.modalPopup.open();
          return;
        }
        let _this = this;
        const res = await $http({
          url: 'zx/wap/order/generate/market',
          method: 'POST',
          data: {
            goods: {
              goodsId: this.shopdetail.goodsId,
              purchaseQuantity: 1,
              payAmount: this.shopdetail.goodsOriginalPrice
            },
            parentsMobile: uni.getStorageSync('phone')
          }
        });
        if (res && res.code == 20000) {
          await this.getExchange();
          this.$refs.exchange.close();
          this.$refs.successPopup.open();
          this.exchangeInfo = res.data;
        }
      },
      closePop() {
        this.$refs.popFramePopup.close();
      },
      closeExPop() {
        this.$refs.exchange.close();
      },
      // 获取商品详情
      async detail() {
        let _this = this;
        const res = await $http({
          url: 'zx/wap/goods/single/detail',
          data: {
            goodsId: _this.goodsId,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        if (res) {
          _this.shopdetail = res.data;
          if (uni.getStorageSync('token')) _this.getDingBi();
        }
      },
      async getDingBi() {
        let _this = this;
        const res = await $http({
          url: 'zx/wap/invite/getDingBi',
          data: {}
        });
        if (res) {
          this.dbNumber = Number(res.data);
        }
      },
      //开通会员
      openMember() {
        $navigationTo('Personalcenter/my/nomyEquity?type=2');
        this.$refs.modalPopup.close();
      },
      getDb() {
        uni.navigateTo({
          url: '/InvitationGifts/index?activityid=' + this.activityId
        });
        this.$refs.modalPopup.close();
      },
      seeInfo() {
        uni.navigateTo({
          url: '/shoppingMall/exchangeRecord'
        });
      }
    }
  };
</script>
<style lang="scss" scoped>
  .product_top_css {
    width: 750rpx;
    background-color: #eee;

    .image_css_content {
      height: 375rpx;
    }
  }

  .cost_content_css {
    width: 686rpx;
    height: 980rpx;
    background: url('https://document.dxznjy.com/course/f12fb50e68cc4cf5b9c433a9e968d4e7.png') no-repeat;
    background-size: 100%;
    padding-top: 290rpx;
    position: relative;

    .cost_header_css {
      text-align: center;
      line-height: 40rpx;
      word-break: break-all;
    }

    .cost_text_css {
      text-align: left;
      word-break: break-all;
    }

    .close_circle {
      position: absolute;
      right: 20rpx;
      top: 6rpx;
      z-index: 8;
    }

    .block_circle {
      position: absolute;
      left: 20rpx;
      top: 296rpx;
    }
  }

  .product_content_css {
    border-radius: 24rpx;
    z-index: 2;
    margin-top: -30rpx;
    background: linear-gradient(180deg, #ffffff 0%, #f0f5fa 32%, #f0f5fa 100%);
    min-height: calc(100vh - 358rpx);
    padding-bottom: 150rpx;
    position: absolute;

    .exchange_css {
      width: 640rpx;
    }

    .product_top_style {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .member_back {
        width: 210rpx;
        height: 44rpx;
        line-height: 44rpx;
        text-align: center;
        background: url('https://document.dxznjy.com/course/8442f0de8da146798babc4aa04065bc9.png') no-repeat;
        background-size: 100%;
        margin-left: 5rpx;
      }
    }

    .button_bottom_css {
      position: fixed;
      bottom: 0;
      left: 0;
      width: 750rpx;
      background-color: #fff;
      padding: 18rpx 0;

      .botton_style_css {
        width: 686rpx;
        height: 74rpx;
        border-radius: 38rpx;
        line-height: 74rpx;
        background-color: #28a781;
        margin: 0 auto;
      }
    }
  }

  .change_button {
    width: 680rpx;
    margin: 10rpx auto;
    margin-top: 80rpx;
    background-color: #428a6f;
    border-radius: 38rpx;
    padding: 18rpx 0;
    bottom: 20rpx;
  }

  .change_buttonEx {
    width: 45%;
    margin-top: 60rpx;
    background-color: #428a6f;
    border-radius: 38rpx;
    padding: 18rpx 0;
  }

  .buttonList {
    background-color: #fff;
    color: #4e9f87;
    border: 1px solid #7baea0;
  }

  .popup_content_css {
    height: 644rpx;
    background-color: #fff;
    border-radius: 24rpx 24rpx 0rpx 0rpx;

    .header_css {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      // position: absolute;
      // top:10rpx;
      width: 660rpx;

      .title_css {
        width: 680rpx;
        text-align: center;
        font-size: 32rpx;
        font-weight: 600;
        line-height: 85rpx;
        padding-left: 20rpx;
        color: #11372e;
      }

      .close_css {
        position: absolute;
        top: 20rpx;
        right: 20rpx;
      }
    }

    .text_tip_css {
      font-size: 24rpx;
      text-align: center;
      background-color: rgba($color: #fd9b2a, $alpha: 0.2);
      line-height: 34rpx;
      padding: 14rpx 0;
      color: #fd9b2a;
      border-radius: 8rpx;
    }

    .product_center_css {
      display: flex;
      justify-content: flex-start;

      .image_left {
        width: 230rpx;
        // height: 200rpx;
        // background-color: #eee;
      }

      .product_right_css {
        margin-left: 20rpx;

        .title_top_css {
          overflow: hidden;
          text-overflow: ellipsis;
          /* 超出部分省略号 */
          word-break: break-all;
          /* break-all(允许在单词内换行。) */
          display: -webkit-box;
          /* 对象作为伸缩盒子模型显示 */
          -webkit-box-orient: vertical;
          /* 设置或检索伸缩盒对象的子元素的排列方式 */
          -webkit-line-clamp: 2;
          /* 显示的行数 */
        }

        .price_css {
          margin-top: 10rpx;

          span {
            display: inline-block;
          }
        }
      }
    }
  }

  .popup_exChange {
    box-sizing: border-box;
    background-color: #fff;
    .exchange_center_text {
      text-align: center;
    }
    .header_css {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      // position: absolute;
      // top:10rpx;
      width: 600rpx;

      .title_css {
        width: 600rpx;
        text-align: center;
        font-size: 32rpx;
        font-weight: 600;
        line-height: 85rpx;
        padding-left: 20rpx;
        color: #11372e;
      }

      .close_css {
        position: absolute;
        top: 20rpx;
        right: 20rpx;
      }
    }

    .product_center_css {
      display: flex;
      justify-content: flex-start;
    }
  }

  .modal_popup_css {
    width: 686rpx;
    height: 502rpx;
    border-radius: 16rpx;
    position: relative;
    text-align: center;

    .flex-s {
      margin-top: 108rpx;
    }

    .modal_popup_close {
      width: 30rpx;
      height: 30rpx;
      position: absolute;
      right: 30rpx;
      top: 32rpx;
    }

    .modal_title_css {
      padding-top: 52rpx;
    }

    .modal_center_css {
      margin-top: 52rpx;
      line-height: 48rpx;
    }

    .cancel_button_css {
      border: 2rpx solid #428a6f;
      color: #428a6f;
    }

    .button_modal {
      width: 280rpx;
      text-align: center;
      padding: 24rpx 0;
      font-size: 32rpx;
      line-height: 40rpx;
      border-radius: 46rpx;
    }

    .determine_button_css {
      background-color: #428a6f;
      color: #fff;
    }
  }

  .success_popup {
    width: 686rpx;
    height: 800rpx;
    border-radius: 8rpx;
    padding-top: 20rpx;

    .payment_image_css {
      display: flex;
      justify-content: space-between;

      .image_icon {
        display: inline-block;
        width: 30rpx;
        height: 30rpx;
        vertical-align: middle;
      }

      .popup_title_css {
        font-size: 24rpx;
        line-height: 34rpx;
        color: #f27120;
        vertical-align: middle;
      }
    }

    .text_css {
      text-align: center;
    }

    .content_css_popup {
      height: 404rpx;
      background-size: 100%;
      background-repeat: no-repeat;

      .card_content_css {
        width: 485rpx;
        margin: 0 auto;
        padding-top: 65rpx;

        .card_good_name {
          height: 40rpx;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .card_exchange_code {
          width: 481rpx;
          word-break: break-all;
          white-space: pre-wrap;
          height: 77rpx;
        }

        .button_css {
          text-align: center;
        }
      }
    }

    .popup_button {
      width: 280rpx;
      text-align: center;
      padding: 24rpx 0;
      background-color: #428a6f;
      border-radius: 46rpx;
      margin: 0 auto;
    }

    .botton_css {
      text-align: center;
      color: #c4c4c4;
    }
  }

  .colorRed {
    color: #fa4f2b;
  }
</style>
