<template>
  <page-meta :page-style="'overflow:' + (rollShow ? 'hidden' : 'visible')"></page-meta>
  <view>
    <view class="detailTop">
      <view :class="detailsIndex === 0 ? 'orserdetails' : ''" @tap="courseOrder">课程订单</view>
      <view :class="detailsIndex === 1 ? 'orserdetails' : ''" @tap="memberorder">会员套餐订单</view>
      <view :class="detailsIndex === 2 ? 'orserdetails' : ''" @tap="historyOrder">会议订单</view>
    </view>
    <view class="" v-if="detailsIndex === 0">
      <u-sticky bgColor="#F3F8FC">
        <view class="pl-25">
          <u-tabs :list="payStatusList" :current="searchStatusIndex" keyName="name" lineWidth="40" lineHeight="11" :activeStyle="{ color: '#333333', fontWeight: 'bold', fontSize: '28rpx' }" :inactiveStyle="{
              color: '#5A5A5A ',
              transform: 'scale(1)',
              fontSize: '28rpx'
            }" itemStyle="padding-left:5px; padding-right: 25px; height: 34px;" :lineColor="`url(${lineBg}) 100% 110%`" @click="tabsClick"></u-tabs>
        </view>
      </u-sticky>
      <view class="mlr-30">
        <view class="plr-30 bg-ff radius-16 mt-30" @tap.stop="toDetail(item, 0)" v-for="(item, index) in listS.data" :key="index">
          <view class="flex order_s">
            <view class="f-30 c-33 flex-box fontWeight">订单编号：{{ item.orderNo }}</view>
            <!-- // 0:待支付 1:待发货 2:已完成 3:已取消 4:退款成功 5:退款中 6:退款失败 7:待收货 8:待评价 9已关闭 10:部分完成 11:支付失败 12:部分退款-->
            <text v-if="item.payStatus == 0" class="f-26 t-c radius-8 tobe_paid button_right_css">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 1" class="f-26 radius-8 t-c paid button_right_css">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 2" class="f-26 radius-8 t-c button_right_css completed">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 3" class="c-ff f-26 t-c button_right_css refunded">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 4" class="f-26 t-c button_right_css button-right-width completed">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 5" class="c-ff f-26 t-c button_right_css paid">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 6" class="f-26 t-c tobe_paid button-right-width button_right_css">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 7" class="f-26 t-c paid button_right_css">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 8" class="f-26 t-c paid button_right_css">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 9" class="c-ff f-26 t-c button_right_css refunded">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 10" class="c-ff f-26 t-c button_right_css button-right-width refunded">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 11" class="c-ff f-26 t-c button_right_css button-right-width tobe_paid">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 12" class="c-ff f-26 t-c button_right_css button-right-width paid">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 13" class="f-26 t-c radius-8 tobe_paid button_right_css">{{ item.payStatusName }}</text>
            <!-- payStatus -->
          </view>
          <view class="pb-40">
            <block v-for="(item2, index2) in item.piUserOrderGoodsList" :key="index2">
              <view class="flex-dir-row">
                <view class="image_order_css radius-10">
                  <image :src="item2.goodsPicUrl" class="wh100"></image>
                </view>
                <view class="flex-box ml-20">
                  <view class="f-28 bold lh-36 c-33 mb-18 spacing">{{ item2.goodsName }}</view>
                  <view class="f-28 c-8896" v-if="item2.goodsType == 3">
                    <text v-if="item2.specPriceQuantity > 0">
                      <text>自有课程：&nbsp;&nbsp;&nbsp;</text>
                      <text>{{ item2.specPriceQuantity }}</text>
                      ；
                    </text>
                    <text v-if="item2.specPriceTwoQuantity >= 0">
                      <text>交付课程：&nbsp;&nbsp;&nbsp;</text>
                      <text>{{ item2.specPriceTwoQuantity }}</text>
                      ；
                    </text>
                  </view>
                  <view class="f-28 c-8896" v-else>
                    <text v-if="item2.specLevelOneType">
                      <text>{{ item2.specLevelOneType }}:</text>
                      <text>{{ item2.specLevelOneName }}</text>
                      ；
                    </text>
                    <text v-if="item2.specLevelTwoType">
                      <text>{{ item2.specLevelTwoType }}:</text>
                      <text>{{ item2.specLevelTwoName }}</text>
                      ；
                    </text>
                    <text>
                      <text>数量：</text>
                      <text>{{ item2.purchaseQuantity }}</text>
                    </text>
                  </view>
                  <view class="flex mt-8">
                    <view class="flex mt-16 use_c">
                      <view>
                        <text v-if="item2.goodsType != 6" class="f-24 bold color_tangerine">
                          ¥
                          <text class="f-32">{{ item.payAmount }}</text>
                        </text>
                        <text v-else class="f-24 bold color_tangerine">
                          <text class="f-32">{{ item.payAmount }}</text>
                          <text class="c-55">鼎币</text>
                        </text>
                      </view>
                      <view class="use_t" v-if="item.source==1">微信小店</view>
                      <!-- <view class="use_t" v-if="item.payStatus == 1 || item.payStatus == 2" -->
                      <view class="use_t" v-if="item.orderStatus !== null" @click.stop="handleProgress(item)">查看操作流程</view>
                    </view>
                  </view>
                  <!-- <view class="mt-20 flex">
								<view class="c-2e8 f-30" v-if="item.payStatus==7">退款成功：￥{{item.refundAmount}}元</view>
							</view> -->
                  <view class="flex mt-8" v-if="(item.payStatus == 4 || item.payStatus == 12)&&item.finalTime">
                    <view class="flex use_c">
                      <view>
                        <text class="f-24">
                          退款时间：
                          <text class="f-32">{{ item.finalTime }}</text>
                        </text>
                      </view>
                    </view>
                  </view>
                  <view class="flex mt-8" v-else-if='item.payTime'>
                    <view class="flex use_c">
                      <view>
                        <text class="f-24">
                          付款时间：
                          <text class="f-32">{{ item.payTime }}</text>
                        </text>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
              <view v-if="item.piUserOrderGoodsList[0].goodsType == 1 && item.expressNo" class="order_css_style flexbox mt-24">
                <view class="f-24 c-55">物流单号：{{ item.expressNo }}</view>
                <view @tap.stop="copyOrderId(item.expressNo)" class="radius-8 copy_css f-24">复制单号</view>
              </view>
            </block>
            <view v-if="item.payStatus == 4" class="f-24 c-8896 t-r">退款时间：{{ item.finalTime || '-' }}</view>
          </view>
          <view class="flex">
            <view class="flex-box flex-dir-row flex-wrap border_t pt-30 pb-20" v-if="item.payStatus == 0">
              <view class="btnone f-28 t-c" @click.stop="cancel(item, 'class')">取消订单</view>
              <view class="btntwo f-28 c-ff t-c" @click.stop="payorder(item)">立即支付</view>
            </view>
            <!-- // 0:待支付 1:待发货 2:已完成 3:已取消 4:退款成功 5:退款中 6:退款失败 7:待收货 8:待评价 9已关闭-->
            <!-- <view class="border_c pt-30 pb-20" v-if="item.payStatus == 1 || item.payStatus == 2"> -->
            <view class="flex-box flex-dir-row flex-wrap border_t pt-30 pb-20" v-if="item.payStatus == 1&&item.piUserOrderGoodsList[0].goodsType==10&&item.finalGoodsId&&item.showFinalButton&&getTime(item.deadline)">
              <view class="btntwo f-28 c-ff t-c" @click.stop="balancePayment(item)">支付尾款</view>
            </view>
            <!-- // 0:待支付 1:待发货 2:已完成 3:已取消 4:退款成功 5:退款中 6:退款失败 7:待收货 8:待评价 9已关闭-->
            <!-- <view class="border_c pt-30 pb-20" v-if="item.payStatus == 1 || item.payStatus == 2"> -->
            <view class="border_c pt-30 pb-20" v-if="item.orderStatus !== null&&!(item.payStatus == 1&&item.piUserOrderGoodsList[0].goodsType==10&&item.finalGoodsId&&item.showFinalButton&&getTime(item.deadline))">
              <view class="t_all">

                状态：
                <text class="t_1">{{ item.orderStatusDesc }}</text>
              </view>
              <view class="dis_reverse" v-if="item.payStatus != 8 && item.payStatus != 2">
                <view class="btnone2 f-28 t-c" @click.stop="openCode(item)">添加专属推荐顾问</view>
                <view class="btntwo2 f-28 c-ff t-c" @click.stop="goZX()">鼎校甄选APP下载</view>
              </view>
            </view>
            <view class="flex b-z flex-x-e w100" v-if="item.payStatus == 8">
              <view class="flex-dir-row b-z pb-20">
                <view class="btnone f-28 c-ff t-c" @click.stop="skintap('splitContent/order/evaluate?type=1&orderNo=' + item.orderNo)">评价</view>
                <!-- <view v-if="item.isEvaluate==0|| item.isTwoEvaluate==0" class="btnone f-28 c-ff t-c" :class="(item.isEvaluate==0 || item.isTwoEvaluate==0)&&item.payStatus==2 && item.isEvaluate!=2?'mr-30':''" @click.stop="skintap(`splitContent/order/viewComments?type=${item.isTwoEvaluate==1?1:2}&orderNo=`+item.orderId)">查看评价</view>
                            <view v-if="(item.isEvaluate==0 || item.isEvaluate==2 ) && item.isTwoEvaluate==1&&item.payStatus==2" class="btnone f-28 c-ff t-c" @click.stop="skintap('splitContent/order/evaluate?type=2&orderNo='+item.orderId)">追加评价</view>
                            <view v-if="item.isEvaluate==1" class="btnone f-28 c-ff t-c" @click.stop="skintap('splitContent/order/evaluate?type=1&orderNo='+item.orderId)">评价</view> -->
              </view>
              <!-- <view v-if="item.cateType == 2&&item.payStatus == 1" class="f-32 c-ff t-c ml-30" @click.stop="gotoVerify(item)">展示核销码</view> -->
            </view>
            <view v-if="item.cateType != 2">
              <view class="flex-box flex-dir-row flex-x-b flex-wrap border_t pt-30 pb-20" v-if="item.type == 1 && item.payStatus == 2 && item.piUserOrderGoodsList[0] != null && item.piUserOrderGoodsList[0].courseLabel == 1">
                <view></view>
                <view class="btntwo f-28 c-ff t-c" @click.stop="gotrial(item)">查看试课单</view>
              </view>
            </view>
            <!-- // 0:待支付 1:待发货 2:已完成 3:已取消 4:退款成功 5:退款中 6:退款失败 7:待收货 8:待评价-->
            <view class="flex b-z flex-x-e w100" v-if="item.payStatus == 7 && item.piUserOrderGoodsList[0].goodsType == 1">
              <view class="flex-dir-row b-z pb-20">
                <view @click.stop="confirmTakeGood(item.orderId)" class="btntwo f-28 c-ff t-c">确认收货</view>
              </view>
              <!-- <view v-if="item.cateType == 2&&item.payStatus == 1" class="f-32 c-ff t-c ml-30" @click.stop="gotoVerify(item)">展示核销码</view> -->
            </view>
            <view v-if="item.type == 2 && item.isExpress == 0" class="flex-a-c flex-x-e border_t pt-30 pb-20">
              <view class="btntwo f-32 c-ff t-c" @click.stop="lookLogistics(item)">查看物流</view>
            </view>
          </view>
        </view>
        <view v-if="listS.data != undefined && listS.data.length == 0" class="t-c flex-col" :style="{ height: useHeight + 'rpx' }">
          <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
          <view style="color: #bdbdbd">暂无数据</view>
        </view>
        <view v-if="no_more && listS.data != undefined && listS.data.length > 0">
          <u-divider text="到底了"></u-divider>
        </view>
      </view>
    </view>
    <view class="" v-if="detailsIndex === 1">
      <u-sticky bgColor="#F3F8FC">
        <view class="pl-25">
          <u-tabs :list="memberpayStatusList" :current="memberStatusIndex" keyName="name" lineWidth="40" lineHeight="11" :activeStyle="{ color: '#333333', fontWeight: 'bold', fontSize: '28rpx' }" :inactiveStyle="{ color: '#5A5A5A ', transform: 'scale(1)', fontSize: '28rpx' }" itemStyle="padding-left:5px; padding-right: 25px; height: 34px;" :lineColor="`url(${lineBg}) 100% 110%`" @click="memberClick"></u-tabs>
        </view>
      </u-sticky>
      <view class="mlr-30">
        <view class="plr-30 bg-ff radius-16 mt-30" @tap.stop="toDetail(item, 1)" v-for="(item, index) in memberlistS.list" :key="index">
          <view class="flex order_s">
            <view class="f-30 c-33 flex-box fontWeight">订单编号：{{ item.orderNo }}</view>
            <text v-if="item.payStatus == 0" class="f-26 radius-8 t-c paid button_right_css">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 1" class="f-26 radius-8 t-c paid button_right_css">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 4" class="f-26 t-c radius-8 tobe_paid button_right_css">{{ item.payStatusName }}</text>
          </view>
          <view class="pb-40">
            <block>
              <view class="flex-dir-row">
                <view class="image_order_css radius-10">
                  <image :src="item.goodsPicUrl" class="wh100"></image>
                </view>
                <view class="flex-box ml-20">
                  <view class="f-28 bold lh-36 c-33 mb-18 spacing">{{ item.commissionMealName }}</view>
                  <view class="f-28 c-8896">
                    <text>
                      <text>数量：</text>
                      <text>{{ item.mealNum }}</text>
                    </text>
                  </view>
                  <view class="flex mt-8">
                    <text class="f-24 bold color_tangerine">
                      ¥
                      <text class="f-32">{{ item.commissionMealPrice }}</text>
                    </text>
                  </view>
                </view>
              </view>
            </block>
          </view>
          <view class="flex">
            <view class="flex-box flex-dir-row flex-wrap border_t pt-30 pb-20" v-if="item.payStatus == 0">
              <!-- <view class="btnone f-28 t-c" @click.stop="vipCancel(item, 'vip')">取消订单</view> -->
              <view class="btntwo f-28 c-ff t-c" @click.stop="vipPayOrder(item)">立即支付</view>
            </view>
          </view>
        </view>
      </view>
      <view v-if="memberlistS.list != undefined && memberlistS.list.length == 0" class="t-c flex-col" :style="{ height: useHeight + 'rpx' }">
        <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
        <view style="color: #bdbdbd">暂无数据</view>
      </view>
      <view v-if="memberno_more && memberlistS.list != undefined && memberlistS.list.length > 0">
        <u-divider text="到底了"></u-divider>
      </view>
    </view>
    <view class="" v-if="detailsIndex === 2">
      <view class="mlr-30">
        <view class="plr-30 bg-ff radius-16 mt-30" v-for="(item, index) in historyListS.list" :key="index">
          <view class="flex order_s">
            <view class="f-30 c-33 flex-box fontWeight">订单编号：{{ item.orderNo }}</view>
            <!-- // 0:待支付 1:待发货 2:已完成 3:已取消 4:退款成功 5:退款中 6:退款失败 7:待收货 8:待评价 9已关闭-->
            <text v-if="item.payStatus == 0" class="f-26 t-c radius-8 tobe_paid button_right_css">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 1" class="f-26 radius-8 t-c paid button_right_css">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 2" class="f-26 radius-8 t-c button_right_css completed">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 3" class="c-ff f-26 t-c button_right_css refunded">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 4" class="f-26 t-c button_right_css button-right-width completed">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 5" class="c-ff f-26 t-c button_right_css paid">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 6" class="f-26 t-c tobe_paid button_right_css">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 7" class="f-26 t-c paid button_right_css">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 8" class="f-26 t-c paid button_right_css">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 9" class="c-ff f-26 t-c button_right_css refunded">{{ item.payStatusName }}</text>
            <text v-if="item.payStatus == 12" class="c-ff f-26 t-c button_right_css button-right-width paid">{{ item.payStatusName }}</text>
            <!-- payStatus -->
          </view>
          <view class="pb-40">
            <block v-for="(item2, index2) in item.orderCourseList" :key="index2">
              <view class="flex-dir-row">
                <view class="image_order_css radius-10">
                  <image :src="item2.courseImage" class="wh100"></image>
                </view>
                <view class="flex-box ml-20">
                  <view class="f-28 bold lh-36 c-33 mb-18 spacing">{{ item2.courseName }}</view>

                  <view class="f-28 c-8896">
                    <text>
                      <text>数量：</text>
                      <text>{{ item2.buyNumber }}</text>
                    </text>
                  </view>
                  <view class="flex mt-8">
                    <view class="flex mt-8">
                      <text class="f-24 bold color_tangerine">
                        ¥
                        <text class="f-32">{{ item2.payAmount }}</text>
                      </text>
                    </view>
                  </view>
                </view>
              </view>
            </block>
            <view v-if="item.payStatus == 4" class="f-24 c-8896 t-r">退款时间：{{ item.finalTime || '-' }}</view>
          </view>
          <view class="flex">
            <!-- 	<view class="flex-box flex-dir-row flex-wrap border_t pt-30 pb-20" v-if="item.payStatus==0">
							<view class="btnone f-28 t-c" @click.stop="cancel(item)">取消订单</view>
							<view class="btntwo f-28 c-ff t-c" @click.stop="payorder(item)">立即支付</view>
						</view> -->
            <!-- 	<view class="flex b-z flex-x-e w100" v-if="item.payStatus==8">
							<view class="flex-dir-row b-z pb-20">
								<view class="btnone f-28 c-ff t-c"
									@click.stop="skintap('splitContent/order/evaluate?type=1&orderNo='+item.orderNo)">评价
								</view>
							</view>
						</view> -->
            <!-- 						<view v-if="item.cateType != 2">
							<view class="flex-box flex-dir-row flex-x-b flex-wrap border_t pt-30 pb-20"
								v-if="item.type==1 && item.payStatus == 2 && item.piUserOrderGoodsList[0] != null && item.piUserOrderGoodsList[0].courseLabel ==1">
								<view></view>
								<view class="btntwo f-28 c-ff t-c" @click.stop="gotrial(item)">查看试课单</view>
							</view>
						</view> -->
            <!-- // 0:待支付 1:待发货 2:已完成 3:已取消 4:退款成功 5:退款中 6:退款失败 7:待收货 8:待评价-->
            <view class="flex b-z flex-x-e w100" v-if="item.payStatus == 7 && item.piUserOrderGoodsList[0].goodsType == 1">
              <view class="flex-dir-row b-z pb-20">
                <view @click.stop="confirmTakeGood(item.orderId)" class="btntwo f-28 c-ff t-c">确认收货</view>
              </view>
            </view>
            <view v-if="item.type == 2 && item.isExpress == 0" class="flex-a-c flex-x-e border_t pt-30 pb-20">
              <view class="btntwo f-32 c-ff t-c" @click.stop="lookLogistics(item)">查看物流</view>
            </view>

            <view v-if="item.orderCourseList[0] && item.orderCourseList[0].qrCodeImage && [1, 2].includes(item.payStatus)" class="flex-a-c flex-x-e border_t pt-30 pb-20 w100">
              <view class="btnone f-28 t-c" @click.stop="handleView(item.orderCourseList[0])">查看联系</view>
            </view>
          </view>
        </view>
        <view v-if="historyListS.list != undefined && historyListS.list.length == 0" class="t-c flex-col" :style="{ height: useHeight + 'rpx' }">
          <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
          <view style="color: #bdbdbd">暂无数据</view>
        </view>
        <view v-if="history_more && historyListS.list != undefined && historyListS.list.length > 0">
          <u-divider text="到底了"></u-divider>
        </view>
      </view>
    </view>

    <view class="positionRelative">
      <uni-popup ref="studentPopup" type="center" @touchmove.stop.prevent="moveHandle" @change="changePopup">
        <view>
          <image class="cartoom_image" src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
        </view>
        <view class="review_close" @click="incoChange">
          <uni-icons type="clear" size="26" color="#B1B1B1"></uni-icons>
        </view>
        <view class="f-30 p-40 positionRelative bg-ff radius-15">
          <view class="f-34 t-c bold mb-40">选择学员</view>
          <view class="details">
            学员名称：
            <uni-data-select class="downbox" v-model="studentName" :localdata="arrayStudent" @change="choose"></uni-data-select>
          </view>
          <view class="details">
            门店名称：
            <uni-data-select class="downbox" v-model="studentSchool" :localdata="arraySchool" @change="choice"></uni-data-select>
          </view>
          <view class="flex_x">
            是否需要教练师：
            <view class="ml-20 flex_x">
              <uniDateCheckbox v-model="radio" :localdata="range" selectedColor="#1D755C" @change="change"></uniDateCheckbox>
            </view>
          </view>
          <view class="mt-40">学员剩余学时：{{ surplus || 0 }} 时</view>
          <view class="determine" @click="close">
            <button class="phone-btn">使用</button>
          </view>
        </view>
      </uni-popup>
    </view>
    <!-- 添加专属顾问二维码弹出 -->
    <view class="positionRelative">
      <uni-popup ref="CodeSale" type="center" @touchmove.stop.prevent="moveHandle" @change="changePopup">
        <view>
          <image class="cartoom_image" src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
        </view>
        <view class="review_close" @click="closeCode">
          <uni-icons type="clear" size="26" color="#B1B1B1"></uni-icons>
        </view>
        <view class="cardCode">
          <view v-if="codeForm.qrType === 'zxapp'" class="f-30 bold c-33">
            下载
            <text style="color: #116254">鼎校甄选APP</text>
            ，即可加入上课专属服务群
          </view>
          <view v-else class="f-34 bold c-33 spacing">添加专属推荐顾问</view>
          <view class="">请长按识别二维码</view>
          <!-- 无推荐人 展示-固定小助手账号二维码 -->
          <!-- <image class="codeImg" :src="codeForm.qrCode" mode="" show-menu-by-longpress="true"></image> -->
          <!-- 有推荐人 展示-推荐人企微账号二维码 -->
          <image class="codeImg" :src="codeForm.qrCode" mode="" show-menu-by-longpress="true"></image>
          <view class="btntwo3 f-28 c-ff t-c" @click.stop="saveCode()">保存二维码</view>
        </view>
      </uni-popup>
    </view>
  </view>
</template>

<script>
const { $navigationTo, $http } = require('@/util/methods.js');
const { httpUser } = require('@/util/luch-request/indexUser.js');
import Config from '@/util/config.js';
import uniDateCheckbox from '../components/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox.vue';
export default {
  components: {
    uniDateCheckbox
  },
  data() {
    return {
      priceDetail: {}, // 价格详情
      addPrices: 0,
      codeForm: {
        qrCode: ''
      },
      payInfo: {},
      flag1: false,
      rollShow: false, //禁止滚动穿透
      tabindex: -1,
      payStatus: -1,
      listS: {},
      memberlistS: {},
      page: 1,
      memberpage: 1,
      historyPage: 1,
      no_more: false,
      memberno_more: false,
      history_more: false,
      historyListS: {},
      meetPage: 1,
      meetListData: {},
      meetMore: false,
      current: 0,
      disabled: false,
      show: false, // 选择学员
      arraySchool: [],
      arrayStudent: [],
      detailsIndex: 0,
      merchantCode: '',
      studentCode: '',
      surplus: '', //剩余学时
      orderId: '',
      isDeliver: 1, // 1需要教练，0不需要教练
      radio: 1,
      range: [
        {
          text: '需要教练',
          value: 1
        },
        {
          text: '不用了',
          value: 0
        }
      ],
      studentName: '',
      studentSchool: '',
      searchOrderId: '',
      useHeight: 0, //除头部之外高度
      imgHost: getApp().globalData.imgsomeHost,
      payStatusIndex: 0,
      // 全部 待支付 已支付  已完成 已失效
      payStatusList: [
        {
          name: '全部'
        },
        {
          name: '待支付'
        },
        {
          name: '已支付'
        },
        {
          name: '已完成'
        },
        {
          name: '已失效'
        }
      ],
      memberpayStatusList: [
        {
          name: '待支付'
        },
        {
          name: '已支付'
        },
        {
          name: '已完成'
        }
      ],
      searchStatusIndex: 0,
      memberStatusIndex: 0,
      lineBg: 'https://document.dxznjy.com/course/01c96465d9ea46f69aa97465b1201aef.png',
      searchStatusArr: [
        {
          value: 0,
          label: '全部'
        },
        {
          value: 1,
          label: '课程'
        },
        {
          value: 2,
          label: '会议'
        }
      ],
      currentOrderId: ''
    };
  },
  onLoad(e) {
    this.tabindex = e.type || -1;
    if (this.tabindex == 1 && e.orderId) {
      this.searchOrderId = e.orderId;
      uni.showLoading();
    }
    uni.startPullDownRefresh();
  },
  onUnload() {
    // uni.removeStorageSync('isDyy');
  },
  onShow() {
    if (this.flag1) {
      uni.$tlpayResult(this.sucees, this.fail, this.payInfo.orderId);
    }
    this.page = 1;
    this.tabsClick({
      index: this.searchStatusIndex,
      ...this.payStatusList[this.searchStatusIndex]
    });
  },
  onReady() {
    uni.getSystemInfo({
      success: (res) => {
        // 可使用窗口高度，将px转换rpx
        let h = res.windowHeight * (750 / res.windowWidth);
        this.useHeight = h - 15;
      }
    });
  },
  onReachBottom() {
    if (this.detailsIndex === 0) {
      if (this.page >= this.listS.totalPage) {
        this.no_more = true;
        return false;
      }
      this.list(true, ++this.page);
    }
    if (this.detailsIndex === 1) {
      if (this.memberpage >= this.memberlistS.totalPage) {
        this.memberno_more = true;
        return false;
      }
      this.memberList(true, ++this.memberpage);
    }
    if (this.detailsIndex === 2) {
      if (this.historyPage >= this.historyListS.totalPage) {
        this.history_more = true;
        return false;
      }
      this.historyList(true, ++this.historyPage);
    }
  },

  onPullDownRefresh() {
    // this.list();
    if (this.detailsIndex == 0) {
      this.tabsClick({
        index: this.searchStatusIndex,
        ...this.payStatusList[this.searchStatusIndex]
      });
    } else if (this.detailsIndex == 1) {
      this.memberClick({
        index: this.memberStatusIndex,
        ...this.memberpayStatusList[this.memberStatusIndex]
      });
    } else if (this.detailsIndex == 2) {
      this.historyPage = 1;
      this.historyList();
    }

    setTimeout(function () {
      uni.stopPullDownRefresh();
    }, 1000);
  },

  methods: {
    sucees() {
      this.flag1 = false;
      uni.redirectTo({
        url: '/splitContent/order/order'
      });
    },
    courseOrder() {
      this.detailsIndex = 0;
      this.searchStatusIndex = 0;
      this.showLoading();
      this.list();
    },
    memberorder() {
      this.detailsIndex = 1;
      this.memberStatusIndex = 0;
      this.showLoading();
      this.memberList();
    },
    historyOrder() {
      this.detailsIndex = 2;
      this.memberStatusIndex = 0;
      this.showLoading();
      this.historyList();
    },
    fail(type) {
      this.flag1 = false;
      if (type === 'cancel') {
        uni.navigateTo({
          url: `/splitContent/order/payCancel?orderId=${this.currentOrderId}&cancelType=${this.detailsIndex == 0 ? 'class' : 'vip'}&type=list`
        });
      }
    },
    fails() {
      uni.showToast({
        title: '支付失败',
        icon: 'none',
        duration: 2000
      });
      this.flag1 = false;
    },
    // 禁止滚动穿透
    changePopup(e) {
      this.rollShow = e.show;
    },
    change(e) {
      this.isDeliver = e.detail.data.value;
    },
    bindPickerChange(e) {
      this.payStatusIndex = Number(e.target.value);
    },
    moveHandle: function () {
      return false;
    },
    // 获取推荐人二维码测试
    async getReferenceQrCode(item) {
      let that = this;
      that.codeForm.qrCode = '';
      uni.showLoading();
      let res = await httpUser.get('zx/wap/order/getReferenceQrCode', {
        orderId: item
      });
      uni.hideLoading();
      that.codeForm.qrCode = res.data.data;
    },
    // 获取固定小助手账号二维码
    async getAssistantQrCode(item) {
      console.log('获取固定小助手账号二维码');
      uni.showLoading();
      let that = this;
      let res = await httpUser.get('zx/wap/order/getReferenceQrCode', {
        orderId: '1353812205784817664'
        // orderId: item
      });
      uni.hideLoading();
      that.codeForm.qrCode = res.data.data;
    },

    //打开二维码添加专属推荐顾问
    openCode(item) {
      console.log('打开二维码添加专属推荐顾问item', item);
      this.$refs.CodeSale.open();
      this.getReferenceQrCode(item.orderId);
      // if (item.hasReferrer == true) {
      //   this.getReferenceQrCode(item.orderId);
      // } else {
      //   this.getAssistantQrCode(item.orderId);
      // }
    },
    //鼎校甄选APP下载页面跳转
    goZX() {
      console.log('甄选页面跳转zx-share.dxznjy');
      uni.navigateTo({
        url: '/Coursedetails/tips/webview?url=' + encodeURIComponent(Config.zxDownloadH5Url)
        // url: '/splitContent/webview/webview?url=' + encodeURIComponent('http://*************:9020')
        // url: '/Coursedetails/tips/webview?url=' + encodeURIComponent('https://www.pgyer.com/BXYLDBVA')
        // url: '/Coursedetails/tips/webview?url=' + encodeURIComponent('https://zx-share.dxznjy.com/')
      });
    },
    handleProgress(item) {
      console.log('🚀 ~ handleProgress ~ item:', item.piUserOrderGoodsList.goodsType);
      // uni.redirectTo({
      uni.navigateTo({
        url: `/Coursedetails/tips/trialProcess?orderStatus=${item.orderStatus}&orderStatusDesc=${item.orderStatusDesc}&goodsType=${item.piUserOrderGoodsList[0].goodsType}`
      });
    },
    closeCode() {
      this.$refs.CodeSale.close();
    },
    saveCode() {
      let that = this;
      uni.downloadFile({
        url: that.codeForm.qrCode,
        success: (res) => {
          console.log('res', res);
          if (res.statusCode === 200) {
            // #ifdef MP-WEIXIN
            // 手动加后缀
            const filePath = res.tempFilePath;
            const newFilePath = `${wx.env.USER_DATA_PATH}/qrcode.png`; // 改成你想要的文件名和后缀
            // 使用 FileSystemManager 复制并加后缀
            const fs = wx.getFileSystemManager();
            fs.copyFile({
              srcPath: filePath,
              destPath: newFilePath,

              success: () => {
                uni.saveImageToPhotosAlbum({
                  filePath: newFilePath,
                  success: () => {
                    uni.showToast({
                      title: '图片已保存',
                      duration: 2000
                    });
                  },
                  fail: (err) => {
                    uni.showToast({
                      title: '保存失败',
                      duration: 2000,
                      icon: 'none'
                    });
                  }
                });
              },
              fail: (err) => {
                console.error('文件重命名失败', err);
                uni.showToast({
                  title: '处理失败',
                  duration: 2000,
                  icon: 'none'
                });
              }
            });
            // #endif
            // #ifndef MP-WEIXIN
            uni.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: function () {
                uni.showToast({
                  title: '图片已保存',
                  duration: 2000
                });
              },
              fail: function () {
                uni.showToast({
                  title: '保存失败',
                  duration: 2000,
                  icon: 'none'
                });
              }
            });
            // #endif
          }
        },
        fail: () => {
          uni.showToast({
            title: '下载失败',
            duration: 2000,
            icon: 'none'
          });
        }
      });
    },
    gotrial(item) {
      let orderId = item.orderId;
      let payStatus = item.payStatus;
      uni.navigateTo({
        url: '/Trialclass/index?orderId=' + orderId + '&payStatus=' + payStatus + '&curriculumId=' + item.curriculumId
      });
    },
    copyOrderId(val) {
      uni.setClipboardData({
        data: val,
        success: function (res) {
          uni.getClipboardData({
            success: function (res) {
              uni.showToast({
                title: '复制成功'
              });
            }
          });
        }
      });
    },
    showLoading() {
      uni.showLoading({
        title: '加载中',
        mask: true
      });
    },
    // <!-- // 0:待支付 1:待发货 2:已完成 3:已取消 4:退款成功 5:退款中 6:退款失败 7:待收货 8:待评价 9已关闭-->
    tabsClick(e) {
      this.showLoading();
      this.searchStatusIndex = e.index;
      let status = '';
      if (e.name == '全部') {
        this.list();
      } else if (e.name == '待支付') {
        status = '0';
        this.list(false, 1, status);
      } else if (e.name == '已支付') {
        status = '1,5,6,7,8';
        this.list(false, 1, status);
      } else if (e.name == '已完成') {
        status = '2';
        this.list(false, 1, status);
      } else if (e.name == '已失效') {
        status = '3,4,9';
        this.list(false, 1, status);
      }
    },
    memberClick(e) {
      this.showLoading();
      this.memberStatusIndex = e.index;
      let memberstatus = '';
      if (e.name == '已支付') {
        memberstatus = '1';
        this.memberList(false, 1, memberstatus);
      } else if (e.name == '已完成') {
        memberstatus = '4';
        this.memberList(false, 1, memberstatus);
      } else if (e.name == '待支付') {
        memberstatus = '0';
        this.memberList(false, 1, memberstatus);
      }
    },
    // 打开弹框
    // open(id) {
    //     this.$refs.studentPopup.open();
    //     this.orderId = id;
    //     this.getStudent();
    // },

    // incoChange() {
    //     this.studentName = '';
    //     this.studentSchool = '';
    //     this.$refs.studentPopup.close();
    // },

    // 确定按钮（关闭弹框）
    // close() {
    //     this.getHoursConvert();
    // },
    //选择学员
    // choose(e) {
    //     this.studentCode = e;
    //     if (this.studentCode != '') {
    //         this.chooseStudentlist();
    //     }
    //     this.merchantCode = '';
    // },
    //选择门店
    // choice(e) {
    //     this.merchantCode = e;
    // },
    //选择学员兑换课时
    // async getHoursConvert() {
    //     let that = this;
    //     uni.showLoading();
    //     const res = await $http({
    //         url: 'zx/order/hoursConvert?orderId=' + that.orderId + '&merchantCode=' + that
    //             .merchantCode + '&studentCode=' + that.studentCode + '&isDeliver=' + that.isDeliver,
    //         method: 'POST',
    //     })
    //     uni.hideLoading();
    //     if (res) {
    //         this.$refs.studentPopup.close()
    //         uni.redirectTo({
    //             url: '/pages/home/<USER>/order?type=2'
    //         })
    //     }
    // },
    //点击选择体验店
    // async chooseStudentlist(e) {
    //     let that = this;
    //     httpUser.get("znyy/review/query/my/studentAndMerchant?studentCode=" + that.studentCode).then(
    //         result => {
    //             if (result.data.success) {
    //                 let list = result.data.data;
    //                 that.arraySchool = []
    //                 list.forEach((item) => that.arraySchool.push({
    //                     value: item.merchantCode,
    //                     text: item.merchantName,
    //                 }))
    //                 if (that.arraySchool.length > 0) {
    //                     that.merchantCode = that.arraySchool[0].value
    //                 }
    //                 that.getClasshour()
    //             }
    //         })
    // },
    // 去订单详情页面
    toDetail(item, type) {
      uni.navigateTo({
        url: `/splitContent/order/orderdetail?`
      });
      uni.setStorageSync('orderType', type);
      uni.setStorageSync('orderDetail', JSON.stringify(item));
    },
    // tab(e) {
    //     this.tabindex = e;
    //     this.page = 1;
    //     this.no_more = false;
    //     this.list();
    //     // console.log(this.listS.list);
    // },
    //获取学员剩余课时数量
    // async getClasshour() {
    //     let that = this;
    //     let res = await httpUser.get("zx/order/getHoursConvertInfo", {
    //         merchantCode: that.merchantCode,
    //         studentCode: that.studentCode
    //     });
    //     if (res.data.status == 1) {
    //         that.surplus = res.data.data;
    //     }
    // },

    // 获取学员列表
    // async getStudent() {
    //     let that = this;
    //     let loginMobile = uni.getStorageSync('LoginMobile');
    //     that.StudentCodeKey = "review_" + loginMobile;
    //     let result = await httpUser.get("znyy/review/query/my/student");
    //     if (result != undefined && result != '') {
    //         if (result.data.data != null) {
    //             let list = result.data.data;
    //             that.arrayStudent = [];
    //             list.forEach((item) => that.arrayStudent.push({
    //                 value: item.studentCode,
    //                 text: item.realName,
    //             }))
    //         }
    //     }
    // },

    async successorder(orderId) {
      let _this = this;
      const resdata = await $http({
        url: 'zx/course/orderPayNoMoney/' + orderId,
        data: {}
      });
      if (resdata) {
        _this.list();
      }
    },
    getTime(deadline) {
      const targetDate = new Date(deadline);
      const currentDate = new Date();

      // 检查 targetDate 是否为无效日期
      if (isNaN(targetDate.getTime())) {
        console.error('提供的目标时间格式无效');
        return false; // 或者根据你的需求返回其他值，比如抛出异常
      }

      return targetDate > currentDate;
    },
    balancePayment(item) {
      if (this.getTime(item.deadline)) {
        $navigationTo('Coursedetails/productDetils?id=' + item.finalGoodsId)
      } else {
        uni.showToast({
          title: '页面停留时间过长，请刷新页面重试',
          icon: 'none',
          duration: 2000
        });
        this.courseOrder();
      }
    },

    // 套餐订单支付
    async payorder(item) {
      uni.setStorageSync('orderId_zx', item.orderId);
      // 默认为false
      uni.setStorageSync('isDyy', false);
      let _this = this;
      if (item.isDyy && item.piUserOrderGoodsList[0]?.goodsType == 3) {
        // 鼎英语上架
        uni.setStorageSync('isDyy', true);
        switch (item.level) {
          case 'xiaoxue':
            this.periodIndex = 0;
            break;
          case 'chuzhong':
            this.periodIndex = 1;
            break;
          case 'gaozhong':
            this.periodIndex = 2;
            break;
        }
        uni.showLoading();
        const res3 = await $http({
          url: 'znyy/bSysConfig/list/course/level/price/zx',
          methods: 'get'
        });
        _this.priceDetail = res3.data?.[0]?.selectResultList ?? [];
        _this.priceDetail.forEach((item) => {
          item.extArr = JSON.parse(item.ext);
          item.price = item.value.split('#')[1];
        });
        const res2 = await $http({
          url: 'znyy/bSysConfig/course/profit/price/zx',
          method: 'get'
        });
        const adjustPrice = Number(res2.data?.adjustCoursePrice || 0) / 100;
        const periodPrice = Number(_this.priceDetail[_this.periodIndex]?.price || 0);
        _this.addPrices = (adjustPrice + periodPrice).toFixed(2);

        let params = {};
        params.studentCode = item.studentCode; // 支付账户
        params.deliverMode = 'CENTER'; // 交付方式
        params.level = item.level; // 学段
        params.courseLength = item.piUserOrderGoodsList[0]?.specPriceQuantity || 0; // 自有课时数量
        params.deliverCourseLength = item.piUserOrderGoodsList[0]?.specPriceTwoQuantity || 0; // 交付课时数量
        params.coursePrice = this.addPrices; // 自有课时单价
        params.deliverCoursePrice = this.priceDetail[this.periodIndex].extArr.CENTER / 100; // 交付课时单价
        params.totalPrice = params.deliverCoursePrice * params.deliverCourseLength + params.coursePrice * params.courseLength; // 合计金额
        params.remark = item.remark; // 充值说明
        params.orderSource = 1; // 订单来源
        params.zxOrderId = item.orderId; // 订单ID
        params.merchantCode = item.merchantCode; // 商户编码
        params.schoolType = item.schoolType; // 校区类型
        uni.showLoading();
        let res = await $http({
          url: 'znyy/areas/student/charge/course/save/zx',
          method: 'post',
          data: params
        });
        res.data.lineCollectInfo.collectUserCodes = JSON.parse(res.data.lineCollectInfo.userCollectDetail);
        this.payBtn(res.data.lineCollectInfo);
      } else {
        if (_this.disabled) {
          return false;
        }
        uni.showLoading();
        _this.disabled = true;
        let resdata;
        _this.disabled = false;
        resdata = await $http({
          url: 'zx/wap/order/continue/pay',
          method: 'POST',
          data: {
            orderId: item.orderId,
            orderGroup: item.orderGroup
          }
        });
        _this.disabled = false;
        if (resdata.data.needPay == 1) {
          _this.payBtn(resdata.data, item.orderId);
        } else {
          // _this.successorder(res.data.orderId);
        }
      }
      // else{
      //     if(resdata.status == 10021){
      //         const resdata = await $http({
      //             url: 'zx/order/courseOrderCancel',
      //             data: {
      //                 orderId: item.orderId,
      //             }
      //         })
      //         if (resdata) {
      //             _this.list();
      //         }
      //     }
      // }
    },

    // async payBtn(data) {
    // 	let _this = this
    //     let resdata = await httpUser.post('mps/line/collect/order/unified/collect', data)
    // 	let res = resdata.data.data
    // 	_this.disabled = false
    // 	if (res) {
    // 		uni.requestPayment({
    // 			provider: 'wxpay',
    // 			timeStamp: res.payInfo.timeStamp,
    // 			nonceStr: res.payInfo.nonceStr,
    // 			package: res.payInfo.packageX,
    // 			signType: res.payInfo.signType,
    // 			paySign: res.payInfo.paySign,
    // 			success: function(ress) {
    // 				console.log('支付成功');
    // 				// _this.successpay(id);
    // 			},
    // 			fail: function(err) {
    // 				uni.showToast({
    // 					title: '支付失败',
    // 					icon: 'none',
    // 					duration: 2000
    // 				})
    // 			}
    // 		})
    // 	}
    // },

    // 课程立即支付
    // async payorder(id) {
    //     let _this = this;
    //     // if (_this.disabled) {
    //     // 	return false
    //     // }
    //     wx.showLoading()
    //     _this.disabled = true
    //     const resdata = await $http({
    //         url: 'zx/course/orderPayAnew/' + id
    //     })
    //     uni.hideLoading()
    //     _this.disabled = false
    //     if (resdata) {
    //         _this.payBtn(resdata.data, id)
    //     }
    // },

    async payBtn(data, orderId) {
      uni.showLoading();
      let _this = this;
      let resdata = await httpUser.post('mps/line/collect/order/unified/multi/collect/check', data);
      let res = resdata.data.data;
      _this.disabled = false;
      uni.hideLoading();
      if (res) {
        if (res.openAllinPayMini) {
          this.flag1 = true;
          this.payInfo = res;
          this.currentOrderId = orderId;
          uni.$payTlian(res);
        } else {
          uni.requestPayment({
            provider: 'wxpay',
            timeStamp: res.payInfo.timeStamp,
            nonceStr: res.payInfo.nonceStr,
            package: res.payInfo.packageX,
            signType: res.payInfo.signType,
            paySign: res.payInfo.paySign,
            success: function (ress) {
              console.log('支付成功');
              uni.redirectTo({
                url: '/splitContent/order/order'
              });
            },
            fail: function (err) {
              uni.showToast({
                title: '支付失败',
                icon: 'none',
                duration: 2000
              });
            }
          });
        }
      }
    },

    async list(isPage, page, status) {
      let _this = this;
      let parms = {
        pageNum: page || 1,
        pageSize: 20,
        orderGroup: '2,3',
        checkHistory: 0
      };
      // payStatus支付状态  -1 全部 0 待付款 1 已支付&待使用 2 已完成(已核销
      // payStatus: _this.tabindex,
      if (status) {
        parms.payStatusListString = status;
      }
      const res = await $http({
        url: 'zx/wap/order/usr/list',
        data: parms
      });
      if (res) {
        if (isPage) {
          let old = _this.listS.data;
          _this.listS.data = [...old, ...res.data.data];
        } else {
          _this.listS = res.data;
        }
        if (_this.tabindex == 1 && _this.searchOrderId != '') {
          let hasSeacherOrderId = false;
          for (var i = 0; i < _this.listS.data.length; i++) {
            if (_this.listS.data[i].orderId == _this.searchOrderId) {
              hasSeacherOrderId = true;
            }
          }
          if (!hasSeacherOrderId) {
            _this.list();
          } else {
            uni.hideLoading();
          }
        }
      }
    },
    async memberList(isPage, page, status) {
      let _this = this;
      let parms = {
        pageNum: page || 1,
        pageSize: 20,
        payStatus: '0'
      };
      if (status) {
        parms.payStatus = status;
      }
      const res = await $http({
        url: 'zx/order/memberMealOrderList',
        data: parms
      });
      if (res) {
        if (isPage) {
          res.data.list.forEach((item) => {
            item.goodsPicUrl = item.commissionMealName.includes('家长会员')
              ? 'https://document.dxznjy.com/course/4f2392fff8cf4359b8e174a76644a779.png'
              : 'https://document.dxznjy.com/course/a5913641f1c64dbf994112d767ef28b8.png';
          });
          let old = _this.memberlistS.list;
          _this.memberlistS.list = [...old, ...res.data.list];
        } else {
          _this.memberlistS = res.data;
          _this.memberlistS.list.forEach((item) => {
            item.goodsPicUrl = item.commissionMealName.includes('家长会员')
              ? 'https://document.dxznjy.com/course/4f2392fff8cf4359b8e174a76644a779.png'
              : 'https://document.dxznjy.com/course/a5913641f1c64dbf994112d767ef28b8.png';
          });
        }
        console.log(_this.memberlistS.list);
        /* 	if (_this.tabindex == 1 && _this.searchOrderId != '') {
let hasSeacherOrderId = false;
for (var i = 0; i < _this.listS.data.length; i++) {
if (_this.memberlistS.data[i].orderId == _this.searchOrderId) {
hasSeacherOrderId = true;
}
}
if (!hasSeacherOrderId) {
_this.list();
} else {
uni.hideLoading();
}
} */
      }
    },
    //历史订单
    async historyList(isPage, page) {
      this.showLoading();
      let _this = this;
      let parms = {
        pageNum: page || 1,
        pageSize: 10,
        cateType: 0
      };
      const res = await $http({
        url: 'zx/order/userCourseOrderList',
        data: parms
      });
      if (res) {
        if (isPage) {
          let old = _this.historyListS.list;
          _this.historyListS.list = [...old, ...res.data.list];
        } else {
          _this.historyListS = res.data;
        }
      }
      uni.hideLoading();
    },
    async getMeetList(isPage, page) {
      this.showLoading();
      let _this = this;
      let parms = {
        pageNum: page || 1,
        pageSize: 100,
        userId: uni.getStorageSync('user_id')
      };
      const res = await $http({
        url: 'zxAdminCourse/course/learningCampList',
        data: parms
      });
      if (res) {
        if (isPage) {
          let old = _this.meetListData.list;
          _this.meetListData.list = [...old, ...res.data];
        } else {
          _this.meetListData = {
            curPage: 1,
            list: res.data,
            pageSize: 100,
            totalCount: res.data.length
          };
        }
      }
      uni.hideLoading();
    },
    //确认收货
    async confirmTakeGood(id) {
      uni.showLoading();
      let _this = this;
      let resdata = await $http({
        url: 'zx/wap/order/confirm',
        method: 'POST',
        data: {
          orderId: id
        }
      });
      if (resdata) {
        _this.tabsClick({
          index: _this.searchStatusIndex,
          ..._this.payStatusList[_this.searchStatusIndex]
        });
        uni.showToast({
          title: '收货成功',
          icon: 'none',
          duration: 2000
        });
      }
    },
    // 取消订单
    async cancel(item, type) {
      let _this = this;
      uni.showLoading({
        title: '取消中',
        mask: true
      });
      setTimeout(() => {
        uni.showModal({
          title: '取消订单',
          content: '是否确定取消订单？',
          cancelText: '再想想',
          success: async function (res) {
            if (res.confirm) {
              const resdata = await $http({
                url: 'zx/wap/order/cancel',
                method: 'POST',
                data: {
                  orderId: item.orderId,
                  orderGroup: item.orderGroup
                }
              });
              // 取消成功跳转填写原因
              if (resdata.code === 20000) {
                uni.navigateTo({
                  url: `/splitContent/order/orderCancel?orderId=${item.orderId}&cancelType=${type}&type=list`
                });
                return;
              }
              if (resdata) {
                _this.tabsClick({
                  index: _this.memberStatusIndex,
                  ..._this.memberpayStatusList[_this.memberStatusIndex]
                });
              }
            } else if (res.cancel) {
              uni.hideLoading();
              console.log('用户点击取消');
            }
          },
          fail: function () {
            uni.hideLoading();
          }
        });
      }, 5000);
    },
    /** 会员套餐取消 */
    async vipCancel(item, type) {
      let _this = this;
      uni.showModal({
        title: '取消订单',
        content: '是否确定取消订单？',
        cancelText: '再想想',
        success: async function (res) {
          if (res.confirm) {
            const resdata = await $http({
              url: 'zx/order/mealOrderCancel',
              data: {
                orderId: item.orderMealId
              }
            });
            // 取消成功跳转填写原因
            if (resdata.status === 1) {
              uni.navigateTo({
                url: `/splitContent/order/orderCancel?orderId=${item.orderMealId}&cancelType=${type}&type=list`
              });
              return;
            }
          } else if (res.cancel) {
            console.log('用户点击取消');
          }
        }
      });
    },
    async vipPayOrder(item) {
      let _this = this;
      if (_this.disabled) {
        return false;
      }
      this.showLoading();
      _this.disabled = true;
      const resdata = await $http({
        url: 'zx/meal/orderPayAnew/' + item.orderMealId
      });
      _this.disabled = false;
      if (resdata) {
        _this.payBtn(resdata.data, item.orderMealId);
      }
    },
    // 查看物流
    lookLogistics(item) {
      uni.navigateTo({
        url: `/splitContent/meal/logistics?orderId=${item.orderId}`
      });
    },
    handleView(item) {
      console.log(item, '0000000000000');
      uni.navigateTo({
        url: `/Personalcenter/my/meetingServiceQRcode?qrCodeImage=${item.qrCodeImage}&meetingQrCodeDesc=${item.meetingQrCodeDesc}`
      });
    },

    goViewcommments(item) {
      // if(item.type)
    },

    skintap(url) {
      $navigationTo(url);
    },

    gotoVerify(item) {
      this.skintap(`meeting/meetVerifyInfo?isType=0&orderId=${item.orderId}`);
    }
    // pickerSearchStatus(e) {
    //     this.searchStatusIndex = e.target.value;
    //     this.list();
    // },
  }
};
</script>

<style lang="scss">
.orserdetails {
  border-bottom: 4rpx solid #339378;
  font-weight: 700;
}

.detailTop {
  margin: 20rpx 0;
  display: flex;
  justify-content: space-evenly;
}

.c-8896 {
  color: #888896;
}

.picker_css {
  width: 150rpx;
  margin: 0 auto;
}

.order_css_style {
  background-color: #f8fff7;
  padding: 6rpx 26rpx;

  .copy_css {
    width: 120rpx;
    height: 48rpx;
    line-height: 48rpx;
    text-align: center;
    color: #469880;
    border: 2rpx solid #469880;
  }
}

.changing-over {
  background-color: #ea6031;
  width: 30rpx;
  height: 4rpx;
}

.image_order_css {
  width: 144rpx;
  height: 160rpx;
}

.order_s {
  // border-bottom: 1px dashed #eee;
  padding: 32rpx 0;
}

.button_right_css {
  width: 90rpx;
  height: 36rpx;
  padding: 4rpx 14rpx;
  border-radius: 4rpx;
  line-height: 36rpx;
}

.button-right-width {
  width: 110rpx !important;
}

.tobe_paid {
  background-color: #fff0ec;
  color: #fa380e;
}

.paid {
  width: 110rpx !important;
  background-color: #fff2e3;
  color: #fd9b29;
}

.completed {
  background-color: #eaffe7;
  color: #469880;
}

.channel {
  background-color: #c6c6c6;
}

.refund {
  background-color: #ea6031;
}

.drawback {
  background-color: #bf61de;
}

.refunded {
  background-color: #f7f7f7;
  color: #888896;
}

.meet-paid {
  background-color: #dfe9ff;
  color: #3e7afb;
}

.spacing {
  letter-spacing: 4rpx;
  -webkit-line-clamp: 2;
  overflow: hidden;
  -webkit-box-orient: vertical;
  display: -webkit-box;
  word-break: break-all;
}

.border_t {
  border-top: 1px dashed #eee;
  justify-content: flex-end;
}

.border_c {
  border-top: 1px dashed #eee;
  width: 100%;
}

.btntwo {
  background-image: linear-gradient(to bottom, #88cfba, #1d755c);
  padding: 0 64rpx;
  height: 60rpx;
  border-radius: 60rpx;
  line-height: 60rpx;
}

.btntwo2 {
  background-image: linear-gradient(to bottom, #88cfba, #1d755c);
  padding: 0 36rpx;
  height: 60rpx;
  // width: 280rpx;
  border-radius: 60rpx;
  line-height: 60rpx;
  box-sizing: border-box;
  white-space: nowrap;
}

.btntwo3 {
  background-image: linear-gradient(to bottom, #88cfba, #1d755c);
  padding: 4rpx 50rpx;
  border-radius: 60rpx;
  line-height: 60rpx;
  box-sizing: border-box;
}

.btnone {
  height: 60rpx;
  line-height: 60rpx;
  color: #4e9f87;
  border: 2rpx solid #4e9f87;
  padding: 0 55rpx;
  border-radius: 60rpx;
  margin-right: 24rpx;
}

.btnone2 {
  height: 60rpx;
  // width: 280rpx;
  line-height: 60rpx;
  color: #4e9f87;
  border: 2rpx solid #4e9f87;
  padding: 0 36rpx;
  border-radius: 60rpx;
  margin-left: 48rpx;
  box-sizing: border-box;
  white-space: nowrap;
}

.flex_s {
  text-align: center;
  vertical-align: middle;
  // margin-top: 200rpx;
}

.flex_x {
  display: flex;
  align-items: center;
}

.img_s {
  width: 160rpx;
}

.b-f {
  border-top: 1px dashed #e0e0e0;
}

.border-t {
  border-top: 1px dashed #eee;
}

.tabs {
  // padding: 30rpx 0;
  width: 100%;
}

.tabs text {
  font-size: 34upx;
  color: #666;
}

.tabs .active {
  color: #000;
  font-weight: bold;
}

.tabs .unchecked {
  color: #666;
  font-size: 32rpx;
}

/deep/.u-radio__text {
  color: #000 !important;
  font-size: 28rpx !important;
}

/deep/.data-v-643b3322 {
  color: #000 !important;
  font-size: 28rpx !important;
}

/deep/.u-radio {
  margin-bottom: 0 !important;
  margin-right: 20rpx;
}

.choice {
  /* 		width: 100%;
		height: 100rpx;
		line-height: 100rpx; */
  border-radius: 30rpx;
  padding: 10rpx 15rpx;
  font-size: 24rpx;
  color: #fff;
  /* margin-top: 400rpx; */
  background: linear-gradient(to right, rgb(244, 142, 110), rgb(246, 76, 83));
}

.details {
  display: flex;
  line-height: 60rpx;
  height: 60rpx;
  margin-bottom: 45rpx;
  font-style: 30rpx;
  border: 0;
}

/deep/.uni-select {
  width: 420rpx !important;
  height: 70rpx !important;
  line-height: 70rpx !important;
  border-radius: 35rpx !important;
  padding: 2 20rpx !important;
}

.determine {
  padding: 0 166rpx;
}

/deep/.phone-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 45rpx;
  margin-top: 80rpx;
  font-size: 30rpx;
  color: #fff;
  background: linear-gradient(to bottom, #88cfba, #1d755c);
}

/deep/.u-safe-area-inset-bottom {
  padding-bottom: 0;
}

.order_tips {
  color: #666;
  font-size: 26rpx;
  line-height: 48rpx;
  margin-top: 15rpx;
}

.plr-30 {
  position: relative;
}

/deep/.btntrial {
  width: 160rpx;
  height: 60rpx;
  line-height: 60rpx;
  text-align: center;
  border-radius: 30rpx;
  font-size: 32rpx;
  color: #fff;
  background: linear-gradient(to bottom, #88cfba, #1d755c);
}

/deep/.uniui-closeempty {
  position: absolute;
  right: 30rpx;
  top: 30rpx;
}

.cartoom_image {
  width: 420rpx;
  position: absolute;
  top: -250rpx;
  left: 145rpx;
  z-index: -1;
}

.review_close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  z-index: 1;
}

.uni-list-cell {
  display: flex;
  margin-left: 20rpx;
}

.flex_s {
  display: flex;
}

/deep/.checklist-box {
  margin-right: 30rpx !important;
}

/deep/.u-sticky {
  z-index: 1 !important;
}

.content-fail {
  border-radius: 30rpx;
  padding: 110rpx 260rpx;
}

.success-img {
  width: 110rpx;
  height: 110rpx;
}

.t_all {
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  box-sizing: border-box;
  letter-spacing: 2rpx;
  font-size: 30rpx;
}

.t_1 {
  color: #048264;
}

.use_c {
  width: 100%;
  padding-right: 10rpx;
  box-sizing: border-box;
}

.use_t {
  color: #4e9f87;
  letter-spacing: 2rpx;
  padding: 20rpx 0 10rpx 0;
}

.cardCode {
  width: 670rpx;
  height: 681rpx;
  background-color: #ffffff;
  border-radius: 24rpx;
  display: flex;
  padding-top: 60rpx;
  padding-bottom: 30rpx;
  box-sizing: border-box;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
}

.codeImg {
  width: 360rpx;
  height: 360rpx;
}
</style>
