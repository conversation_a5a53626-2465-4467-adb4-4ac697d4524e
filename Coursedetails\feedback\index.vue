<template>
  <view class="space">
    <!-- 自定义导航栏 -->
    <view class="box-bg">
      <uni-nav-bar backgroundColor="#F3F8FC">
        <block slot="left">
          <view class="left-icon" @click="back">
            <uni-icons type="back" color="#666" size="26" />
          </view>
        </block>
        <view class="tabs-view">
          <u-tabs
            :current="current"
            :list="list"
            @click="tabIndex"
            @change="change"
            lineWidth="0"
            lineHeight="0"
            :activeStyle="{
              color: '#303133',
              fontWeight: 'bold'
            }"
          ></u-tabs>
        </view>
      </uni-nav-bar>
    </view>
    <!-- 自定义导航栏 -->
    <view class="content plr-30">
      <view v-if="current == 0">
        <study :userInfo="userInfo"></study>
      </view>
      <view v-else>
        <review :userInfo="userInfo"></review>
      </view>
    </view>
  </view>
</template>
<script>
  import Study from '@/Coursedetails/feedback/components/study.vue';
  import Review from '@/Coursedetails/feedback/components/review.vue';
  export default {
    components: {
      Study,
      Review
    },
    data() {
      return {
        list: [
          {
            name: '学习反馈'
          },
          {
            name: '复习反馈'
          }
        ],
        current: 0, // 0学习反馈 1复习反馈
        userInfo: {},
        type: '' // 类型
      };
    },
    onLoad(option) {
      this.userInfo = JSON.parse(option.data);
      console.log(this.userInfo);
      this.type = this.userInfo.type;
      if (this.type == 2) {
        this.current = 1;
      }
    },
    methods: {
      // 左侧按钮返回上一页
      back() {
        uni.navigateBack();
      },
      // tabs栏切换
      tabIndex(e) {
        this.current = e.index;
      },
      change() {
        if (this.type === 2) {
          this.current == 1;
        } else {
          this.current == 0;
        }
      },
      onPullDownRefresh() {
        console.log('onPullDownRefresh');
        setTimeout(function () {
          uni.stopPullDownRefresh();
          console.log('stopPullDownRefresh');
        }, 1000);
      }
    }
  };
</script>

<style lang="scss" scoped>
  $nav-height: 30px;

  // 导航栏
  .box-bg {
    height: 140rpx;

    /deep/.uni-navbar__header {
      position: fixed;
      padding-bottom: 20rpx;
      width: 100%;
      height: 140rpx;
      z-index: 999;
    }

    .left-icon {
      margin-top: 100rpx;
    }

    .tabs-view {
      margin-top: 90rpx;

      /deep/.u-tabs__wrapper__nav__item {
        padding: 0 35rpx;
      }

      /deep/.u-tabs__wrapper__nav__item__text {
        font-size: 36rpx !important;
      }
    }
  }

  // 内容
  .content {
    padding-bottom: 30rpx;
  }
</style>
