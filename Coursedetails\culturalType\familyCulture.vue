<template>
  <view class="bg-ff family_content_css">
    <view class="w100 family_banner_main plr-32">
      <swiper
        :autoplay="true"
        indicator-color="rgba(227, 231, 230, 1)"
        indicator-active-color="#04614B"
        class="swiper_css"
        :indicator-dots="indicatorDots"
        :current="bannerIndex"
        :interval="3000"
        :duration="1000"
      >
        <block v-for="(item, index) in bannerList" :key="index">
          <swiper-item class="flex-c radius-16 swiper_css_item">
            <image :src="item.bannerPicUrl" mode="aspectFill" class="wh100" lazy-load="true" @tap="bannerTab(item)"></image>
          </swiper-item>
          <!-- #04614B -->
        </block>
      </swiper>
    </view>
    <view class="mt-24">
      <view class="plr-32 mb-30">
        <u-tabs
          :list="familyList"
          :current="familyCurrent"
          keyName="name"
          lineWidth="40"
          lineHeight="11"
          :activeStyle="{ color: '#333333', fontWeight: 'bold', fontSize: '28rpx' }"
          :inactiveStyle="{
            color: '#5A5A5A ',
            transform: 'scale(1)',
            fontSize: '28rpx'
          }"
          itemStyle="padding-left:1px; padding-right: 18px; height: 42px;"
          :lineColor="`url(${lineBg}) 100% 110%`"
          @click="familyClick"
        ></u-tabs>
      </view>
      <scroll-view
        :scroll-top="scrollTop"
        class="family_content_style"
        @scrolltolower="scrolltolower"
        @scroll="scroll"
        :show-scrollbar="false"
        bounces
        :throttle="false"
        scroll-with-animation
        scroll-anchoring
        scroll-y
        enhanced
      >
        <view v-if="familyCurrent == 0">
          <view v-for="(item, index) in recordedLessons" class="plr-32" :key="index">
            <view class="f-28 c-55 lh-40 mt-35">
              {{ item.goodsName }}
            </view>
            <view v-for="(info, i) in item.goodsCatalogueList" :key="i" class="flex-x-s flex-a-c mt-24" @click="goVideo(info)">
              <view class="image_left_content positionRelative">
                <image class="imagele radius-8" :src="item.goodsPicUrl"></image>
                <image class="video_play positionAbsolute" src="https://document.dxznjy.com/course/25d0f57150cf4aeaac8cd9a7489b7a9d.png"></image>
              </view>
              <view class="ml-15 right_record_css">
                <view class="f-28 c-55 lh-40">{{ item.goodsName }}</view>
                <view class="f-24 c-55 lh-36">{{ info.catalogueName }}</view>
                <view class="mt-15">
                  <u-line-progress activeColor="#339378" :showText="false" :percentage="info.learningProgress" height="16"></u-line-progress>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view v-if="familyCurrent == 1">
          <view class="answer_content_css mt-24 plr-32" v-if="questionsAnswersList.length > 0">
            <questionsAnswers
              @refreshInfo="refreshInfo"
              @addThumb="addThumb"
              questionsType="1"
              @buyAnswers="buyAnswers"
              v-for="(item, index) in questionsAnswersList"
              @hearAnswers="hearAnswers"
              :key="index"
              :questionsAnswersInfo="item"
            ></questionsAnswers>
          </view>
        </view>
        <view v-if="familyCurrent == 2">
          <view class="pb-70">
            <view v-for="item in releaseSelecte" :key="item.id" class="plr-32 pb-20 pt-24 border_color">
              <releaseItem @showImage="showImage" releaseType="3" :showLike="true" :releaseStyle="releaseStyle" :releaseInfo="item"></releaseItem>
            </view>
            <suspensionBtn @release="release" :showIcon="true" :fixedText="fixedText"></suspensionBtn>
          </view>
        </view>
        <view v-if="familyCurrent == 3" class="mt-24">
          <view class="answer_content_css plr-32">
            <questionsAnswers
              @refreshInfo="refreshInfo"
              :showLike="true"
              questionsType="2"
              @addRadius="addRadius"
              v-for="(item, index) in questionsAnswersList"
              @hearAnswers="addHearAnswers"
              :key="index"
              :itemIndex="index"
              :questionsAnswersInfo="item"
            ></questionsAnswers>
          </view>
        </view>
        <view
          class="pt-28"
          v-if="
            (familyCurrent == 0 && recordedLessons.length == 0) ||
            (familyCurrent == 1 && questionsAnswersList.length == 0) ||
            (familyCurrent == 2 && releaseSelecte.length == 0) ||
            (familyCurrent == 3 && questionsAnswersList.length == 0)
          "
        >
          <emptyPage></emptyPage>
        </view>
      </scroll-view>
      <view class="bottom_button_css" v-if="familyCurrent == 1">
        <button class="butoton_content_css c-ff lh-40 f-28" @click="goQuestions()">我要提问</button>
        <view @click="costClick" class="f-28 left_text_css ml-12">费用说明</view>
      </view>
    </view>
    <uni-popup ref="video_popup" type="center" style="padding: 0">
      <view class="bg-00 video_content_css">
        <view class="tips_content_close" @click="closeVideo()">
          <u-icon name="close-circle-fill" color="#B1B1B1" size="38"></u-icon>
        </view>
        <view style="width: 100%; height: 450rpx">
          <!-- 	<polyv-player id="polyv_player"  :defaultQuality="startTime" :startTime="startTime"  @loadedmetadata="bindloadedmetadata" @ended="bindEnded"  @pause="bindpause" :autoplay="true" :playerId="playerIdcont" :vid="videoInfo.videoUrl" :width="width" :height="height"
				 :ts="ts" :sign="sign">
				</polyv-player> -->
          <polyv-player
            id="polyv_player"
            :defaultQuality="startTime"
            @timeupdate="onBindtimeupdate"
            :autoplay="true"
            :startTime="startTime"
            :playerId="playerIdcont"
            :vid="videoInfo.videoUrl"
            :width="width"
            :height="height"
            :ts="ts"
            :sign="sign"
            @pause="bindpause"
            @playing="bindplaying"
            @loadedmetadata="bindloadedmetadata"
            @statechange="statechange"
          ></polyv-player>
        </view>
      </view>
    </uni-popup>
    <tipsContentPopup ref="tipsContentPopupRefs" @payAnswers="payAnswers" :tipsType="tipsType"></tipsContentPopup>
    <uni-popup ref="cost_content_popup" type="center" style="padding: 0">
      <view class="cost_content_css lh-32">
        <view class="f-22 plr-15 bg-ff radius-30">
          <view>
            专家问答板块付费说明: 欢迎来到“鼎校甄选--家庭文化”问答板块，一个汇聚智慧交流与专业解答的知识殿堂。
            为了让您的求知之旅更加便捷与高效，我们特设以下付费规则，确保每位用户都能享受到高质量的体验。
          </view>
          一、会员权益:
          <br />
          ·每月免费提问机会:作为尊贵的家长会员，您每月享有1次免费向专家提问的机会，探索知识无界限。
          <br />
          ·免费聆听: 无需额外费用，您可以无限畅听其他家长会员的提问及其对应的专家解答，拓宽视野，共享智慧。
          <br />
          二、超过免费次数的提问费用:
          <br />
          ·家长会员提问费用:若您的提问次数超过1次免费机会，作为家长会员，每次提问仅需支付会员价5.9元/次，即可持 续解锁专业解答。
          <br />
          ·非会员用户提问费用:对于非会员用户，提问费用为9.9元/次，让您也能轻松触及知识巅峰。
          <br />
          三、聆听他人提问的费用:
          <br />
          ·普通用户聆听费:如果您是普通用户，对其他会员提问及专家回答感兴趣，每次聆听的费用为1元，经济实惠，学海 无涯。
          <br />
          四、支付方式:
          <br />
          我们支持微信支付，确保您的交易安全无忧。
          <br />
          温馨提醒:
          <br />
          ·请合理安排您的提问机会，充分利用免费资源。如有任何疑问或需要帮助，我们的客服团队24小时在线，随时准备 为您提供服务。
          <br />
          ·让每一次提问都成为成长的阶梯，让学习之路因互动而生动，因解答而通达。
          <br />
        </view>
        <!-- <view class="f-22 plr-15">专家问答板块付费说明:
				欢迎来到“鼎校甄选--家庭文化”问答板块，一个汇聚智慧交流与专业解答的知识殿堂。
				为了让您的求知之旅更加便捷与高效，我们特设以下付费规则，确保每位用户都能享受到高质量的体验。
				<view class="pl-10">一、会员权益:·每月免费提问机会:作为尊贵的家长会员，您每月享有1次免费向专家提问的机会，探索知识无界限。
				，免费聆听: 无需额外费用，您可以无限畅听其他家长会员的提问及其对应的专家解答，拓宽视野，共享智慧。
				</view>
				<view class="pl-10">二、超过免费次数的提问费用:·家长会员提问费用:若您的提问次数超过1次免费机会，作为家长会员，每次提问仅需支付会员价5.9元/次，即可持
				续解锁专业解答。
				·非会员用户提问费用:对于非会员用户，提问费用为9.9元/次，让您也能轻松触及知识峰。
				</view>
				<view class="pl-10">三、聆听他人提问的费用:·普通用户聆听费: 如果您是普通用户，对其他会员提问及专家回答感兴趣，每次聆听的费用为1元，经济实惠，学海
				无涯。</view>
				
				<view class="pl-10">四、支付方式:我们支持微信支付，确保您的交易安全无忧。</view>
				<view class="pl-10">温弊提醒:</view>
				·请合理安排您的提问机会，充分利用免费资源。如有任何疑问或需要帮助，我们的客服团队24小时在线，随时准备
				为您提供服务。
				·让每一次提问都成为成长的阶梯，让学习之路因互动而生动，因解答而通达。
				</view> -->
        <view class="close_circle" @click="$refs.cost_content_popup.close()">
          <u-icon name="close-circle" color="#b1b1b1" size="38"></u-icon>
        </view>
      </view>
    </uni-popup>
    <growthPopup ref="growthPopupRefs"></growthPopup>
  </view>
</template>

<script>
  const { $navigationTo, $http } = require('@/util/methods.js');
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  import questionsAnswers from '../components/questionsAnswers.vue';
  import releaseItem from '../components/releaseItem.vue';
  import suspensionBtn from '../components/suspensionBtn.vue';
  import tipsContentPopup from '../components/tipsContentPopup.vue';
  import growthPopup from '../components/growthPopup.vue';
  import emptyPage from '../components/emptyPage.vue';
  const MD5 = require('../../util/md5.js');
  let secretkey = 'Jkk4ml1Of8';
  let vid = '';
  let ts = new Date().getTime();
  let sign = '';
  export default {
    components: { questionsAnswers, releaseItem, suspensionBtn, tipsContentPopup, growthPopup, emptyPage },
    data() {
      return {
        familyList: [
          { name: '家庭文化录播课', key: 1 },
          { name: '问答专题', key: 2 },
          { name: '温馨时刻', key: 3 }
        ],
        domId: 'polyvPlayer',
        playerIdcont: 'polyvPlayercont',
        startTime: 0,
        ts: ts,
        sign: sign,
        width: '100%',
        height: '100%',
        videoInfo: {},
        bannerIndex: 0,
        bannerList: [],
        indicatorDots: true,
        tipsType: 1,
        lineBg: 'https://document.dxznjy.com/course/01c96465d9ea46f69aa97465b1201aef.png',
        fixedText: '我要上传',
        infoLists: {},
        familyCurrent: 0,
        //温馨时刻
        releaseSelecte: [],
        releaseStyle: {
          leftImageWidth: '100rpx'
        },
        addThumbFalse: false,
        recordedLessons: [],
        payAnswersInfo: {},
        questionsAnswersList: [],
        identityType: uni.getStorageSync('identityType'),
        flag1: false,
        studyTime: 0,
        payInfo: {},
        findPriceInfo: {},
        scrollTop: 0,
        scrollTopNum: 0,
        page: 1,
        showImageType: false
      };
    },
    onLoad(e) {
      this.banner();
      this.getIfGreatMaster();
      this.getFindPrice();
      this.familyCurrent = e.familyCurrent >= 0 ? e.familyCurrent : this.familyCurrent;
    },
    onShow() {
      this.page = 1;
      this.scrollTop = 0;
      if (this.flag1) {
        uni.$tlpayResult(this.sucees, this.fail, this.payInfo.orderId);
      }
      if (this.showImageType) {
        this.showImageType = false;
        return;
      }
      this.familyClick({ index: this.familyCurrent, inKey: -1 });
    },
    onUnload() {
      if (this.$refs.video_popup.showPopup) {
        this.closeVideo();
      }
    },
    methods: {
      scrolltolower() {
        if (this.page * 10 >= this.infoLists.totalItems) {
          return false;
        }
        if (this.familyCurrent == 2) {
          this.getCultureCircle(true, ++this.page);
        } else if (this.familyCurrent == 0) {
          this.getFindRecordedCoursesList(true, ++this.page);
        } else if (this.familyCurrent == 1) {
          this.getQaList(0, true, ++this.page);
        } else {
          this.getQaList(1, true, ++this.page);
        }
      },
      scroll(e) {
        this.scrollTopNum = e.detail.scrollTop;
      },
      async getFindPrice() {
        let _this = this;
        const res = await $http({
          url: 'zx/wap/qa/findPrice',
          data: {}
        });
        if (res) {
          this.findPriceInfo = res.data;
        }
      },
      sucees() {
        this.flag1 = false;
        this.paySuccessFun();
      },
      fail() {
        this.flag1 = false;
        uni.redirectTo({
          url: '/splitContent/order/order'
        });
      },
      fails() {
        uni.showToast({
          title: '支付失败',
          icon: 'none',
          duration: 2000
        });
        uni.redirectTo({
          url: '/splitContent/order/order'
        });
        this.flag1 = false;
      },
      // //点赞
      async addThumb(info, key) {
        if (this.addThumbFalse) {
          return;
        }
        let url = '';
        if (key == 1) {
          url = 'zx/wap/CultureCircle/cancelThumb';
        } else {
          url = 'zx/wap/CultureCircle/thumb';
        }
        this.addThumbFalse = true;
        const res = await $http({
          url: url,
          method: 'POST',
          showLoading: true,
          data: {
            topicId: info.id,
            topicType: 'QUESTION_ANSWER_PREFECTURE',
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        if (res) {
          this.familyClick({ index: this.familyCurrent });
        }
      },
      paySuccessFun() {
        uni.showLoading({
          title: '加载答案中...',
          mask: true
        });
        let _this = this;
        setTimeout(() => {
          uni.redirectTo({
            url: '/splitContent/order/order'
          });
          uni.hideLoading();
        }, 1000);
      },
      refreshInfo() {
        this.familyClick({ index: this.familyCurrent });
      },
      buyAnswers(info) {
        this.payAnswersInfo = info;
        this.tipsType = 1;
        this.$refs.tipsContentPopupRefs.open();
      },
      async payAnswers() {
        uni.showLoading({
          title: '支付中，请稍后'
        });
        let _this = this;
        const res = await $http({
          url: 'zx/wap/qa/generate',
          method: 'POST',
          data: {
            gender: _this.payAnswersInfo.gender,
            amount: _this.findPriceInfo.nonMemberQuestionPrice,
            age: _this.payAnswersInfo.age,
            questionId: _this.payAnswersInfo.id,
            questionText: _this.payAnswersInfo.questionText,
            type: 1
          }
        });
        if (res) {
          if (res.data.needPay == 1) {
            this.payBtn(res.data.applyPayDto);
          }
        }
      },
      async payBtn(data) {
        let _this = this;
        let resdata = await httpUser.post('mps/line/collect/order/unified/collect', data);
        let res = resdata.data.data;
        _this.disabled = false;
        uni.hideLoading();
        if (res) {
          if (res.openAllinPayMini) {
            this.flag1 = true;
            this.payInfo = res;
            uni.$payTlian(res);
          } else {
            uni.requestPayment({
              provider: 'wxpay',
              timeStamp: res.payInfo.timeStamp,
              nonceStr: res.payInfo.nonceStr,
              package: res.payInfo.packageX,
              signType: res.payInfo.signType,
              paySign: res.payInfo.paySign,
              success: function (ress) {
                uni.showToast({
                  title: '支付成功'
                });
                this.paySuccessFun();
              },
              fail: function (err) {
                uni.showToast({
                  title: '支付失败'
                });
                setTimeout(function () {
                  uni.redirectTo({
                    url: '/splitContent/order/order'
                  });
                }, 1500);
              }
            });
          }
        }
      },
      async banner() {
        let _this = this;
        const res = await $http({
          url: 'zx/wap/layout/banner/list',
          showLoading: true,
          data: {
            bannerPosition: 3
          }
        });
        if (res) {
          _this.bannerList = res.data;
          console.log(_this.bannerList);
        }
      },
      // // /zx/wap/qa/ifGreatMaster
      async getIfGreatMaster() {
        const res = await $http({
          url: 'zx/wap/qa/ifGreatMaster',
          data: {}
        });
        if (res) {
          if (res.data) {
            this.familyList = [
              { name: '家庭文化录播课', key: 1 },
              { name: '问答专题', key: 2 },
              { name: '温馨时刻', key: 3 },
              { name: '回答问题', key: 4 }
            ];
          } else {
            this.familyList = [
              { name: '家庭文化录播课', key: 1 },
              { name: '问答专题', key: 2 },
              { name: '温馨时刻', key: 3 }
            ];
          }
        }
      },
      showImage(list, index) {
        this.showImageType = true;
        console.log('...................................................');
        let photoList = list.map((item) => {
          return item.url;
        });
        uni.previewImage({
          urls: photoList,
          current: index,
          indicator: 'default'
        });
      },
      async getCultureCircle(isPage, page) {
        // page = page || 1;
        let _this = this;
        const res = await $http({
          url: 'zx/wap/CultureCircle',
          showLoading: true,
          data: {
            topicType: 'FAMILY_CULTURE',
            pageNum: page || 1,
            pageSize: 10,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        if (res) {
          this.addThumbFalse = false;
          this.infoLists = res.data;
          if (isPage == -1) {
            _this.scrollTop = this.scrollTopNum;
            this.$nextTick(() => {
              _this.scrollTop = 0;
            });
          }
          if (isPage && isPage != -1) {
            this.releaseSelecte = [...this.releaseSelecte, ...res.data.data];
          } else {
            this.releaseSelecte = res.data.data || [];
          }
        }
      },
      async getFindRecordedCoursesList(isPage, page) {
        let _this = this;
        const res = await $http({
          url: 'zx/wap/recorded/course/findRecordedCoursesList',
          showLoading: true,
          data: {
            courseType: 1,
            pageNum: page || 1,
            pageSize: 10
          }
        });
        if (res) {
          this.infoLists = res.data;
          if (isPage == -1) {
            _this.scrollTop = this.scrollTopNum;
            this.$nextTick(() => {
              _this.scrollTop = 0;
            });
          }
          if (isPage && isPage != -1) {
            this.recordedLessons = [...this.recordedLessons, ...res.data.data];
          } else {
            this.recordedLessons = res.data.data || [];
          }
          this.addThumbFalse = false;
        }
      },
      //
      bannerTab(item) {
        if (item.needLogin == 0 && !uni.getStorageSync('token')) {
          uni.navigateTo({
            url: '/Personalcenter/login/login'
          });
        } else {
          if (item.goodsId) {
            $navigationTo('Coursedetails/productDetils?id=' + item.goodsId);
          } else {
            $navigationTo(item.bannerLinkUrl);
          }
        }
      },
      familyClick(e) {
        if (e.index != this.familyCurrent) {
          this.questionsAnswersList = [];
        }
        this.familyCurrent = e.index;
        if (this.familyCurrent == 2) {
          this.page = 1;
          this.getCultureCircle(e.inKey);
        } else if (this.familyCurrent == 0) {
          let that = this;
          setTimeout(() => {
            that.getFindRecordedCoursesList(-1);
          }, 200);
        } else if (this.familyCurrent == 1) {
          this.questionsAnswersList = [];
          this.getQaList(0);
        } else {
          this.questionsAnswersList = [];
          this.getQaList(1);
        }
      },
      // /zx/wap/qa/getQaList
      async getQaList(type, isPage, page) {
        uni.showLoading({
          title: '加载中...',
          mask: true
        });
        let _this = this;
        const res = await $http({
          url: 'zx/wap/qa/getQaList',
          showLoading: true,
          data: {
            type: type,
            pageNum: page || 1,
            pageSize: 10
          }
        });
        if (res) {
          this.infoLists = res.data;
          if (isPage) {
            this.questionsAnswersList = [...this.questionsAnswersList, ...res.data.data];
          } else {
            this.questionsAnswersList = res.data.data || [];
          }
          this.addThumbFalse = false;
          uni.hideLoading();
        }
      },
      //视频
      onBindtimeupdate(currentTime) {},
      bindplaying() {
        console.log('开始开始开始');
      },
      bindpause() {},
      statechange(e) {},
      goVideo(info) {
        if (this.identityType == 4) {
          // "culturalType/palyVideo
          uni.navigateTo({
            url: '/Coursedetails/culturalType/palyVideo?eventType=LEARN'
          });
          uni.setStorageSync('videoInfo', info);
          // this.$refs.video_popup.open()
        } else {
          this.$refs.tipsContentPopupRefs.open();
          this.tipsType = 2;
        }
      },
      bindEnded() {
        this.getGrowthValue('LEARN');
      },
      async closeVideo() {
        this.$refs.video_popup.close();
        let polyvPlayerContext = this.selectComponent('#polyv_player');
        let learningProgress = (polyvPlayerContext.rCurrentTime / polyvPlayerContext.rDuration).toFixed(2);
        let rCurrentTime = polyvPlayerContext.rCurrentTime.toFixed(2);
        let _this = this;
        const res = await $http({
          url: 'zx/wap/recorded/course/saveLeanProcess?courseType=1&learningProgress=' + learningProgress * 100 + '&courseId=' + this.videoInfo.id + '&studyTime=' + rCurrentTime,
          showLoading: true,
          method: 'POST',
          data: {
            courseType: 1,
            learningProgress: learningProgress * 100,
            courseId: this.videoInfo.id
          }
        });
        if (res) {
          this.getFindRecordedCoursesList();
        }
      },
      bindloadedmetadata() {
        let polyvPlayerContext = this.selectComponent('#polyv_player');
        polyvPlayerContext.pause();
        polyvPlayerContext.seek(Number(this.studyTime));
      },
      release() {
        if (this.identityType == 4) {
          uni.navigateTo({
            url: '/memberCenter/releaseIndex?type=1'
          });
        } else {
          this.$refs.tipsContentPopupRefs.open();
          this.tipsType = 3;
        }
      },
      hearAnswers(info) {
        if (this.identityType !== 1) {
          this.tipsType = 1;
          this.$refs.tipsContentPopupRefs.open();
        }
      },
      addHearAnswers() {
        recorderManager.start();
      },
      goQuestions() {
        uni.navigateTo({
          url: '/memberCenter/culturalType/familyPage/teacherIntroduction?identityType=' + this.identityType
        });
      },

      addRadius(url, index) {
        this.$set(this.questionsAnswersList[index], 'voicePath', url);
      },
      costClick() {
        this.$refs.cost_content_popup.open();
      },
      async getGrowthValue(text) {
        if (this.identityType != 4) {
          return;
        }
        const res = await $http({
          url: 'zx/wap/CultureCircle/getGrowthValue?eventType=' + text + '&userId=' + uni.getStorageSync('user_id'),
          method: 'POST',
          data: {}
        });
        if (res) {
          if (Number(res.data)) {
            this.$refs.growthPopupRefs.open(res.data);
          }
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  /deep/.u-textarea--radius {
    padding-bottom: 50rpx !important;
  }
  .family_content_css {
    height: 100vh;
    width: 750rpx;
    overflow-x: hidden;
    overflow-y: scroll;
  }
  .family_content_style {
    height: calc(100vh - 480rpx);
  }
  .image_left_content {
    .imagele {
      width: 260rpx;
      height: 146rpx;
    }
    .video_play {
      width: 64rpx;
      height: 64rpx;
      left: 96rpx;
      top: 50rpx;
    }
  }
  .right_record_css {
    width: 410rpx;
  }
  .video_content_css {
    height: 100vh;
    width: 750rpx;
    position: relative;
    z-index: 5;
    .video_css {
      width: 750rpx;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }
    .tips_content_close {
      position: absolute;
      right: 32rpx;
      top: 40rpx;
      z-index: 3;
    }
  }
  .family_banner_main {
    padding-top: 24rpx;
    .swiper_css {
      height: 300rpx;
      width: 686rpx;
      .swiper_css_item {
        height: 240rpx !important;
      }
    }
  }
  .border_color {
    border-bottom: 1rpx solid #ecf0f4;
  }
  .bottom_button_css {
    position: fixed;
    left: 0rpx;
    bottom: 40rpx;
    display: flex;
    justify-content: center;
    .butoton_content_css {
      width: 360rpx;
      height: 96rpx;
      background: #339378;
      box-shadow: 0rpx 4rpx 8rpx 0rpx #36a586;
      border-radius: 48rpx;
      text-align: center;
      line-height: 90rpx;
      margin-left: 180rpx;
    }
    .left_text_css {
      line-height: 140rpx;
    }
    .button_css {
      margin-top: 32rpx;
    }
  }
  .answer_content_css {
    background: #f8f8f8;
    border-radius: 40rpx 40rpx 0rpx 0rpx;
    padding-top: 12rpx;
    padding-bottom: 220rpx;
  }

  .cost_content_css {
    width: 686rpx;
    height: 980rpx;
    background: url('https://document.dxznjy.com/course/f12fb50e68cc4cf5b9c433a9e968d4e7.png') no-repeat;
    background-size: 100%;
    padding-top: 290rpx;
    position: relative;
    .close_circle {
      position: absolute;
      right: 20rpx;
      top: 0;
    }
  }
</style>
