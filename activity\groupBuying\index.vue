<!-- 拼团 -->
<template>
  <view class="page-content">
    <view class="act-img"></view>

    <view class="content">
      <view class="title">
        <text>发起拼团</text>
        <text>邀请用户参团</text>
        <text>人满拼团成功</text>
      </view>
      <view class="step">
        <view class="line"></view>
        <view>1</view>
        <view>2</view>
        <view>3</view>
      </view>

      <view class="goods-list">
        <template v-if="dataList.length > 0">
          <goods-card-item :dataSource="item" v-for="(item, index) in dataList" :key="index"></goods-card-item>
        </template>
        <view v-else class="t-c flex-col noData">
          <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 wh100" mode="widthFix"></image>
          <view style="color: #bdbdbd">暂无数据</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  const { $http, $navigationTo } = require('@/util/methods.js');
  import GoodsCardItem from './components/GoodsCardItem.vue';
  export default {
    data() {
      return {
        imgHost: getApp().globalData.imgsomeHost,
        img: 'https://document.dxznjy.com/dxSelect/pintuan_bg.png',
        dataList: [],
        groupActivityId: ''
      };
    },
    components: {
      GoodsCardItem
    },
    methods: {
      handleCountdownEnd() {
        // 倒计时结束时的处理逻辑
        console.log('倒计时已结束');
      },
      async getActivityGoods() {
        uni.showLoading({
          title: '加载中...',
          mask: true
        });
        const res = await $http({
          url: 'zx/wap/group/activity/good/page',
          method: 'get',
          data: {
            groupActivityId: this.groupActivityId,
            pageSize: 1000
          }
        });

        if (res.data) {
          this.dataList = res.data.data || [];
        }
      }
    },

    onShow() {
      this.getActivityGoods();
    },

    onLoad(options) {
      this.groupActivityId = options.groupActivityId;
    }
  };
</script>

<style lang="scss" scoped>
  .page-content {
    width: 100%;
    min-height: 100vh;
    padding-top: 34rpx;
    box-sizing: border-box;
    background-color: #defcfd;
    background-image: url('https://document.dxznjy.com/dxSelect/pintuan_bg.png');
    background-size: contain;
    background-repeat: no-repeat;

    .act-img {
      width: 686rpx;
      height: 588rpx;
      margin: 0 auto;
      background-image: url('https://document.dxznjy.com/dxSelect/pic_toubutupian.png');
      background-size: 100% 100%;
    }

    .content {
      // position: absolute;
      // left: 50%;
      // bottom: 0;
      width: 646rpx;
      min-height: calc(100vh - 570rpx);
      padding: 32rpx 22rpx 0 24rpx;
      // margin-left: -323rpx;
      margin-left: 50%;
      transform: translateX(-50%);
      margin-top: -52rpx;
      box-sizing: border-box;
      border-radius: 4rpx;
      background: #f7faf8;

      .title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 10rpx 0 6rpx;
        font-family: ZiZhiQuXiMaiTi;
        font-size: 28rpx;
        color: #525252;
        line-height: 36rpx;
      }

      .step {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 8rpx;
        padding: 0 54rpx 0 28rpx;
        font-family: AlibabaPuHuiTiBold;
        font-weight: bold;

        .line {
          position: absolute;
          top: 16rpx;
          left: 52rpx;
          width: 470rpx;
          height: 16rpx;
          background-image: url('https://document.dxznjy.com/dxSelect/act_pic_chang.png');
          background-size: 100% 100%;
        }

        view {
          position: relative;
          z-index: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 46rpx;
          height: 46rpx;
          font-size: 28rpx;
          color: #23826a;
          line-height: 44rpx;
          background-image: url('https://document.dxznjy.com/dxSelect/pic_yuan.png');
          background-size: 100% 100%;
        }
      }
    }

    .goods-list {
      height: calc(100% - 115rpx);
      margin-top: 24rpx;
      // overflow-y: scroll;
      font-family: AlibabaPuHuiTiBold;
      font-weight: bold;
      // .goods-item {
      //   display: flex;
      //   align-items: center;
      //   justify-content: space-between;
      //   width: 600rpx;
      //   height: 224rpx;
      //   padding: 14rpx 14rpx 16rpx 16rpx;
      //   margin-bottom: 24rpx;
      //   background: #f7faf8;
      //   box-sizing: border-box;

      //   .goods-img {
      //     width: 154rpx;
      //     height: 194rpx;
      //     margin-right: 16rpx;
      //   }
      // }

      // .goods-content {
      //   display: flex;
      //   flex-direction: column;
      //   justify-content: space-between;
      //   align-items: center;
      //   height: 100%;
      //   .goods-title {
      //     width: 100%;
      //     max-width: 398rpx;
      //     // height: 88rpx;
      //     font-size: 28rpx;
      //     color: #555555;
      //     line-height: 44rpx;
      //   }

      //   .goods-price {
      //     display: flex;
      //     align-items: center;
      //     justify-content: space-between;
      //     width: 100%;
      //     & > view {
      //       margin-right: 8rpx;
      //     }
      //     .group-number {
      //       min-width: 100rpx;
      //       height: 40rpx;
      //       background: #ed7d4d;
      //       font-size: 24rpx;
      //       border-radius: 8rpx;
      //       color: #ffffff;
      //       text-align: center;
      //       line-height: 40rpx;
      //     }
      //     .sale-price {
      //       font-size: 28rpx;
      //       color: #ed7d4d;
      //       line-height: 44rpx;
      //     }
      //     .origin-price {
      //       margin-right: 0;
      //       font-size: 24rpx;
      //       color: #a1a1a1;
      //       line-height: 44rpx;
      //     }
      //   }

      //   .goods-time-end {
      //     width: 100%;
      //     font-size: 24rpx;
      //     color: #555555;

      //     .time {
      //       font-size: 28rpx;
      //       color: #ed7d4d;
      //     }

      //     .plr {
      //       padding-left: 12rpx;
      //       padding-right: 16rpx;
      //     }
      //   }
      // }
    }

    .noData {
      height: 45vh;

      image {
        width: 150rpx;
      }
    }
  }
</style>
