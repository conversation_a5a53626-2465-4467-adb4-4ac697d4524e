<template>
  <page-meta :page-style="'overflow:' + (rollShow ? 'hidden' : 'visible')"></page-meta>
  <view style="height: 100vh; background-color: #f5f8fa">
    <view>
      <view class="f-28 p-20">
        <view class="pointsBox mb-20 flex-s">
          <view class="flex-a-c">
            余额：
            <view style="color: #339378; font-weight: 700">
              {{ payAmount }}
            </view>
          </view>
          <view class="flex-a-c">
            结算中余额：
            <view style="color: #339378; font-weight: 700">
              {{ unPayAmount }}
            </view>
            <uni-icons type="info" size="20" @tap="tooltipInfo"></uni-icons>
          </view>
        </view>
        <view class="" v-if="dbList.list && dbList.list.length > 0">
          <view class="radius-16 mb-20" v-for="(item, index) in dbList.list" :key="index">
            <view class="bg-ff p-10 sizing">
              <view class="flex-s lh-90" style="line-height: 90rpx; border-bottom: 2rpx solid #d0d0d0">
                <view class="type">
                  类型:
                  <view style="color: #339378; font-weight: 700; margin-left: 20rpx">
                    {{ item.orderType == 1 ? '课程订单' : item.orderType == 2 ? '套餐订单' : item.orderType == 3 ? '邀请码采购单' : '' }}
                  </view>
                  <view style="font-weight: 700; margin-left: 20rpx" :style="{ color: item.direction == 1 ? '#339378' : '#c5483d' }">
                    {{ item.direction == 1 ? '（收入）' : '（支出）' }}
                  </view>
                </view>
                <view class="f-24 typeStatus t-c" v-if="item.commissionOperationType != 2">
                  <text v-if="item.isOver == 0" class="f-26 c-ff t-c tobe_paid">未结算</text>
                  <text v-if="item.isOver == 1" class="f-26 c-ff t-c completed">已结算</text>
                </view>
              </view>
              <view class="mtb-25">申请人名称: {{ item.buyerName }}</view>
              <view class="">申请人手机号: {{ item.buyerMobile }}</view>
              <view class="mtb-25">下单总金额: {{ item.orderPrice }}元</view>
              <view class="" v-if="item.direction == 1">获得奖励: {{ item.amount }}元</view>
              <view class="" v-if="item.direction == 2">支出金额: {{ item.amount }}元 （{{ item.remark }}）</view>
              <view class="mtb-25">推荐完成时间: {{ item.createdTime }}</view>
            </view>
          </view>
        </view>
        <view v-else class="curriculum_css_no pt-30 pb-55 f-28">
          <image class="curriculum_image" src="https://document.dxznjy.com/course/ac587707bf314badadb28a158852c77d.png"></image>
          <view class="c-66 f-24 mtb-25">暂无明细</view>
        </view>
      </view>
      <view v-if="dbNoMore && dbList.list != undefined && dbList.list.length > 0">
        <u-divider text="到底了"></u-divider>
      </view>
    </view>
    <uni-popup ref="popup" type="center" @change="change">
      <view class="shareCard">
        <view class="activityRule">提示</view>
        <view class="review_close" @click="closePopup">
          <uni-icons type="clear" size="26" color="#B1B1B1"></uni-icons>
        </view>
        <view class="rule">考虑到课程退款等原因，获取的奖励会在7天后结算到您的账户</view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        page: 1,
        dbPage: 1,
        creditList: {},
        dbList: {},
        dbNoMore: false,
        tooltipInfoStatus: false,
        timerId: '',
        type: '', //4合伙人 5俱乐部 6品牌
        userCode: '',
        payAmount: '',
        unPayAmount: '',
        shows: false
      };
    },
    onHide() {
      clearTimeout(this.timerId);
    },
    onLoad(option) {
      if (option != null) {
        this.type = option.type;
        this.userCode = option.userCode;
      }
    },
    onShow() {
      this.fetchDbList();
      this.fetchPriceDetail();
    },
    onReachBottom() {
      if (this.dbPage >= this.dbList.totalPage) {
        this.dbNoMore = true;
        return false;
      }
      this.fetchDbList(true, ++this.dbPage);
    },
    methods: {
      //获取余额明细
      async fetchPriceDetail() {
        let res = await $http({
          url: 'zx/user/allIncome',
          data: {
            fieldType: '0',
            userCode: this.userCode
          }
        });
        if (res) {
          this.payAmount = res.data.payAmount;
          this.unPayAmount = res.data.unPayAmount;
        }
      },
      async fetchDbList(isPage, page) {
        let params = {
          pageNum: page || 1,
          pageSize: 10,
          userCode: this.userCode,
          merchantType: this.type
        };
        const res = await $http({
          url: 'zx/user/incomeDetail',
          data: params
        });
        if (res) {
          if (isPage) {
            let old = this.dbList.list;
            this.dbList.list = [...old, ...res.data.list];
          } else {
            this.dbList = res.data;
          }
        }
      },
      tooltipInfo() {
        this.$refs.popup.open();
      },
      closePopup() {
        this.$refs.popup.close();
      },
      // 禁止滚动穿透
      change(e) {
        this.show = e.show;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .details {
    border-bottom: 4rpx solid #339378;
    font-weight: 700;
  }

  .pointsBox {
    height: 96rpx;
    background-color: #f9fcfe;
    display: flex;
    align-items: center;
  }

  .detailTop {
    margin: 20rpx 0;
    display: flex;
    justify-content: space-evenly;
  }

  .type {
    display: flex;
  }

  .bgf {
    width: 376rpx;
    height: 112rpx;
    margin: 0 auto;
    background: url('https://document.dxznjy.com/course/1e6afed624714ef4825a7dba609ccd58.png') no-repeat;
    background-size: 100%;
  }

  .curriculum_css_no {
    position: relative;
    width: 710rpx;
    margin: auto;
    margin-top: 400rpx;
    text-align: center;

    .curriculum_image {
      width: 122rpx;
      height: 114rpx;
      display: block;
      margin: 16rpx auto;
    }

    .curriculum_title {
      text-align: left;
    }
  }

  .tobe_paid {
    width: 90rpx;
    line-height: 40rpx;
    background: rgba(255, 221, 167, 0.15);
    border-radius: 8rpx;
    border: 2rpx solid #ffdda7;
    color: #fd9b2a;
  }

  .completed {
    background-color: rgba(129, 226, 175, 0.15);
    width: 90rpx;
    line-height: 40rpx;
    border-radius: 8rpx;
    border: 2rpx solid #81e2af;
    color: #81e2af;
  }

  .detailPrice {
    margin-top: 100rpx;
  }

  .shareCard {
    width: 80vw;
    height: 300rpx;
    background: #ffffff;
    color: #000;
    padding-top: 40rpx;
    box-sizing: border-box;
  }

  .activityRule {
    text-align: center;
    font-weight: bold;
  }

  .rule {
    padding: 20rpx;
    line-height: 50rpx;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }
</style>
