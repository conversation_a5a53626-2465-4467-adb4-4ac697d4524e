<template>
  <page-meta :page-style="'overflow:' + (rollShow ? 'hidden' : 'visible')"></page-meta>
  <view>
    <view class="col-12 header_css">
      <view class="home_bg" style="display: flex">
        <view class="ml-30 mr-20">
          <image :src="userinfo != null && userinfo.headPortrait ? userinfo.headPortrait : avaUrl" class="box-128 radius-all"></image>
        </view>
        <view class="center_css">
          <view v-if="userinfo != null && userinfo.nickName" class="nicknameElli f-32 c-ff mt-10">
            {{ userinfo != null && userinfo.nickName ? userinfo.nickName : '昵称' }}
          </view>
          <view v-else class="nicknameElli f-32 c-ff mt-10" @click="goToLogin">请登录</view>
          <view class="mt-8">
            <view class="f-30 identity_css radius-8 f-24" v-if="userinfo != null && userinfo.identityTypeName">
              {{ userinfo.identityTypeName }}
            </view>
          </view>
        </view>
        <view class="flex-a-c content_right" @tap="getIntegral">
          <text class="c-ff f-24 integral_css">积分中心</text>
          <u-icon name="arrow-right" color="#555555" size="28"></u-icon>
        </view>
      </view>
      <view @click="changeStudentAll(info, current)">
        <image style="width: 60rpx; height: 60rpx; margin-left: 86%" src="https://document.dxznjy.com/course/5a018026c2f0469c96a5f6929e4c9b7b.png"></image>
      </view>
      <view class="home_con mb-25">
        <view class="bg-ff radius-15 course-img mb-30">
          <view class="plan" v-if="studentShow && info && info.length > 0">
            <!-- 只展示当前学员 -->
            <view v-if="info[current]" @click="changeStudentAll(info, current)">
              <view class="title">
                <view class="title-name">{{ info[current].content }}</view>
                <view class="title-merchant">门店编号：{{ info[current].studentFormMerchant }}</view>
                <view class="title-line"></view>
              </view>

              <view class="hour">
                <view class="time">
                  <text>已用学时:{{ info[current].useHours === '0.00' ? 0 : info[current].useHours || 0 }}时</text>
                  <text style="float: right">剩余学时:{{ info[current].haveHours === '0.00' ? 0 : info[current].haveHours || 0 }}时</text>
                </view>
                <u-line-progress
                  class="progress_bar"
                  :percentage="info[current].percentage"
                  :showText="false"
                  activeColor="#378771"
                  height="20"
                  inactiveColor="#E4E4E4"
                ></u-line-progress>
              </view>
            </view>
          </view>

          <view class="empty-content" v-else>
            <image :src="imgHost + 'dxSelect/lake_page.png'" class="empty-img"></image>
            <view style="color: #bdbdbd; margin-top: 20rpx">暂无数据，请先在我的学员里添加学员</view>
          </view>
        </view>
        <!-- 我的课程 -->
        <mycurriculum ref="mycurriculum"></mycurriculum>
        <!-- 最近一节交付课 -->
        <RecentDeliverCourse ref="recentCourseRef" :merchant-code="info[current].studentFormMerchant" :student-code="currentStudentCode"></RecentDeliverCourse>
      </view>

      <view class="f-28 c-66 plr-30">
        <!-- start 最近使用 -->
        <view class="bg-ff radius-15 pt-40 mb-30" v-if="showTabDetail && recentlyToolList.length > 0">
          <view class="study_service mb-40 bold f-28 c-33">最近使用</view>
          <view class="templateItem">
            <view class="template" v-for="(item, index) in recentlyToolList" :key="index" @click="goRecentlyClick(item)">
              <image :src="item.iconAddress" class="w44"></image>
              <view>{{ item.toolName }}</view>
            </view>
          </view>
        </view>
        <!-- end 最近使用 -->
        <!-- start 学习服务 -->
        <view class="bg-ff radius-15 pt-40 mb-30" v-if="showTabDetail && generalToolList.length > 0">
          <view class="study_service mb-40 bold f-28 c-33">学习服务</view>
          <view class="templateItem">
            <view class="template" v-for="(item, index) in generalToolList" :key="index" @click="goGeneralClick(item)">
              <image :src="item.iconAddress" class="w44"></image>
              <view>{{ item.toolName }}</view>
            </view>
          </view>
        </view>
        <!-- end 学习服务 -->
        <!-- start 课程工具 -->
        <view v-if="showTabDetail && courseToolList.length > 0">
          <view class="bg-ff radius-15 pt-40 mb-30" v-for="(item, index) in courseToolList" :key="index">
            <view class="study_service mb-40 bold f-28 c-33" style="display: flex; align-items: center">
              <span style="margin-right: 10rpx">{{ item.curriculumName }}</span>
              <image v-if="!item.isPurchase" class="w33" src="https://document.dxznjy.com/manage/1752746557000.png"></image>
            </view>

            <view class="templateItem bg-ff radius-15">
              <view
                class="template"
                v-for="(courseItem, courseIndex) in item.curriculumTool"
                :key="courseIndex"
                @click="goCourseClick(courseItem, item.curriculumName, item.isPurchase)"
              >
                <image :src="item.isPurchase ? courseItem.iconAddress : courseItem.ashIconAddress" class="w44"></image>
                <view>{{ courseItem.toolName }}</view>
              </view>
            </view>
          </view>
        </view>
        <!-- end 课程工具 -->
      </view>
      <view style="height: 30rpx"></view>
    </view>

    <u-popup :show="show" mode="center" :round="10" :closeable="true" :safeAreaInsetBottom="false" @close="close">
      <view class="phonecon t-c">
        <view class="tit f-32 bold mb-40">手机号码授权</view>
        <text class="tips pb-30 f-30">为了提供更好的服务请\n允许获取您的手机号码</text>
        <button class="login_in f-32 c-ff" open-type="getPhoneNumber" @getphonenumber="$noMultipleClicks(onGetPhoneNumber)">确认授权</button>
      </view>
    </u-popup>

    <!-- 选择校区弹窗 -->
    <uni-popup ref="popopChooseSchool" type="center" @change="changeSchool">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold pb-20">选择校区</view>
            <scroll-view :scroll-top="scrollTop" scroll-y="true" class="scroll-Y" @scrolltoupper="upper" @scrolltolower="lower" @scroll="scroll">
              <view
                class="dialogContent"
                @click="chooseSchoollist(item, index)"
                v-for="(item, index) in arraySchool"
                :key="index"
                :class="isChoosemerchantCode == index ? 'addclass' : 'not-selected'"
              >
                {{ item.merchantName }}
              </view>
            </scroll-view>
            <view class="review_btn" @click="$noMultipleClicks(confirmSchool)">确定</view>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 选择学员弹窗 -->
    <uni-popup ref="popopChooseStudent" :mask-click="false" type="center" @change="changeStudent">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold pb-10">选择学员</view>
            <scroll-view :scroll-top="scrollTop" scroll-y="true" class="scroll-Y" @scrolltoupper="upper" @scrolltolower="lower" @scroll="scroll">
              <view
                class="dialogContent"
                @click="chooseStudentlist(item, index)"
                v-for="(item, index) in displayStudents"
                :key="index"
                :class="isactive == index ? 'addclass' : 'not-selected'"
              >
                {{ item.content + '（' + item.studentCode + '）' }}
              </view>
            </scroll-view>
            <view class="mask-footer">
              <button class="confirm-button" @click="confirmStudent()">确定</button>
              <button class="cancel-button" v-show="aiIntelligentWords" @click="closeDialog">取消</button>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 没有开通权限 -->
    <uni-popup ref="popopPower" type="center" @change="changePower">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold">温馨提示</view>
            <view class="c-f0 t-c mtb-50 ptb-50">您还没有开通使用权限</view>
            <view class="review_btn" @click="nowBuy()">立即购买</view>
          </view>
        </view>
      </view>
    </uni-popup>

    <user-dialog />
  </view>
</template>
<script>
  import Config from '@/util/config.js';
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  const { $navigationTo, $showMsg, $http } = require('@/util/methods.js');
  import mycurriculum from '@/components/mycurriculum.vue';
  import UserDialog from '@/components/user-dialog/index.vue';
  import dayjs from 'dayjs';
  import getNotReadNews from '@/util/messages.js';
  import RecentDeliverCourse from '../../../Personalcenter/my/lessonListPage/components/recentDeliverCourse.vue'; //学员伴学课
  export default {
    components: {
      RecentDeliverCourse,
      mycurriculum,
      UserDialog
    },
    data() {
      return {
        currentStudentCode: null, // 当前选择的学员
        currentMerchantCode: null, // 当前选择的校区
        generalToolList: [], // 通用工具列表
        courseToolList: [], // 课程对应工具列表
        recentlyToolList: [], // 最近使用工具列表
        title: '',
        showTabDetail: true,
        noClick: true, //防抖
        show2: false,
        arrayPyf: [],
        index1: 0,
        show1: true,
        info: [],
        current: 0,
        dotsStyles: {
          backgroundColor: 'rgba(236, 236, 236, 1)',
          border: '1px rgba(236, 236, 236, 1) solid',
          color: '#48917D',
          selectedBackgroundColor: '#48917D',
          selectedBorder: '1px #48917D solid'
        },
        show: false,
        pyfOrRead: 0,
        pageShow: true,
        userinfo: null,
        code1: null,
        studyCentre: '',
        percentage: '', // 进度条数值
        scheduleList: {}, // 进度列表
        schedule: {}, // 学员进度详情
        studentShow: true,
        bannerIndex: 0,
        superCodeList: [],
        userCode: '',
        studentCode: '',
        interestType: 1,
        name: '',
        studentFormMerchant: '', //课时区域门店编号显示
        haveHours: 0, //课时区域剩余学时显示
        useHours: 0, //课时区域已用学时显示
        merchantCode: '',
        tudentcode: '', // 学员进度详情code
        mobile: '', // 账号手机号
        imgHost: getApp().globalData.imgsomeHost,
        avaUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
        rollShow: false, //禁止滚动穿透,
        myClassList: {
          lastStudyCourse: {},
          nextStudyCourse: {}
        },
        isactive: -1, // 选中索引
        isChoosemerchantCode: -1, // 门店索引
        interestSelect: false,
        LessonpPracticeSelect: false,
        txtContent: '您还没有开通使用权限',
        arraySchool: [],
        arrayStudent: [],
        displayStudents: [],
        courseStudyDetail: [], // 学时详情
        scrollTop: '',
        //选择学生信息
        studentInfo: {},
        //21天
        showMask: false, // 控制遮罩层的显示与隐藏
        activeIndex: -1,
        Buttonclick: '',
        buttonclickName: '',
        //5天
        pyfStudentCode: '',
        // 订阅消息
        useropenid: '',
        mytoken: '',
        templateId: 'a2pCjUTIytYmKob9R2w5u_e4OrvqqhV5s4EOhe9NLts', // 要发送订阅的模板id
        wxappid: '', // 小程序appID
        wxsecret: '', // 小程序密钥
        pushmsg: {
          touser: '', // 获取到的用户openid
          template_id: '', // 对应的模板id（微信公众平台中的订阅消息，选择对应模板）
          data: {
            thing1: {
              value: '今日抗遗忘训练还未完成，请点击进入'
            },
            thing2: {
              value: '合理有效的循环是抵抗遗忘最好的方法'
            }
          },
          page: 'pages/home/<USER>/index' //进入哪个页面
        },
        authorizationShow: false,
        subscribeStatus: false, // 授权状态
        defaultSelectStudent: null,
        curriculumId: '1283284867302313984', //正式1283284867302313984  测试1223288510123233280
        PYFcurriculumId: '1283343602584145920', //正式1283343602584145920
        oneStudy: false
      };
    },
    mounted() {},

    async onShow() {
      this.jumpDxnBuy();
      let _this = this;
      let token = uni.getStorageSync('token');
      this.current = uni.getStorageSync('currentStudent') || 0;
      if (token) {
        getNotReadNews.getNotReadNews();
        uni.removeStorage({
          key: 'logintokenReview'
        });
        setTimeout(function () {
          _this.$refs.mycurriculum.getSubject();
        });
        _this.homeData();
        _this.getCurriculumId();
        _this.getGeneral();
        this.getSubject();

        uni.login({
          success(res) {
            _this.code1 = res.code;
          }
        });
      }
      await this.$refs.recentCourseRef.getRecentCourseInfo();
    },
    onTabItemTap(e) {
      getApp().sensors.track('tabBarClick', {
        pagePath: e.pagePath,
        title: e.text
      });
    },
    methods: {
      async clickUsed(id) {
        const res = await $http({
          url: 'znyy/curriculumTool/app/toolClick',
          method: 'POST',
          data: {
            studentCode: this.currentStudentCode || this.info[0]?.studentCode,
            toolId: id,
            channel: 1
          }
        });
      },
      // 获取学时详情
      async getStudyDetail() {
        const res = await $http({
          url: `znyy/course/getMemberStudentList?memberId=${this.userinfo.userCode}`
        });
        console.log('学时详情', res.data);
        this.courseStudyDetail = res.data;
      },
      // 获取最近使用
      async getRecentlyUsed() {
        const res = await $http({
          url: `znyy/curriculumTool/app/recentlyToolList?studentCode=${this.currentStudentCode || this.info[0]?.studentCode}&channel=1`
        });
        this.recentlyToolList = res.data;
      },
      goRecentlyClick(item) {
        console.log(item, 'xueneng');
        getApp().sensors.track('studyServiceItemClick', {
          name: item.toolName
        });
        if (item.jumpAddress == '/antiAmnesia/review/funReview') {
          this.getPadToken();
          this.interestSelect = true;
          uni.setStorageSync('itemDYYId', item.id);
        } else if (item.toolName.includes('一课一练')) {
          this.studentcode = uni.getStorageSync('currentStudentCode');
          this.merchantCode = uni.getStorageSync('currentMerchantCode');
          this.goLessonpPractice();
          this.goCourseTools(item.jumpAddress, item.id);
        } else if (item.jumpAddress == '/interestModule/pyfinterest/index') {
          this.goCourseTools(item.jumpAddress, item.id);
        } else {
          uni.navigateTo({
            url:
              item.jumpAddress +
              '?memberId=' +
              this.userinfo.userCode +
              '&studentName=' +
              this.currentRealName +
              '&studentCode=' +
              this.currentStudentCode +
              '&merchantCode=' +
              this.currentMerchantCode,
            success: () => {
              // 页面跳转成功时调用
              this.clickUsed(item.id);
            },
            fail: (err) => {
              uni.showToast({
                title: '请确保使用最新版本，若仍有问题可稍后再试',
                icon: 'none',
                duration: 2000
              });
            }
          });
        }
      },
      // 获取通用工具
      async getGeneral() {
        const res = await $http({
          url: 'znyy/curriculumTool/findGeneralTool' + '?showChannel=1'
        });
        this.generalToolList = res.data;
      },
      goGeneralClick(item) {
        getApp().sensors.track('studyServiceItemClick', {
          name: item.toolName
        });
        uni.navigateTo({
          url:
            item.jumpAddress +
            '?memberId=' +
            this.userinfo.userCode +
            '&studentName=' +
            this.currentRealName +
            '&studentCode=' +
            this.currentStudentCode +
            '&merchantCode=' +
            this.currentMerchantCode,
          success: () => {
            // 页面跳转成功时调用
            if (this.arrayStudent[0]?.studentCode) {
              this.clickUsed(item.id);
            }
          },
          fail: (err) => {
            uni.showToast({
              title: '请确保使用最新版本，若仍有问题可稍后再试',
              icon: 'none',
              duration: 2000
            });
          }
        });
      },
      // 获取课程对应工具
      async getCourse() {
        const res = await $http({
          url: `znyy/curriculum/type/queryCourseAndTool?studentCode=${this.currentStudentCode ?? this.info[0]?.studentCode ?? ''}&merchantCode=${
            this.currentMerchantCode ?? this.info[0]?.studentFormMerchant ?? ''
          }&showChannel=1`
        });
        this.courseToolList = res.data.data;
      },
      goCourseClick(item, curriculumName, isPurchase) {
        getApp().sensors.track('studyServiceItemClick', {
          name: item.toolName
        });
        if (!isPurchase) {
          uni.showToast({
            title: `此功能需要购买${curriculumName}课程后体验`,
            icon: 'none',
            duration: 2000
          });
          return;
        }
        if (item.jumpAddress === 'DXN') {
          this.studentcode = uni.getStorageSync('currentStudentCode');
          this.merchantCode = uni.getStorageSync('currentMerchantCode');
          this.goLessonpPractice();
          this.goCourseTools(item.jumpAddress, item.id);
        } else if (item.jumpAddress == '/antiAmnesia/review/funReview') {
          this.getPadToken();
          this.interestSelect = true;
          uni.setStorageSync('itemDYYId', item.id);
        } else if (item.jumpAddress == '/interestModule/pyfinterest/index') {
          this.goCourseTools(item.jumpAddress, item.id);
        } else {
          uni.navigateTo({
            url:
              item.jumpAddress +
              '?memberId=' +
              this.userinfo.userCode +
              '&studentName=' +
              this.currentRealName +
              '&studentCode=' +
              this.currentStudentCode +
              '&merchantCode=' +
              this.currentMerchantCode,
            success: () => {
              // 页面跳转成功时调用
              this.clickUsed(item.id);
            },
            fail: (err) => {
              uni.showToast({
                title: '请确保使用最新版本，若仍有问题可稍后再试',
                icon: 'none',
                duration: 2000
              });
            }
          });
        }
      },
      goCourseTools(jumpAddress, itemId) {
        this.pyfOrRead = 0;
        let that = this;
        let httpUrl = '';
        let curriculumId = this.curriculumId;
        if (jumpAddress == '/interestModule/pyfinterest/index') {
          curriculumId = this.PYFcurriculumId;
        }
        if (this.aiIntelligentWords) {
          httpUrl = 'znyy/superReadReview/queryMerchantList';
        } else {
          if (jumpAddress == 'DXN') {
            httpUrl = 'zx/wap/course/student/course/summary';
            uni.setStorageSync('ItemDXNId', itemId);
          } else if (jumpAddress == '/interestModule/pyfinterest/index') {
            httpUrl = 'znyy/pd/mobile/funReview/exist/entrance';
          }
        }
        httpUser.get(httpUrl + '?studentCode=' + this.currentStudentCode + '&curriculumId=' + curriculumId).then((result) => {
          // uni.hideLoading()
          if (result.data.success) {
            that.oneStudy = false;
            that.$refs.popopChooseStudent.close();
            // 没有购买去购买
            if (result.data.code == 20004) {
              setTimeout(function () {
                that.$refs.popopPower.open();
              }, 500);
              return;
            }
            if (result.data.success) {
              that.oneStudy = false;
              that.$refs.popopChooseStudent.close();
              that.arraySchool = result.data.data;
              // uni.setStorageSync('merchantCode', that.arraySchool[0].merchantCode)
              // console.log(jumpAddress, '22222222222');
              // debugger;
              if (jumpAddress == 'DXN' && (result.data.data == null || result.data.data.length == 0)) {
                that.$util.alter('您还没学习过学能正式课程');
              } else if (result.data.data == null || result.data.data.length == 0) {
                that.$util.alter('您还没学习过课程');
              }
              if (result.data.data.length == 1 && result.data.data != null) {
                that.merchantCode = that.arraySchool[0].merchantCode;
                if (jumpAddress == '/interestModule/pyfinterest/index') {
                  uni.navigateTo({
                    url: `/interestModule/pyfinterest/index?studentCode=${this.currentStudentCode}&merchantCode=${this.currentMerchantCode}`
                  });
                  this.clickUsed(itemId);
                } else {
                  that.getPadToken();
                }
              } else if (result.data.data.length > 1 && result.data.data != null) {
                if (jumpAddress == '/interestModule/pyfinterest/index') {
                  that.merchantCode = that.arraySchool[0].merchantCode;
                  let flagTemp = that.arraySchool[0].merchantCode == that.arraySchool[1].merchantCode;
                  if (!flagTemp) {
                    // 若购买高低年级课程不一致，则默认选择第一个校区（后续根据实际情况修改）
                    that.merchantCode = that.arraySchool[0].merchantCode;
                  }
                  // console.log('arraySchool', that.arraySchool, that.merchantCode);
                  uni.navigateTo({
                    url: `/interestModule/pyfinterest/index?studentCode=${this.currentStudentCode}&merchantCode=${that.merchantCode}`
                  });
                  this.clickUsed(itemId);
                } else {
                  this.isactive = -1;
                  that.$refs.popopChooseSchool.open();
                }
              }
            }
          } else {
            // console.log(result);
            that.$util.alter(result.data.message);
          }
        });
      },
      // 切换学员
      changeStudentAll(info, index) {
        // 确保 info 是数组且每个对象都有 studentCode 和 studentFormMerchant（允许为空字符串）
        if (
          !Array.isArray(info) ||
          info.some((item) => item.studentCode === undefined || item.studentCode === null || item.studentFormMerchant === undefined || item.studentFormMerchant === null)
        ) {
          console.error('数据格式不正确，缺少 studentCode 或 studentFormMerchant，或非数组');
          return;
        }
        // 创建一个临时对象来记录已出现的 studentcode
        const seenStudentCodes = {};
        // 过滤出唯一的学员信息
        const uniqueStudents = info.filter((student) => {
          if (!seenStudentCodes[student.studentCode]) {
            seenStudentCodes[student.studentCode] = true;
            return true;
          }
          return false;
        });

        // displayStudents 用于页面展示，arrayStudent 保留完整数据
        this.displayStudents = uniqueStudents;
        this.arrayStudent = info; // 保留完整数据
        this.$refs.popopChooseStudent.open();
      },
      goZX() {
        uni.setClipboardData({
          data: Config.zxDownloadH5Url,
          success: () => {
            uni.showToast({
              title: '链接已复制',
              icon: 'none'
            });
          }
        });
      },
      ///从鼎学能返回去购买学能正式课
      jumpDxnBuy() {
        let dxnBack = uni.getStorageSync('dxnBack');
        console.log('---jumpDxnBuy-dxnBack--', dxnBack);
        if (dxnBack == '1') {
          uni.setStorageSync('dxnBack', '');
          let url = '';
          if (Config.DXHost.includes('gateway') || Config.DXHost.includes('linetest')) {
            url = '/Coursedetails/productDetils?id=1282351832153198592';
          } else {
            url = '/Coursedetails/productDetils?id=1294692051485462528';
          }
          uni.navigateTo({
            url: url
          });
        }
      },
      async getCurriculumId() {
        let { data } = await $http({
          url: 'znyy/bvstatus/findByEnCodeAndEnType?enCode=PYF&enType=CURRICULUM'
        });
        this.PYFcurriculumId = data.id;
      },
      goToLogin() {
        uni.navigateTo({
          url: '/Personalcenter/login/login'
        });
      },
      ChangeHourMinutestr(str) {
        if (str !== '0' && str !== '' && str !== null) {
          return Math.floor(str / 60).toString() + ':' + (str % 60).toString();
        } else {
          return '';
        }
      },
      async getDetailList(e) {
        const res = await $http({
          url: 'zx/wap/course/user/record/growth/track/query',
          data: {
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        if (res) {
          this.superCodeList = res.data;
          this.superCodeList.forEach((item) => {
            item.studyDurationMinutesToday = this.ChangeHourMinutestr(item.studyDurationMinutesToday);
            item.studyDurationMinutesLastWeek = this.ChangeHourMinutestr(item.studyDurationMinutesLastWeek);
            item.studyDurationMinutesTotal = this.ChangeHourMinutestr(item.studyDurationMinutesTotal);
          });
        }
      },
      // 时间判断
      checkNextTime(time) {
        if (time != undefined) {
          let datetime = time;
          let nowTime = Date.now();
          let setTime = dayjs(datetime).unix() * 1000;
          return nowTime < setTime;
        }
      },
      // 获取我的课程
      async getSubject() {
        let res = await this.$httpUser.get('deliver/app/parent/getCourse');
        if (res.data.success) {
          this.myClassList = res.data.data;
        } else {
          this.$util.alter(res.data.message);
        }
      },

      // 禁止滚动穿透
      changeStudent(e) {
        this.rollShow = e.show;
      },
      changePower(e) {
        this.rollShow = e.show;
      },
      changeSchool(e) {
        this.rollShow = e.show;
      },
      changeSecurities(e) {
        this.rollShow = e.show;
      },
      // scroll-view 滑动
      upper: function (e) {
        console.log(e);
      },
      lower: function (e) {
        console.log(e);
      },
      scroll: function (e) {
        console.log(e);
        this.old.scrollTop = e.detail?.scrollTop;
      },

      change(e) {
        this.current = e.detail.current;
        this.studentcode = this.info[this.current].studentCode;
        this.name = this.info[this.current].content;
        this.studentFormMerchant = this.info[this.current].studentFormMerchant;
        this.haveHours = this.info[this.current].haveHours;
        this.useHours = this.info[this.current].useHours;
        this.percentage = this.info[this.current].percentage;
        // this.getDetails()
      },
      async getSchedule() {
        let that = this;
        that.info = [];
        let result = await httpUser.get(
          `znyy/course/getMerchantAndStudentList?memberId=${that.userinfo.userCode}&studentCode=${that.currentStudentCode}&merchantCode=${that.currentMerchantCode}`
        );
        if (result.data.success) {
          that.scheduleList = result.data.data;
          if (that.scheduleList.length != 0) {
            if (that.info.length == 0) {
              that.scheduleList.forEach((item, index) => {
                if (item.merchantVos === null) {
                  that.info.push({
                    content: item.studentName,
                    studentCode: item.studentCode,
                    grade: item.grade,
                    studentFormMerchant: '',
                    haveHours: '',
                    useHours: '',
                    percentage: ''
                  });
                } else {
                  item.merchantVos.forEach((merchant, indexMer) => {
                    // 查找学时详情数据中对应的学员信息
                    const studyDetail = that.courseStudyDetail.find((d) => d.studentCode === item.studentCode && d.merchantCode === item.merchantCode);
                    that.info.push({
                      content: item.studentName,
                      studentCode: item.studentCode,
                      grade: item.grade,
                      studentFormMerchant: merchant.merchantCode,
                      // 如果学时详情数据存在，则使用详情数据，否则使用 merchant 默认值
                      haveHours: studyDetail ? studyDetail.haveHours : merchant.haveHours,
                      useHours: studyDetail ? studyDetail.useHours : merchant.useHours,
                      percentage: studyDetail ? studyDetail.rate : merchant.rate
                    });
                  });
                }
              });
            }

            this.current = uni.getStorageSync('currentStudent') || 0;
            if (that.info.length >= 1) {
              that.studentShow = true;
              that.studentcode = that.info[that.current].studentCode;
              that.name = that.info[that.current].content;
              that.studentFormMerchant = that.info[that.current].studentFormMerchant;
              that.haveHours = that.info[that.current].haveHours;
              that.useHours = that.info[that.current].useHours;
              that.percentage = that.info[that.current].percentage;
            } else {
              that.studentShow = false;
            }
          }
          that.arrayStudent = that.info;
          that.handleStudentAndMerchant(
            that.arrayStudent[this.current]?.studentCode,
            that.arrayStudent[this.current]?.studentFormMerchant,
            that.arrayStudent[this.current]?.content,
            this.current
          );
          if (that.arrayStudent[0]?.studentCode) {
            that.getCourse();
            that.getRecentlyUsed();
          }
        }
      },

      //点击选择学员
      chooseStudentlist(item, index) {
        this.isactive = index;
        this.studentCode = item.studentCode;
        this.studentInfo = {
          ...item
        };
        console.log('🚀 ~ chooseStudentlist ~ this.studentInfo:', this.studentInfo);
      },
      async getCourseHours(token) {
        let that = this;
        const res = await $http({
          url: 'zx/wap/course/judge',
          data: {
            studentCode: uni.getStorageSync('currentStudentCode')
          }
        });
        if (res) {
          if (res.errMessage == 'true') {
            wx.navigateToMiniProgram({
              appId: 'wxcbd506017e4956dc',
              extraData: {
                token: token,
                code: uni.getStorageSync('currentStudentCode'),
                studentName: uni.getStorageSync('currentStudentName'),
                grade: that.info[that.current].grade
              },
              envVersion: 'release', //trial代表开发版，release代表线上版
              // envVersion: 'trial', //trial代表开发版，release代表线上版
              success: (res) => {
                that.closeDialog();
                console.log('打开成功', res);
                that.LessonpPracticeSelect = false;
                this.clickUsed(uni.getStorageSync('ItemDXNId'));
              },
              fail: (err) => {
                console.log('打开失败', err);
                that.closeDialog();
                that.LessonpPracticeSelect = false;
              }
            });
            return;
          }
        }
      },
      // 选择学员确定
      async confirmStudent() {
        // 基本验证
        if (!this.studentCode) {
          uni.showToast({
            title: '请选择学员',
            icon: 'none',
            duration: 3000
          });
          return;
        }

        try {
          uni.removeStorageSync('logintokenReview'); // 同步移除缓存

          const result = await httpUser.get(`znyy/course/getMerchantAndStudentList`);

          if (!result.data.success) {
            this.$util.alter(result.data.message || '获取门店数据失败');
            return;
          }

          const studentData = result.data.data;

          // 查找当前选中学员的门店信息
          const selectedStudent = studentData.find((student) => student.studentCode === this.studentCode);

          if (!selectedStudent) {
            this.$util.alter('未找到对应的学生数据');
            return;
          }

          const merchantList = selectedStudent.merchantVos || [];

          if (merchantList.length === 0) {
            this.merchantCode = '';
            this.current = this.arrayStudent.findIndex((item) => item.studentCode === this.studentCode);
            this.handleStudentAndMerchant(this.studentCode, this.merchantCode, this.studentInfo.content, this.current);
            this.$refs.popopChooseStudent.close();
            return;
          }

          this.selectedMerchantList = merchantList;
          this.arraySchool = merchantList;

          // 关闭学员选择弹窗
          this.$refs.popopChooseStudent.close();

          // 处理单个或多个门店的情况
          if (merchantList.length === 1) {
            this.merchantCode = merchantList[0].merchantCode;
            this.current = this.arrayStudent.findIndex((item) => item.studentCode === this.studentCode && item.studentFormMerchant === this.merchantCode);
            this.handleStudentAndMerchant(this.studentCode, this.merchantCode, this.studentInfo.content, this.current);
          } else {
            this.isactive = -1;
            this.$refs.popopChooseSchool.open();
          }
        } catch (error) {
          console.error('获取门店数据出错:', error);
          if (this.info[0]?.studentFormMerchant == '') {
            this.$refs.popopChooseStudent.close();
            return;
          }
          this.$util.alter('获取门店数据出错');
        }
      },

      //点击选择校区
      chooseSchoollist(item, index) {
        this.merchantCode = item.merchantCode;
        this.isChoosemerchantCode = index;
      },
      // 选择校区确定
      confirmSchool() {
        if (!this.merchantCode) {
          uni.showToast({
            title: '请选择校区',
            icon: 'none',
            duration: 3000
          });
          return;
        }

        // 确保studentCode存在
        if (!this.studentCode) {
          console.error('未获取到学员编号');
          return;
        }

        // 原有业务逻辑
        if (this.pyfOrRead) {
          uni.navigateTo({
            url: `/ReadForget/index?merchantCode=${this.merchantCode}&studentCode=${this.pyfStudentCode}`
          });
        } else {
          // this.getPadToken(this.merchantCode);
        }
        // 处理学员和门店code
        this.current = this.arrayStudent.findIndex((item) => item.studentCode === this.studentCode && item.studentFormMerchant === this.merchantCode);
        this.handleStudentAndMerchant(this.studentCode, this.merchantCode, this.studentInfo.content, this.current);
        this.closeDialog();
      },
      handleStudentAndMerchant(studentCode, merchantCode, realName, current, grade) {
        // 1. 保存到本地存储
        uni.setStorageSync('currentStudentCode', studentCode);
        uni.setStorageSync('currentMerchantCode', merchantCode);
        uni.setStorageSync('currentStudentName', realName);
        uni.setStorageSync('currentStudent', current);
        uni.setStorageSync('gradeDXN', grade);
        this.currentStudentCode = uni.getStorageSync('currentStudentCode');
        this.currentMerchantCode = uni.getStorageSync('currentMerchantCode');
        this.currentRealName = uni.getStorageSync('currentStudentName');
        this.current = uni.getStorageSync('currentStudent');
        if (this.currentStudentCode !== '') {
          this.getCourse();
          this.getRecentlyUsed();
        }
      },
      //立即购买趣味复习
      nowBuy() {
        let code = this.studentCode;
        console.log('立即购买趣味复习');
        this.closeDialog();
        uni.navigateTo({
          url: `/Personalcenter/interest/orderDetail?studentCode=${code}`
        });
      },
      getIntegral() {
        getApp().sensors.track('integralCenterClick', {
          name: '积分中心'
        });
        $navigationTo('Personalcenter/my/myIntegral');
      },
      //关闭弹窗
      closeDialog() {
        this.pyfOrRead = 0;
        this.isactive = -1;
        this.studentCode = '';
        this.merchantCode = '';
        this.isChoosemerchantCode = -1;
        this.oneStudy = false;
        this.$refs.popopChooseStudent.close();
        this.$refs.popopChooseSchool.close();
        this.$refs.popopPower.close();
      },
      // 获取pad token
      getPadToken(scheduleCode) {
        let that = this;
        var logintoken = uni.getStorageSync('token');
        uni.showLoading();
        httpUser
          .get(`new/security/v2/login/student/member/token?memberToken=${logintoken}&studentCode=${uni.getStorageSync('currentStudentCode')}&merchantCode=${that.merchantCode}`)
          .then((res) => {
            uni.hideLoading();
            if (res.data.success) {
              uni.setStorageSync('logintokenReview', res.data.data.token);
              if (that.LessonpPracticeSelect) {
                this.getCourseHours(res.data.data.token);
                return;
              }
              that.isactive = -1;
              if (that.aiIntelligentWords) {
                uni.navigateTo({
                  url: `/aiIntelligentWords/wordsSelect/wordsSelect?studentCode=${that.studentCode}&merchantCode=${that.merchantCode}`
                });
              }
              if (that.interestSelect) {
                uni.navigateTo({
                  url: `/antiAmnesia/review/funReview?studentCode=${uni.getStorageSync('currentStudentCode')}&merchantCode=${uni.getStorageSync('currentMerchantCode')}`
                });
                that.clickUsed(uni.getStorageSync('itemDYYId'));
              }
              this.closeDialog();
            } else {
              that.$util.alter(res.data.message);
            }
          });
      },
      goLessonpPractice() {
        if (!uni.getStorageSync('token')) {
          this.goToLogin();
          return;
        }
        this.LessonpPracticeSelect = true;
        this.interestSelect = false;
        this.aiIntelligentWords = false;

        this.oneStudy = true;
      },

      // 学员进度详情
      async getDetails() {
        let result = await httpUser.post('deliver/app/parent/getScheduleInfo/' + this.studentcode);
        if (result.data.success) {
          this.schedule = result.data.data;
          this.percentage = this.schedule.progress;
        }
      },
      skintap(url, name) {
        getApp().sensors.track('studyServiceItemClick', {
          name: name
        });
        if (uni.getStorageSync('token')) {
          $navigationTo(url);
        } else {
          this.goToLogin();
        }
      },
      close() {
        this.show = false;
      },
      // 手机号授权
      async onGetPhoneNumber(e) {
        let _this = this;
        uni.showLoading({
          title: '请等待',
          mask: false
        });
        // 检查登录态是否过期
        wx.checkSession({
          success: async function (res) {
            const encryptedData = e.detail.encryptedData;
            const iv = e.detail.iv;
            if (e.detail.errMsg == 'getPhoneNumber:ok') {
              const resdata = await $http({
                url: 'zx/common/decodeWechatPhone',
                method: 'POST',
                data: {
                  code: _this.code1,
                  encryptedData: encryptedData,
                  iv: iv
                }
              });
              if (resdata) {
                _this.show = false;
                uni.showToast({
                  title: '手机号码授权成功',
                  icon: 'none'
                });
                _this.homeData();
              }
            } else {
              uni.showToast({
                title: '请重新获取',
                icon: 'none'
              });
            }
          },
          fail(err) {
            uni.login({
              success: (res) => {
                _this.code1 = res.code;
              }
            });
          }
        });
      },
      // 获取首页信息
      async homeData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.pageShow = false;
          _this.userinfo = res.data;
          _this.getSchedule(); // 学员进度列表
          _this.getStudyDetail();
          _this.get;
          uni.setStorageSync('user_id', res.data.userId);
          if (!res.data.mobile) {
            _this.show = true;
          }
          uni.setStorageSync('isBindPayPhone', res.data.isBindPayPhone || 0); //是否绑定手机号
          uni.setStorageSync('isMember', res.data.isMember);
          uni.setStorageSync('identityType', res.data.identityType);

          uni.setStorageSync('certStatus', res.data.certStatus); //0未认证  1已认证
          uni.setStorageSync('merchantId', res.data.merchantId);
          uni.setStorageSync('headPortrait', res.data.headPortrait);
          uni.setStorageSync('nickName', res.data.nickName);
          uni.setStorageSync('userCode', res.data.userCode);
          uni.setStorageSync('log_userCode', res.data.userCode);
        }
      },

      // 用户授权
      subScribeMsg(url) {
        var that = this;
        uni.getSetting({
          withSubscriptions: true,
          success(res) {
            if (!res.subscriptionsSetting.mainSwitch) {
              uni.openSetting({
                //打开设置页
                success(res) {
                  console.log('打开设置页', res.authSetting);
                }
              });
            } else {
              uni.requestSubscribeMessage({
                tmplIds: ['a2pCjUTIytYmKob9R2w5u_e4OrvqqhV5s4EOhe9NLts'],
                success(res) {
                  console.log('requestSubscribeMessage 订阅信息', res);
                  if (res['a2pCjUTIytYmKob9R2w5u_e4OrvqqhV5s4EOhe9NLts'] == 'accept') {
                    // 用户点击确定后
                    console.log('用户订阅点击确定按钮');
                  } else {
                    console.log('拒绝');
                  }
                },
                fail(errMessage) {
                  console.log('订阅消息 失败', errMessage);
                }
              });
            }
          }
        });
      }
    },

    onShareAppMessage(res) {
      return {
        title: '阿拉鼎星球会员',
        success(res) {},
        fail(res) {}
      };
    }
  };
</script>

<style lang="scss" scoped>
  .fullscreen-popup {
    width: 100vw;
    height: 100vh;
    background: url('https://document.dxznjy.com/dxSelect/share/img_download_app.png') no-repeat;
    background-size: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 99999;

    .downLoad {
      position: absolute;
      left: 50%;
      bottom: 90rpx;
      transform: translate(-50%, 0);

      .downLoad-btn {
        width: 360rpx;
        height: 80rpx;
        border-radius: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        background: #339378;
      }

      .download_tip {
        color: #339378;
        width: 360rpx;
        text-align: center;
        margin-top: 10rpx;
      }
    }
  }

  .fullscreen-image {
    width: 100%;
    max-width: 750rpx;
    z-index: 99999;
  }

  /deep/ .u-toolbar.data-v-55c89db1 {
    border-bottom: 1px solid #e0e0e0;
  }

  /deep/ .u-line-1 {
    line-height: 68rpx !important;
    background-color: #f4f4f4 !important;
  }

  /deep/ .u-picker__view {
    height: 440rpx !important;
  }

  /deep/ .u-picker__view__column.data-v-f45a262e {
    border-radius: 12rpx;
  }

  /deep/ .u-popup__content.data-v-3a231fda {
    border-radius: 12rpx;
    margin: 0 20rpx 20rpx 20rpx;
  }

  /deep/ .u-picker__view__column__item {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 30rpx !important;
    margin-left: 10rpx;
  }

  /deep/ .u-toolbar__title.data-v-6d25fc6f {
    color: #303133;
    padding: 0 60rpx;
    font-size: 16px;
    font-weight: 700;
    flex: 1;
    text-align: center;
    background-color: #ffffff !important;
  }

  /deep/ .u-toolbar.data-v-6d25fc6f {
    height: 42px;
    width: 96%;
    display: flex;
    margin: 0 auto;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e0e0e0;
    padding: 20rpx 0;
    box-sizing: b;
  }

  .personl_header_bg {
    height: 462upx;
    background: linear-gradient(to bottom, #2f8c70, #c7e0d8);
  }

  .banner_indicator_style {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    position: absolute;
    bottom: -40rpx;
    left: 50%;
    width: 48rpx;
    height: 8rpx;
    background-color: #fae1c5;
    transform: translateX(-50%);
    border-radius: 4rpx;

    .indicator_style_css {
      height: 8rpx;
      border-radius: 4rpx;
    }

    .active_current {
      height: 8rpx;
      background-color: #fd9b2a;
      border-radius: 4rpx;
    }
  }

  .page_title {
    position: absolute;
    top: 80upx;
    width: 100%;
    text-align: center;
  }

  .study_service {
    padding-left: 24rpx;
  }

  page {
    background-color: #f7f7f7;
  }

  .home_bg {
    height: 160rpx;
  }

  .icon_img {
    position: absolute;
    top: 80rpx;
    left: 30rpx;
    z-index: 999 !important;
  }

  /deep/ .mlr-20 {
    margin-right: 0 !important;
  }

  .box {
    position: absolute;
    right: -5rpx;
  }

  /deep/ .data-v-26a60cd2 {
    padding: 0;
  }

  /deep/ .wid36 {
    width: 39upx;
    height: 39upx;
  }

  .home_con {
    position: relative;
    border-radius: 20upx 20upx 0 0;
  }

  .nicknameElli {
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .phonecon {
    padding: 50upx 30upx;
  }

  .login_in {
    background-image: linear-gradient(to right, #329e8b, #2c6e62);
    height: 100upx;
    border-radius: 50upx;
    width: 500upx;
    line-height: 100upx;
    margin-top: 50upx;
  }

  /deep/ .swiper-box {
    height: 100rpx !important;
  }

  /deep/ .data-v-1fff95d2 {
    height: 100rpx !important;
  }

  /deep/ .u-badge--not-dot.data-v-662d25bf {
    padding: 2rpx 6rpx;
  }

  // 学习进度
  .plan {
    position: absolute;
    top: 0;
    border-radius: 30rpx;
    z-index: 9;
    color: #000;
    height: 240rpx;
    width: 92%;
  }

  .title {
    position: relative;
    font-size: 32rpx;
    font-family: Microsoft YaHei;
    padding: 30rpx 0 0 0;
    margin-left: 30rpx;
    margin-right: 30rpx;
  }

  .title-line {
    height: 1rpx;
    background-color: #eeeeee;
    margin-top: 60rpx;
  }

  .title-name {
    position: absolute;
    left: 0;
  }

  .title-merchant {
    position: absolute;
    right: 0;
  }

  .hour {
    padding: 20rpx 30rpx 0 30rpx;
    color: #000;
  }

  .time {
    font-size: 26rpx;
    margin-bottom: 20rpx;
  }

  .w44 {
    width: 44upx;
    height: 44upx;
  }

  .w33 {
    width: 28upx;
    height: 28upx;
    margin-left: 6upx;
  }

  /deep/ .uni-swiper__warp swiper {
    height: 296rpx !important;
  }

  .myClassList {
    height: 100upx;
    box-sizing: border-box;
    line-height: 100upx;
  }

  .flex_s {
    display: flex;
  }

  .border-t {
    border-top: 1px dashed #eee;
  }

  .shike-img {
    position: absolute;
    width: 45rpx;
    height: 45rpx;
    top: 320rpx;
    left: 210rpx;
  }

  .templateItem {
    width: 100%;
    text-align: center;
    display: flex;
    align-content: flex-start;
    flex-flow: row wrap;
    font-size: 24rpx;
  }

  .template {
    flex: 0 0 25%;
    margin-bottom: 50rpx;
    font-size: 24rpx;
  }

  /* 弹窗样式 */
  .dialogBG {
    width: 100%;
    /* height: 100%; */
  }

  /* 21天结束复习弹窗样式 */
  .reviewCard_box {
    width: 670rpx;
    /* height: 560rpx; */
    position: relative;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }

  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 145rpx;
    z-index: -1;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }

  .dialogContent {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }

  .review_btn {
    width: 240upx;
    height: 80upx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 80upx;
    margin: 60rpx auto 0 auto;
    justify-content: center;
    text-align: center;
  }

  .addclass {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #2e896f;
    border: 1px solid #2e896f;
    border-radius: 35rpx;
  }

  .not-selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #000;
    border: 1px solid #c8c8c8;
    border-radius: 35rpx;
  }

  .scroll-Y {
    height: 520rpx;
    /* 固定高度，允许上下滚动 */
    overflow-y: scroll;
    /* 启用垂直滚动 */
  }

  .btn_orange {
    background: linear-gradient(to bottom, #88cfba, #1d755c);
    color: #fff !important;
    height: 60rpx;
    width: 150rpx;
    font-size: 28rpx;
    line-height: 60rpx;
    border-radius: 45rpx;
    border: none !important;
  }

  /deep/ .uni-swiper__dots-box {
    bottom: 75rpx !important;
  }

  /deep/ .uni-swiper__dots-item {
    width: 6rpx !important;
    height: 6rpx !important;
  }

  .course-img {
    margin-left: 30rpx;
    margin-right: 30rpx;
    height: 254rpx;
  }

  // 21天抗遗忘
  /* 底部按钮样式 */
  .mask-footer {
    margin-top: 20px;
    display: flex;
    justify-content: space-around;
  }

  .mask-footer button {
    width: 250rpx;
    height: 80rpx;
    font-size: 30rpx;
    border-radius: 45rpx;
  }

  .confirm-button {
    display: flex;
    justify-content: space-around;
    align-items: center;
    /* 文本垂直居中对齐 */
    background: #2e896f;
    color: #ffffff !important;
  }

  .cancel-button {
    display: flex;
    justify-content: space-around;
    align-items: center;
    /* 文本垂直居中对齐 */
    color: #2e896f !important;
    border: 1rpx solid #2e896f !important;
  }

  .empty-img {
    width: 114rpx;
    height: 107rpx;
  }

  .empty-content {
    z-index: 9;
    width: 100%;
    display: flex;
    margin-bottom: 30rpx !important;
    padding-top: 18rpx;
    flex-direction: column;
    /* 垂直布局，子视图按列排列 */
    justify-content: center;
    /* 水平居中 */
    align-items: center;
    /* 垂直居中 */
  }

  .header_css {
    background: url('https://document.dxznjy.com/course/7cf268d57fba49739ee8514bfe813b8f.png') no-repeat;
    background-size: 100%;
    width: 750rpx;
    height: 460rpx;
    padding-top: 40rpx;

    .box-128 {
      width: 128rpx;
      height: 128rpx;
    }

    .identity_css {
      color: #006f57;
      line-height: 34rpx;
      padding: 1rpx 40rpx;
      display: inline-block;
      background-color: #dfffe4;
    }

    .center_css {
      width: 270rpx;
      margin-left: 2rpx;
    }

    .content_right {
      height: 60rpx;
      margin-left: 150rpx;

      .integral_css {
        vertical-align: middle;
        display: block;
        margin-right: 18rpx;
      }
    }

    .content_main_css {
      width: 686rpx;
      margin: 0 auto;
      height: 254rpx;
      margin-bottom: 74rpx;
    }

    .content_main_css_item {
      width: 638rpx !important;
      border-radius: 24rpx;
      margin: 0 auto;
      padding: 18rpx 24rpx;
      height: 219rpx !important;
    }

    .no_study_time {
      background-color: #f6fcff;
      border-radius: 16rpx;
      width: 636rpx;
      text-align: center;
    }

    .study_time {
      display: flex;
      justify-content: space-around;
      align-items: center;
      background-color: #f6fcff;
      padding-top: 20rpx;
      padding-bottom: 32rpx;
      margin-top: 24rpx;

      .study_time_item {
        width: 33%;
        text-align: center;
        position: relative;

        .green_css {
          color: #339378;
          line-height: 54rpx;
          display: inline-block;
        }
      }

      .study_time_item::after {
        content: ' ';
        height: 48rpx;
        border-right: 2rpx solid #ebf2f6;
        position: absolute;
        top: 30rpx;
        right: 0;
      }

      .study_time_item:last-child::after {
        display: none;
      }
    }
  }
</style>
