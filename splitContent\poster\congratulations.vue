<!-- 喜报 -->
<template>
  <div>
    <page-meta :page-style="'overflow:' + (rollShow ? 'hidden' : 'visible')"></page-meta>
    <view class="plr-30 pb-30 pt-30">
      <view class="bg-ff radius-15">
        <canvas canvas-id="poster" class="poster_canvas"></canvas>
        <view>
          <swiper
            class="swiper"
            :autoplay="false"
            :interval="5000"
            :duration="500"
            previous-margin="0"
            next-margin="0"
            :circular="circular"
            @change="swiperBindchange"
            :current="currentSwiperIndex"
          >
            <block v-for="(item, index) in posterList" :key="index">
              <swiper-item>
                <view class="swiper_item">
                  <image :src="item" class="slide-image" mode="aspectFill" lazy-load="true" />
                </view>
                <view class="input-pos">
                  <!-- <input name="username" v-model="username" type="text" :maxlength="18" placeholder="" class="input" /> -->
                  <input @input="handleInput" :maxlength="18" name="username" v-model="username" type="text" placeholder="请输入" class="input" />
                </view>
              </swiper-item>
            </block>
          </swiper>
        </view>
        <view class="flex-dir-row flex-x-c mt-20 pb-30">
          <!-- <view class="flex-a-c flex-x-e border_t pt-30 pb-20"> -->
          <button class="changeBtn f-30 c-ff t-c" @tap="handleChange">换一换</button>
          <button :loading="generateLoading" class="saveBtn f-30 c-ff t-c" @tap="handleGenerate">生成</button>
          <!-- </view> -->
        </view>
      </view>
    </view>

    <!-- 温馨提示 -->
    <uni-popup ref="popopTips" type="center" @change="change">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold">温馨提示</view>
            <view class="t-c mb-10 ptb-5 mt-35">请点击下方保存按钮保存喜报</view>
            <view class="review_btn" @click="handleSave">保存</view>
          </view>
        </view>
      </view>
    </uni-popup>
  </div>
</template>

<script>
  const { $http, $navigationTo, $showMsg } = require('@/util/methods.js');
  let ctx = null;
  export default {
    data() {
      return {
        circular: true,
        posterList: [
          'https://document.dxznjy.com/course/c4adf31cc6bd4d20a9630b6bde07bdf4.png',
          'https://document.dxznjy.com/course/bf52e8c83dca471a8ad46fcb43497b52.jpg',
          'https://document.dxznjy.com/course/f853e4e6146a4022a103561756083870.jpg'
        ],
        currentSwiperIndex: 0,
        code: null,
        posterBg: null,
        pageShow: false,
        canvasImg: null,
        userId: '',
        imgHost: getApp().globalData.imgsomeHost,
        mealLists: [],
        isClose: false, //是否关闭当前页
        username: '',
        generateLoading: false,
        rollShow: false
      };
    },
    onLoad(options) {},
    created() {},
    onReady() {
      ctx = uni.createCanvasContext('poster', this);
    },
    onShow() {
      if (uni.getStorageSync('token')) {
        this.homeData();
      } else {
        uni.navigateTo({
          url: '/pagePersonalcenters/login/login'
        });
      }
    },
    onUnload() {
      uni.hideLoading();
    },
    onHide() {
      uni.hideLoading();
    },

    methods: {
      handleInput(e) {
        if (this.validateInput(e.detail.value)) {
          this.username = e.detail.value;
        }
      },
      change(e) {
        this.rollShow = e.show;
      },
      closeDialog() {
        this.$refs.popopTips.close();
      },
      skintap(url) {
        $navigationTo(url);
      },
      async homeData() {
        this.currentSwiperIndex = 0;
        const res = await $http({
          url: `zx/user/userInfoNew`
        });
        if (res) {
          this.pageShow = true;
          uni.setStorageSync('user_id', res.data.userId);
          uni.setStorageSync('identityType', res.data.identityType);
          uni.setStorageSync('phone', res.data.mobile);
        }
      },
      validateInput(input) {
        const maxChineseChars = 6;
        const maxDigits = 11;
        const maxLetters = 18;
        const maxLength = 18;
        const chineseCharRegex = /[\u4e00-\u9fa5]/g;
        const digitRegex = /\d/g;
        const letterRegex = /[a-zA-Z]/g;
        const emojiRegex = /[\uD800-\uDBFF][\uDC00-\uDFFF]/g;

        // 计算汉字数量
        const chineseCharCount = (input.match(chineseCharRegex) || []).length;
        // 计算数字数量
        const digitCount = (input.match(digitRegex) || []).length;
        // 计算字母数量
        const letterCount = (input.match(letterRegex) || []).length;
        // 检查是否包含表情包
        const containsEmoji = emojiRegex.test(input);

        // 计算总长度，汉字算2个字符
        const totalLength = input.length + chineseCharCount;

        // 验证输入内容
        if (containsEmoji) {
          $showMsg('不支持输入表情符号');
          return false;
        }
        if (digitCount > 0 && chineseCharCount === 0 && letterCount === 0 && digitCount > maxDigits) {
          $showMsg('最多输入11位数字');
          return false;
        }
        if (letterCount > 0 && chineseCharCount === 0 && digitCount === 0 && letterCount > maxLetters) {
          $showMsg('最多输入18位字母');
          return false;
        }
        if (chineseCharCount > 0 && digitCount === 0 && letterCount === 0 && chineseCharCount > maxChineseChars) {
          $showMsg('最多输入6个汉字');
          return false;
        }
        if (totalLength > maxLength) {
          $showMsg('最多输入18个字符');
          return false;
        }
        return true;
      },
      drawCanvas(crtImg) {
        let _this = this;
        if (this.isClose) return;
        this.generateLoading = true;
        uni.showLoading({
          title: '生成中'
        });

        uni.getImageInfo({
          src: crtImg,
          success(res) {
            _this.posterBg = res.path;
            console.log('🚀 ~ success ~ posterBg:', _this.posterBg);
            ctx.beginPath();
            ctx.setFillStyle('#fff');
            ctx.fillRect(0, 0, 375, 667);
            ctx.restore();

            ctx.beginPath();
            ctx.drawImage(_this.posterBg, 0, 0, 750, 1334);
            ctx.restore();

            // 用户昵称
            const text_size = 56;
            ctx.setFontSize(uni.upx2px(text_size));
            ctx.setFillStyle('#ffffff');
            // ctx.fillText(_this.username, 245, 895);
            // ctx.restore();

            // 绘制带省略号的文本
            const maxWidth = 260; // 最大宽度
            const x = 260; // 文本起始x坐标
            const y = 880; // 文本起始y坐标
            const text = _this.username;
            const ellipsis = '...';

            // 计算文本宽度并截断
            let truncatedText = text;
            if (ctx.measureText(text).width > maxWidth) {
              while (ctx.measureText(truncatedText + ellipsis).width > maxWidth) {
                truncatedText = truncatedText.slice(0, -1);
              }
              truncatedText += ellipsis;
            }
            ctx.fillText(truncatedText, x, y);
            ctx.restore();

            ctx.draw(false, () => {
              // canvas画布转成图片并返回图片地址
              uni.canvasToTempFilePath(
                {
                  canvasId: 'poster',
                  width: 750 * 2,
                  height: 1334 * 2,
                  success: (result) => {
                    _this.canvasImg = result.tempFilePath;
                    console.log('🚀 ~ file ~ result.tempFilePath:', _this.canvasImg);
                    _this.generateLoading = false;
                    uni.hideLoading();
                    _this.$refs.popopTips.open();
                    // uni.showModal({
                    //   title: '喜报生成成功',
                    //   content: '请点击下方保存按钮保存喜报',
                    //   showCancel: false,
                    //   confirmText: '保存',
                    //   success: () => {
                    //     _this.$refs.popopTips.open();
                    //   },
                    //   fail: (err) => {
                    //     console.log('海报保存失败！', err);
                    //   }
                    // });
                  },
                  fail: (err) => {
                    _this.generateLoading = false;
                    uni.hideLoading();
                    console.log('海报制作失败！', err);
                  }
                },
                _this
              );
            });
          },
          fail: function (err) {
            uni.hideLoading();
            console.log(err);
          }
        });
      },
      swiperBindchange(e) {
        this.currentSwiperIndex = e.detail.current;
      },

      handleChange() {
        if (this.currentSwiperIndex === this.posterList.length - 1) {
          this.currentSwiperIndex = 0;
        } else {
          this.currentSwiperIndex = this.currentSwiperIndex + 1;
        }
      },
      // 生成海报
      handleGenerate() {
        if (!this.username) {
          $showMsg('请输入姓名');
          return;
        }
        const isAllowText = this.validateInput(this.username);
        console.log(isAllowText);
        if (isAllowText) {
          $http({
            url: `zx/common/isSensitiveWord?message=${this.username}`
          }).then((res) => {
            if (!res.data) {
              $showMsg('请勿输入敏感词');
              return;
            }
            this.drawCanvas(this.posterList[this.currentSwiperIndex]);
          });
        }
      },
      // 保存海报
      handleSave() {
        let _this = this;
        uni.getImageInfo({
          src: this.canvasImg,
          success: function (res1) {
            uni.saveImageToPhotosAlbum({
              filePath: res1.path,
              success: function (res) {
                _this.closeDialog();
                uni.showToast({
                  title: '保存成功',
                  icon: 'success'
                });
              },
              fail: function (res) {
                if (
                  res.errMsg === 'saveImageToPhotosAlbum:fail:auth denied' ||
                  res.errMsg === 'saveImageToPhotosAlbum:fail auth deny' ||
                  res.errMsg === 'saveImageToPhotosAlbum:fail authorize no response'
                ) {
                  // 这边微信做过调整，必须要在按钮中触发，因此需要在弹框回调中进行调用
                  uni.showModal({
                    title: '提示',
                    content: '需要您授权保存相册',
                    showCancel: false,
                    success: (modalSuccess) => {
                      uni.openSetting({
                        success(settingdata) {
                          if (settingdata.authSetting['scope.writePhotosAlbum']) {
                            uni.showModal({
                              title: '提示',
                              content: '获取权限成功,再次点击图片即可保存',
                              showCancel: false
                            });
                          } else {
                            uni.showModal({
                              title: '提示',
                              content: '获取权限失败，将无法保存到相册哦~',
                              showCancel: false
                            });
                          }
                        },
                        fail(failData) {
                          console.log('failData', failData);
                        },
                        complete(finishData) {
                          console.log('finishData', finishData);
                        }
                      });
                    }
                  });
                }
              }
            });
          }
        });
      },
      //分享好友
      share() {
        if (this.posterList.length == 0) {
          uni.showToast({
            icon: 'none',
            title: '海报生成中'
          });
          return;
        }
        uni.share({
          provider: 'weixin',
          scene: 'WXSceneSession',
          type: 2,
          imageUrl: this.posterList[this.currentSwiperIndex],
          success: function (res) {
            console.log('success:' + JSON.stringify(res));
            uni.showToast({
              icon: 'none',
              title: '分享成功'
            });
          },
          fail: function (err) {
            console.log('fail:' + JSON.stringify(err));
            uni.showToast({
              icon: 'none',
              title: '分享失败'
            });
          }
        });
      },
      //分享朋友圈
      shareBig() {
        if (this.posterList.length == 0) {
          uni.showToast({
            icon: 'none',
            title: '海报生成中'
          });
          return;
        }
        uni.share({
          provider: 'weixin',
          scene: 'WXSceneTimeline',
          type: 2,
          imageUrl: this.posterList[this.currentSwiperIndex],
          success: function (res) {
            console.log('success:' + JSON.stringify(res));
            uni.showToast({
              icon: 'none',
              title: '分享成功'
            });
          },
          fail: function (err) {
            console.log('fail:' + JSON.stringify(err));
            uni.showToast({
              icon: 'none',
              title: '分享失败'
            });
          }
        });
      }
    },

    onUnload() {
      this.isClose = true;
      uni.hideLoading();
    }
  };
</script>

<style lang="less" scoped>
  /* 弹窗样式 */
  .dialogBG {
    width: 100%;

    .reviewCard_box {
      width: 670rpx;
      position: relative;
    }

    .reviewCard_box image {
      width: 100%;
      height: 100%;
    }

    .reviewCard {
      position: relative;
      width: 100%;
      height: 100%;
      background: #ffffff;
      color: #000;
      border-radius: 24upx;
      padding: 50upx 55upx;
      box-sizing: border-box;
    }

    .cartoom_image {
      width: 420rpx;
      position: absolute;
      top: -250rpx;
      left: 145rpx;
      z-index: -1;
    }

    .review_close {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      z-index: 1;
    }

    .reviewTitle {
      width: 100%;
      text-align: center;
      font-size: 34upx;
      display: flex;
      justify-content: center;
    }

    .dialogContent {
      box-sizing: border-box;
      font-size: 32upx;
      line-height: 45upx;
      text-align: center;
      margin-top: 40rpx;
    }

    .review_btn {
      width: 240upx;
      height: 80upx;
      background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
      border-radius: 45upx;
      font-size: 30upx;
      color: #ffffff;
      line-height: 80upx;
      margin: 60rpx auto 0 auto;
      justify-content: center;
      text-align: center;
    }
  }

  .text-con {
    position: absolute;
    bottom: 300rpx;
    left: 50%;
    margin-left: -250rpx;
    width: 500rpx;
    height: 300rpx;
    text-align: center;
  }

  .changeBtn {
    color: #4e9f87;
    border: 2rpx solid #4e9f87;
    width: 100%;
    height: 90upx;
    line-height: 90upx;
    border-radius: 45upx;
    margin: 0 10rpx;
  }

  .saveBtn {
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    width: 100%;
    height: 90upx;
    line-height: 90upx;
    border-radius: 45upx;
    margin: 0 20rpx;
  }

  .swiper_item {
    height: 100%;
  }

  .swiper {
    width: 690rpx;
    height: 1227rpx;
    position: relative;

    .input-pos {
      position: absolute;
      top: 770rpx;
      left: 240rpx;
      width: 260rpx;
      height: 50rpx;
      z-index: 9;
      // border: 1px solid goldenrod;

      .input {
        width: 100%;
        height: 100%;
        color: #fff;
        font-size: 28rpx;
      }

      /deep/.input-placeholder {
        color: #eee;
      }
    }
  }

  .slide-image {
    width: 100%;
    height: 100%;
  }

  .shareline {
    width: 10upx;
    height: 32upx;
    background-color: #006658;
  }

  .icon_hyh {
    width: 38upx;
  }

  .poster_canvas {
    width: 750px;
    height: 1334px;
    position: fixed;
    top: 2000upx;
  }

  .wh160 {
    width: 160rpx;
  }

  .colour {
    color: #ea6031;
  }

  .marginTop100 {
    margin-top: 100rpx;
  }

  .orange {
    color: #f17427;
  }
</style>