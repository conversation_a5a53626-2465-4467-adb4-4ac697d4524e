<!-- 陪跑营 -->
<template>
  <page-meta :page-style="'overflow:' + (show ? 'hidden' : 'visible')"></page-meta>
  <view class="jogging">
    <view class="title-bg"></view>
    <view class="banner-swiper">
      <uni-swiper-dot :info="bannerList" :current="bannerIndex" mode="dot" field="content" :dotsStyles="dotsStyles">
        <swiper
          @change="(e) => (bannerIndex = e.detail.current)"
          style="height: 306rpx"
          :autoplay="true"
          :indicator-dots="false"
          circular
          :current="bannerIndex"
          :interval="5000"
          :duration="1000"
        >
          <block v-for="(item, index) in bannerList" :key="index">
            <swiper-item class="flex-c radius-16 swiper_css_item">
              <image :src="item" mode="aspectFill" class="wh100" lazy-load="true"></image>
            </swiper-item>
          </block>
        </swiper>
      </uni-swiper-dot>
    </view>

    <view class="content">
      <view class="subtitle">
        <view class="left"></view>
        <view class="center">陪跑计划</view>
        <view class="right"></view>
      </view>

      <view class="camp-list" v-if="campList.length > 0">
        <view v-for="(item, index) in campList" :key="index" class="camp-item" @click="handleGo(item)">
          <view class="poster">
            <image mode="aspectFill" lazy-load="true" :src="item.courseImage" style="width: 100%; height: 100%"></image>
          </view>
          <view class="desc">
            <view class="name">
              {{ item.courseName }}
              <!-- <template v-if="item.buyStatus == 1">(已报名)</template> -->
            </view>
            <view class="time">活动时间</view>
            <view class="time">{{ item.endBuyTime }}</view>
          </view>
        </view>
      </view>
      <view v-else class="t-c flex-col" :style="{ height: 500 + 'rpx' }">
        <image :src="imgHost + 'alading/correcting/no_data.png'" style="width: 160rpx" class="mb-20 img_s" mode="widthFix"></image>
        <view style="color: #bdbdbd">暂无数据</view>
      </view>
    </view>
  </view>
</template>

<script>
  const { $http, $navigationTo } = require('@/util/methods.js');
  export default {
    data() {
      return {
        show: false, //禁止穿透
        currentCampAct: null,
        bannerIndex: 0,
        dotsStyles: {
          backgroundColor: '#CCEBE6',
          selectedBackgroundColor: '#054036'
        },
        bannerList: ['https://document.dxznjy.com/course/b0168d09cb174ea18c228719d40b631c.jpg'],
        campList: [],
        imgHost: getApp().globalData.imgsomeHost
      };
    },
    components: {},
    computed: {},
    mounted() {
      uni.showLoading({
        title: '加载中...'
      });
      this.getCampList();
    },
    methods: {
      // 跳转去活动页
      handleGo(item) {
        uni.navigateTo({
          url: '/Coursedetails/my/joggingCampAct?id=' + item.courseId
          // url: `/Coursedetails/my/joggingCampAct?data=${encodeURIComponent(item)}`
        });
      },
      getCampList() {
        $http({
          url: 'zx/course/courseList',
          data: {
            usageScenario: 1,
            phone: uni.getStorageSync('phone'),
            indexShow: 0,
            page: 1,
            pageSize: 100
          }
        }).then((res) => {
          this.campList = res.data.list;
        });
      }
    },
    onShow() {
      let token = uni.getStorageSync('token');
      if (!token) {
        uni.navigateTo({
          url: '/Personalcenter/login/login'
        });
      }
    },

    onLoad(options) {
      console.log(options);
    }
  };
</script>
<style lang="scss" scoped>
  .camp-item {
    display: flex;
    justify-content: space-between;
    padding: 24rpx 16rpx;
    align-items: center;
    background: #035a4d;
    border-radius: 16rpx;
    margin-bottom: 24rpx;

    &:nth-child(1) {
      margin-top: 32rpx;
    }

    .poster {
      width: 42%;
      height: 192rpx;
      border-radius: 8rpx;
      background: #d8d8d8;
      overflow: hidden;
    }

    .desc {
      width: 58%;
      padding: 24rpx 16rpx;
      .name {
        font-size: 28rpx;
        color: #f7e7af;
        line-height: 40rpx;
      }
      .time {
        margin-top: 8rpx;
        font-size: 28rpx;
        color: #ffffff;
        line-height: 40rpx;
      }
    }
  }

  .jogging {
    width: 100%;
    min-height: 1458rpx;
    padding-bottom: 20rpx;
    background-color: #01392f;
    background-image: url('https://document.dxznjy.com/course/6587b1bd9ba74f008ed5b7542f5497fc.png');
    background-repeat: no-repeat;
    background-size: contain;
    box-sizing: border-box;
    padding-top: 55rpx;
    .title-bg {
      width: 188rpx;
      height: 68rpx;
      margin: 0 auto 29rpx;
      background: url('https://document.dxznjy.com/course/32ee96a24b8d434e8b093702556fc20f.png') no-repeat;
      background-size: 100% 100%;
    }
  }

  .banner-swiper {
    width: 686rpx;
    height: 306rpx;
    margin: 0 auto;

    .swiper_css_item {
      overflow: hidden;

      image {
        height: 100%;
      }
    }

    /deep/.uni-swiper__dots-box {
      right: 14rpx !important;
      bottom: 14rpx !important;
      left: unset !important;
      > .uni-swiper__dots-item {
        width: 16rpx !important;
        height: 16rpx !important;
        border-color: transparent !important;
      }
    }
  }

  .content {
    width: 100%;
    padding-top: 30rpx;
    padding-right: 24rpx;
    padding-left: 32rpx;
    box-sizing: border-box;

    .center {
      height: 44rpx;
      font-size: 32rpx;
      color: #ecc492;
      line-height: 44rpx;
    }
  }

  .subtitle {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 30rpx;
    .left,
    .right {
      width: 148rpx;
      height: 2rpx;
      background: url('https://document.dxznjy.com/course/8c6e23db64064da89b62b48fbee8d46a.png') no-repeat;
      background-size: 100% 100%;
    }
    .left {
      margin-right: 16rpx;
    }
    .right {
      margin-left: 16rpx;
      transform: rotate(180deg);
    }
    .center {
      height: 44rpx;
      font-size: 32rpx;
      color: #ecc492;
      line-height: 44rpx;
    }
  }
</style>
