<template>
  <view class="plr-30">
    <view class="bg-ff radius-15 plr-30" v-for="(item, index) in mystudentslist" :key="index">
      <view class="flex-s f-30 ptb-30" @click="setUp(item)" :class="index != mystudentslist.length - 1 ? 'line' : ''">
        <view>
          {{ item.realName }}
          <text class="c-66">{{ '（' + item.studentCode + '）' }}</text>
        </view>
        <view>
          <text class="mr-15 c-66">{{ voiceName(item.voiceModel) }}</text>
          <uni-icons type="right" size="18" color="#666"></uni-icons>
        </view>
      </view>
    </view>

    <view v-if="mystudentslist.length == 0 && mystudentslist != null" :style="{ height: useHeight + 'rpx' }" class="t-c flex-col bg-ff radius-15 mt-30 f-30">
      <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s"></image>
      <view style="color: #bdbdbd">暂无数据</view>
      <view class="mt-10 add_text" @click="skintap('Personalcenter/my/mystudent?memberId=' + memberId)">请先添加学员</view>
    </view>
  </view>
</template>

<script>
  const { $navigationTo } = require('@/util/methods.js');
  export default {
    data() {
      return {
        ifshowmore: 1,
        ifshowzanwu: 1,
        mystudentslist: [],
        studentCode: '',
        memberId: '', //当前用户membercode
        useHeight: 0, //屏幕高度
        imgHost: getApp().globalData.imgsomeHost
      };
    },
    onLoad(option) {
      this.memberId = option.memberId;
      this.studentCode = option.studentCode;
    },
    onShow() {
      this.loadData();
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 60;
        }
      });
    },
    methods: {
      skintap(url) {
        $navigationTo(url);
      },
      async loadData() {
        this.mystudentslist = [];
        const that = this;
        that.$httpUser
          .get(`znyy/course/queryStudentList/1/10`, {
            memberId: that.memberId
          })
          .then((res) => {
            if (res.data.success) {
              let result = res.data.data;
              console.log('获取学员列表', result);
              if (result.data != null) {
                if (result.data.length == 0) {
                  that.mystudentslist = [];
                } else {
                  if (result.data.length > 0 && result.data.length > 0) {
                    const studentList = result.data.find((item) => item.studentCode === that.studentCode);
                    that.mystudentslist = that.mystudentslist.concat(studentList);
                    console.log('mystudentslist', that.mystudentslist);
                    if (result.data.length > 2) that.ifshowmore = 0;
                    that.ifshowzanwu = 0;
                  }
                }
              }
            } else {
              uni.showToast({
                title: res.data.message,
                icon: 'none'
              });
            }
          });
      },

      voiceName(item) {
        if (item) {
          let name = item.split('#');
          let rq = name[1] == 1 ? '英式' : '美式';
          let sex = name[2] == 'M' ? '男声' : '女声';
          return rq + '-' + sex;
        }
      },

      setUp(item) {
        let list = item.voiceModel.split('#');
        // skintap('pages/home/<USER>/pronunciationSettings?studentCode='+item.studentCode)
        let data = {
          studentCode: item.studentCode,
          v: list[0],
          rq: list[1],
          sex: list[2]
        };
        uni.navigateTo({
          url: '/Personalcenter/my/pronunciationSettings?list=' + encodeURIComponent(JSON.stringify(data))
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .line {
    border-bottom: 1px solid #eee;
  }

  .img_s {
    width: 160rpx;
    height: 160rpx;
  }

  .add_text {
    color: #2e896f;
  }
</style>
