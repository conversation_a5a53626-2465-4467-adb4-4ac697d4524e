<template>
	<view>
		<view class="flex-c mt-80 positionRelative">
			<uni-icons type="left" size="22" class="left_icon" @click="back"></uni-icons>
			<view class="f-32 bold c-00">课程规划</view>
		</view>
		<view class="search-content">
			<text class="text-content">课程名称：</text>
			<view class="search-broder">
				<input type="text" @input="inputSearch" :value="search" name="search" placeholder="请输入"
					placeholder-class="input" class="input" />
				<view class="search-text" @click="searchOption">搜索</view>
			</view>
		</view>
		<view class="content-option" :style="{'height': (useHeight-360)+'rpx'}">
			<!-- 已选择 -->
			<view v-if="courseChoseList.length>0" style="border-bottom: 1px solid #C8C8C8;margin-bottom: 30rpx;">
				<view style="margin-bottom: 15rpx;font-size: 26rpx;">已选择课程</view>
				<view class="course-item flex-s" v-for="(item,index) in courseChoseList"
					@click="choseCourse(item,index,true)">
					<view class="flex-c">
						<image class="item-icon-style"
							:src="getChoseIndexForChosed(item)==-1?'https://document.dxznjy.com/course/77eadf32d6fe49569f12b590e534df13.png':'https://document.dxznjy.com/course/8d049c03e25543249b9959c04eabd079.png'">
						</image>
						<view class="text-content f-28 course">{{item.courseName}}</view>
					</view>
					<view v-if="courseId== item.courseId" class="f-28" style="color: #2e896f;"
						@click.stop="setupThesaurus(item)">{{show?'取消首节课词库':'设为首节课词库'}}</view>
					<view v-if="!courseId && !show" class="f-28" style="color: #2e896f;"
						@click.stop="setupThesaurus(item)">{{show?'取消首节课词库':'设为首节课词库'}}</view>
				</view>
			</view>
			<view style="margin-bottom: 15rpx;font-size: 26rpx;">全部课程</view>
			<scroll-view v-if="searchResult.length>0" :style="{'height': (useHeight-getChosedHeight())+'rpx'}"
				scroll-y="true" :scroll-top="scrollTop" @scrolltolower="scrolltolower">
				<view class="course-item" @click="choseCourse(item,index,false)" v-for="(item,index) in searchResult">
					<image class="item-icon-style"
						:src="getChoseIndex(item)==-1?'https://document.dxznjy.com/course/77eadf32d6fe49569f12b590e534df13.png':'https://document.dxznjy.com/course/8d049c03e25543249b9959c04eabd079.png'"></image>
					<view class="text-content" v-html="item.courseNameCopy?item.courseNameCopy:item.courseName"></view>
				</view>
				<view v-if="no_more && searchResult.length>0">
					<u-divider text="到底了"></u-divider>
				</view>
			</scroll-view>

			<uni-popup ref="notifyPopup" type="top" mask-background-color="rgba(0,0,0,0)">
				<view class="plr-60 mt-80">
					<view class="t-c bg-ff flex-c ptb-25 radius-50 mt-30 notify">
						<image :src="imgHost+'dxSelect/image/warn.png'" class="warn"></image>
						<view class="f-32 ml-15">请选择首节课词库</view>
					</view>
				</view>
			</uni-popup>

			<view v-if="searchResult.length>0" class="top-button">
				<button class="radius-50 confirm-button" @click="confirm()">确定</button>
			</view>

			<view v-if="searchResult.length<=0" class="empty-content">
				<image :src="imgHost+'dxSelect/lake_page.png'" class="empty-img"></image>
				<view style="color: #BDBDBD;margin-top: 28rpx;">暂无数据</view>
			</view>
		</view>
	</view>
</template>

<script>
	const {
		$getSceneData,
		$showError,
		$showMsg,
		$http,
	} = require("@/util/methods.js")
	import Util from '@/util/util.js'
	const {
		httpUser
	} = require('@/util/luch-request/indexUser.js')
	import dayjs from "dayjs"
	export default {
		data() {
			return {
				useHeight: 0,
				imgHost: getApp().globalData.imgsomeHost,

				search: '',
				courseChoseList: [],
				courseListData: {},
				searchResult: [],

				scrollTop: 0,
				no_more: false,
				page: 1,
				pageSize: 20,
				curriculumId:'',
				curriculumIs:'',
				show: false, // 是否为首节课词库
				courseId: '', // 课程id

			}
		},

		onLoad(e) {
			console.log(e);
			this.curriculumId=e.curriculumId
			this.curriculumIs=e.curriculumIs
			if (JSON.stringify(e) != '{}') {
				let list = JSON.parse(decodeURIComponent(e.choseList));
				if (list&&list.length) {
					this.courseChoseList = list;
					let data = this.courseChoseList.find(item => item.isFirstWordBase == true);
					this.courseId = data.courseId;
					this.show = true;
				}
			}
		},

		onReady() {
			let that = this;
			uni.getSystemInfo({ //调用uni-app接口获取屏幕高度
				success(res) {
					// 可使用窗口高度，将px转换rpx
					let h = (res.windowHeight * (750 / res.windowWidth));
					that.useHeight = h - 65;
				}
			})
		},

		onShow() {
			this.initData();
		},

		methods: {
			// 设置首节课词库
			setupThesaurus(val) {
				this.show = !this.show;
				this.courseId = val.courseId;
				if (!this.show) {
					this.courseId = '';
				}
			},
			getChosedHeight() {
				if (this.courseChoseList.length == 1) {
					return 658;
				} else if (this.courseChoseList.length == 2) {
					return 738;
				} else if (this.courseChoseList.length == 3) {
					return 810;
				}
				return 510;
			},
			initData() {
				this.page = 1;
				this.clickHandle();
				this.getCourseList();
			},
			// 滚动条回到顶部
			clickHandle() {
				this.scrollTop = this.scrollTop === 0 ? 1 : 0;
			},
			scrolltolower() {
				if (this.page >= this.courseListData.totalPage) {
					this.no_more = true;
					return false;
				}
				++this.page;
				this.getCourseList(true);
			},

			async getCourseList(isPage) {
				let _this = this;
				uni.showLoading();
				let res ={}
				if(this.curriculumIs!=1){
					 res.data = await $http({
							url: 'zxAdminCourse/web/course/student/course/subcategories',
							method: 'get',
							data: {
								curriculumId:this.curriculumId
							}
						})
				}else{
					 res = await this.$httpUser.get(
						`znyy/course/project/list/${_this.page}/${_this.pageSize}?courseName=${_this.search}`);
				
				}
				if (res && res.data) {
					if (isPage) {
						let old = _this.courseListData.data;
						_this.courseListData.data = [...old, ...res.data.data.data];
					} else {
						_this.courseListData = res.data.data;
					}
					if(this.curriculumIs!=1){
						this.searchResult=[]
						_this.courseListData.forEach(item=>{
							this.searchResult.push({id:item.courseId,...item})
						})
					}else{
						if (_this.search.length != "") {
							_this.searchResult = _this.changeColor(_this.courseListData.data);
						} else {
							_this.searchResult = _this.courseListData.data.slice();
						}
					}
					
				}
				uni.hideLoading();
			},
			choseCourse(item, index, isChosed) {
				console.log(item)
				console.log('6666666666666666666666666666666666666666')
				let courseIndex = -1;
				if (isChosed) {
					if (item.courseId == this.courseId) {
						this.courseId = '';
						this.show = false;
					}
					courseIndex = this.getChoseIndexForChosed(item);
				} else {
					if (item.id == this.courseId) {
						this.courseId = '';
						this.show = false;
					}
					courseIndex = this.getChoseIndex(item);
				}
				if (courseIndex == -1) {
					if (this.courseChoseList.length >= 3) {
						uni.showToast({
							icon: 'none',
							title: '最多可选3个课程~'
						})
						return;
					}
					let courseId = item.id
					if (isChosed) {
						courseId = item.courseId
					}
					this.courseChoseList.push({
						courseName: item.courseName,
						courseId: courseId
					})
				} else {
					this.courseChoseList.splice(courseIndex, 1);
				}
			},

			getChoseIndex(item) {
				for (let i = 0; i < this.courseChoseList.length; i++) {
					if (this.courseChoseList[i].courseId == item.id) {
						return i
					}
				}
				return -1;
			},

			getChoseIndexForChosed(item) {
				for (let i = 0; i < this.courseChoseList.length; i++) {
					if (this.courseChoseList[i].courseId == item.courseId) {
						return i
					}
				}
				return -1;
			},

			confirm() {
				let that = this;
				console.log(that.courseId)
				if (!that.courseId) {
					that.$refs.notifyPopup.open();
					setTimeout(() => {
						that.$refs.notifyPopup.close();
					}, 1500)
					return;
				}
				that.courseChoseList = that.courseChoseList.map(item => ({
					...item,
					isFirstWordBase: that.courseId == item.courseId
				}));
				uni.$emit('coursePlan', that.courseChoseList)
				uni.navigateBack({
					delta: 1
				})
			},

			inputSearch(e) {
				this.search = e.detail.value;
				if (this.search == "") {
					this.initData();
				}
			},

			searchOption() {
				this.initData();
			},

			changeColor(arr) {
				arr.map((item, index) => {
					if (this.search) {
						let str = this.search
						// for (var i = 0; i < str.length; i++) {
						let replaceReg = new RegExp(str, "ig"); //str[i]
						let replaceString =
							`<span style="color: #EA6031;font-size: 30rpx">${str}</span>`; //str[i]
						arr[index].courseNameCopy = item.courseName.replace(replaceReg, replaceString);
						// }
					}
				});
				return arr;
			},

			back() {
				uni.navigateBack()
			}
		}
	}
</script>

<style lang="scss" scoped>
	page {
		background-color: #F3F8FC;
	}

	.search-content {
		height: 140rpx;
		background-color: #fff;
		border-radius: 14rpx;
		margin: 30rpx 30rpx;
		padding: 0 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.search-broder {
		height: 70rpx;
		width: 432rpx;
		border-radius: 14rpx;
		border: 1px solid #e0dddd;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 15rpx 0 25rpx;
	}

	.input {
		color: #999999;
		font-size: 30rpx;
	}

	.search-text {
		padding-left: 14rpx;
		color: #1D755C;
		font-size: 30rpx;
		border-left: 1px solid #EFEFEF;
	}

	.text-content {
		color: #333333;
		font-size: 30rpx;
	}

	.content-option {
		margin: 0 30rpx 30rpx 30rpx;
		background-color: #fff;
		border-radius: 14rpx;
		padding: 40rpx 30rpx 30rpx 30rpx;
	}

	.course-item {
		height: 40rpx;
		margin-bottom: 30rpx;
		display: flex;
		align-items: center;
	}

	.item-icon-style {
		width: 34rpx;
		height: 34rpx;
		margin-right: 20rpx;
	}

	.top-button {
		margin-top: 30rpx;
		height: 80rpx;
		display: flex;
		justify-content: center;
	}

	.confirm-button {
		width: 250rpx;
		height: 80rpx;
		background: linear-gradient(180deg, #88CFBA 0%, #1D755C 100%);
		color: #fff;
		font-size: 32rpx;
		display: flex;
		justify-content: center;
		/* 文本水平居中对齐 */
		align-items: center;
		/* 文本垂直居中对齐 */
	}

	.empty-content {
		z-index: 9;
		height: 100%;
		width: 100%;
		display: flex;
		flex-direction: column;
		/* 垂直布局，子视图按列排列 */
		justify-content: center;
		/* 水平居中 */
		align-items: center;
		/* 垂直居中 */
	}

	.empty-img {
		width: 114rpx;
		height: 107rpx;
	}


	.notify {
		box-shadow: 0rpx 0rpx 20rpx #e0e0e0;
	}

	.warn {
		width: 38rpx;
		height: 38rpx;
	}

	.left_icon {
		position: absolute;
		left: 20rpx;
		top: 3rpx;
	}

	.course {
		width: 380rpx;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}
</style>