<template>
  <view class="contain" :style="{ height: useHeight + 'rpx' }">
    <view class="title">
      <view class="introduction">听对话回答下面{{ data && data.questionDetailVoList.length }}个问题</view>
      <view class="percent">
        <span style="color: #428a6f">{{ active + 1 }}</span>
        /{{ data ? data.questionDetailVoList.length : 0 }}
      </view>
    </view>
    <view class="line"></view>
    <view class="topic" v-if="data">
      <span style="font-size: 34rpx; font-weight: bold">{{ data.questionDetailVoList[active].questionText.replaceAll('##', '__') }}</span>
      <view class="interpretFlag" @click="interpretChange">
        {{ interpret ? '隐藏翻译' : '问题翻译' }}
      </view>
    </view>
    <view class="topicInterpret" style="margin-bottom: 68rpx; margin-top: 42rpx" v-if="interpret">
      {{ data.questionDetailVoList[active].questionTextTranslation.replaceAll('##', '__') }}
    </view>
    <view>
      <view v-if="renewal">
        <view class="options" v-for="item in data.questionDetailVoList[active].optionDetailVoList" :key="item.id" :class="item.choiceOption == 'A' ? 'mt68' : ''">
          <view
            class="option"
            :class="
              item.choiceOption == data.questionDetailVoList[active].correctAnswer ? 'correct' : item.choiceOption == data.questionDetailVoList[active].studyAnswer ? 'error' : ''
            "
          >
            {{ item.choiceOption }}.{{ item.content }}
          </view>
          <view class="topicInterpret" v-if="interpret">{{ item.contentTranslation }}</view>
        </view>
      </view>
      <view v-else>
        <view class="options" v-for="item in data.questionDetailVoList[active].optionDetailVoList" :key="item.id" :class="item.choiceOption == 'A' ? 'mt68' : ''">
          <view class="option" @click="checkOption(item)" :class="item.choiceOption == data.questionDetailVoList[active].studyAnswer ? 'correct' : ''">
            {{ item.choiceOption }}.{{ item.content }}
          </view>
          <view class="topicInterpret" v-if="interpret">{{ item.contentTranslation }}</view>
        </view>
      </view>
    </view>

    <view class="lookReport" v-if="renewal" @click="lookReport">查看报告</view>
    <view style="height: 340rpx"></view>
    <my-audio ref="audio" :src="data.listeningAudioUrl" @translate="translate" @last="last" @next="next" original last next>
      <template v-slot:footer>
        <view v-if="renewal" class="btn" @click="renewalClick">重做</view>
        <view v-else :class="allCheck ? 'btn' : 'disabled'" @click="sumbit">提交</view>
      </template>
    </my-audio>
    <!--    <view @click="getTime">获取当前时间</view> -->
  </view>
</template>

<script>
  import MyAudio from './components/my-audio.vue';
  export default {
    components: {
      MyAudio
    },
    data() {
      return {
        time: null,
        allCheck: false,
        studentCode: null,
        materialsId: null,
        useHeight: 0,
        active: 0,
        data: null,
        renewal: true,
        title: '',
        submitShow: false,
        interpret: false //翻译隐现
      };
    },
    onLoad(option) {
      this.studentCode = option.studentCode;
      this.materialsId = option.id;
      this.title = option.title;
      uni.setNavigationBarTitle({
        title: this.title
      });
      this.init();
      // this.$refs.audio.changePlayProgress(20);
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          this.useHeight = res.windowHeight * (750 / res.windowWidth);
        }
      });
    },
    methods: {
      renewalClick() {
        this.time = Date.now();
        this.active = 0;
        for (let i = 0; i < this.data.questionDetailVoList.length; i++) {
          this.data.questionDetailVoList[i].studyAnswer = '';
          console.log(this.data.questionDetailVoList[i].studyAnswer);
        }
        this.renewal = false;
      },
      interpretChange() {
        this.interpret = !this.interpret;
      },
      lookReport() {
        this.$refs.audio.audioPause();
        uni.navigateTo({
          url: `/Listen/listenReport?studentCode=${this.studentCode}&materialsId=${this.materialsId}&title=${this.title}`
        });
      },
      async init() {
        let { data } = await this.$httpUser.get('dyf/zxListening/wap/getTitleDetail?studentCode=' + this.studentCode + '&materialsId=' + this.materialsId);
        if (data.success) {
          this.data = data.data;
        }
      },
      checkALL() {
        let a = this.data.questionDetailVoList.findIndex((o) => o.studyAnswer == '');
        if (a == -1) {
          this.allCheck = true;
        }
      },
      checkOption(e) {
        this.data.questionDetailVoList[this.active].studyAnswer = e.choiceOption;
        this.checkALL();
      },
      async sumbit() {
        if (!this.allCheck) return;
        uni.showLoading({
          title: '提交中...'
        });
        let useTime = Math.floor((Date.now() - this.time) / 1000);
        let obj = {
          studentCode: this.studentCode,
          listeningId: this.materialsId,
          listeningName: this.title,
          type: 2,
          questionSubmitCmdList: this.data.questionDetailVoList,
          useTime,
          audioTime: Math.floor(this.$refs.audio.sliderIndex)
        };
        await this.$httpUser.post('dyf/zxListening/wap/saveData', obj);
        this.submitShow = true;
        uni.redirectTo({
          url: `/Listen/listenReport?studentCode=${this.studentCode}&materialsId=${this.materialsId}&title=${this.title}`
        });
      },
      translate() {
        this.$refs.audio.audioPause();
        let a = encodeURIComponent(JSON.stringify(this.data));
        uni.navigateTo({
          url: `/Listen/originalTranslation?data=${a}&title=${this.title}`
        });
        // uni.navigateTo({
        //   url: `/Listen/originalTranslation?studentCode=${this.studentCode}&id=${this.materialsId}&title=${this.title}`
        // });
      },
      next() {
        if (this.active < this.data.questionDetailVoList.length - 1) {
          this.interpret = false;
          this.active++;
        } else {
          uni.showToast({
            icon: 'none',
            title: '已经是最后一题了',
            duration: 2000
          });
        }
      },
      last() {
        if (this.active == 0) {
          return uni.showToast({
            icon: 'none',
            title: '已经是第一题了',
            duration: 2000
          });
        } else {
          this.interpret = false;
          this.active--;
        }
      },
      getTime() {
        console.log(this.$refs.audio.sliderIndex);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .contain {
    box-sizing: border-box;
    padding: 32rpx;
    background-color: #fff;
    overflow-y: auto;
    .lookReport {
      position: fixed;
      border-radius: 33rpx 0rpx 0rpx 33rpx;
      background: #effbf6;
      width: 158rpx;
      height: 72rpx;
      line-height: 72rpx;
      font-size: 28rpx;
      font-weight: bold;
      color: #1ca178;
      text-align: center;
      right: 0;
      bottom: 400rpx;
    }
    .title {
      height: 40rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      // font-size: 28rpx;
    }
    .topicInterpret {
      margin-top: 6rpx;

      // font-size: 24rpx;
      color: #939393;
      line-height: 1.5;
    }
    .mt68 {
      margin-top: 68rpx !important;
    }
    .introduction {
      font-weight: bold;
      color: #428a6f;
    }
    .options {
      margin-top: 32rpx;
    }
    .option {
      padding-left: 40rpx;
      min-height: 96rpx;
      line-height: 54rpx;
      border-radius: 16rpx;
      border: 2rpx solid #efeff0;
      display: flex;
      align-items: center;
    }
    .line {
      height: 2rpx;
      background-color: #e1e3e4;
      margin: 30rpx 0;
    }
    .correct {
      border: 2rpx solid #94e6c7;
      background: rgba(148, 230, 199, 0.15);
      color: #31cf93;
    }
    .error {
      border: 2rpx solid #ffaf85;
      background: rgba(255, 172, 129, 0.1);
      color: #ffac80;
    }
    .interpretFlag {
      height: 40rpx;
      // width: 200rpx;
      position: absolute;
      bottom: -40rpx;
      line-height: 40rpx;
      color: #428a6f;
      // font-size: 24rpx;
      text-align: center;
      right: 0;
      border-bottom: 2rpx solid #428a6f;
    }
    .topic {
      position: relative;
      line-height: 50rpx;
      color: #555555;
      // font-size: 28rpx;
    }
    .btn {
      width: 686rpx;
      height: 74rpx;
      background: #339378;
      border-radius: 38rpx;
      line-height: 74rpx;
      text-align: center;
      color: #fff;
      margin: 20rpx auto 0;
    }
    .disabled {
      width: 686rpx;
      height: 74rpx;
      background: #d3d2d7;
      border-radius: 38rpx;
      line-height: 74rpx;
      text-align: center;
      color: #8a8a8e;
      margin: 20rpx auto 0;
    }
  }
</style>
