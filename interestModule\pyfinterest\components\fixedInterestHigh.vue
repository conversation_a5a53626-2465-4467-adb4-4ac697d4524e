<template>
  <view class="container" :style="{ height: windowHeight + 'rpx' }">
    <view class="word">
      <view style="width: 144rpx; height: 144rpx; margin-right: 10rpx">
        <image src="https://document.dxznjy.com/course/ae33333815ed4628a04f1271d58e13dd.png" style="width: 100%; height: 100%"></image>
      </view>
      <view class="wordTitle">{{ word.wordSyllable }}</view>
    </view>
    <!-- 拖拽盒子 -->
    <view>
      <draggable-box
        v-for="(darg, index) in longTypeList"
        :key="index"
        :label="darg"
        :value="darg"
        :index="index"
        :initial-position="dragBoxes[index]"
        :isOK="isOK"
        @drag-move="handleDragMove"
        @drag-end="handleDragEnd"
      ></draggable-box>
    </view>
    <!-- 静态盒子 -->
    <view class="static-box">
      <view v-for="(item, index) in list" :key="index" style="margin-right: 20rpx" :class="item.isStress || item.isSecondaryStress ? '' : 'staticitem'">
        {{ item.isStress || item.isSecondaryStress ? '' : item.text.replace(/\[.*\]$/, '') }}
        <view v-if="(!isSecond && item.isStress) || (isSecond && item.isSecondaryStress)" style="display: flex">
          <view v-for="(box, i) in item.list" :key="i" class="static-boxs" :id="'box' + index + '' + i" :class="box.class" @click="checkOut(index, i, box)">
            <view class="small" v-for="(checkItem, checkIndex) in longTypeList" :key="checkIndex">
              <view v-if="box.check == checkIndex + 1">{{ checkItem }}</view>
            </view>
            {{ box.text.replace(/\[.*\]$/, '') }}
          </view>
        </view>
      </view>
    </view>
    <view class="rightItems" v-if="isOK">
      <view
        v-for="(item, index) in list"
        :key="index"
        :class="item.isStress || item.isSecondaryStress ? '' : 'rightItem'"
        :style="{
          color: item.isStress || item.isSecondaryStress ? '' : '#c9c9c9'
        }"
      >
        {{ item.isStress || item.isSecondaryStress ? '' : item.text.replace(/\[.*\]$/, '') }}
        <view v-if="item.isStress || item.isSecondaryStress" style="display: flex; flex-wrap: wrap">
          <view v-for="(box, i) in item.list" :key="i" class="rightItem" :class="box.text === item.LongWord ? 'right' : ''">
            <view class="small" v-if="box.text === item.LongWord">{{ Types[Number(item.answer) || 0] }}</view>
            {{ box.text.replace(/\[.*\]$/, '') }}
          </view>
        </view>
      </view>
    </view>
    <view class="btn" @click="btnOK">{{ isOK ? (end ? '提交' : '下一题') : '确定' }}</view>
    <!-- 引导 -->
    <uni-popup ref="guideOne" type="bottom" :maskClick="true" :classBG="''">
      <view style="position: relative" class="test-bg">
        <image
          :mode="aspectFit"
          style="width: 100%; height: 100%"
          :src="isSecond ? 'https://document.dxznjy.com/dxSelect/interest/cizhongchangyin1.png' : 'https://document.dxznjy.com/course/dae3ae3ddb9c47198f837c9bdfa5fdc2.png'"
        ></image>
        <image class="guide_btn_next" @click="guideNext(1)" src="https://document.dxznjy.com/dxSelect/interest/xiayibu.png"></image>
      </view>
    </uni-popup>
    <uni-popup ref="guideTwo" type="bottom" :maskClick="true" :classBG="''">
      <view style="position: relative" class="test-bg">
        <image
          :mode="aspectFit"
          style="width: 100%; height: 100%"
          :src="isSecond ? 'https://document.dxznjy.com/dxSelect/interest/cizhongchangyin2.png' : 'https://document.dxznjy.com/dxSelect/interest/zhongchangyin2.png'"
        ></image>
        <image class="guide_btn_next" @click="guideNext(2)" src="https://document.dxznjy.com/dxSelect/interest/xiayibu.png"></image>
      </view>
    </uni-popup>
    <uni-popup ref="guideEnd" type="bottom" :maskClick="true" :classBG="''">
      <view style="position: relative" class="test-bg">
        <image
          @click="guideClose()"
          :mode="aspectFit"
          style="width: 100%; height: 100%"
          :src="isSecond ? 'https://document.dxznjy.com/dxSelect/interest/cizhongchangyin3.png' : 'https://document.dxznjy.com/dxSelect/interest/zhongchangyin.png'"
        ></image>
        <!-- <image class="guide_btn_next_other" src="https://document.dxznjy.com/dxSelect/interest/queding.png"></image> -->
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import DraggableBox from './DraggableBox.vue';
  export default {
    components: {
      DraggableBox
    },
    data() {
      return {
        status: 0,
        longTypeList: [],
        dragBoxes: [], // 动态存储每个拖拽盒子的位置信息
        isDragging: {}, // 记录每个拖拽盒子的拖拽状态
        windowWidth: 0,
        windowHeight: 0,
        screenHeight: 0,
        screenWidth: 0,
        isOK: false,

        Guide: uni.getStorageSync('fixedGuide'),
        cGuide: uni.getStorageSync('fixedGuideSecond'),
        Types: ['', '', '', '', '', '', 'a-e', '', '', 'i-e', 'e-e', 'o-e', 'u-e', 'oo', ''],
        goNum: 6
      };
    },
    props: {
      word: {
        type: Object,
        default: {}
      },
      end: {
        type: Boolean,
        default: false
      },
      isSecond: {
        type: Boolean,
        default: false
      }
    },
    onReady() {
      let that = this;
      uni.getSystemInfo({
        success: (res) => {
          this.screenHeight = res.windowHeight * 2;
          this.screenWidth = res.windowWidth * 2;
          that.windowWidth = res.windowWidth;
          that.windowHeight = res.windowHeight * (750 / res.windowWidth) - 100;
          // 可使用窗口高度，将px转换rpx
        }
      });
      this.list.forEach((e, i) => {
        if (e.isStress || e.isSecondaryStress) {
          e.list.forEach((o, index) => {
            this.setarea(i, index);
          });
        }
      });
    },
    created() {
      this.$emit('setTitle', this.isSecond ? 6 : 5);
      this.init();
      // this.$emit('goNum', this.isSecond ? 7 : 6);
      this.goNext();
      this.initDragBoxes(); // 初始化拖拽盒子
    },
    mounted() {
      if (this.isSecond) {
        if (!this.cGuide) {
          this.$refs.guideOne.open();
        } else if (this.Guide == 1) {
          this.$refs.guideTwo.open();
        } else if (this.Guide == 2) {
          this.$refs.guideEnd.open();
        }
      } else {
        if (!this.Guide) {
          this.$refs.guideOne.open();
        } else if (this.Guide == 1) {
          this.$refs.guideTwo.open();
        } else if (this.Guide == 2) {
          this.$refs.guideEnd.open();
        }
      }
    },
    methods: {
      guideNext(e) {
        if (this.isSecond) {
          uni.setStorageSync('fixedGuideSecond', e);
        } else {
          uni.setStorageSync('fixedGuide', e);
        }

        if (e == 1) {
          this.$refs.guideOne.close();
          this.$refs.guideTwo.open();
        } else {
          this.$refs.guideTwo.close();
          this.$refs.guideEnd.open();
        }
      },
      guideClose() {
        if (this.isSecond) {
          uni.setStorageSync('fixedGuideSecond', 3);
        } else {
          uni.setStorageSync('fixedGuide', 3);
        }
        this.$refs.guideEnd.close();
      },
      checkOut(index, i, e) {
        if (this.isOK) return;
        if (e.check) {
          this.list[index].list[i].check = 0;
          this.list[index].list[i].type = 0;
          this.list[index].list[i].class = '';
          this.$forceUpdate();
        }
      },
      addSuffix(arr) {
        const countMap = {};

        // 遍历数组，标记重复元素
        return arr.map((item, index) => {
          // 更新当前元素的出现次数
          countMap[item] = (countMap[item] || 0) + 1;

          // 如果当前元素是重复的（出现次数大于1），则标记次数
          if (countMap[item] > 1) {
            return `${item}[${countMap[item]}]`;
          }
          // 否则直接返回元素
          return item;
        });
      },

      addSuffixToRepeatedSyllables(objects) {
        // 创建一个对象来跟踪每个syllable出现的次数
        const syllableCounts = {};
        return objects.map((obj, index) => {
          const wordSyllable = obj.wordSyllable;
          // 如果syllable已经出现过，则增加计数并添加后缀
          if (syllableCounts[wordSyllable]) {
            syllableCounts[wordSyllable]++;
            return {
              ...obj,
              wordSyllable: `${wordSyllable}[${syllableCounts[wordSyllable]}]`
            };
          } else {
            // 如果是第一次出现，则记录该syllable并设置计数为1
            syllableCounts[wordSyllable] = 1;
            return obj;
          }
        });
      },
      goNext() {
        // 高年级定长短
        // 若在重音阶段且有次重音定长短则去次重音定长短，否则下一题或提交
        if (this.word.syllableList.find((e) => e.wordSyllableType == 7 && e.syllableList.length && !this.isSecond)) {
          this.goNum = 6;
        } else {
          this.goNum = 7;
          console.log(3333);
          this.$emit('goNum', this.goNum);
        }
      },
      btnOK() {
        if (this.isOK) {
          this.$emit('next', this.status, this.goNum);
        } else {
          let a = true;
          this.list.forEach((e) => {
            console.log(e, 'eeeeee');
            if (e.isStress || e.isSecondaryStress) {
              if (e.list.find((o) => o.type == 1)) {
                a = false;
              }
            }
          });
          if (a)
            return uni.showToast({
              icon: 'none',
              title: '请选择'
            });

          this.isOK = true;

          this.list.forEach((e, i) => {
            console.log(e, '1eeiiii');

            if (e.isStress || e.isSecondaryStress) {
              let tempNum = 0;
              this.longTypeList.forEach((typeItem, index) => {
                console.log(typeItem, 'typei11', this.Types[Number(e.answer) || 0], index);
                if (typeItem === this.Types[Number(e.answer) || 0]) {
                  tempNum = index + 1;
                } else {
                  console.log('typei22', e);
                }
              });
              e.list.forEach((o, p) => {
                console.log(o, 'ooooo1eeeee', e);
                if (o.check) {
                  if (o.check === tempNum && o.text === e.LongWord) {
                    this.list[i].list[p].class = 'right';
                  } else {
                    this.list[i].list[p].class = 'error';
                  }
                }
                if (o.text === e.LongWord && !o.check) {
                  this.list[i].list[p].class = 'error';
                  this.list[i].list[p].check = tempNum;
                  console.log('temp', tempNum, e, o);
                }

                this.list[i].list[p].tempNum = tempNum;
              });
            }
          });
          this.$forceUpdate();
          let temp = true;
          this.list.forEach((e) => {
            if (e.isStress || e.isSecondaryStress) {
              e.list.forEach((u) => {
                if (u.class == 'error') {
                  temp = false;
                }
              });
            }
          });
          this.status = temp ? 1 : 0;
        }
      },
      init() {
        let arr = this.addSuffixToRepeatedSyllables(this.word.splitList);
        this.list = arr.map((e, i) => {
          return {
            text: e.wordSyllable
          };
        });
        console.log(this.list, 'list', arr);
        this.word.syllableList.forEach((e) => {
          // this.list.find((o) => o.text === e.wordSyllable).isSyllable = true;
          if (this.isSecond) {
            if (e.wordSyllableType == 7 && e.syllableList.length) {
              this.longTypeList = [...this.longTypeList, ...this.setLangType(e.syllableList[0].wordSyllableType)];
              this.longTypeList = [...new Set(this.longTypeList)];
              console.log(this.longTypeList, 'eeeee1');
              this.$forceUpdate();
              console.log(e.syllableList, 'eeeee2');
              this.list.find((o) => o.text === e.wordSyllable).isSecondaryStress = true;
              this.list.find((o) => o.text === e.wordSyllable).answer = e.syllableList[0].wordSyllableType;
              if (e.syllableList) this.list.find((o) => o.text === e.wordSyllable).list = this.addSuffix(e.wordSyllable.replace(/\[.*\]$/, '').split(''));
              this.list.find((o) => o.text === e.wordSyllable).LongWord = e.syllableList[0].wordSyllable.match(/\(([^)]+)\)/)[1];
            }
          } else {
            if (e.wordSyllableType == 3 && e.syllableList.length) {
              this.longTypeList = this.setLangType(e.syllableList[0].wordSyllableType);
              console.log(this.longTypeList);
              this.$forceUpdate();
              console.log(e.syllableList[0].wordSyllableType);
              this.list.find((o) => o.text === e.wordSyllable).isStress = true;
              this.list.find((o) => o.text === e.wordSyllable).answer = e.syllableList[0].wordSyllableType;
              if (e.syllableList) this.list.find((o) => o.text === e.wordSyllable).list = this.addSuffix(e.wordSyllable.replace(/\[.*\]$/, '').split(''));
              this.list.find((o) => o.text === e.wordSyllable).LongWord = e.syllableList[0].wordSyllable.match(/\(([^)]+)\)/)[1];
            }
          }
        });

        this.list.forEach((e, i) => {
          if ((!this.isSecond && e.isStress) || (this.isSecond && e.isSecondaryStress)) {
            this.list[i].list = e.list.map((p) => {
              return {
                text: p,
                type: 0
              };
            });
          }
        });
        console.log(this.list, 'list2');
      },
      setLangType(e) {
        let a = e - 0;
        let array = [
          ['a-e', 'a'],
          ['i-e', 'i'],
          ['e-e', 'e'],
          ['o-e', 'o'],
          ['u-e', 'oo', 'u']
        ];
        switch (a) {
          case 6:
            return array[0];
          case 9:
            return array[1];
          case 10:
            return array[2];
          case 11:
            return array[3];
          default:
            return array[4];
        }
      },
      setarea(e, i) {
        let that = this;
        const query = uni.createSelectorQuery().in(this);
        query
          .select('#box' + e + '' + i)
          .boundingClientRect((data) => {
            // let res = JSON.parse(data);
            that.list[e].list[i].x = data.left;
            that.list[e].list[i].y = data.top - 88;
            that.list[e].list[i].width = data.width;
            that.list[e].list[i].height = data.height;
          })
          .exec();
      },
      initDragBoxes() {
        const screenWidth = uni.getSystemInfoSync().windowWidth;
        let num = this.longTypeList.length;
        let gap = (screenWidth - num * 52) / (num + 3);
        gap = Math.round(gap);
        console.log(screenWidth, '屏幕宽度px', gap);
        this.dragBoxes = this.longTypeList.map((_, index) => ({
          x: 2 * gap + index * (52 + gap), // 盒子之间间隔gap px,两边留2倍gap
          y: 400,
          width: 52,
          height: 47
        }));

        console.log(this.dragBoxes, 9999);
        this.isDragging = this.dragBoxes.reduce((acc, _, index) => {
          acc[index] = false;
          return acc;
        }, {});
      },
      handleDragMove(dragBox, index) {
        console.log(dragBox, index, 101010);
        // 遍历静态盒子，判断是否有重叠
        this.list.forEach((box, i) => {
          if (box.isStress || box.isSecondaryStress) {
            box.list.forEach((p, u) => {
              if (p.class == 'check' || p.class == 'check hover') {
                if (this.isColliding(i, dragBox, u)) {
                  p.class = 'check hover'; // 添加 hover 样式
                } else {
                  p.class = 'check';
                }
              } else {
                if (this.isColliding(i, dragBox, u)) {
                  p.class = 'hover'; // 添加 hover 样式
                } else {
                  p.class = '';
                }
              }
            });
          }
        });
        this.$forceUpdate();
      },
      handleDragEnd(dragBox, index) {
        if (this.isOK) return;
        this.list.forEach((e, i) => {
          if (e.isStress || e.isSecondaryStress) {
            console.log('ccchheck2', e);
            e.list.forEach((p, u) => {
              if (this.isColliding(i, dragBox, u)) {
                console.log('indexindex', index);
                p.check = index + 1;
                p.type = 1;
                p.class = 'check';
              }
            });
          }
          console.log(this.list, 456, this.longTypeList, dragBox, this.longTypeList[index]);
        });
        this.$forceUpdate();
      },
      isColliding(index, boxIndex, i) {
        // console.log(index, boxIndex, i, this.list, 999);
        const dragBox = boxIndex;
        const box = this.list[index].list[i];
        // console.log(box, dragBox, 999888888);
        const dragBoxCenterX = dragBox.x + dragBox.width / 2;
        const dragBoxCenterY = dragBox.y + dragBox.height / 2;
        return dragBoxCenterX >= box.x && dragBoxCenterX <= box.x + box.width && dragBoxCenterY >= box.y && dragBoxCenterY <= box.y + box.height;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .test-bg {
    padding-top: env(safe-area-inset-top);
    // padding-bottom: env(safe-area-inset-bottom);
    height: 100vh;
    box-sizing: border-box;
  }

  .guide_btn_next {
    position: absolute;
    bottom: 338rpx;
    right: 64rpx;
    width: 186rpx;
    height: 80rpx;
  }

  .guide_btn_close {
    position: absolute;
    bottom: 57rpx;
    right: 64rpx;
    width: 269rpx;
    height: 142rpx;
  }

  .btn {
    width: 632rpx;
    height: 84rpx;
    background: #89c844;
    box-shadow: 0 16rpx 2rpx -2rpx rgba(99, 180, 11, 1);
    border-radius: 42rpx;
    text-align: center;
    line-height: 84rpx;
    position: fixed;
    bottom: 50rpx;
    left: 50%;
    transform: translateX(-50%);
  }

  .word {
    margin-top: 60rpx;
    height: 144rpx;
    padding-left: 32rpx;
    display: flex;
    align-items: center;
    margin-bottom: 90rpx;
  }

  .wordTitle {
    width: 474rpx;
    height: 110rpx;
    padding-left: 14rpx;
    background: url('https://document.dxznjy.com/course/a37e49a58c724f7ca8d2fbd8ce85252d.png') no-repeat;
    background-size: contain;
    text-align: center;
    line-height: 100rpx;
    font-size: 40rpx;
    color: #555555;
  }

  .rightItems {
    position: fixed;
    width: 686rpx;
    max-height: 286rpx;
    overflow-y: auto;
    bottom: 200rpx;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    border-radius: 32rpx;
    padding: 20rpx 0;
    background-color: rgba(255, 255, 255, 0.51);

    .nocheck {
      color: #c9c9c9;
    }

    .rightItem {
      position: relative;
      background-color: #fff;
      color: #555555;
      font-size: 36rpx;
      height: 92rpx;
      line-height: 92rpx;
      margin-right: 8rpx;
      border-radius: 16rpx;
      margin-bottom: 10rpx;
      padding: 0 40rpx;
    }

    .right {
      background-color: #4bb051;
      color: #ffffff;
    }
  }

  .container {
    overflow: hidden;
    position: relative;
    width: 100vw;

    /* height: 100vh; */
  }

  .small {
    position: absolute;
    height: 30rpx;
    width: 100%;
    top: 4rpx;
    right: 0;
    line-height: 1;
    padding-right: 8rpx;
    text-align: right;
    font-size: 20rpx;
    color: #555;
  }

  .drag-box {
    position: absolute;
    height: 92rpx;
    width: 102rpx;
    box-sizing: border-box;
    text-align: center;
    line-height: 92rpx;
    border-radius: 24rpx;
    font-size: 36rpx;
    color: #555;
    background-color: #ffffff;
    box-shadow: 0 16rpx 2rpx -2rpx rgba(97, 170, 244, 1);
    // opacity: 0.8;
    z-index: 2;
  }

  .check {
    border: 2rpx solid #ffc800;
    background-color: #fff7db;
  }

  .error {
    background-color: #ffa332 !important;
    box-shadow: 0 16rpx 2rpx -2rpx rgba(231, 133, 13, 1) !important;
    color: #ffffff !important;
  }

  .static-box {
    /* height: 80rpx; */
    margin: 40rpx;
    transition: background-color 0.3s;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;

    .right {
      background-color: #4bb051 !important;
      box-shadow: 0 16rpx 2rpx -2rpx rgba(57, 141, 61, 1);
      color: #ffffff !important;
    }
  }

  .staticitem {
    position: relative;
    height: 92.11rpx;
    min-width: 100rpx;
    padding: 0 30rpx;
    font-size: 36rpx;
    box-sizing: border-box;
    // border: 2rpx solid #000;
    margin-right: 10rpx;
    text-align: center;
    background-color: #ffffff;
    color: #c9c9c9;
    border-radius: 24rpx;
    line-height: 90rpx;
    box-shadow: 0 16rpx 2rpx -2rpx rgba(97, 170, 244, 1);
    margin-bottom: 32rpx;
  }

  .static-boxs {
    position: relative;
    height: 92.11rpx;
    min-width: 100rpx;
    padding: 0 30rpx;
    box-sizing: border-box;
    border: 2rpx solid transparent;
    font-size: 36rpx;
    // margin-right: 18rpx;
    text-align: center;
    background-color: #ffffff;
    color: #555;
    border-radius: 24rpx;
    line-height: 90rpx;
    box-shadow: 0 16rpx 2rpx -2rpx rgba(97, 170, 244, 1);
    margin-bottom: 32rpx;
  }

  .static-boxs.hover {
    background-color: #ff0000; // 到达中心时背景颜色
  }

  .check {
    border: 2rpx solid #ffc800;
    background-color: #fff7db;
  }
</style>
