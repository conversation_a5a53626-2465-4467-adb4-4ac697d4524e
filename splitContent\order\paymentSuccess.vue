<template>
	<view class="plr-32">
		<view class="bg-ff payment_content_css">
			<view class="payment_image_css">
				<u-icon name="checkmark-circle-fill" color="#2DC032" size="112"></u-icon>
			</view>
			<view class="text_css f-32 bold lh-42">
				<view class="mt-16">支付成功</view>
				<view class="mt-16">感谢您的购买</view>
			</view>
			<view class="bold c-FA4F2B price_number">
				<text class="f-22">￥</text>
				<text class="f-44">{{orderInfoPayAmount.payAmount}}</text>
			</view>
			<view class="f-32 c-55">
				<view class="order_item_css">
					<text>订单编号：</text>
					<text>{{orderInfoPayAmount.orderNo}}</text>
				</view>
				<view class="order_item_css">
					<text>支付时间：</text>
					<text>{{orderInfoPayAmount.payAmountTime}}</text>
				</view>
				<view class="order_item_css">
					<text>支付方式：</text>
					<text>{{orderInfoPayAmount.payAmountMode}}</text>
				</view>
			</view>
			<view class="button_css">
				<view class="flexbox">
					<!-- <button hover-class="none" open-type="contact" plain="true" class="btn btn_plan">联系客服</button> -->
					<navigator url="plugin://qiyukf/chat">
						<button hover-class="none" plain="true" class="btn btn_plan">联系客服</button>
					</navigator>
					<button hover-class="none" @tap="GoOrderList" class="btn btn_color ml-25">订单列表</button>
				</view>
				<view v-if="this.orderInfoPayAmount.goodsType==2||this.orderInfoPayAmount.goodsType==3" class="f-20 c-A8A9AA lh-42 mt-24">该课程为交付课，会有老师主动联系您对接，若有疑问可联系客服</view>
			</view>
		</view>
		<!-- 
 -->
	</view>
</template>

<script>
	export default {
		data() {
			return {
				orderInfoPayAmount:{}
			}
		},
		onShow() {
			if(uni.getStorageSync('orderInfoPayAmount')){
				this.orderInfoPayAmount=JSON.parse(uni.getStorageSync('orderInfoPayAmount'))
			}
		},
		methods: {
			GoOrderList(){
				uni.redirectTo({
			        url: '/splitContent/order/order'
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	/deep/.u-icon{
		display: inline-block;
	}
.payment_content_css{
	width: 686rpx;
	text-align: center;
	padding-top: 98rpx;
	height: 90vh;
	position: relative;
	border-radius: 28rpx;
	.payment_image_css{
		width: 112rpx;
		height: 112rpx;
		margin:0 auto;
		
	}
	.text_css{
		width: 628rpx;
		margin:0 auto;
		padding-bottom: 90rpx;
		border-bottom: 2rpx solid #F5F5F5;
	}
	.c-FA4F2B{
		color:#FA4F2B;
	}
	.c-A8A9AA{
		color:#A8A9AA;
	}
	.order_item_css{
		width: 520rpx;
		text-align: left;
		margin-top: 24rpx;
		margin-left: 105rpx;
	}
	.price_number{
		margin-top: 102rpx;
	}
	.button_css{
		position: absolute;
		bottom:10rpx;
		width: 100%;
		.flexbox{
			width: 565rpx;
			margin-left: 60rpx;
		}
	}
	.btn {
		width: 278rpx;
		height: 60rpx;
		line-height: 60rpx;
		box-sizing: border-box;
		border-radius: 30upx;
		font-size: 24upx;
		display: inline-block;
	}
	.btn_plan{
		color:#4E9F87 !important;
		border-color: #7BAEA0 !important;
		line-height: 59rpx;
	}
	.btn_color{
		color:#fff !important;
		background: linear-gradient( 180deg, #88CFBA 0%, #1D755C 100%)!important;
		line-height: 59rpx;
	}
}
</style>
