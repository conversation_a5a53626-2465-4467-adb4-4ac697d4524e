<template>
  <view class="page">
    <polyv-player
      :ref="'polyv_player'"
      :id="'polyv_player'"
      @timeupdate="onBindtimeupdate"
      :autoplay="true"
      :showProgressBar="true"
      :startTime="startTime"
      :playerId="playerIdcont"
      :vid="videoInfo.videoUrl"
      :width="width"
      :height="height"
      :ts="ts"
      :sign="sign"
      :isAllowSeek="yes"
      :preview="true"
      @pause="bindpause"
      @playing="bindplaying()"
      @loadedmetadata="bindloadedmetadata"
      @statechange="statechange"
      @fullscreenchange="bindfullscreenchange"
    >
      <view slot="custom" style="position: absolute; top: 30rpx; color: #ffffff; width: 100vw; text-align: center">{{ fullScreen ? time : '' }}</view>
    </polyv-player>
  </view>
</template>

<script>
  import dayjs from 'dayjs';
  const MD5 = require('../../util/md5.js');
  // 修改下面的 vid 和 secretkey
  let secretkey = 'Jkk4ml1Of8';
  let vid = '';
  let ts = new Date().getTime();
  let sign = '';
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        videoInfo: {},
        startTime: 0,
        playerIdcont: 'polyvPlayercont',
        vid: vid,
        ts: ts,
        sign: sign,
        width: '100%',
        height: '100vh',
        studentProgress: {},
        time: dayjs().format('HH:mm'),
        timer: null,
        fullScreen: false
      };
    },
    onLoad(e) {
      console.log(e, 'eeeeeee');
      this.videoInfo = JSON.parse(e.info);
      console.log('🚀 ~ onLoad ~ videoInfo:', this.videoInfo);
      this.getStudyPross();
    },
    onReady() {
      // this.getStudyPross();
    },
    mounted() {
      // 每秒更新一次 time
      this.timer = setInterval(() => {
        this.time = dayjs().format('HH:mm');
      }, 1000);
    },
    beforeDestroy() {
      // 清除定时器
      if (this.timer) {
        clearInterval(this.timer);
      }
    },
    methods: {
      async getStudyPross() {
        const res = await $http({
          url: 'zxAdminCourse/file/student/progress',
          data: {
            goodsId: this.videoInfo.goodsId,
            userCode: uni.getStorageSync('user_code') || '',
            fileId: this.videoInfo.fileId
          }
        });
        console.log('getStudyPross', res);
        this.studentProgress = res.data ? res.data : 0;
        this.getTimeInfo(Number(this.studentProgress.progress));
      },
      getTimeInfo(time) {
        this.$nextTick(() => {
          let polyvPlayerContext = this.selectComponent('#polyv_player');
          console.log('🚀 ~ onLoad ~ polyvPlayerContext11:', polyvPlayerContext);
          if (polyvPlayerContext) {
            this.getChangeVideo(polyvPlayerContext, this.videoInfo.videoUrl, time);
          } else {
            setTimeout(() => {
              this.getTimeInfo();
            }, 200);
          }
        });
      },
      getChangeVideo(polyvPlayerContext, vid, time) {
        console.log('getChangeVideo', polyvPlayerContext, vid, time);
        const ts = new Date().getTime();
        const sign = MD5.md5(`${secretkey}${vid}${ts}`);
        console.log('🚀 ~ 切换视频 ~ vid:', vid, ts, sign);
        this.startTime = time;
        polyvPlayerContext.changeVid({
          vid: vid,
          ts,
          sign
        });
      },
      onBindtimeupdate(e) {
        console.log('onBindtimeupdate', e);
      },
      bindpause(e) {
        let _this = this;
        let polyvPlayerContext = _this.selectComponent('#polyv_player' + _this.videoInfo.videoUrl);
        polyvPlayerContext.pause();
        console.log('bindpause', e);
        // clearInterval(_this.timer);
        // _this.courseInfo[0].videoList.forEach((itemVid, index) => {
        //   console.log('itemVid.videoVid == e.detail.vid:', itemVid.videoVid == e.detail.vid);
        //   if (itemVid.videoVid == e.detail.vid) {
        //     console.log('🚀 ~ this.courseInfo[0].videoList.forEach ~ itemVid:', itemVid);
        //     _this.$set(itemVid, 'play', false);
        //   }
        // });
        // console.log('this.courseInfo', _this.courseInfo);
      },
      bindplaying() {
        console.log('bindplaying');
      },
      bindloadedmetadata() {
        let polyvPlayerContext = this.selectComponent('#polyv_player');
        // polyvPlayerContext.pause();
        if (Number(this.startTime) > Number(polyvPlayerContext.rDuration)) {
          this.startTime = Number(polyvPlayerContext.rDuration);
        }
        polyvPlayerContext.seek(Number(this.startTime));
        polyvPlayerContext.play();
      },
      statechange(e) {
        console.log('statechange', e);
      },
      bindfullscreenchange(e) {
        console.log('bindfullscreenchange', e);
        this.fullScreen = e.detail.fullScreen;
      },
      async saveProgress() {
        let polyvPlayerContext = this.selectComponent('#polyv_player');
        let data = {
          goodsId: this.videoInfo.goodsId,
          userCode: uni.getStorageSync('user_code') || '',
          fileId: this.videoInfo.fileId,
          progress: String(polyvPlayerContext.getCurrentTime())
        };
        if (this.studentProgress && this.studentProgress.id) {
          data.id = this.studentProgress.id;
        }
        const res = await $http({
          url: 'zxAdminCourse/file/student/studentFileProgress',
          method: 'POST',
          data: data
        });
        console.log('saveProgress', res);
      }
    },
    onUnload() {
      console.log('onHide');
      this.saveProgress();
    }
  };
</script>

<style lang="scss" scoped>
  .page {
    width: 100vw;
    height: 100vh;
  }
</style>
