<template>
  <view class="pt-12 bg-ff question_content_css">
    <view class="picker_item_css flex-a-c flex-x-s">
      <view class="f-28 c-55">描述对象性别</view>
      <picker class="grade-picker" @change="genderPickerChange" :value="genderIndex" :range="genderArray" name="studentGrade">
        <view class="f-30" :class="genderArray[genderIndex] != null ? 'c-55' : 'text01'">{{ genderArray[genderIndex] != null ? genderArray[genderIndex] : '请选择性别' }}</view>
      </picker>
      <u-icon name="arrow-right" color="#B1B1B1" size="28"></u-icon>
    </view>
    <view class="picker_item_css flex-a-c flex-x-s">
      <view class="f-28 c-55">描述对象年龄</view>
      <picker class="grade-picker" @change="agePickerChange" :value="ageIndex" :range="ageArray">
        <view class="f-30" :class="ageArray[ageIndex] != null ? 'c-55' : 'text01'">{{ ageArray[ageIndex] != null ? ageArray[ageIndex] : '请选择年龄' }}</view>
      </picker>
      <u-icon name="arrow-right" color="#B1B1B1" size="28"></u-icon>
    </view>
    <view class="mt-28 plr-32">
      <textarea class="textarea_css" :class="textareaValue ? 'textarea_value_css' : ''" v-model="textareaValue" placeholder="请详细描述您的问题"></textarea>
    </view>
    <view class="mt-75 queston_btn">
      <button @click="addQuestion()" class="btn_css c-ff f-28">{{ hasFreeQaFalse == '1' ? '确认提问' : '确认提问并支付' }}</button>
    </view>
  </view>
</template>

<script>
  const { $showMsg, $http } = require('@/util/methods.js');
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  export default {
    data() {
      return {
        identityType: '',
        genderArray: ['女', '男'],
        genderIndex: '1',
        hasFreeQaFalse: uni.getStorageSync('hasFreeQaFalse'),
        ageIndex: '9',
        textareaValue: '',
        addQuestionFalse: false,
        ageArray: [
          '1周岁',
          '2周岁',
          '3周岁',
          '4周岁',
          '5周岁',
          '6周岁',
          '7周岁',
          '8周岁',
          '9周岁',
          '10周岁',
          '11周岁',
          '12周岁',
          '13周岁',
          '14周岁',
          '15周岁',
          '16周岁',
          '17周岁',
          '19周岁',
          '20周岁',
          '21周岁',
          '22周岁',
          '23周岁',
          '24周岁',
          '25周岁',
          '26周岁',
          '27周岁',
          '28周岁',
          '29周岁',
          '30周岁',
          '31周岁',
          '32周岁',
          '33周岁',
          '34周岁',
          '35周岁',
          '36周岁',
          '37周岁',
          '38周岁',
          '39周岁',
          '40周岁',
          '41周岁',
          '42周岁',
          '43周岁',
          '44周岁',
          '45周岁',
          '46周岁',
          '47周岁',
          '48周岁',
          '49周岁',
          '50周岁',
          '51周岁',
          '52周岁',
          '53周岁',
          '54周岁',
          '55周岁',
          '56周岁',
          '57周岁',
          '58周岁',
          '59周岁',
          '60周岁',
          '61周岁',
          '62周岁',
          '63周岁',
          '64周岁',
          '65周岁',
          '66周岁',
          '67周岁',
          '68周岁',
          '69周岁',
          '70周岁',
          '71周岁',
          '72周岁',
          '73周岁',
          '74周岁',
          '75周岁',
          '76周岁',
          '77周岁',
          '78周岁',
          '79周岁',
          '80周岁',
          '81周岁',
          '82周岁',
          '83周岁',
          '84周岁',
          '85周岁',
          '86周岁',
          '87周岁',
          '88周岁',
          '89周岁',
          '90周岁',
          '91周岁',
          '92周岁',
          '93周岁',
          '94周岁',
          '95周岁',
          '96周岁',
          '97周岁',
          '98周岁',
          '99周岁',
          '100周岁'
        ],
        flag1: false,
        payInfo: {},
        findPriceInfo: {}
      };
    },
    onLoad(e) {
      this.identityType = e.identityType;
      this.getFindPrice();
    },
    onShow() {
      if (this.flag1) {
        uni.$tlpayResult(this.sucees, this.fail, this.payInfo.orderId);
      }
    },
    methods: {
      sucees() {
        this.flag1 = false;
        this.paySuccessFun();
      },
      fail() {
        this.flag1 = false;
        uni.redirectTo({
          url: '/splitContent/order/order'
        });
      },
      fails() {
        uni.showToast({
          title: '支付失败',
          icon: 'none',
          duration: 2000
        });
        uni.redirectTo({
          url: '/splitContent/order/order'
        });
        this.flag1 = false;
      },
      async getFindPrice() {
        let _this = this;
        const res = await $http({
          url: 'zx/wap/qa/findPrice',
          data: {}
        });
        if (res) {
          this.findPriceInfo = res.data;
        }
      },
      async addQuestion() {
        if (!this.genderArray[this.genderIndex]) {
          $showMsg('请选择描述对象性别');
          return false;
        }
        if (!this.ageArray[this.ageIndex]) {
          $showMsg('请选择描述对象年龄');
          return false;
        }
        if (!this.textareaValue) {
          $showMsg('请输入你的问题');
          return false;
        }
        if (this.addQuestionFalse) {
          return;
        }
        this.addQuestionFalse = true;
        this.auditContent();
      },
      async auditContent(pageInfo) {
        let _this = this;
        const res = await $http({
          url: 'zx/common/auditContent',
          method: 'POST',
          showLoading: true,
          data: {
            content: this.textareaValue,
            scene: 4
          }
        });
        if (res) {
          if (res.data == 'pass') {
            if (this.hasFreeQaFalse == '1') {
              let _this = this;
              const res = await $http({
                url: 'zx/wap/qa/saveQaData',
                showLoading: true,
                method: 'POST',
                data: {
                  age: this.ageArray[this.ageIndex],
                  gender: this.genderIndex,
                  questionText: this.textareaValue
                }
              });
              if (res) {
                uni.navigateBack({
                  delta: 1
                });
                this.addQuestionFalse = false;
              }
            } else {
              this.payAnswers();
            }
          } else if (res.data == 'risk') {
            $showMsg('您提出的内容有风险');
          }
        }
      },
      async payAnswers() {
        let _this = this;
        uni.showLoading({
          title: '支付中，请稍后'
        });
        const res = await $http({
          url: 'zx/wap/qa/generate',
          method: 'POST',
          data: {
            gender: _this.genderIndex,
            amount: _this.identityType == 4 ? _this.findPriceInfo.memberQuestionCreationPrice : _this.findPriceInfo.nonMemberQuestionCreationPrice,
            age: this.ageArray[this.ageIndex],
            questionText: this.textareaValue,
            type: 0
          }
        });
        if (res) {
          if (res.data.needPay == 1) {
            this.payBtn(res.data.applyPayDto);
          }
          this.addQuestionFalse = false;
        }
      },
      async payBtn(data) {
        let _this = this;
        let resdata = await httpUser.post('mps/line/collect/order/unified/collect', data);
        let res = resdata.data.data;
        _this.disabled = false;
        uni.hideLoading();
        if (res) {
          if (res.openAllinPayMini) {
            this.flag1 = true;
            this.payInfo = res;
            uni.$payTlian(res);
          } else {
            uni.requestPayment({
              provider: 'wxpay',
              timeStamp: res.payInfo.timeStamp,
              nonceStr: res.payInfo.nonceStr,
              package: res.payInfo.packageX,
              signType: res.payInfo.signType,
              paySign: res.payInfo.paySign,
              success: function (ress) {
                uni.showToast({
                  title: '支付成功'
                });
                this.paySuccessFun();
              },
              fail: function (err) {
                uni.showToast({
                  title: '支付失败'
                });
                setTimeout(function () {
                  uni.redirectTo({
                    url: '/splitContent/order/order'
                  });
                }, 1500);
              }
            });
          }
        }
      },
      paySuccessFun() {
        uni.showLoading({
          title: '加载问题中...',
          mask: true
        });
        setTimeout(() => {
          uni.redirectTo({
            url: '/splitContent/order/order'
          });
          uni.hideLoading();
        }, 1000);
      },
      genderPickerChange(e) {
        this.genderIndex = e.target.value;
      },
      agePickerChange(e) {
        this.ageIndex = e.target.value;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .question_content_css {
    height: calc(100vh - 20rpx);
    .queston_btn {
      position: absolute;
      left: 0;
      bottom: 40rpx;
      width: 750rpx;
      .btn_css {
        width: 686rpx;
        height: 74rpx;
        line-height: 74rpx;
        text-align: center;
        border-radius: 38rpx;
        margin: 0 auto;
        background-color: #339378;
      }
    }
  }
  .picker_item_css {
    padding: 32rpx;
    border-bottom: 1rpx solid #ecf0f4;
    .grade-picker {
      width: 470rpx;
      text-align: right;
      padding-right: 15rpx;
    }
    .text01 {
      color: #b1b1b1;
    }
  }
  .textarea_css {
    background: #fbfcfd;
    color: #b1b1b1;
    font-size: 24rpx;
    padding: 32rpx;
    line-height: 40rpx;
  }
  .textarea_value_css {
    color: #555 !important;
  }
</style>
