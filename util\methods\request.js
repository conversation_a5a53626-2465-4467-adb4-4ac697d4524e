import { $confirmLogin } from './common.js';
import { $showMsg } from './prompt.js';
import { host } from '../publicVariable';
import { wxAppVersion } from '../config.js';
import sensors from 'sa-sdk-miniprogram';

/**
 * 请求封装
 * @param {Object} opt  request对象,url,data,method
 * @param {Object} arg  是否显示loading以及校验登录
 * @returns {Object}
 */
const $http = (opt, arg) => {
  let that = this;
  let { showLoading = false, checkLogin = false } = arg || {};
  // 判断是否登录
  if (checkLogin) {
    let confirmLogin = $confirmLogin();
    if (!confirmLogin) {
      return false;
    }
  }
  var jump = uni.getStorageSync('jump');
  if (jump) return;
  // 判断是否显示loading
  if (opt.showLoading) {
    uni.showLoading({
      title: '加载中...',
      mask: true
    });
  }
  // 获取个人token
  let token = uni.getStorageSync('token');
  // 门店充值需要的token
  let payToken = uni.getStorageSync('payToken');
  // if(payToken){
  // 	token = payToken
  // }
  // 从opt中结构参数
  let { url, method = 'GET', data = {}, header } = opt;
  // 配置请求地址
  // let host1 = uni.getStorageSync('url') ? uni.getStorageSync('url') : host;
  // console.log(host1);
  opt.url = host + url;
  opt.data = Object.assign(data, {
    //invitor_id:  uni.getStorageSync('invitor_id') || 0,
  });
  if (url.indexOf('znyy/school/recharge/getRechargeLineOrderCreateDto') != -1) {
    token = uni.getStorageSync('token');
  }
  let getContentType = header && header['Content-Type'];
  header = {
    anonymous_id: sensors.getAnonymousID(),
    Token: token,
    'Content-Type': getContentType ? header['Content-Type'] : 'application/json',
	'dx-source': 'ZHEN_XUAN##WX##MINIAPP',
	'temp-dx-source': 'ZHEN_XUAN##WX##MINIAPP',
    'mini-app-withdraw': 'true'
  };
  if (payToken || token) {
    header['x-www-iap-assertion'] = payToken ? payToken : token;
  }
  // #ifdef MP-WEIXIN
  if (wxAppVersion) {
  	header['dx-app-version'] = wxAppVersion;
  }
  // #endif

  // opt.data.store_id = store_id
  // 根据token判断请求头
  // if (token !== '') {
  // 	opt.header.Token = token
  // } else {
  // 	opt.header.Token = ''
  // }
  // opt.header["Token"]=uni.getStorageInfoSync("token")||''
  return new Promise((resolve, reject) => {
    uni.request({
      url: opt.url,
      method: opt.method,
      header: header,

      data: opt.data,
      success(res) {
        if (res.data.status == 1 || res.data.success || res.data.code == 80002) {
          // 成功且code=1时抛出
          resolve(res.data);
        } else if (res.data.code == 50004 || res.data.code == 40018) {
          // 50004 请重新登录   40018 登陆已过期，请重新登陆
          var jump = uni.getStorageSync('jump'); //以下解决多次跳转登录页的重点
          if (!jump) {
            setTimeout(() => {
              uni.navigateTo({
                url: '/Personalcenter/login/login'
              });
            }, 100);
            uni.removeStorage({
              key: 'token'
            });
            uni.removeStorage({
              key: 'club'
            });
            uni.removeStorage({
              key: 'brand'
            });
            uni.removeStorage({
              key: 'Partner'
            });
            uni.setStorageSync('jump', 'true');
          }
        } else if (res.data.status == 10000) {
          if (opt.url.indexOf('/zx/user/userInfoNew') == -1) {
            uni.navigateTo({
              url: '/Personalcenter/login/login'
            });
            uni.removeStorage({
              key: 'token'
            });
            uni.removeStorage({
              key: 'club'
            });
            uni.removeStorage({
              key: 'brand'
            });
            uni.removeStorage({
              key: 'Partner'
            });
          }
          resolve(false);
        } else {
          // $showMsg(res.data.message)
          if (opt.showError !== 1) {
            uni.showModal({
              title: '温馨提示',
              content: res.data.message,
              showCancel: false,
			  success(response) {
				  // 如果当前商品无法在C端访问，则提示错误信息并点击确认返回首页
			  	if (response.confirm && url.indexOf('zx/wap/goods/single/detail')!= -1 && res.data.code == 50001) {
					uni.switchTab({
						url: '/pages/index/index'
					})
				}
			  }
            });
          }
          if (url.indexOf('zx/course/orderPayAnew') != -1) {
            resolve(res.data);
          } else {
            resolve(false);
          }
          // resolve(res.data);
        }
        uni.hideLoading();
      },
      fail(err) {
        $showMsg('网络连接失败');
        uni.hideLoading();
      },
      complete() {
        uni.hideLoading();
      }
    });
  });
};

module.exports = {
  $http
};
