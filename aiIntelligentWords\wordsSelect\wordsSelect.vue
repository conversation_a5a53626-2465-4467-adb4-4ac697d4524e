<template>
  <page-meta :page-style="'overflow:' + (rollShow ? 'hidden' : 'visible')"></page-meta>
  <view class="wordsContent">
    <view class="bg_wrap" :style="{ height: useHeight - 160 + 'rpx' }" v-if="wordTotal > 0">
      <view class="wordsContent__wrap">
        <text class="wordsContent__title">
          AI智阅共有{{ wordTotal || '' }}个单词
          <text class="wordsContent__title__now" v-show="wordSelect.length > 0">已选中{{ wordSelect.length }}个单词</text>
        </text>
        <view class="flex" @click="autoSelect">
          <uni-icons v-if="autoSelectVal" color="green" type="checkbox-filled" size="18"></uni-icons>
          <uni-icons v-else type="checkbox" size="18"></uni-icons>
          <text class="wrapBtn">自动</text>
        </view>
      </view>

      <!-- 手动编写的折叠面板 -->
      <myCollapse ref="collapse" accordion>
        <!-- 分类 阅读 语法 单词 -->
        <myCollapseItem v-for="(item, index) in dataList" :key="index" :title="getGrade(item.type)" name="type" ref="collapseHeight">
          <!-- 年级 小学初中高中 -->
          <myCollapse ref="collapse1" accordion>
            <myCollapseItem v-for="(child, childIndex) in item.courseList" :key="childIndex" :title="getType(child.courseStageName)" name="courseStageName">
              <myCollapse ref="collapse2" accordion @change="clearStatus">
                <view class="" v-for="(children, childrenIndex) in child.courseList" :key="childrenIndex" @click="getDetail(children)">
                  <!-- 词库 -->
                  <myCollapseItem :title="children.courseName" name="courseName">
                    <view class="accordion-body" v-show="showWord">
                      <text
                        class="wordsItem"
                        :key="subIndex"
                        :class="[subItem.selected ? 'selectActive' : 'wordsItem']"
                        @click.stop="selectNow(subItem)"
                        v-for="(subItem, subIndex) in showWordList"
                      >
                        {{ subItem.word }}
                      </text>
                    </view>
                  </myCollapseItem>
                </view>
              </myCollapse>
            </myCollapseItem>
          </myCollapse>
        </myCollapseItem>
      </myCollapse>

      <view class="determine" :class="wordSelect.length > 0 ? 'determineActive' : ''" @click="movieLogging">生成短文</view>
    </view>
    <view class="no_words" v-else>
      <text>学习超级阅读新版本文章，\n添加生词后可学习此模块</text>
    </view>
  </view>
</template>
<script>
  const { $showMsg, $http } = require('@/util/methods.js');
  import myCollapseItem from '../component/myCollapseItem/myCollapseItem.vue';
  import myCollapse from '../component/myCollapse/myCollapse.vue';
  export default {
    components: {
      myCollapseItem,
      myCollapse
    },
    data() {
      return {
        useHeight: 0,
        selectList: [],
        wordSelect: [],
        autoSelectVal: false,
        showWord: false,
        studentCode: null,
        merchantCode: null,
        wordTotal: null,
        dataList: [],
        showWordList: [],
        openWordObj: {}
      };
    },
    onLoad(option) {
      this.studentCode = option.studentCode;
      this.merchantCode = option.merchantCode;
      this.getWordList();
    },
    onShow() {
      this.clearStatus();
      // 清除面板选中状态，清除所选的单词、
      this.autoSelectVal = false;
    },
    onReady() {
      let that = this;
      uni.getSystemInfo({
        //调用uni-app接口获取屏幕高度
        success(res) {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          that.useHeight = h;
        }
      });
    },
    methods: {
      clearStatus() {
        let that = this;
        that.showWord = false;
        that.showWordList = [];
        that.autoSelectVal = false;
        that.wordSelect = [];
      },
      async getDetail(e) {
        let that = this;
        that.openWordObj = e;
        that.showWordList = [];
        if (e.courseList) {
          that.showWordList = JSON.parse(JSON.stringify(e.courseList));
          that.showWordList.forEach((i) => (i.selected = false));
          that.$set(that, 'showWord', true);
        } else {
          let res = await $http({
            url: 'znyy/superReadStudentWordBook/word-books',
            method: 'get',
            data: {
              studentCode: that.studentCode,
              merchantCode: that.merchantCode,
              courseCode: e.courseCode
            }
          });
          if (res) {
            console.log(res.data);
            that.showWordList = res.data;
            that.$set(e, 'courseList', res.data);
            that.showWordList.forEach((i) => (i.selected = false));
            that.$set(that, 'showWord', true);
          }
        }
      },

      async getWordList() {
        let that = this;
        let res = await $http({
          url: 'znyy/superReadStudentWordBook/work-book-stage',
          method: 'get',
          data: {
            studentCode: that.studentCode,
            merchantCode: that.merchantCode
          }
        });
        if (res) {
          // console.log(res.data);
          let data = res.data;
          data.forEach((item) => {
            item.open = false;
            that.wordTotal += item.wordBookCount;
            // item.courseList.forEach((i) => (that.wordTotal += i.wordBookCount));
          });
          this.dataList = data;
        }
      },
      selectNow(subItem) {
        if (subItem.selected) {
          this.$set(subItem, 'selected', false);
          let arr = this.wordSelect.filter((item) => item.id != subItem.id);
          this.wordSelect = arr;
        } else {
          this.$set(subItem, 'selected', true);
          this.wordSelect.push(subItem);
        }
      },
      autoSelect() {
        // 寻找是否有打开的面板
        let that = this;
        if (that.showWordList.length < 1) {
          $showMsg('请选择展开阶段词库');
        } else {
          that.$set(that, 'autoSelectVal', !that.autoSelectVal);
          if (!that.autoSelectVal) {
            that.showWordList.forEach((o) => (o.selected = false));
            that.wordSelect = [];
          } else {
            let randomLength = that.showWordList.length >= 5 ? 5 : that.showWordList.length;
            let wordsIndexArr = [];
            const max = that.showWordList.length;
            while (wordsIndexArr.length < randomLength) {
              const index = Math.floor(Math.random() * max);
              if (wordsIndexArr.indexOf(index) === -1) {
                wordsIndexArr.push(index);
                that.showWordList[index].selected = true;
              }
            }
            wordsIndexArr.forEach((index) => {
              that.wordSelect.push(that.showWordList[index]);
            });
          }
        }
      },
      getType(type) {
        switch (type) {
          case 'read':
            return '阅读';
          case 'word':
            return '单词';
          case 'grammar':
            return '语法';
          default:
            return '';
        }
      },
      getGrade(type) {
        switch (type) {
          case 'XiaoXue':
            return '小学';
          case 'ChuZhong':
            return '初中';
          case 'GaoZhong':
            return '高中';
          case 'College':
            return '大学';
          case 'YaSi':
            return '雅思';
          case 'TuoFu':
            return '托福';
          default:
            return '';
        }
      },
      movieLogging() {
        if (this.wordSelect.length > 0) {
          let wordSelect = this.wordSelect.map((i) => i.word);
          uni.navigateTo({
            url: `/aiIntelligentWords/wordsSelect/aiLogging?wordSelect=${JSON.stringify(wordSelect)}&courseStage=${this.openWordObj.courseStageName}`
          });
        } else {
          return;
        }
      }
    }
  };
</script>
<style lang="scss">
  page {
    height: 100vh;
    padding: 0;
  }

  .wordsContent {
    width: 100%;
    box-sizing: border-box;
    height: 100%;
    padding: 18rpx 32rpx;
    background-color: #f3f8fc;

    .bg_wrap {
      background-color: #fff;
      border-radius: 24rpx;
      // height: 1246rpx;
      padding: 40rpx 32rpx 0 32rpx;
      overflow-y: auto;
    }

    .no_words {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 90%;
    }

    .wordsContent__wrap {
      display: flex;
      justify-content: space-between;
      padding-bottom: 30rpx;
      border-bottom: 2px dashed #efefef;

      .wordsContent__title {
        font-size: 32rpx;
        color: #428a6f;
      }

      .wordsContent__title__now {
        padding-left: 10rpx;
        color: #ee6436;
        font-size: 12px;
      }

      .wrapBtn {
        font-size: 14px;
      }
    }
  }

  .accordion {
    margin-top: 30rpx;
    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10rpx 24rpx;
      background: #f5f8fa;
      border-radius: 8rpx;
      color: #555555;
    }

    &-body {
      max-height: 850rpx;
      overflow-y: scroll;
      margin-top: 10rpx;
      border-radius: 4rpx;
      padding: 16rpx 14rpx;
      background-color: #f7f7f7;
      display: flex;
      flex-wrap: wrap;

      .wordsItem {
        background-color: #fff;
        min-width: 10%;
        text-align: center;
        padding: 10rpx 20rpx;
        border-radius: 22rpx;
        margin: 10rpx;
        font-size: 14px;
      }

      .selectActive {
        color: #428a6f;
        background-color: #a7eeb8;
      }
    }
  }

  .determine {
    width: 586upx;
    height: 80upx;
    background-color: #e9e9e9;
    border-radius: 45upx;
    position: absolute;
    bottom: 10rpx;
    color: #b3b3b3;
    line-height: 80upx;
    font-size: 30upx;
    left: 80upx;
    text-align: center;
  }

  .determineActive {
    background-color: #339378;
    border-radius: 45upx;
    position: absolute;
    bottom: 60upx;
    color: #fff;
  }
</style>
