<template>
  <view>
    <u-sticky>
      <view class="bg-f3">
        <u-tabs
          :list="listCate"
          keyName="cateName"
          lineWidth="30"
          lineHeight="4"
          lineColor="#EA6031"
          :activeStyle="{
            color: '#303133',
            fontWeight: 'bold',
            transform: 'scale(1.05)'
          }"
          :inactiveStyle="{
            color: '#606266',
            transform: 'scale(1)'
          }"
          itemStyle="padding-left: 25px; padding-right: 25px; height: 44px;"
          @click="click"
        ></u-tabs>
      </view>
    </u-sticky>

    <view class="mt-40 pb-50" v-if="infoLists">
      <view class="courseList plr-30">
        <block v-for="(item, index) in infoLists.list" :key="index">
          <view class="courseItem radius-20 pb-10 positionRelative" @tap.stop="skintap('Coursedetails/productDetils?id=' + item.courseId)">
            <view class="courseimg relative">
              <image :src="item.courseImage" class="wh100" mode="widthFix"></image>
              <view class="positionAbsolute courseTip">
                <image :src="item.courseLabel == 1 ? imgHost + 'dxSelect/tip_tyk.png' : imgHost + 'dxSelect/tip_zsk.png'" class="ty_img" mode="widthFix"></image>
              </view>
            </view>
            <view class="plr-30 mtb-20">
              <view class="bold f-30">{{ item.courseName }}</view>
              <view class="color_red font12 mtb-15 displayflex displayflexbetween">
                <view>
                  会员价
                  <span class="bold f-34">￥{{ item.memberPrice }}</span>
                </view>
                <image class="productShare" :src="imgHost + 'dxSelect/share_icon.png'" @click.stop="shareVip('1', item)"></image>
              </view>
              <view class="displayflex color_grey f-24" style="justify-content: space-between">
                <view>
                  原价
                  <text style="text-decoration: line-through">￥{{ item.originalPrice }}</text>
                </view>
                <view>{{ item.studyNumber }}+人付款</view>
              </view>
            </view>
          </view>
        </block>
        <view v-if="no_more && infoLists.list.length > 0" style="width: 100%; text-align: center">
          <u-divider text="到底了"></u-divider>
        </view>
      </view>
    </view>

    <view v-if="infoLists.list != undefined && infoLists.list.length == 0" class="t-c flex-col" :style="{ height: useHeight + 'rpx' }">
      <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
      <view style="color: #bdbdbd">暂无数据</view>
    </view>

    <!-- 分享弹窗 -->
    <uni-popup ref="sharePopup" type="bottom" style="padding: 0">
      <view class="shareCard">
        <view class="reviewTitle bold">立即分享给好友</view>
        <view class="displayflexbetween mt-60" style="justify-content: space-around">
          <view class="t-c" @click="posterShare" v-if="token">
            <image :src="imgHost + 'dxSelect/share_img.png'" class="shareIcon" mode=""></image>
            <view class="mt-10 f-26 c-66">海报分享</view>
          </view>
          <!-- #ifdef MP-WEIXIN -->
          <view class="t-c">
            <button open-type="share" class="fillButton">
              <image :src="imgHost + 'dxSelect/share_lj.png'" class="shareIcon" mode=""></image>
              <view class="mt-10 c-66 f-26">链接分享</view>
            </button>
          </view>
          <!-- #endif -->

          <!-- #ifdef APP-PLUS -->
          <view class="t-c" @click="linkShare">
            <button open-type="share" class="fillButton">
              <image :src="imgHost + 'dxSelect/share_lj.png'" class="shareIcon" mode=""></image>
              <view class="mt-10 c-66 f-26">链接分享</view>
            </button>
          </view>
          <!-- #endif -->
        </view>
        <view class="shareCancelBox mt-60">
          <view class="share_cancel" @click="closeDialog">取消</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import Config from '@/util/config.js';
  const { $navigationTo, $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        lineBg: 'http://mshnimg.vtui365.com/20220908104716f2e325872.png',
        listCate: [],
        cateId: '',
        infoLists: {},
        page: 1,
        no_more: false,
        useHeight: 0, //除头部之外高度
        imgHost: getApp().globalData.imgsomeHost,
        token: '',
        shareContent: {} // 分享内容
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 140;
        }
      });
    },
    onShareAppMessage(res) {
      let userId = uni.getStorageSync('user_id');
      let url = `/pages/beingShared/index?scene=${userId}&type=1&id=${this.shareContent.id}`;
      setTimeout(() => {
        this.$refs.sharePopup.close();
      }, 2000);
      if (!uni.getStorageSync('token')) {
        return {
          title: '叮，你的好友敲了你一下，赶紧过来看看',
          imageUrl: this.shareContent.imgurl,
          // imageUrl: "https://document.dxznjy.com/dxSelect/superman_share.jpg",
          path: `Coursedetails/productDetils?id=${this.shareContent.id}`
        };
      }
      return {
        title: '叮，你的好友敲了你一下，赶紧过来看看',
        imageUrl: this.shareContent.imgurl,
        path: `/pages/beingShared/index?scene=${userId}&type=1&id=${this.shareContent.id}`
      };
    },
    onLoad() {
      this.list();
    },
    onShow() {
      this.token = uni.getStorageSync('token');
    },
    onReachBottom() {
      if (this.page >= this.infoLists.totalPage) {
        this.no_more = true;
        return false;
      }
      this.course(true, ++this.page);
    },
    methods: {
      click(item) {
        console.log(item);
        let _this = this;
        _this.page = 1;
        _this.no_more = false;
        _this.cateId = item.cateId;
        _this.course();
      },
      navigationToto(id) {
        uni.navigateTo({
          url: './courseDetail?id=' + id
        });
      },
      async list() {
        let _this = this;
        const res = await $http({
          url: 'zx/course/cateList',
          data: {
            type: 1 //不展示会议页签
          }
        });
        if (res) {
          let newToDo = {
            cateId: '',
            cateName: '全部'
          };
          _this.listCate = [newToDo, ...res.data];
          _this.cateId = _this.listCate[0].cateId;
          _this.course();
        }
      },
      async course(isPage, page) {
        let _this = this;
        const res = await $http({
          url: 'zx/course/courseList',
          data: {
            indexShow: 0,
            cityCode: '',
            cateId: _this.cateId,
            page: page || 1,
            cateType: 1
          }
        });
        if (res) {
          if (isPage) {
            let old = _this.infoLists.list;
            _this.infoLists.list = [...old, ...res.data.list];
          } else {
            _this.infoLists = res.data;
          }
        }
      },

      // 会员专享分享
      shareVip(type, item) {
        console.log('会员专享分享');
        this.$refs.sharePopup.open();
        this.shareContent.type = type;
        // id = item.courseId;
        // imgurl = item.courseImage;
        this.shareContent.id = item.courseId;
        this.shareContent.imgurl = item.courseImage;
      },

      // 点击海报
      posterShare() {
        this.$refs.sharePopup.close();
        uni.navigateTo({
          url: `/splitContent/poster/index?type=${this.shareContent.type}&id=${this.shareContent.id}`
        });
      },

      ///app链接分享 报错 图片太大
      linkShare() {
        setTimeout(() => {
          this.$refs.sharePopup.close();
        }, 2000);
        uni.share({
          provider: 'weixin',
          scene: 'WXSceneSession',
          type: 5,
          title: '我推荐给你一个好物，叮，你的好友敲了你一下，赶紧过来看看',
          imageUrl: this.shareContent.imgurl, //分享封面
          miniProgram: {
            id: Config.miniOriginalId,
            path: '/pages/beingShared/index?scene=' + uni.getStorageSync('user_id') + '&type=' + this.shareContent.type + '&id=' + this.shareContent.id,
            type: 0,
            webUrl: Config.webUrl
          },
          success: (ret) => {
            console.log(JSON.stringify(ret));
            uni.showToast({
              icon: 'none',
              title: '分享成功'
            });
          },
          fail: (ret) => {
            console.log(JSON.stringify(ret));
            uni.showToast({
              icon: 'none',
              title: '分享失败'
            });
          }
        });
      },

      //关闭弹窗
      closeDialog() {
        this.$refs.sharePopup.close();
        this.shareContent = {};
      },

      skintap(url) {
        if (!uni.getStorageSync('token')) {
          uni.navigateTo({
            url: '/Personalcenter/login/login'
          });
        } else {
          $navigationTo(url);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  page {
    background-color: #f3f8fc;
  }

  /deep/.u-sticky {
    padding: 0 20rpx;
  }

  .list .item {
    background-color: #fff;
    margin-bottom: 20rpx;
  }

  .img_s {
    width: 160rpx;
  }
  /deep/.u-tabs__wrapper__nav__line {
    margin-left: 14rpx !important;
  }

  .courseList {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .courseItem {
    width: 330upx;
    border-radius: 20upx;
    background-color: #fff;
    margin-bottom: 30rpx;
  }

  .courseTip {
    width: 95upx;
    height: 40rpx;
    top: 20rpx;
    right: 0;
  }

  .courseItem_tig {
    width: 115upx;
    top: 0;
    left: 0;
  }

  .productShare {
    width: 30upx;
    height: 30upx;
  }

  /*分享弹窗样式*/
  .shareCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    padding-top: 50upx;
    box-sizing: border-box;
  }

  .shareIcon {
    width: 100upx;
    height: 100upx;
  }

  .shareCancelBox {
    background-color: #f3f8fc;
    width: 100%;
    height: 120upx;
    padding-top: 20upx;
    box-sizing: border-box;
  }
  .share_cancel {
    width: 100%;
    height: 100upx;
    line-height: 100upx;
    text-align: center;
    font-size: 30upx;
    background-color: #fff;
    color: #666666;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }

  .dialogContent {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }
  .ty_img {
    width: 95rpx;
    height: 50rpx;
  }
</style>
