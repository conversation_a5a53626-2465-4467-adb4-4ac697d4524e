<template>
	<view>
		<view :style="{ height: titleHeight + 'px' }" class="pageTopstyle">
			<u-navbar title="甄选好课" titleStyle="color: #333333;font-size: 32rpx;font-weight: bold" bgColor="transparent"
				leftIconSize="0" :border="false"></u-navbar>
		</view>
		<view class="">
			<!-- 固定搜索框 -->
			<u-sticky bgColor="transparent" :z-index="99">
				<view class="search-container" :style="{ paddingTop: titleHeight + 30 + 'px' }">
					<view class="search-wrapper">
						<view class="search-css" @click="searchFocus">
							<view class="" style="margin-left: 24rpx">
								<u-icon name="search" class="search-image" color="#8A8A8A" size="48"></u-icon>
							</view>
							<view class="search-input" type="text" readonly>请输入</view>
						</view>
					</view>
				</view>

				<!-- 滚动时显示的tabs -->
				<view v-show="showTabs" class="sticky-tabs">
					<u-tabs :list="list" :current="currentItem" @click="tabChange" :lineColor="'transparent'"
						height="100" :activeStyle="{
							color: '#ffffff',
							backgroundColor: '#22ac39',
							transform: 'scale(1.05)',
							fontWeight: 'bold',
							padding: '0 20rpx',
							height: '60rpx',
							lineHeight: '60rpx',
							borderRadius: '40rpx'
						}" :inactiveStyle="{
							color: '#333333',
							backgroundColor: 'transparent',
							transform: 'scale(1)',
							fontWeight: 'normal',
							height: '60rpx',
							lineHeight: '60rpx'
						}" :key="'tabs-' + currentItem">
					</u-tabs>
				</view>

				<!-- 销量和价格排序 -->
				<view v-show="showTabs" class="sort-section">
					<view class="meet_top_content">
						<view class="dot_css f-24">
							<view class="mr-55 flexbox" style="padding-top: 4rpx">
								<view class="text mr-8">销量</view>
								<image v-if="goodsSalesOrderType == ''" @tap="getSelectInfo('goodsSales', 'desc', '销量')"
									class="wh24"
									src="https://document.dxznjy.com/course/e1ed9cd113a1408b9782c9b6baa5350a.png" mode="">
								</image>
								<image v-if="goodsSalesOrderType == 'desc'" @tap="getSelectInfo('goodsSales', 'asc', '销量')"
								class="wh24"
									src="https://document.dxznjy.com/course/a6dea11bfce04d8ebb10613944e7a72e.png" mode="">
								</image>
								
								<image v-if="goodsSalesOrderType == 'asc'" @tap="getSelectInfo('goodsSales','','销量')"
									class="wh24"
									src="https://document.dxznjy.com/course/80dd312f9b4e45e9bb1d2eebf2e666e6.png">
								</image>
							</view>
							<view>
								<text class="text" style="color: #555555">价格</text>
								<image v-if="goodsOriginalPriceOrderType == ''"
									@tap="getSelectInfo('goodsOriginalPrice', 'desc', '价格')" class="wh24"
									src="https://document.dxznjy.com/course/e1ed9cd113a1408b9782c9b6baa5350a.png">
								</image>
								<image v-if="goodsOriginalPriceOrderType == 'desc'"
									@tap="getSelectInfo('goodsOriginalPrice', 'asc', '价格')" class="wh24"
									src="https://document.dxznjy.com/course/a6dea11bfce04d8ebb10613944e7a72e.png">
								</image>
								<image v-if="goodsOriginalPriceOrderType == 'asc'"
									@tap="getSelectInfo('goodsOriginalPrice', '', '价格')" class="wh24"
									src="https://document.dxznjy.com/course/80dd312f9b4e45e9bb1d2eebf2e666e6.png">
								</image>
							</view>
						</view>
					</view>
				</view>
			</u-sticky>

			<view class="gangDistrict" :class="{ hidden: showTabs }" style="">
				<view class="bodyStyle">
					<view class="gangDistrictmodule">
						<!-- 少于5个时单行均衡排版 -->
						<view v-if="list.length < 5" class="category-single-row">
							<view v-for="(item, index) in list" :key="index" class="category-item-single"
								:class="{ 'selected': currentItem === index }"
								@click="selectCategory(index)">
								<image :src="item.picUrl ? item.picUrl : allImage" class="category-image"></image>
								<view class="category-text-wrapper">
									<text class="category-text" :class="{ 'selected-text': currentItem === index }">{{ item.name }}</text>
									<image v-if="currentItem === index" class="selected-icon" 
										src="https://document.dxznjy.com/course/5fe6dd0f43fb4c63b5498616be83b145.png"></image>
								</view>
							</view>
						</view>

						<!-- 等于5个时单行均衡排版（无分割线） -->
						<view v-else-if="list.length === 5" class="category-single-row-no-divider">
							<view v-for="(item, index) in list" :key="index" class="category-item-single-no-divider"
								:class="{ 'selected': currentItem === index }"
								@click="selectCategory(index)">
								<image :src="item.picUrl ? item.picUrl : allImage" class="category-image"></image>
								<view class="category-text-wrapper">
									<text class="category-text" :class="{ 'selected-text': currentItem === index }">{{ item.name }}</text>
									<image v-if="currentItem === index" class="selected-icon" 
										src="https://document.dxznjy.com/course/5fe6dd0f43fb4c63b5498616be83b145.png"></image>
								</view>
							</view>
						</view>

						<!-- 5-10个时正常布局 -->
						<view v-else-if="list.length <= 10" class="category-normal">
							<!-- 第一行：前5个项目响应式布局 -->
							<view class="first-row">
								<view v-for="(item, index) in list.slice(0, 5)" :key="index"
									class="category-item-normal" :class="{ 'selected': currentItem === index }"
									@click="selectCategory(index)">
									<image :src="item.picUrl ? item.picUrl : allImage" class="category-image"></image>
									<view class="category-text-wrapper">
										<text class="category-text" :class="{ 'selected-text': currentItem === index }">{{ item.name }}</text>
										<image v-if="currentItem === index" class="selected-icon" 
											src="https://document.dxznjy.com/course/5fe6dd0f43fb4c63b5498616be83b145.png"></image>
									</view>
								</view>
							</view>
							<!-- 第二行：剩余项目居左布局 -->
							<view v-if="list.length > 5" class="second-row">
								<view v-for="(item, index) in list.slice(5)" :key="index + 5"
									class="category-item-normal" :class="{ 'selected': currentItem === index + 5 }"
									@click="selectCategory(index + 5)">
									<image :src="item.picUrl ? item.picUrl : allImage" class="category-image"></image>
									<view class="category-text-wrapper">
										<text class="category-text" :class="{ 'selected-text': currentItem === index + 5 }">{{ item.name }}</text>
										<image v-if="currentItem === index + 5" class="selected-icon" 
											src="https://document.dxznjy.com/course/5fe6dd0f43fb4c63b5498616be83b145.png"></image>
									</view>
								</view>
							</view>
						</view>

						<!-- 多于10个时显示滑动布局 -->
						<view v-else class="scroll-container">
							<swiper class="category-swiper" :indicator-dots="false" :autoplay="false" :duration="300"
								@change="onSwiperChange">
								<!-- 第一页：固定显示前10条数据 -->
								<swiper-item class="swiper-item">
									<view class="category-page">
										<!-- 第一行：前5条数据 -->
										<view class="category-row">
											<view v-for="(item, index) in firstRowItems" :key="index"
												class="category-item-scroll" :class="{ 'selected': currentItem === index }"
												@click="selectCategory(index)">
												<image :src="item.picUrl ? item.picUrl : allImage"
													class="category-image">
												</image>
												<view class="category-text-wrapper">
													<text class="category-text" :class="{ 'selected-text': currentItem === index }">{{ item.name }}</text>
													<image v-if="currentItem === index" class="selected-icon" 
														src="https://document.dxznjy.com/course/5fe6dd0f43fb4c63b5498616be83b145.png"></image>
												</view>
											</view>
										</view>
										<!-- 第二行：6-10条数据 -->
										<view class="category-row" v-if="secondRowItems.length > 0">
											<view v-for="(item, index) in secondRowItems" :key="'second-' + index"
												class="category-item-scroll" :class="{ 'selected': currentItem === index + 5 }"
												@click="selectCategory(index + 5)">
												<image :src="item.picUrl ? item.picUrl : allImage"
													class="category-image">
												</image>
												<view class="category-text-wrapper">
													<text class="category-text" :class="{ 'selected-text': currentItem === index + 5 }">{{ item.name }}</text>
													<image v-if="currentItem === index + 5" class="selected-icon" 
														src="https://document.dxznjy.com/course/5fe6dd0f43fb4c63b5498616be83b145.png"></image>
												</view>
											</view>
										</view>
									</view>
								</swiper-item>

								<!-- 剩余页面：每页显示10条数据 -->
								<swiper-item v-for="(pageItems, pageIndex) in remainingPages" :key="'page-' + pageIndex"
									class="swiper-item">
									<view class="category-page">
										<!-- 每页的第一行：5条数据 -->
										<view class="category-row">
											<view v-for="(item, index) in pageItems.slice(0, 5)"
												:key="'page-' + pageIndex + '-first-' + index"
												class="category-item-scroll"
												:class="{ 'selected': currentItem === 10 + pageIndex * 10 + index }"
												@click="selectCategory(10 + pageIndex * 10 + index)">
												<image :src="item.picUrl ? item.picUrl : allImage"
													class="category-image">
												</image>
												<view class="category-text-wrapper">
													<text class="category-text" :class="{ 'selected-text': currentItem === 10 + pageIndex * 10 + index }">{{ item.name }}</text>
													<image v-if="currentItem === 10 + pageIndex * 10 + index" class="selected-icon" 
														src="https://document.dxznjy.com/course/5fe6dd0f43fb4c63b5498616be83b145.png"></image>
												</view>
											</view>
										</view>
										<!-- 每页的第二行：剩余数据 -->
										<view class="category-row" v-if="pageItems.length > 5">
											<view v-for="(item, index) in pageItems.slice(5)"
												:key="'page-' + pageIndex + '-second-' + index"
												class="category-item-scroll"
												:class="{ 'selected': currentItem === 10 + pageIndex * 10 + 5 + index }"
												@click="selectCategory(10 + pageIndex * 10 + 5 + index)">
												<image :src="item.picUrl ? item.picUrl : allImage"
													class="category-image">
												</image>
												<view class="category-text-wrapper">
													<text class="category-text" :class="{ 'selected-text': currentItem === 10 + pageIndex * 10 + 5 + index }">{{ item.name }}</text>
													<image v-if="currentItem === 10 + pageIndex * 10 + 5 + index" class="selected-icon" 
														src="https://document.dxznjy.com/course/5fe6dd0f43fb4c63b5498616be83b145.png"></image>
												</view>
											</view>
										</view>
									</view>
								</swiper-item>
							</swiper>
							<!-- 滑动指示条 -->
							<view class="scroll-indicator">
								<view class="indicator-dots">
									<view class="dot left-dot"
										:class="{ active: currentSwiperIndex === 0, capsule: currentSwiperIndex > 0 }">
									</view>
									<view class="dot right-dot"
										:class="{ active: currentSwiperIndex > 0, capsule: currentSwiperIndex === 0 }">
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<view class="" style="padding: 0 32rpx">
				<!-- 始终显示的排序部分，不依赖showTabs -->

				<view class="meet_top_content">
					<view class="dot_css f-24">
						<view class="mr-55 flexbox" style="padding-top: 4rpx">
							<text class="text" style="color: #555555">销量</text>

							<image v-if="goodsSalesOrderType == ''" @tap="getSelectInfo('goodsSales', 'desc', '销量')"
								class="wh24"
								src="https://document.dxznjy.com/course/e1ed9cd113a1408b9782c9b6baa5350a.png" mode="">
							</image>
							<image v-if="goodsSalesOrderType == 'desc'" @tap="getSelectInfo('goodsSales', 'asc', '销量')"
							class="wh24"
								src="https://document.dxznjy.com/course/a6dea11bfce04d8ebb10613944e7a72e.png" mode="">
							</image>

							<image v-if="goodsSalesOrderType == 'asc'" @tap="getSelectInfo('goodsSales','','销量')"
								class="wh24"
								src="https://document.dxznjy.com/course/80dd312f9b4e45e9bb1d2eebf2e666e6.png">
							</image>


<!-- 
							<u-icon name="arrow-down-fill" @tap="getSelectInfo('goodsSales', 'asc', '销量')"
								v-if="goodsSalesOrderType == 'desc'" color="#555555" size="24"></u-icon>
							<u-icon name="arrow-up-fill" @tap="getSelectInfo('goodsSales', 'desc', '销量')"
								v-if="goodsSalesOrderType == 'asc'" color="#565656" size="24"></u-icon> -->
						</view>
						<view>
							<text class="text" style="color: #555555">价格</text>
							<image v-if="goodsOriginalPriceOrderType == ''"
								@tap="getSelectInfo('goodsOriginalPrice', 'desc', '价格')" class="wh24"
								src="https://document.dxznjy.com/course/e1ed9cd113a1408b9782c9b6baa5350a.png">
							</image>
							<image v-if="goodsOriginalPriceOrderType == 'desc'"
								@tap="getSelectInfo('goodsOriginalPrice', 'asc', '价格')" class="wh24"
								src="https://document.dxznjy.com/course/a6dea11bfce04d8ebb10613944e7a72e.png">
							</image>
							<image v-if="goodsOriginalPriceOrderType == 'asc'"
								@tap="getSelectInfo('goodsOriginalPrice', '', '价格')" class="wh24"
								src="https://document.dxznjy.com/course/80dd312f9b4e45e9bb1d2eebf2e666e6.png">
							</image>
						</view>
					</view>
				</view>


				<view class="course-container" :style="{ marginTop: showTabs ? '400rpx' : '0' }">
					<template v-if="courseData && courseData.length > 0">
						<course-list v-for="(item, index) in courseData" :key="index" :item="item" :index="index"
							:width="'326rpx'"
							@click="skintap('Coursedetails/productDetils?id=' + item.goodsId, item.goodsId)"></course-list>
					</template>
					<template v-else>
						<view class="empty-data">
							<!-- <image class="empty-image" src="https://document.dxznjy.com/course/empty_data.png"></image> -->
							<view class="empty-text">暂无数据</view>
						</view>
					</template>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import courseList from '@/components/course-list/course-list.vue';
	const {
		$navigationTo,
		$http
	} = require('@/util/methods.js');
	export default {
		components: {
			courseList
		},
		data() {
			return {
				titleHeight: 0,
				scrollLeft: 0, // 滚动距离
				containerWidth: 0, // 容器宽度
				contentWidth: 0, // 内容宽度
				currentSwiperIndex: 0, // 当前swiper页面索引
				//排序
				orderBy: '',
				goodsOriginalPriceOrderType: '',
				goodsSalesOrderType: '',



				courseData: [],
				showTabs: false, // 控制是否显示u-tabs
				pageScrollTop: 0, // 页面滚动距离
				list: [],
				page: 1,
				totalPage: 0,
				courseid: 0,
				currentItem: 0,
				allImage: 'https://document.dxznjy.com/course/55e56ae65d264039bf9a8924e8134ca3.png'
			};
		},
		computed: {
			// 滑动布局：第一页数据（前10条，按权重顺序）
			firstPageItems() {
				return this.list.slice(0, 10);
			},
			// 滑动布局：第一页的第一行数据（前5条）
			firstRowItems() {
				return this.firstPageItems.slice(0, 5);
			},
			// 滑动布局：第一页的第二行数据（6-10条）
			secondRowItems() {
				return this.firstPageItems.slice(5);
			},
			// 滑动布局：剩余页面数据（10条以后的数据）
			remainingItems() {
				return this.list.slice(10);
			},
			// 将剩余数据按每页10条分组
			remainingPages() {
				const pages = [];
				const pageSize = 10;
				for (let i = 0; i < this.remainingItems.length; i += pageSize) {
					pages.push(this.remainingItems.slice(i, i + pageSize));
				}
				return pages;
			}
		},
		onTabItemTap() {
			//点击甄选好课tabBar触发埋点事件
			getApp().sensors.track('bottomDetailsClick', {

			});
  		},
		onLoad() {
			this.getHeight(); //获取导航栏距离胶囊的安全距离
			this.getTabs()
			// 默认选中第一个分类
			this.currentItem = 0;
		},
		onShow() {
			// 重置所有选中状态为初始化
			this.resetAllStatus();
			this.getCourse(1);
		},
		onPageScroll(e) {
			this.pageScrollTop = e.scrollTop;
			// 当滚动距离超过分类区域高度时显示tabs
			this.showTabs = e.scrollTop > 200; // 可根据实际需要调整这个阈值
		},
		onReachBottom() {
			console.log('触底--------------', this.page, this.totalPage)
			if (this.page < Number(this.totalPage)) {
				console.log(6666666)
				this.getCourse(++this.page);
			}

		},
		methods: {
			leftClicks() {
				console.log('返回---------------');
				uni.navigateBack();
			},
			async getTabs() {
				const res = await $http({
					url: 'zx/wap/homePage/zxGoodCourse/list',
					data: {
						placement: ''
					}
				});
				if (res.code == 20000) {
					this.list = res.data.data
				}
				console.log(res, '金刚区----------0--')
			},
			getHeight() {
				let res = uni.getMenuButtonBoundingClientRect(); //胶囊按钮
				console.log(res, '高度');
				this.titleHeight = res.top + res.height;
			},
			//跳转到搜索页
			searchFocus() {
				getApp().sensors.track('indexSearchHandleClick', {
					name: '甄选搜索'
				});
				uni.navigateTo({
					url: '/interestModule/searchPage'
				});
			},
			// 跳转商品详情
			skintap(url, goodsId) {
				//埋点
				getApp().sensors.track('topCourseListClick', {
					goods_id: goodsId
				});
				$navigationTo(url);
			},
			// 甄选好课列表
			async getCourse(page) {
				// this.courseData = []
				uni.showLoading({
					title: '加载中'
				});

				page = page || 1;
				const res = await $http({
					url: 'zx/wap/homePage/zxGoodCourse/goodsList',
					data: {
						pageSize: 20,
						pageNum: page || 1,
						courseId: this.courseid, //甄选好课id（查全部传0）
						orderBy: this[this.orderBy + 'OrderType'] ? this.orderBy : '',
						orderType: this[this.orderBy + 'OrderType'] ? this[this.orderBy + 'OrderType'] : '',
						userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
					}
				});

				console.log('甄选好课列表', res);
				if (res.code == 20000) {
				console.log('甄选好课列表====', page);
					this.totalPage = res.data.totalPages
					// 如果是第一页，直接替换数据；如果是分页，则合并数据
					if (page == 1) {
						this.courseData = res.data.data || [];
					} else {
						this.courseData = [...this.courseData, ...(res.data.data || [])];
					}
					console.log(this.courseData, '甄选好课列表数据----------');
					uni.hideLoading()
				} else {
					uni.hideLoading()
				}
			},
			// 获取第二行项目的真实索引（已废弃，保留注释说明）
			// getSecondRowIndex(index) {
			// 	const half = Math.ceil(this.list.length / 2);
			// 	return index + half;
			// },

			// tabs切换事件
			tabChange(ads) {
				console.log('选中分类:', ads);
				// 找到被点击项目在list中的索引
				const selectedIndex = this.list.findIndex(item => item.id === ads.id);
				if (selectedIndex !== -1) {
					this.currentItem = selectedIndex; // 同步金刚区的选中状态
				}
				this.courseid = ads.id;
				this.page = 1; // 重置页码
				this.getCourse(1); // 从第一页开始获取数据
				// 这里可以添加其他逻辑，比如触发分类筛选
				// console.log('选中分类:', this.list[index].name);
			},
			// 监听滚动事件（保留以防其他地方需要）
			onScroll(e) {
				this.scrollLeft = e.detail.scrollLeft;
			},
			// 监听swiper切换事件
			onSwiperChange(e) {
				this.currentSwiperIndex = e.detail.current;
			},
			// 排序选择
			getSelectInfo(orderBy, orderType, name) {
				// getApp().sensors.track('selectionTabClick', {
				// 	name: name,
				// 	orderType: orderType
				this[orderBy + 'OrderType'] = orderType;
				this.orderBy = orderBy;
				if (this.orderBy == 'goodsSales') {
					this.goodsOriginalPriceOrderType = ''
				} else {
					this.goodsSalesOrderType = ''
				}
				this.page = 1; // 重置页码
				this.getCourse(1); // 从第一页开始获取数据
			},
			async selectCategory(index) {
				const item = this.list[index];
				//埋点
				getApp().sensors.track('topCourseClick', {
					name: item.name
				});
				console.log(item, '当前选中的item');
				console.log(index, '下标索引');
				this.courseid = item.id;
				this.page = 1; // 重置页码
				await this.getCourse(1); // 从第一页开始获取数据

				// 强制更新tabs选中状态，特别处理index=0的情况
				this.$nextTick(() => {
					// 如果是切换到index=0，先设置为其他值再设置回0
					if (index === 0 && this.currentItem === 0) {
						this.currentItem = -1; // 临时设置为一个不存在的索引
						this.$nextTick(() => {
							this.currentItem = 0; // 再设置回0
						});
					} else {
						this.currentItem = index; //tabs 联动
					}
				});
			},
			// 重置所有选中状态为初始化
			resetAllStatus() {
				// 重置金刚区和tabs选中状态
				this.currentItem = 0;
				this.courseid = 0;
				// 重置排序状态
				this.orderBy = '';
				this.goodsOriginalPriceOrderType = '';
				this.goodsSalesOrderType = '';
				// 重置页码
				this.page = 1;
			}
		}
	};
</script>

<style>
	body {
		background-color: #fafcfe;
	}
</style>
<style lang="scss" scoped>
	.pageTopstyle {
		background: linear-gradient(to right, #f4fafc, #eefdf6);
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		z-index: 99999;
	}

	.gangDistrict {
		// padding-top: 24rpx;
		width: 100%;
		background: linear-gradient(35deg, rgba(245, 250, 253, 1) 50%, rgb(240, 253, 247) 100%);
		transition: all 0.3s ease;

		&.hidden {
			height: 0;
			overflow: hidden;
			padding: 0;
			margin: 0;
		}
	}

	.gangDistrictmodule {
		margin-top: 0;
		background-color: #ffffff;
		border-radius: 20rpx;
		padding: 32rpx 10rpx 0;
	}

	.bodyStyle {
		width: 686rpx;
		margin: 0 auto;
	}

	.course-container {
		min-height: 1200rpx;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		// align-items: center;
	}

	/* 搜索框容器 */
	.search-container {
		background: linear-gradient(to right, #f4fafc, #f0fcf8);
		padding-bottom: 24rpx;
		/* 添加内部底部边距，不会影响与下方盒子的贴合 */
	}

	/* 滚动时显示的tabs */
	.sticky-tabs {
		background-color: #ffffff;
		// padding: 0 32rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}

	/* 排序部分样式 */
	.sort-section {
		background-color: #ffffff;
		border-bottom: 1rpx solid #f0f0f0;
		padding: 0 32rpx;

		// /* 自定义选中状态的圆角背景 */
		// :deep(.u-tabs__wrapper) {
		// 	height: 100rpx !important;
		// 	padding: 10rpx 0 !important;
		// }

		// :deep(.u-tabs__wrapper__nav__item--active) {
		// 	// background: linear-gradient(to right, #22ac39, #22d87d) !important;
		// 	color: #ffffff !important;
		// 	border-radius: 20rpx !important;
		// 	margin: 0 4rpx !important;
		// 	height: 80rpx !important;
		// 	line-height: 80rpx !important;
		// 	box-shadow: 0 2rpx 8rpx rgba(34, 172, 57, 0.2) !important;
		// }

		// :deep(.u-tabs__wrapper__nav__item) {
		// 	background-color: transparent !important;
		// 	border-radius: 20rpx !important;
		// 	margin: 0 4rpx !important;
		// 	height: 80rpx !important;
		// 	line-height: 80rpx !important;
		// 	transition: all 0.3s ease !important;
		// 	display: flex !important;
		// 	align-items: center !important;
		// 	justify-content: center !important;
		// }

		// /* 确保文字颜色正确 */
		// ::v-deep .u-tabs__wrapper__nav__item--active .u-tabs__wrapper__nav__item__text {
		// 	color: #ffffff !important;
		// }

		// ::v-deep .u-tabs__wrapper__nav__item .u-tabs__wrapper__nav__item__text {
		// 	font-weight: 500;
		// }
	}

	.search-wrapper {
		width: 686rpx;
		margin: 0 auto;
	}

	/* 搜索框 */
	.search-css {
		height: 72rpx;
		display: flex;
		align-items: center;
		border-radius: 40rpx;
		width: 100%;
		background-color: #ffffff;

		.search-input {
			color: #bebebe;
			height: 100%;
			font-size: 32rpx;
			line-height: 72rpx;
			margin-left: 16rpx;
		}
	}

	/* 少于5个时单行均衡排版 */
	.category-single-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		flex-wrap: nowrap;

		.category-item-single {
			display: flex;
			flex-direction: column;
			align-items: center;
			flex: 1;
			// margin: 0 8rpx;
			// padding: 12rpx;
			border-radius: 12rpx;
			border: 2rpx solid transparent;
			box-sizing: border-box;
			position: relative;

			// background-color: red;
			/* 添加右侧分割线，最后一个元素除外 */
			&:not(:last-child)::after {
				content: '';
				position: absolute;
				right: -8rpx;
				top: 50%;
				transform: translateY(-50%);
				width: 2rpx;
				height: 60rpx;
				background-color: #e5e5e5;
			}
		}
	}

	/* 等于5个时单行均衡排版（无分割线） */
	.category-single-row-no-divider {
		display: flex;
		justify-content: space-around;
		align-items: center;
		flex-wrap: nowrap;

		.category-item-single-no-divider {
			display: flex;
			flex-direction: column;
			align-items: center;
			flex: 1;
			margin: 0 8rpx;
			padding: 12rpx;
			border-radius: 12rpx;
			border: 2rpx solid transparent;
			box-sizing: border-box;
		}
	}

	/* 5-10个时正常布局 */
	.category-normal {

		/* 第一行：响应式布局 */
		.first-row {
			display: flex;
			justify-content: space-between;
			width: 100%;
			margin-bottom: 32rpx;
		}

		/* 第二行：居左布局 */
		.second-row {
			display: flex;
			justify-content: flex-start;
			width: 100%;
		}

		.category-item-normal {
			display: flex;
			flex-direction: column;
			align-items: center;
			width: 20%;
			// padding: 12rpx;
			border-radius: 12rpx;
			border: 2rpx solid transparent;
			box-sizing: border-box;
			// background-color: red;
		}

		/* 第二行项目间距 */
		.second-row .category-item-normal {
			margin-right: 5rpx;
		}

		/* 第二行最后一个项目不需要右边距 */
		.second-row .category-item-normal:last-child {
			margin-right: 0;
		}
	}

	/* 多于10个时滑动布局 */
	.scroll-container {
		width: 100%;

		.category-swiper {
			width: 100%;
			height: 300rpx;
			/* 增加高度以适应新的item高度 */

			.swiper-item {
				width: 100%;
				height: 100%;

				/* 每页容器 */
				.category-page {
					display: flex;
					flex-direction: column;
					/* 垂直排列两行 */
					width: 100%;
					height: 100%;
				}

				/* 每行 */
				.category-row {
					display: flex;
					flex-direction: row;
					width: 100%;
					margin-bottom: 32rpx;
					justify-content: flex-start;
					/* 居左对齐 */

					.category-item-scroll {
						display: flex;
						flex-direction: column;
						align-items: center;
						width: 135rpx;
						/* 固定宽度保证对齐 */
						height: 140rpx;
						/* 增加高度以容纳定位图标 */
						flex-shrink: 0;
						border-radius: 12rpx;
						border: 2rpx solid transparent;
						box-sizing: border-box;
						transition: opacity 0.2s ease;
						overflow: visible;
						/* 确保定位图标不被裁剪 */
						/* 添加过渡效果 */
					}

					/* 每行最后一个项目不需要右边距 */
					.category-item-scroll:last-child {
						margin-right: 0;
					}
				}

				/* 每页最后一行 */
				.category-page .category-row:last-child {
					margin-bottom: 0;
				}
			}
		}

		/* 滑动指示条 */
		.scroll-indicator {
			width: 100%;
			height: 20rpx;
			margin-top: 20rpx;
			display: flex;
			justify-content: center;

			.indicator-dots {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 60rpx;
				/* 缩小整体宽度，减少间距 */

				.dot {
					width: 12rpx;
					height: 12rpx;
					border-radius: 50%;
					background-color: #E5EFF6;
					transition: all 0.3s ease;

					/* 胶囊形状（保持灰色） */
					&.capsule {
						width: 40rpx;
						height: 12rpx;
						border-radius: 6rpx;
						background-color: #E5EFF6;
						/* 胶囊始终保持灰色 */
					}

					/* 激活状态（只有小圆点才绿色高亮） */
					&.active:not(.capsule) {
						background-color: #22ac39;
					}
				}
			}
		}
	}

	/* 共同样式 */
	.category-image {
		width: 72rpx;
		height: 72rpx;
		// border-radius: 8rpx;
		// margin-bottom: 24rpx;
		/* 移除图片的边框，改为给容器添加边框 */
	}

	/* 文本包装器 - 用于相对定位图标 */
	.category-text-wrapper {
		position: relative;
		display: inline-block;
		margin-top: 10rpx;
	}

	.category-text {
		font-size: 26rpx;
		color: #555555;
		line-height: 36rpx;
		text-align: center;
		white-space: nowrap;
		transition: color 0.3s ease;
	}

	/* 选中图标样式 */
	.selected-icon {
		position: absolute;
		bottom: -6rpx;
		right: -12rpx;
		width: 15rpx;
		height: 15rpx;
		z-index: 999;
		/* 提高z-index确保不被遮挡 */
	}

	/* 选中状态样式 */
	.selected-text {
		color: #22ac39 !important;
		font-weight: bold;
	}	/* 添加点击效果 */
	.category-item-single,
	.category-item-single-no-divider,
	.category-item-normal,
	.category-item-scroll {
		transition: opacity 0.2s ease;

		&:active {
			opacity: 0.7;
		}

		/* 选中状态的容器样式 */
		&.selected {
			.category-text {
				color: #22ac39;
				font-weight: bold;
			}
		}
	}

	/* 暂无数据样式 */
	.empty-data {
		width: 100%;
		height: 500rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;

		// .empty-image {
		// 	width: 200rpx;
		// 	height: 200rpx;
		// 	margin-bottom: 20rpx;
		// }

		.empty-text {
			font-size: 28rpx;
			color: #999999;
		}
	}

	.meet_top_content {
		height: 104rpx;
		display: flex;
		align-items: center;

		/* 居右显示 */
		.dot_css {
			display: flex;
			color: #5a5a5a;

			.wh24 {
				display: inline-block;
				width: 22rpx;
				height: 24rpx;
				margin-left: 8rpx;
				vertical-align: middle;
			}

			.text {
				vertical-align: middle;
			}
		}
	}
</style>