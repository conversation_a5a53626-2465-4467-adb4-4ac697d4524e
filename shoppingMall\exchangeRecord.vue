<template>
	<page-meta :page-style="'overflow:'+(show?'hidden':'visible')"></page-meta>
	<view>
		<view v-if="exchangeList.length>0">
			<view v-for="item in exchangeList" :key="item.id" class="record_list_css">
				<view class="record_list">
					<view class="record_image_css">
						<image class="record_image" mode="widthFix" :src="item.piGoodsVo.goodsPicUrl"></image>
					</view>
					<view class="record_content_css">
						<view class="f-28 c-33 lh-40 bold">
							<span>订单编号：</span>
							<span>{{item.orderNo}}</span>
						</view>
						<view class="f-28 c-55 lh-40 mt-12 record_name_css">{{item.piGoodsVo.goodsName}}</view>
					</view>
				</view>
				<view @click="exchange(item)" class="record_bottom_css f-28 mt-35 mr-15 bold">查看兑换码></view>
			</view>
		</view>
		<view class="pt-140" v-else>
			<emptyPage ></emptyPage>
		</view>
		<uni-popup ref="successPopup" type="center"  @change="change">
			<view class="success_popup bg-ff">
				<view class="payment_image_css plr-25">
					<view @click="usageInstruction(exchangeInfo)">
						<image class="image_icon" src="https://document.dxznjy.com/course/1ea3945f14f44fb5aa34d76c3e874cbd.png"></image>
						<span class="popup_title_css ml-8">使用说明</span>
					</view>
					<u-icon @click="costClose()" name="close-circle-fill" color="#B1B1B1" size="32"></u-icon>
				</view>
				<view class="text_css f-32 bold lh-42 c-33">
					<view class="mt-30">卡券兑换成功！</view>
				</view>
				<view class="content_css_popup mt-24 c-ff" :style="'background-image: url(https://document.dxznjy.com/course/04f38aff62fb48e3a682c0f64cdd8515.png);'" >
					<view class="card_content_css">
						<view class="f-32 lh-44 card_good_name">{{exchangeInfo.piGoodsVo.goodsName}}</view>
						<view class="mt-24 f-28 lh-40">卡号</view>
						<view class="f-40 lh-50 card_exchange_code">{{ exchangeInfo.exchangeCode }}</view>
						<view @click.stop="copeCardNum(exchangeInfo.exchangeCode)" class="mt-40 f-28 button_css">点击复制卡号</view>
					</view>
				</view>
				<button  @click="costClose()" class="popup_button f-32 lh-44 c-ff mt-16">确定</button>
			</view>
		</uni-popup>
		<uni-popup ref="costRef" type="center"  @change="change">
			<view class="cost_content_css">
				<view class="cost_header_css f-32 bold pt-15">使用说明</view>
				<view class="lh-44 f-28 mt-20 c-55 plr-32 cost_text_css" v-if="exchangeInfo.piGoodsVo&&exchangeInfo.piGoodsVo.usageInstruction">
					<p  v-html="exchangeInfo.piGoodsVo.usageInstruction.replace(/(\r\n|\n|\r)/gm, '<br />')"></p>
				</view>
				<view class="block_circle">
					<u-icon @click="costBlock()" name="arrow-left" color="#555" size="36"></u-icon>
				</view>
				<view class="close_circle" @click="costClose()" >
					<u-icon name="close-circle-fill" color="#555" size="38"></u-icon>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
	const {
		$navigationTo,
		$showError,
		$showMsg,
		$http
	} = require("@/util/methods.js")
	import emptyPage from "./components/emptyPage.vue";
	export default {
		components: {
			emptyPage,
		},
		data() {
			return {
				exchangeList:[],
				exchangeInfo:{},
				page:1,
				show:false,
				infoLists:{}
			}
		},
		onReachBottom() {
			if (this.page*20 >= this.infoLists.totalCount) {
				this.no_more = true
				return false;
			}
			this.getExchange(true, ++this.page);
		},
		onLoad() {
			this.getExchange()
		},
		methods: {
			usageInstruction(){
				if(this.exchangeInfo.piGoodsVo&&this.exchangeInfo.piGoodsVo.usageInstruction){
					this.$refs.successPopup.close()
					this.$refs.costRef.open()
				}
				
			},
			change(e) {
				this.show = e.show
			},
			costBlock(){
				this.$refs.costRef.close()
				this.$refs.successPopup.open()
			},
			costClose(){
				this.$refs.successPopup.close()
				this.$refs.costRef.close()
				this.$refs.modalPopup.close()
			},
			async getExchange(isPage,page) {
			    let _this = this
			    const res = await $http({
			        url: 'zx/wap/goods/exchange/card/list',
			        data: {
			            pageNum:page || 1,
						pageSize:10,
						userId:uni.getStorageSync('user_id')?uni.getStorageSync('user_id'):''
			        }
			    })
			    if (res) {
					console.log(res)
					console.log('======================================')
					if (isPage) {
						let old = _this.exchangeList
						_this.exchangeList = [...old, ...res.data.data];
					} else {
						_this.exchangeList = res.data.data||[];
					}
					this.infoLists=res.data
			    }
			},
			copeCardNum(code){
				uni.setClipboardData({
					data: code,
					success: function(res) {
						uni.getClipboardData({
							success: function(res) {
								uni.showToast({
									title: "复制成功",
								});
							},
						});
					},
				});
			},
			exchange(info){
				this.exchangeInfo=info
				this.$refs.successPopup.open()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.record_list_css{
		background-color: #fff;
		width: 686rpx;
		margin:16rpx auto;
		border-radius: 16rpx;
		padding-bottom: 32rpx;
		.record_bottom_css{
			text-align: right;
			color:#428A6F;
		}
	}
	.cost_content_css{
		width: 686rpx;
		height: 980rpx;
		background:url('https://document.dxznjy.com/course/f12fb50e68cc4cf5b9c433a9e968d4e7.png') no-repeat;
		background-size: 100%;
		padding-top:290rpx;
		position: relative;
		.cost_header_css{
			text-align: center;
			line-height: 40rpx;
			word-break: break-all;
		}
		.cost_text_css{
			text-align: left;
			word-break: break-all;
		}
		.close_circle{
			position: absolute;
			right:20rpx;
			top:6rpx;
			z-index: 8;
		}
		.block_circle{
			position: absolute;
			left:20rpx;
			top:296rpx;
		}
	}
	.success_popup{
		width: 686rpx;
		height: 800rpx;
		border-radius: 8rpx;
		padding-top: 20rpx;
		.payment_image_css{
			display: flex;
			justify-content: space-between;
			.image_icon{
				display: inline-block;
				width: 30rpx;
				height: 30rpx;
				vertical-align: middle;
			}
			.popup_title_css{
				font-size: 24rpx;
				line-height: 34rpx;
				color:#F27120;
				vertical-align: middle;
			}
		}
		.text_css{
			text-align: center;
		}
		.content_css_popup{
			height: 404rpx;
			background-size: 100%;
			background-repeat: no-repeat;
			.card_content_css{
				width: 485rpx;
				margin:0 auto;
				padding-top: 65rpx;
				.card_good_name{
					height: 40rpx;
					overflow: hidden;
					white-space: nowrap; 
					text-overflow:ellipsis; 
				}
				.card_exchange_code{
					width: 481rpx;
					word-break: break-all;
					white-space: pre-wrap;
					height: 77rpx;
				}
				.button_css{
					text-align: center;
				}
			}
		}
		.popup_button{
			width: 280rpx;
			text-align: center;
			padding:24rpx 0;
			background-color: #428A6F;
			border-radius: 46rpx;
			margin:0 auto;
		}
		.botton_css{
			text-align: center;
			color:#C4C4C4;
		}
	}
	.record_list{
		display: flex;
		justify-content: flex-start;
		margin-top: 32rpx;
		.record_image_css{
			width: 124rpx;
			height: 124rpx;
			background-color:#F3F8FC;
			margin-top: 32rpx;
			margin-left: 24rpx;
			.record_image{
				width: 124rpx;
			}
		}
		.record_content_css{
			margin-top: 32rpx;
			margin-left: 24rpx;
			.record_name_css{
				width: 500rpx;
			}
		}
		
	}
</style>
