<template>
  <view class="curriculum_css" v-if="classList && classList.length > 0">
    <view class="curriculum_title ml-30 fontWeight f-28 pt-30">我的课程</view>
    <view class="curriculum_list mlr-30">
      <view v-for="item in classList" :key="item.id" class="flexbox list_item">
        <view class="curriculum_left_css">
          <view class="fontWeight curriculum_name f-28 lh-40 c-55">{{ item.courseName }}</view>
          <view class="color_grey f-22 mt-16 time-style">
            <!-- <text class="icon icon_deliver fontWeight f-24" v-if="item.courseType!=4&&!item.lastStudyTime">等待交付</text> -->
            <text class="icon icon_learning fontWeight f-24" v-if="item.courseType == 4 && item.lastStudyTime">正在学习</text>
            <text>创建时间：{{ item.createdTime }}</text>
          </view>
        </view>
        <view v-if="item.refundStatus != 2">
          <span v-if="item.courseType == 4 && item.lastStudyTime" @tap="gostudy(item)" class="f-24 fontWeight button_learning">继续学习</span>
          <span v-if="item.courseType == 4 && !item.lastStudyTime" @tap="gostudy(item)" class="f-24 fontWeight button_learning">进入学习</span>
          <span v-if="item.courseType == 3" @click="goLastback(item)" class="f-24 fontWeight button_learning">查看反馈</span>
        </view>
      </view>
    </view>
    <view class="arrow_bottom" @tap.stop="goCurriculum"></view>
  </view>
  <view v-else class="curriculum_css_no pt-30 pb-55 f-28">
    <view class="curriculum_title f-28 c-33 pl-45 bold">我的课程</view>
    <image class="curriculum_image" src="https://document.dxznjy.com/course/ac587707bf314badadb28a158852c77d.png"></image>
    <view class="c-66 f-24">暂无课程</view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        showCurriculum: false,
        classList: [],
        classInfo: {}
      };
    },
    created() {
      if (uni.getStorageSync('token')) {
        this.getSubject();
      }
    },
    methods: {
      //查看全部课程
      goCurriculum() {
        getApp().sensors.track('indexViewMoreClick', {
          name: '查看更多'
        });
        uni.navigateTo({
          url: '/Personalcenter/my/myCourse'
        });
      },
      // 查看上次反馈
      goLastback(item) {
        getApp().sensors.track('indexViewFeedbackClick', {
          name: item.courseName
        });
        uni.navigateTo({
          url: '/Coursedetails/feedback/index?data=' + JSON.stringify(this.classInfo)
        });
      },
      // 我的课程列表
      async getSubject(page) {
        let _this = this;
        const res = await $http({
          url: 'zx/wap/course/user/list',
          data: {
            pageSize: 5,
            pageNum: page || 1,
            isRefundExcluded: 1,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        if (res) {
          _this.classList = res.data.data;
        }
      },
      // 去学习
      gostudy(item) {
        uni.setStorageSync('studentName', item.studentName);
        uni.navigateTo({
          url: '/Coursedetails/study/courseDetail?courseId=' + item.courseId + '&studentCode=' + item.studentCode
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .curriculum_css_no {
    position: relative;
    width: 710rpx;
    margin: auto;
    text-align: center;
    background: url('https://document.dxznjy.com/course/18ddefbdbe7846e7a4c1103974e0baa0.png') no-repeat;
    background-size: 100%;
    .curriculum_image {
      width: 74rpx;
      height: 76rpx;
      display: block;
      margin: 16rpx auto;
    }
    .curriculum_title {
      text-align: left;
    }
  }
  .curriculum_css {
    background: url('https://document.dxznjy.com/course/457b5633f7dc44b89509f156fc9ea4d8.png') no-repeat;
    background-size: 100%;
    max-height: 475rpx;
    padding-bottom: 108rpx;
    position: relative;
    width: 710rpx;
    margin: auto;
    overflow: hidden;
    .curriculum_title {
      .title_right {
        wdith: 300upx;
      }
    }
    .curriculum_list {
      max-height: 388rpx;
      overflow: auto;
      .list_item {
        padding: 32rpx 0rpx 24rpx 0rpx;
        border-bottom: 1rpx solid #c8c8c8;
        .time-style {
          line-height: 30rpx;
        }
        .icon {
          display: inline-block;
          line-height: 34rpx;
          padding: 0 16rpx;
        }
        .icon_deliver {
          background-color: #fff2e3;
          color: #fd9b2a;
        }
        .icon_learning {
          background-color: #dfffe4;
          color: #006f57;
        }
        .button_learning {
          border: 2rpx solid #006f57;
          color: #006f57;
          line-height: 34rpx;
          padding: 3rpx 16rpx;
          border-radius: 24rpx;
        }
      }
      .curriculum_left_css {
        .curriculum_name {
          -webkit-line-clamp: 1;
          overflow: hidden;
          -webkit-box-orient: vertical;
          display: -webkit-box;
          word-break: break-all;
        }
        width: 480rpx;
      }
    }
    .arrow_bottom {
      position: absolute;
      background: url('https://document.dxznjy.com/course/f25ad6eafb9446629cc414ba592db677.png') no-repeat;
      background-size: 100%;
      width: 686rpx;
      height: 104rpx;
      left: 12rpx;
      bottom: 0rpx;
      margin: 0 auto;
    }
  }
</style>
