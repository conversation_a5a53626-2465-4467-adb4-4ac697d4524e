<template>
  <view>
    <view>
      <view class="plr-30">
        <!-- <view class="ptb-30">
          <block v-for="(item2, index2) in item.orderCourseList" :key="index2">
            <view class="flex-dir-row">
              <view class="box-180 radius-10">
                <image :src="item2.courseImage" class="wh100"></image>
              </view>
              <view class="flex-box ml-20">
                <view class="f-32 bold mb-30 spacing overstepSingle">{{ item2.courseName }}</view>
                <view class="f-28 flex">
                  <text class="f-32 bold color_tangerine">¥{{ item2.coursePrice }}</text>
                </view>
                <view class="mt-20 flex">
                  <text>x{{ item2.buyNumber }}</text>
                </view>
              </view>
            </view>
          </block>
        </view> -->
        <view class="list ptb-30" v-if="dataList.length > 0">
          <view class="list-item" v-for="item in dataList" :key="item.meetingQrId" @click.stop="handleClickGo(item)">
            <!-- 核销状态payStatus 1 待核销 2 已核销 3已退款 -->
            <view class="left">
              <view>{{ item.courseName || '' }}</view>
              <view class="line">
                <image src="https://document.dxznjy.com/course/3237e6ca40e145ecb7fea9f2eb13fbf6.png" mode="widthFix"></image>
                <view></view>
              </view>
              <view :class="item.payStatus == 2 || item.payStatus == 3 ? 'used' : ''">
                核销码：{{ item.meetingQrId || '' }}{{ item.payStatus == 2 ? '（已核销）' : item.payStatus == 3 ? '（已退款）' : '' }}
              </view>
            </view>
            <view class="right" v-if="item.payStatus != 3">
              <u-icon name="arrow-right" color="#555555" bold size="24"></u-icon>
            </view>
          </view>
        </view>

        <view v-if="dataList.length == 0" class="t-c flex-col" :style="{ height: '90vh' }">
          <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
          <view style="color: #bdbdbd">暂无数据</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  const { $navigationTo, $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        dataList: [],
        page: 1,
        no_more: false,
        imgHost: getApp().globalData.imgsomeHost,
        courseId: ''
      };
    },
    onLoad(e) {
      this.courseId = e.courseId;
    },
    onShow() {
      this.getCodeList();
    },

    methods: {
      async getCodeList() {
        uni.showLoading({
          title: '加载中...',
          mask: true
        });
        const res = await $http({
          url: 'zx/order/selMeetingQRList',
          data: {
            courseId: this.courseId
          }
        });
        if (res.data) {
          console.log('🚀 ~ getCodeList ~ res.data:', res.data);
          this.dataList = res.data;
        }
      },
      handleClickGo(data) {
        console.log('🚀 ~ gotoVerify ~ data:', data);
        if (data.payStatus == 3) {
          return;
        }
        // this.skintap(`meeting/meetVerifyInfo?isType=0&orderId=${item.orderId}`);
        this.skintap(`meeting/meetVerifyInfo?isType=0&meetingQrId=${data.meetingQrId}`);
      },
      skintap(url) {
        $navigationTo(url);
      }
    }
  };
</script>

<style lang="scss">
  .list-item {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 686rpx;
    height: 200rpx;
    padding: 0 8rpx 10rpx;
    background: #ffffff;
    border-radius: 24rpx;
    font-size: 28rpx;
    color: #555555;
    line-height: 40rpx;
    border-radius: 16rpx;
    box-sizing: border-box;
    margin-bottom: 24rpx;

    .left {
      width: 100%;

      > view {
        padding-left: 28rpx;
      }

      > view:first-child {
        display: flex;
        align-items: center;
        height: 94rpx;
        font-size: 28rpx;
        font-weight: bold;
        color: #555555;
      }

      > view:last-child {
        display: flex;
        align-items: center;
        width: 100%;
        height: 88rpx;
        font-size: 28rpx;
        font-weight: bold;
        box-sizing: border-box;
        color: #cfd0d2;
        background: #f8fff9;
        color: #339378;
        border-radius: 8rpx 8rpx 24rpx 24rpx;
      }

      .line {
        display: flex;
        align-items: center;
        width: 100%;
        margin-bottom: 6rpx;
        padding-left: 0;
        box-sizing: border-box;

        image {
          width: 14rpx;
          height: 12rpx;
        }
        view {
          width: calc(100% - 14rpx);
          border-bottom: 2rpx dashed #dadee9;
        }
      }

      .used {
        background: #fafbfe !important;
        color: #cfd0d2 !important;
        text-decoration: line-through;
      }
    }

    .right {
      position: absolute;
      top: 36rpx;
      right: 14rpx;
      width: 28rpx;
      height: 28rpx;
    }
    &:last-child {
      margin-bottom: 0;
    }

    &.active {
      color: #46d49e;
      background: rgba(49, 207, 147, 0.1);
      border: 2rpx solid #31cf93;
    }
  }

  .img_s {
    width: 160rpx;
    height: 160rpx;
  }
</style>
