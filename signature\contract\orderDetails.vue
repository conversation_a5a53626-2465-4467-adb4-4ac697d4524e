<template>
  <view class="ctxt plr-30 bg-ff">
    <view class="plr-15 bg-ff radius-20 ptb-30">
      <view class="binggo_c">
        <!-- <view class="flex-self-s">

					<view class="lc_yellow">
						未认证
					</view>
				</view> -->

        <view class="flex-self-s">应用订单号：{{ signatureOrder.id }}</view>
        <view class="flex-self-s">
          <view>支付状态：</view>
          <view>
            <view v-if="signatureOrder.status === 1">待支付</view>
            <view v-if="signatureOrder.status === 3">支付成功</view>
            <view v-if="signatureOrder.status === 4">支付失败</view>
            <view v-if="signatureOrder.status === 8">已取消</view>
            <view v-if="signatureOrder.status === 9">订单已过期</view>
          </view>
          <view v-if="signatureOrder.orderStatus" class="lc_yellow">已过期</view>
          <!-- 			<view class="lc_yellow lc_gray">
						已认证
					</view> -->
        </view>
        <!-- <view class="flex-self-s">套餐名称：{{ signatureOrder.packageName }}</view> -->
        <view class="flex-self-s">套餐名称：套餐一</view>
        <view class="flex-self-s">套餐内合同款（份）：{{ signatureOrder.num }}</view>
        <view class="flex-self-s">
          有效期：{{ signatureOrder.expireTime }}
          <text style="color: #fd9b2a">{{ signatureOrder.expirationDesc }}</text>
        </view>
        <view class="flex-self-s">套餐总金额（元）：{{ signatureOrder.originalPrice }}</view>
        <view class="flex-self-s">应付金额（元）：{{ signatureOrder.price }}</view>
        <view class="flex-self-s">实付金额（元）：{{ signatureOrder.price }}</view>
        <view class="flex-self-s">创建时间：{{ signatureOrder.createTime }}</view>
        <view class="flex-self-s">支付时间：{{ signatureOrder.price || '-' }}</view>
        <view class="flex-self-s">取消时间：{{ signatureOrder.cancelTime || '-' }}</view>
      </view>
      <!-- <view class="botBtn" v-if="signatureOrder.status === 1"> -->
      <!-- <button class="nextstep" form-type="submit" @click="submit" :disabled="disabled">前往认证</button>
				<button class="nextstep" form-type="submit" @click="submit" :disabled="disabled">前往认证</button> -->
      <!-- <view class="btn_b b_r" @click="cancelPay(signatureOrder)">取消订单</view>
        <view class="btn_b b_l" @click="continuePay(signatureOrder)">支付</view> -->
      <!-- </view> -->
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  export default {
    data() {
      return {
        signatureOrder: {},
        flag1: false,
        payInfo: {}
      };
    },
    onShow() {
      this.signatureOrder = uni.getStorageSync('signatureOrder');
      if (this.flag1) {
        uni.$tlpayResult(this.sucees, this.fail, this.payInfo.orderId);
      }
    },
    methods: {
      sucees() {
        this.flag1 = false;
        uni.redirectTo({
          url: '/signature/contract/orderRecord'
        });
      },
      fail() {
        this.flag1 = false;
      },
      fails() {
        uni.showToast({
          title: '支付失败',
          icon: 'none',
          duration: 2000
        });
        this.flag1 = false;
      },
      async continuePay(item) {
        let _this = this;
        if (_this.disabled) {
          return false;
        }
        uni.showLoading();
        _this.disabled = true;
        let resdata;
        resdata = await $http({
          url: 'zx/wap/pay/package/record/continue/pay',
          method: 'POST',
          data: {
            orderId: item.id
          }
        });
        _this.disabled = false;
        if (resdata.data.needPay == 1) {
          _this.payBtn(resdata.data.applyPayDto);
        }
      },
      async payBtn(data) {
        let _this = this;
        let resdata = await httpUser.post('mps/line/collect/order/unified/collect', data);
        let res = resdata.data.data;
        _this.disabled = false;
        // uni.hideLoading();
        if (res) {
          if (res.openAllinPayMini) {
            this.flag1 = true;
            this.payInfo = res;
            uni.$payTlian(res);
          } else {
            uni.requestPayment({
              provider: 'wxpay',
              timeStamp: res.payInfo.timeStamp,
              nonceStr: res.payInfo.nonceStr,
              package: res.payInfo.packageX,
              signType: res.payInfo.signType,
              paySign: res.payInfo.paySign,
              success: function (ress) {
                uni.redirectTo({
                  url: '/signature/contract/orderRecord'
                });
              },
              fail: function (err) {
                uni.showToast({
                  title: '支付失败'
                });
              }
            });
          }
        }
      },
      async cancelPay(item) {
        let _this = this;
        uni.showModal({
          title: '提示',
          content: '确认取消该订单吗？',
          success: async function (res) {
            if (res.confirm) {
              const resdata = await $http({
                url: 'zx/wap/pay/package/record/cancel',
                // method: 'POST',
                data: {
                  orderId: item.id
                }
              });
              if (resdata) {
                uni.redirectTo({
                  url: '/signature/contract/orderRecord'
                });
              }
            } else if (res.cancel) {
              console.log('用户点击取消');
            }
          }
        });
      },
      submit() {
        uni.navigateTo({
          url: '/signature/contract/cManagement'
        });
      }
    }
  };
</script>

<style scoped>
  .ctxt {
    height: 100vh;
  }

  .binggo_c {
    font-size: 30rpx;
    color: #555555;
    margin-top: 10rpx;
  }

  .lc_yellow {
    color: #fd9b2a;
    text-align: center;
    width: 116rpx;
    border: 1px solid #ffe1be;
    background-color: #fdf6ed;
    border-radius: 8rpx;
    margin-left: 32rpx;
  }

  .flex-self-s {
    margin-bottom: 40rpx;
  }

  .lc_gray {
    color: #cccccc;
    border: 1px solid #cccccc;
    background-color: #f7f7f7;
  }
  .botBtn {
    display: flex;
    flex-direction: row-reverse;
    position: absolute;
    bottom: 35rpx;
    right: 32rpx;
  }
  .btn_b {
    width: 328rpx;
    height: 74rpx;
    border-radius: 60rpx;
    line-height: 74rpx;
    text-align: center;
  }
  .b_l {
    background-color: #fff;
    color: #4e9f87;
    border: 1px solid #7baea0;
  }
  .b_r {
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    color: #ffffff;
    margin-left: 32rpx;
  }
</style>
