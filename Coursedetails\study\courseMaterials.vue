<template>
  <view class="study_materials_conent">
    <view class="study_materials_tabs" v-if="isShowTab">
      <view class="tab_box">
        <view :class="currentComponent === 'LearningMaterials' ? 'tabs_check' : 'tabs_item'" @click="currentComponent = 'LearningMaterials'">学习文件</view>
        <view v-if="currentComponent === 'LearningMaterials'" class="tab_line"></view>
      </view>
      <view class="tab_box">
        <view :class="currentComponent === 'VideoMaterials' ? 'tabs_check' : 'tabs_item'" @click="currentComponent = 'VideoMaterials'">学习视频</view>
        <view v-if="currentComponent === 'VideoMaterials'" class="tab_line"></view>
      </view>
    </view>

    <DownloadProfile v-if="currentComponent === 'LearningMaterials'" :ref="'downloadProfileRef'" :onloadInfo="onloadInfo" :fileType="'file'" />
    <DownloadProfile v-if="currentComponent === 'VideoMaterials'" :ref="'downloadProfileRefVideo'" :onloadInfo="onloadInfo" :fileType="'video'" />

    <view v-if="currentComponent === ''" class="no_data">
      <view style="position: relative; width: 534rpx; height: 306rpx">
        <image style="width: 100%; height: 100%" src="https://document.dxznjy.com/dxSelect/6343e7f0-aa5d-4fa2-a8f6-f1580144d126.png" />
        <view style="position: absolute; bottom: 26rpx; width: 534rpx; font-size: 26rpx; color: #a6a6a6; text-align: center">暂无数据</view>
      </view>
    </view>
  </view>
</template>

<script>
  // 先引入你的组件
  import DownloadProfile from './downloadProfile.vue';
  const { $http } = require('@/util/methods.js');

  export default {
    data() {
      return {
        onloadInfo: {},
        currentComponent: '', // 默认显示学习资料
        isShowTab: false, // 是否显示切换tab
        videoList: [],
        fileList: [] // 学习文件列表
      };
    },
    components: {
      DownloadProfile
    },
    onLoad(e) {
      this.onloadInfo = e;
      console.log('🚀 ~ onLoad ~ onloadInfo:', this.onloadInfo);
      this.getProfileList();
    },
    onShow() {
      console.log('onShow', this.$refs.downloadProfileRef);
      this.$refs.downloadProfileRef && this.$refs.downloadProfileRef.refreshData();
      this.$refs.downloadProfileRefVideo && this.$refs.downloadProfileRefVideo.refreshData();
    },
    methods: {
      // 获取资料数据
      async getProfileList() {
        const res = await $http({
          url: 'zx/wap/goods/learn/materials',
          data: {
            goodsId: this.onloadInfo.id
          }
        });

        if (res) {
          res.data.forEach((element) => {
            if (element.fileType == '1') {
              this.videoList = element.fileDtos;
            } else {
              this.fileList = element.fileDtos;
            }
          });

          if (this.videoList.length > 0 && this.fileList.length > 0) {
            this.isShowTab = true;
          } else {
            this.isShowTab = false;
          }
          if (this.fileList.length > 0) {
            this.currentComponent = 'LearningMaterials';
          } else if (this.videoList.length > 0) {
            this.currentComponent = 'VideoMaterials';
          } else {
            this.currentComponent = '';
          }
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .study_materials_conent {
    box-sizing: border-box;
    // padding: 0 30rpx;
    .study_materials_tabs {
      position: relative;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      height: 106rpx;
      font-size: 26rpx;
      margin: 0 30rpx 0rpx 30rpx;
      // padding: 28rpx 163rpx 10rpx 163rpx;
      padding: 28rpx 202rpx 10rpx 163rpx;
      background-color: #fff;
    }
    .no_data {
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 690rpx;
      height: 100vh;
      margin: 0 30rpx;
      padding: 321rpx 73rpx 0 74rpx;
      background: #ffffff;
      border-radius: 0rpx 0rpx 14rpx 14rpx;
    }
    .tab_box {
      position: relative;
    }
    .tabs_item {
      box-sizing: border-box;
      height: 42rpx;
      margin-top: 6rpx;
      line-height: 37rpx;
      vertical-align: top;
    }
    .tabs_check {
      height: 42rpx;
      font-weight: 600;
      font-size: 30rpx;
      color: #333333;
      line-height: 42rpx;
    }
    .tab_line {
      position: absolute;
      bottom: 0rpx;
      left: 35rpx;
      width: 50rpx;
      height: 10rpx;
      background: #3eaa8c;
      border-radius: 5rpx;
      text-align: center;
    }
  }
</style>
