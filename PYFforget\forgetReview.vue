<template>
  <view class="ctxt bg-ff">
    <view class="bg-h">
      <!-- <view class="positioning" @click="goback" v-if='!isShare'> -->
      <view class="positioning" @click="goback">
        <uni-icons type="left" size="24" color="#000"></uni-icons>
      </view>
      <view class="word-position t-c col-12" style="">
        <view class="f-34">拼音法抗遗忘</view>
      </view>
    </view>
    <view class="plr-15 bg-ff radius-20 ptb-30">
      <view class="midTxt">
        <image src="https://document.dxznjy.com/course/ab25daa95bff4cecbc6452fdfb5ac814.png" mode="scaleToFill" style="width: 750rpx; height: 634rpx"></image>
      </view>
      <view class="botBtn">
        <view class="btn_b b_r" @click="goToday">今日复习</view>
        <view class="btn_b b_l" @click="goPast">往期复习</view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    onLoad(option) {
      this.studentCode = option.studentCode || uni.getStorageSync('pyfStudentCode');
      // uni.setStorageSync('pyfStudentCode',this.studentCode)
      uni.setStorageSync('pyfStudentCode', this.studentCode);
      console.log('this.studentCode', this.studentCode);
    },
    data() {
      return {
        studentCode: '',
        wordList: {} // 单词
      };
    },
    methods: {
      async goToday() {
        let res = await this.$httpUser.post('znyy/pd/planReview/queryReviewedCount', {
          studentCode: this.studentCode
        });
        console.log('res------', res);
        if (res.data.success) {
          this.wordList = res.data.data;
          if (this.wordList.sumReviewCount == 0) {
            uni.showToast({
              icon: 'none',
              title: '今日无可复习课程',
              duration: 2000
            });
          } else {
            uni.redirectTo({
              url: '/PYFforget/todayReview?studentCode=' + this.studentCode
            });
          }
        }
      },
      goPast() {
        var token = uni.getStorageSync('token');
        if (token != '') {
          uni.redirectTo({
            url: '/PYFforget/pastReview?studentCode=' + this.studentCode
          });
        } else {
          uni.navigateTo({
            url: '/pages/login/login'
          });
        }
      },
      goback() {
        // uni.navigateBack()
        uni.switchTab({
          url: '/pages/home/<USER>/index'
        });
      }
    }
  };
</script>

<style scoped>
  .ctxt {
    height: 100vh;
  }

  .bg-h {
    position: fixed; /* 确保背景颜色显示 */
    top: 0; /* 固定在页面顶部 */
    left: 0;
    width: 100%; /* 使背景颜色覆盖整个宽度 */
    z-index: 8; /* 高于其他内容 */
  }

  .positioning {
    position: fixed;
    top: 110rpx;
    left: 30rpx;
    z-index: 9;
  }

  .word-position {
    position: fixed;
    top: 0;
    left: 0;
    background-color: #f3f8fc;
    height: 190rpx;
    padding-top: 110rpx;
    box-sizing: border-box;
  }

  .midTxt {
    margin-top: 50%;
  }

  .midTit {
    font-size: 40rpx;
    line-height: 56rpx;
    color: #428a6f;
    font-weight: 900;
  }

  .lc_yellow {
    color: #fd9b2a;
    text-align: center;
    width: 116rpx;
    border: 1px solid #ffe1be;
    background-color: #fdf6ed;
    border-radius: 8rpx;
    margin-left: 32rpx;
  }

  .flex-self-s {
    margin-bottom: 40rpx;
  }

  .lc_gray {
    color: #cccccc;
    border: 1px solid #cccccc;
    background-color: #f7f7f7;
  }
  .botBtn {
    display: flex;
    flex-direction: row-reverse;
    position: absolute;
    bottom: 60rpx;
    right: 32rpx;
  }
  .btn_b {
    width: 328rpx;
    height: 92rpx;
    border-radius: 60rpx;
    line-height: 92rpx;
    text-align: center;
  }
  .b_l {
    background-color: #fff;
    color: #4e9f87;
    border: 1px solid #7baea0;
  }
  .b_r {
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    color: #ffffff;
    margin-left: 32rpx;
  }
</style>
