<template>
  <view class="contain" :style="{ height: useHeight + 'rpx' }">
    <view class="search">
      <view>学员：</view>
      <view class="searchInput">
        <view>
          {{ studentNameStudy }}
        </view>
      </view>
    </view>
    <view class="search">
      <view>阶段：</view>
      <view class="searchInput" @click="open(2)">
        <view>
          {{ query.courseStage ? stageList.find((e) => e.value == query.courseStage).label : '请选择阶段' }}
        </view>
        <uni-icons type="down" size="14"></uni-icons>
      </view>
    </view>
    <view class="search">
      <view>听力模式：</view>
      <view class="searchInput" @click="open(3)">
        <view>
          {{ query.listeningCategory ? modeList.find((e) => e.value == query.listeningCategory).label : '请选择模式' }}
        </view>
        <uni-icons type="down" size="14"></uni-icons>
      </view>
    </view>
    <view class="search" v-if="query.listeningCategory && query.listeningCategory == 1">
      <view>听力分类：</view>
      <view class="searchInput" @click="open(4)">
        <view>
          {{ query.listeningType ? listeningTypeList.find((e) => e.value == query.listeningType).label : '请选择分类' }}
        </view>
        <uni-icons type="down" size="14"></uni-icons>
      </view>
    </view>

    <view class="search" v-if="query.listeningCategory && query.listeningCategory == 2">
      <view>听力年份：</view>
      <view class="searchInput" @click="open(5)">
        <view>
          {{ query.listeningYear ? listeningYearList.find((e) => e.value == query.listeningYear).label : '请选择年份' }}
        </view>
        <uni-icons type="down" size="14"></uni-icons>
      </view>
    </view>
    <view class="search">
      <view>试题类型：</view>
      <view class="searchInput" @click="open(6)">
        <view>
          {{ query.status !== '' ? statusList.find((e) => e.value == query.status).label : '请选择类型' }}
        </view>
        <uni-icons type="down" size="14"></uni-icons>
      </view>
    </view>
    <view class="btnsList">
      <view class="btn reset" @click="reset">重置</view>
      <view class="btn confirm" @click="btnOk">确定</view>
    </view>

    <u-picker
      v-if="selectorShow"
      :show="selectorShow"
      :immediateChange="true"
      mode="selector"
      :default-selector="[0]"
      :columns="selectorArray"
      keyName="label"
      confirmColor="#357B71"
      @confirm="confirm"
      @cancel="cancel"
    ></u-picker>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        query: {
          studentCode: '',
          courseStage: '',
          listeningCategory: '',
          listeningType: '',
          status: '',
          listeningYear: ''
        },
        studentNameStudy: '',
        useHeight: 0,
        students: [],
        selectorShow: false,
        statusList: [
          { label: '未复习', value: 0 },
          { label: '已复习', value: 1 }
        ],
        selectorArray: [],
        modeList: [
          { label: '练习模式', value: 1 },
          { label: '真题模式', value: 2 }
        ],
        stageList: [],
        listeningYearList: [],
        listeningTypeList: [
          { label: '短对话', value: 1 },
          { label: '长对话', value: 2 },
          { label: '独白', value: 3 }
        ]
      };
    },
    onLoad(e) {
      this.studentNameStudy = e.studentName;
	  this.query.studentCode = e.studentCode;
    },
    onShow() {
      this.init();
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          this.useHeight = res.windowHeight * (750 / res.windowWidth);
        }
      });
    },
    methods: {
      async btnOk() {
        if (!this.query.studentCode)
          return uni.showToast({
            icon: 'none',
            title: '请选择学员',
            duration: 2000
          });
        if (!this.query.courseStage)
          return uni.showToast({
            icon: 'none',
            title: '请选择阶段',
            duration: 2000
          });
        if (!this.query.listeningCategory)
          return uni.showToast({
            icon: 'none',
            title: '请选择模式',
            duration: 2000
          });
        if (this.query.listeningCategory == 1) {
          if (!this.query.listeningType)
            return uni.showToast({
              icon: 'none',
              title: '请选择分类',
              duration: 2000
            });
        } else {
          if (!this.query.listeningYear)
            return uni.showToast({
              icon: 'none',
              title: '请选择年份',
              duration: 2000
            });
        }

        if (this.query.status === '')
          return uni.showToast({
            icon: 'none',
            title: '请选择类型',
            duration: 2000
          });
        let { data } = await this.$httpUser.get('dyf/zxListening/wap/getZxListeningReviewList', this.query);
        if (data.success) {
          if (data.data == 0) {
            if (this.query.status == 1) {
              uni.showToast({
                icon: 'none',
                title: '请在未复习答题后再来查看！',
                duration: 2000
              });
            } else {
              uni.showToast({
                icon: 'none',
                title: '还没有试题哦！',
                duration: 2000
              });
            }
          } else {
            uni.navigateTo({
              url: '/Listen/previewListen?query=' + JSON.stringify(this.query)
            });
          }
        }
      },
      async init() {
        let { data } = await this.$httpUser.get('znyy/review/query/my/student');
        if (data.success) {
          this.students = data.data.map((e) => {
            return { label: e.realName, value: e.studentCode };
          });
        }
        // this.reset();
      },
      reset() {
        this.query = {
          studentCode: '',
          courseStage: '',
          listeningCategory: '',
          listeningType: '',
          status: '',
          listeningYear: ''
        };
        this.stageList = [];
        this.listeningYearList = [];
      },
      confirm(e) {
        if (this.searchType == 1) {
          if (this.query.studentCode == e.value[0].value) {
          } else {
            this.query = {
              studentCode: e.value[0].value,
              courseStage: '',
              listeningCategory: '',
              listeningType: '',
              status: '',
              listeningYear: ''
            };
          }
        } else if (this.searchType == 2) {
          this.query.courseStage = e.value[0].value;
        } else if (this.searchType == 3) {
          this.query.listeningCategory = e.value[0].value;
          if (this.query.listeningCategory == 1) {
            this.query.listeningYear = '';
          } else {
            this.query.listeningType = '';
          }
        } else if (this.searchType == 4) {
          this.query.listeningType = e.value[0].value;
        } else if (this.searchType == 5) {
          this.query.listeningYear = e.value[0].value;
        } else {
          this.query.status = e.value[0].value;
        }
        this.selectorShow = false;
      },
      cancel() {
        this.selectorShow = false;
        this.selectorArray = [];
      },
      async open(e) {
        this.searchType = e;
        this.selectorArray = [];
        if (e == 1) {
          this.selectorArray = [this.students];
        } else if (e == 2) {
          if (!this.query.studentCode)
            return uni.showToast({
              icon: 'none',
              title: '请先选择学员',
              duration: 2000
            });
          let { data } = await this.$httpUser.get('dyf/zxListening/wap/getFindConditions?studentCode=' + this.query.studentCode);
          if (data.success) {
            this.stageList = data.data.courseStage;
            this.listeningYearList = data.data.listeningYear;
          }
          this.selectorArray = [this.stageList];
        } else if (e == 3) {
          this.selectorArray = [this.modeList];
        } else if (e == 4) {
          this.selectorArray = [this.listeningTypeList];
        } else if (e == 5) {
          this.selectorArray = [this.listeningYearList];
        } else {
          this.selectorArray = [this.statusList];
        }
        this.selectorShow = true;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .contain {
    padding: 46rpx 32rpx;
    box-sizing: border-box;
    background-color: #fff;
    ::v-deep .u-picker__view__column__item {
      line-height: 34px !important;
    }
    .btnsList {
      position: fixed;
      bottom: 100rpx;
      width: 686rpx;
      height: 92rpx;
      display: flex;
      justify-content: space-between;
      .btn {
        box-sizing: border-box;
        text-align: center;
        line-height: 92rpx;
        width: 324rpx;
        height: 92rpx;
        font-size: 32rpx;
        border-radius: 46rpx;
      }
      .confirm {
        background: #428a6f;
        color: #fff;
      }
      .reset {
        background: #fff;
        border: 2rpx solid #428a6f;
        color: #428a6f;
      }
    }
    .search {
      height: 92rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #555555;
      font-size: 28rpx;
      margin-bottom: 32rpx;
      .searchInput {
        width: 500rpx;
        height: 92rpx;
        border: 2rpx solid #dfdfdf;
        border-radius: 56rpx;
        box-sizing: border-box;
        margin-right: 30rpx;
        display: flex;
        justify-content: space-between;
        padding: 0 32rpx;
        align-items: center;
        color: #555555;
      }
    }
  }
</style>
