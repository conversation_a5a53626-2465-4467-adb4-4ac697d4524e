<template>
  <page-meta :page-style="'overflow:' + (show ? 'hidden' : 'visible')"></page-meta>
  <view style="padding-bottom: 84rpx; background-color: #f3faf9">
    <!-- :class="scrollTop?'':'tifry'" -->
    <scroll-view class="scroll-view_H" scroll-x="true">
      <view class="item" v-for="(item, index) in list" :key="item" @click="goScroll(item, index)">
        <view class="icon" v-if="scrollTop">
          <image :src="item.icon" style="width: 100%; height: 100%"></image>
        </view>
        <view class="text" :class="active == index ? 'active' : ''">{{ item.name }}</view>
      </view>
    </scroll-view>
    <!--   		<view class="banans">
			<u-swiper height="280" circular :list="banansList" @click="banansclick"></u-swiper>
		</view> -->
    <view class="return common" id="return">
      <view class="title">
        <view class="titlef">
          <span class="linew"></span>
          会员礼包
        </view>
        <view class="mores" v-if="nowList[0].coinNumber" style="width: 158rpx" @click="gomoreMoney">领取记录></view>
      </view>
      <view class="escription">价值500元鼎币分12月返还，可兑换商城好礼。价值500元精选实物礼包，3款任选1款</view>
      <view style="display: flex; justify-content: space-between">
        <view class="draw">
          <view class="dingMonry" v-if="nowList[0].coinNumber">
            {{ nowList[0].coinNumber }}
            <span style="font-size: 28rpx; margin-left: 8rpx">鼎币</span>
          </view>
          <view class="goMoney" v-if="nowList[0].status == 2" @click="goMoney(nowList[0])">立即领取</view>
          <view class="noMoney" v-if="nowList[0].status == 1">已领取</view>
        </view>
        <view class="draw nodarw">
          <view class="dingMonry" v-if="nowList[0].coinNumber">
            {{ nowList[1].coinNumber }}
            <span style="font-size: 28rpx; margin-left: 8rpx">鼎币</span>
          </view>
          <view class="goMoney" v-if="nowList[0].coinNumber">{{ nowList[1].year }}年{{ nowList[1].month }}月可领取</view>
        </view>
      </view>
      <view class="giftPacks" @click="greatGiftPack">
        <image src="https://document.dxznjy.com/course/1d3f7dfa162b4fb4a30cff7298f09d06.png" style="width: 100%; height: 100%"></image>
      </view>
      <view class="coupon_box" @click="couponCollection">
        <image src="https://document.dxznjy.com/course/d05e20f7d7fa4021b8b9c33998fcf3ba.png" style="width: 100%; height: 100%"></image>
      </view>
    </view>
    <view class="discount" id="discount">
      <view class="title">
        <view>会员专享价</view>
        <view class="mores" @click="skintap('pages/selectCourse/selectCourse')">更多></view>
      </view>
      <view class="escription" style="color: #059880">全平台95折，畅享全学龄段优质教育产品</view>
      <view class="discountItems">
        <helang-waterfall
          v-for="item in discountList"
          width="300rpx"
          :item="item"
          :share="false"
          @click="skintap('Coursedetails/productDetils?id=' + item.goodsId)"
          tag="right"
          :index="index"
        ></helang-waterfall>
      </view>
    </view>
    <view class="talent common" id="talent">
      <view class="title">
        <view class="titlef">
          <span class="linew"></span>
          天赋测评 发掘孩子超能力
        </view>
      </view>
      <view class="escription">八大智能测评 | 发现孩子未来天赋 | 因材施教科学养育</view>
      <view style="display: flex; justify-content: space-between">
        <view class="talentBtn" @tap="startTest"></view>
        <view class="talentBtn1" @tap="testReport"></view>
      </view>
    </view>
    <view class="career common" id="career">
      <view class="title">
        <view class="titlef">
          <span class="linew"></span>
          学业生涯规划 探索孩子未来
        </view>
      </view>
      <view class="escription">成长、学科、升学规划，绘就孩子璀璨未来蓝图</view>
      <swiper class="swiper" v-if="careerList.length" previous-margin="30rpx" next-margin="30rpx" :autoplay="false">
        <swiper-item v-for="item in careerList" style="border-radius: 24rpx">
          <view class="card">
            <view style="display: flex; height: 92rpx">
              <image :src="item.avatar" mode="" class="avater"></image>
              <view>
                <view style="display: flex; height: 44rpx; align-items: center">
                  <span class="name">{{ item.nick }}</span>
                  <span class="glard">{{ array.find((e) => e.value == item.grade).label }}</span>
                </view>
                <view style="display: flex; height: 44rpx; align-items: center">
                  <view class="old">{{ item.gender == 0 ? '男孩' : '女孩' }}·{{ item.age }}岁</view>
                  <view class="type">
                    {{ item.type[0] }}
                  </view>
                </view>
              </view>
            </view>
            <view class="cardBtn" @click="goPlan(item.reportId)">查看报告</view>
          </view>
        </swiper-item>
      </swiper>
      <view class="careerbtn" v-if="careerList.length != 3" @click="career"></view>
    </view>
    <view class="earnings common" id="earnings">
      <view class="title">
        <view class="titlef">
          <span class="linew"></span>
          副业高收入 既省钱又赚钱
        </view>
      </view>
      <view class="escription">分享课程及会员，推广共赢，教育投资收益并行</view>
      <view class="earning" @click="earnings">
        <image src="https://document.dxznjy.com/course/ea33eacf926f47cebb6d238527396460.jpg" style="width: 100%; height: 100%"></image>
      </view>
    </view>
    <view class="discount" style="background: url('https://document.dxznjy.com/course/52908b1f61204f3aa9dfc3895d3a94b3.png') no-repeat" id="shop">
      <view class="title">
        <view>鼎币商城 好礼带回家</view>
        <view class="mores" @tap="skintap('shoppingMall/index')">更多></view>
      </view>
      <view class="escription" style="color: #059880">鼎币为平台虚拟货币，价值等同人民币，可自由兑换心仪的各类商品</view>
      <view class="discountItems">
        <block v-for="(item, index) in shopList" :key="index">
          <view class="courseItem radius-20 pb-10 positionRelative" @tap="goDetails(item)" style="width: 316rpx; background-color: #fff">
            <view class="courseimg relative">
              <image :src="item.goodsPicUrl" style="width: 316rpx; height: 406rpx"></image>
            </view>
            <view class="plr-15 mt-12">
              <view class="bold f-28 c-55 lh-40 course_name_css">{{ item.goodsName }}</view>
              <view class="font12 mt-24 mtb-16 displayflex displayflexbetween">
                <view>
                  <span class="bold f-32 color_red">{{ item.goodsShowPrice ? item.goodsShowPrice : item.goodsOriginalPrice }}</span>
                  <span class="f-24 cB4B1B1 price_text_css">鼎币</span>
                </view>
                <view class="f-24 cB4B1B1">已兑换{{ item.goodsSales }}件</view>
              </view>
            </view>
          </view>
        </block>
      </view>
    </view>

    <view class="society common" id="society">
      <view class="title">
        <view class="titlef">
          <span class="linew"></span>
          文化社区 习惯养成
        </view>
      </view>
      <view class="escription">涵盖家庭、读书、劳动、运动，多维文化交流中心，成就卓越人生，积蓄无限可能</view>
      <view class="societys" @click="skintap('memberCenter/index')">
        <image src="https://document.dxznjy.com/course/21e0323273994e4e9d6b66452ee245b3.jpg" style="width: 100%; height: 100%"></image>
      </view>
      <view class="societyTitle">
        <span class="rhomboid"></span>
        家庭教育
      </view>
      <view class="">
        <view style="margin-bottom: 24rpx" :key="index">
          <!-- 	<view class="f-28 c-55 lh-40 mt-35">
						{{item.goodsName}}
					</view> -->
          <view v-for="(info, i) in familyList.goodsCatalogueList" :key="i" class="flex-x-s flex-a-c mt-24" style="margin-bottom: 26rpx" @click="goVideo(info, 1)">
            <view class="image_left_content positionRelative">
              <image class="imagele radius-8" :src="familyList.goodsPicUrl"></image>
              <image class="video_play positionAbsolute" src="https://document.dxznjy.com/course/25d0f57150cf4aeaac8cd9a7489b7a9d.png"></image>
            </view>
            <view class="ml-15 right_record_css">
              <view class="f-28 c-55 lh-40" style="font-weight: bold">{{ familyList.goodsName }}</view>
              <view class="f-24 c-55 lh-36">{{ info.catalogueName }}</view>
              <view class="mt-15">
                <u-line-progress activeColor="#339378" :showText="false" :percentage="Math.round(info.learningProgress)" height="16"></u-line-progress>
              </view>
            </view>
          </view>
        </view>
        <view class="moress" @click="skintap('Coursedetails/culturalType/familyCulture')">
          查看更多课程
          <span class="right"><u-icon name="arrow-right" color="#339378" size="18"></u-icon></span>
        </view>
      </view>
      <view class="societyTitle">
        <span class="rhomboid"></span>
        快速阅读
      </view>
      <view class="">
        <view style="margin-bottom: 24rpx" :key="index">
          <!-- 	<view class="f-28 c-55 lh-40 mt-35">
						{{item.goodsName}}
					</view> -->
          <view v-for="(info, i) in ReadList.goodsCatalogueList" :key="i" class="flex-x-s flex-a-c mt-24" style="margin-bottom: 26rpx" @click="goVideo(info, 2)">
            <view class="image_left_content positionRelative">
              <image class="imagele radius-8" :src="ReadList.goodsPicUrl"></image>
              <image class="video_play positionAbsolute" src="https://document.dxznjy.com/course/25d0f57150cf4aeaac8cd9a7489b7a9d.png"></image>
            </view>
            <view class="ml-15 right_record_css">
              <view class="f-28 c-55 lh-40" style="font-weight: bold">{{ ReadList.goodsName }}</view>
              <view class="f-24 c-55 lh-36">{{ info.catalogueName }}</view>
              <view class="mt-15">
                <u-line-progress activeColor="#339378" :showText="false" :percentage="Math.round(info.learningProgress)" height="16"></u-line-progress>
              </view>
            </view>
          </view>
        </view>
        <view class="moress" @click="skintap('Coursedetails/culturalType/readCultural')">
          查看更多课程
          <span class="right"><u-icon name="arrow-right" color="#339378" size="18"></u-icon></span>
        </view>
      </view>
      <view class="societyBan" @click="skintap('Coursedetails/culturalType/familyCulture?familyCurrent=1')">
        <image src="https://document.dxznjy.com/course/01023d382a60420da30a040bc39386f4.jpg" style="width: 100%; height: 100%"></image>
      </view>
    </view>
    <view class="fullImg" v-if="fullIshow" @click="fullIshow = !fullIshow">
      <image @click.stop="" style="width: 100%" mode="widthFix" :src="fullImages"></image>
    </view>
    <uni-popup ref="video_popup" type="center" style="padding: 0" @change="change">
      <view class="bg-00 video_content_css">
        <view class="tips_content_close" @click="closeVideo()">
          <u-icon name="close-circle-fill" color="#B1B1B1" size="38"></u-icon>
        </view>
        <polyv-player
          id="polyv_player"
          @pause="bindpause"
          @loadedmetadata="bindloadedmetadata"
          :autoplay="true"
          :playerId="playerIdcont"
          :vid="videoInfo.videoUrl"
          width="100%"
          height="100%"
          :ts="ts"
          :sign="sign"
        ></polyv-player>
      </view>
    </uni-popup>
    <uni-popup ref="addressInfo" type="bottom" style="padding: 0" @change="change">
      <view class="addressInfo">
        <view class="review_close" @click="closeAddress">
          <uni-icons type="clear" size="26" color="#B1B1B1"></uni-icons>
        </view>
        <view class="addressTitle">领取礼包</view>
        <view class="addressTitle2">
          <sapn class="addressLine"></sapn>
          选择礼包
        </view>
        <view class="picImageItems">
          <view class="picImageItem" :class="addressActive == item.id ? 'picActive' : ''" v-for="item in bigBaglist" :key="item.id" @click="checkBigBag(item)">
            <view class="full" @click.stop="lookFull(item.giftPackPhoto)"></view>
            <image :src="item.giftPackPhoto" style="width: 100%; height: 100%"></image>
            <view class="noHave" v-if="item.number == 0">已领完了</view>
            <image src="https://document.dxznjy.com/course/2b6447d68f124999bedb25a1457a1a5e.png" class="checked" v-if="addressActive == item.id"></image>
          </view>
        </view>
        <view class="flex bg-ff radius-15 mt-30" @tap="selectadd">
          <view class="flex-box mlr-20">
            <view class="f-30 mb-20" v-if="addressInfo.buyerName">
              <text class="mr-30">{{ addressInfo.buyerName }}</text>
              <text>{{ addressInfo.buyerPhone }}</text>
            </view>
            <view class="f-30 mr-30" v-else>请选择收货地址</view>
            <view class="f-30 c-88">{{ addressInfo.address }}</view>
          </view>
          <image :src="imgHost + 'dxSelect/image/right.png'" class="arrow" mode="widthFix"></image>
        </view>
        <view style="height: 2rpx; background: #f6f7f9; margin: 22rpx 0 32rpx"></view>
        <view class="addressTitle2">
          <sapn class="addressLine"></sapn>
          备注
        </view>
        <u--textarea v-model="value2" height="200" placeholder="请输入内容" count></u--textarea>
        <view class="paybtn f-32 c-ff" @click="getBigBag">立即领取</view>
      </view>
    </uni-popup>

    <uni-popup ref="money" type="center" style="padding: 0" @change="change">
      <view class="shadowMoney">
        <view class="review_close" @click="closeMonry">
          <uni-icons type="clear" size="26" color="#B1B1B1"></uni-icons>
        </view>
        <view class="moneyTitle">鼎币领取记录</view>
        <view class="moneylist">
          <view class="item" :class="item.status != 0 ? 'moneyActive' : 'moneyNo'" v-for="(item, index) in allMoneyList" :key="item.id">
            <view v-if="item.status != 0">
              <view class="dingMonry">
                {{ item.coinNumber }}
                <span style="font-size: 28rpx; margin-left: 8rpx">鼎币</span>
              </view>
              <view class="goMoney" v-if="item.status == 2" @click="goMoney(item, true)">立即领取</view>
              <view class="noMoney" v-if="item.status == 1">已领取</view>
            </view>
            <view v-else>
              <view class="dingMonry">
                {{ item.coinNumber }}
                <span style="font-size: 28rpx; margin-left: 8rpx">鼎币</span>
              </view>
              <view class="goMoney">{{ item.year }}年{{ item.month }}月可领取</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
    <uni-popup ref="testing" type="center" @change="change">
      <view class="shareCard">
        <view class="activityRule" style="font-size: 32rpx">提醒</view>
        <view class="review_close" @click="closeTesting">
          <uni-icons type="clear" size="26" color="#B1B1B1"></uni-icons>
        </view>
        <view class="rule">
          <view style="color: #428a6f; font-size: 40rpx; display: flex; align-items: center; justify-content: center">
            {{ testCode }}
            <view
              style="
                background: url('https://document.dxznjy.com/course/c76e5247efa54ea79a52db0fba0dd104.png');
                background-size: 100%;
                width: 32rpx;
                height: 32rpx;
                margin-left: 10rpx;
              "
              @click="copyCode"
            ></view>
          </view>
          <view class="" style="width: 70%; margin: 20rpx auto; text-align: center; margin-bottom: 40rpx">
            恭喜您获得会员专属的激活码,在测评时请输入激活码，评测完成后约1-2个工作日生成报告
          </view>
          <view
            class=""
            style="color: #fff; background-color: #428a6f; width: 342rpx; line-height: 92rpx; margin: auto; font-size: 32rpx; text-align: center; border-radius: 46rpx"
            @tap="goTest"
          >
            前往测评
          </view>
        </view>
      </view>
    </uni-popup>
    <uni-popup ref="coupon" type="center" @change="change">
      <view class="coupon_popup">
        <view style="width: 48rpx; height: 48rpx; position: absolute; right: 0rpx; top: -48rpx" @click="closeCouponPopun">
          <image style="width: 100%; height: 100%" src="https://document.dxznjy.com/course/9fe1ddf92d4441ff84f063772b3122d3.png"></image>
        </view>
        <view style="height: 526rpx; overflow-y: auto">
          <view v-for="(item, index) in couponsList" :key="index" class="item-coupons">
            <view style="display: flex; align-items: center">
              <view class="activity-type">
                <!-- 优惠券类型 1:满减，2:折扣 -->
                <view class="activity-name" :class="{ '.used-color': active === 2 || active === 3 }">
                  <text>减</text>
                  <text style="font-size: 30rpx">{{ item.couponDiscount }}</text>
                  <text>元</text>
                  <!-- {{ item.couponType === 1 ? `减${item.couponDiscount}元` : `减${item.couponDiscount}元`}} -->
                </view>
                <!-- 是否有门槛 1:有，0:无 -->
                <view class="activity-limit" :class="{ 'used-color': active === 2 || active === 3 }">
                  {{ item.couponHasCondition === 1 ? `满${item.couponCondition}元` : '无门槛' }}
                </view>
              </view>
              <view class="activity-content">
                <view class="title">{{ item.couponName }}</view>
                <view class="time">{{ item.couponStartTime.split(' ')[0] }} - {{ item.couponEndTime.split(' ')[0] }}</view>
              </view>
            </view>
          </view>
        </view>
        <view style="width: 262rpx; height: 70rpx; position: absolute; left: 140rpx" @click="closeCouponPopun">
          <image style="width: 100%; height: 100%" src="https://document.dxznjy.com/course/9b871519aa964a128be384fd0bb2867c.png"></image>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import helangWaterfall from '@/components/helang-waterfall/helang-waterfall';
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  const { $http, $navigationTo } = require('@/util/methods.js');
  const MD5 = require('../../util/md5.js');
  let secretkey = 'Jkk4ml1Of8';
  let vid = '';
  let ts = new Date().getTime();
  let sign = '';
  export default {
    data() {
      return {
        zxProductList: [],
        scrollTop: true,
        mobile: null,
        careerList: [],
        show: false, //禁止穿透
        discountList: [],
        shopList: [],
        familyList: [],
        courseType: null,
        active: 0,
        ReadList: [],
        playerIdcont: 'polyvPlayercont',
        videoInfo: {},
        array: [],
        value2: '',
        addressActive: 0,
        fullIshow: false,
        fullImages: '',
        allMoneyList: [],
        addressInfo: {
          address: '',
          buyerName: '',
          buyerPhone: '',
          addressId: ''
        },
        banansList: ['https://cdn.uviewui.com/uview/swiper/swiper1.png', 'https://cdn.uviewui.com/uview/swiper/swiper2.png', 'https://cdn.uviewui.com/uview/swiper/swiper3.png'],
        list: [
          {
            name: '会员礼包',
            id: 'return',
            top: 55,
            icon: 'https://document.dxznjy.com/course/f0f9b968793e42a5b0113cc617fe9cb7.png'
          },
          {
            name: '会员折扣',
            id: 'discount',
            top: 55,
            icon: 'https://document.dxznjy.com/course/d82b53573c0d4cd0af995dd1eea13788.png'
          },
          {
            name: '天赋测评',
            id: 'talent',
            top: 421,
            icon: 'https://document.dxznjy.com/course/dfa0f5daf73d4111b0f8b6fa95178264.png'
          },
          {
            name: '学业生涯规划',
            id: 'career',
            top: 555,
            icon: 'https://document.dxznjy.com/course/d5f6eaf8fd824f2b81e78b863041b685.png'
          },
          {
            name: '推广收益',
            id: 'earnings',
            top: 1062,
            icon: 'https://document.dxznjy.com/course/41363ca388f549e4bbfea03d159451c8.png'
          },
          {
            name: '鼎币商城',
            id: 'shop',
            top: 703,
            icon: 'https://document.dxznjy.com/course/b8d2c9fe5c9440a7be2be2c263419ef6.png'
          },
          {
            name: '文化社区',
            id: 'society',
            top: 1223,
            icon: 'https://document.dxznjy.com/course/3470081ebccf41bc9e1db7e6d31a741b.png'
          }
        ],
        identityType: uni.getStorageSync('identityType'),
        imgHost: getApp().globalData.imgsomeHost,
        shareid: '', //邀请人的id
        scene: '',
        testCode: '',
        studyTime: 0,
        falg: false,
        nowList: [],
        bigBaglist: [],
        addressName: '',
        userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '',
        code: false,
        couponsList: [{}, {}, {}],
        expandList: []
      };
    },
    components: {
      helangWaterfall
    },
    async onLoad(e) {
      this.mobile = uni.getStorageSync('phone');
    },
    onShow() {
      this.init();
      this.getdiscount();
      this.getShopList();
      this.getsociety();
      this.getsociety1();
      this.getCoinByMonth();
      let _this = this;
      uni.$once('updateAddress', (data) => {
        console.log(data);
        _this.addressInfo.buyerName = data.plate.consigneeName;
        _this.addressInfo.buyerPhone = data.plate.consigneePhone;
        _this.addressInfo.addressId = data.plate.addressId;
        _this.addressInfo.address = data.plate.provinceName + data.plate.cityName + data.plate.districtName + data.plate.address;
      });
    },
    mounted() {
      this.getTop();
    },
    methods: {
      // 查看大图
      lookFull(e) {
        this.fullImages = e;
        this.fullIshow = true;
      },
      //领取大礼包弹框
      async greatGiftPack() {
        let res = await $http({ url: 'zx/wap/giftOrder/byUserId?userId=' + this.userId });
        if (res.data) {
          uni.showToast({
            title: '已领取礼包',
            icon: 'none'
          });
        } else {
          let res = await $http({ url: 'zx/wap/GiftPack/list?type=1' });
          this.bigBaglist = res.data;
          this.$refs.addressInfo.open();
        }
      },
      checkBigBag(e) {
        if (e.number == 0) return;
        this.addressName = e.giftPackName;
        this.addressActive = e.id;
      },

      // 填写地址
      selectadd() {
        uni.navigateTo({
          url: '/splitContent/address/list/list?from=goods' + '&addressId=' + this.addressInfo.addressId
        });
      },
      //领取大礼包
      async getBigBag() {
        if (this.falg) return;
        this.falg = true;
        if (!this.addressActive) {
          this.falg = false;
          return uni.showToast({
            title: '请选择大礼包',
            icon: 'none'
          });
        }

        if (!this.addressInfo.address) {
          this.falg = false;
          return uni.showToast({
            title: '请选择地址',
            icon: 'none'
          });
        }
        uni.showLoading({
          title: '领取中...'
        });
        let data = {
          userId: this.userId,
          giftId: this.addressActive,
          giftPackName: this.addressName,
          deliveryAddress: this.addressInfo.address,
          deliveryName: this.addressInfo.buyerName,
          deliveryPhone: this.addressInfo.buyerName,
          deliveryName: this.addressInfo.buyerName,
          recipientName: uni.getStorageSync('nickName'),
          recipientPhone: uni.getStorageSync('phone'),
          remark: this.value2
        };
        let res = await $http({ url: 'zx/wap/giftOrder/create', method: 'post', data });
        if (res.data && res.success) {
          uni.hideLoading();
          uni.showToast({
            title: '领取成功',
            icon: 'none'
          });
          this.falg = false;
          this.closeAddress();
        } else {
          this.falg = false;
          uni.showToast({
            title: '该礼包已领完',
            icon: 'none'
          });
          uni.hideLoading();
        }
      },
      // 关闭大礼包弹框
      closeAddress() {
        this.addressActive = null;
        this.addressInfo = {};
        this.addressName = '';
        this.value2 = '';
        this.$refs.addressInfo.close();
      },
      getTop() {
        uni.showLoading({
          title: '加载中'
        });
        setTimeout(() => {
          this.list.forEach(async (e) => {
            let p = await this.getElementPositionInParent(e.id);
            e.top = p.top;
          });
          uni.hideLoading();
        }, 1500);
      },
      // 禁止穿透滚动
      change(e) {
        this.show = e.show;
      },
      earnings() {
        httpUser.get('zx/wap/invite/ifShowInvite').then((res) => {
          let activityId = res.data.data.activityId;

          uni.navigateTo({
            url: `/InvitationGifts/index?activityid=${activityId}`
          });
        });
      },
      async getShopList() {
        const res = await $http({
          url: 'zx/wap/goods/select/list',
          data: {
            goodsTypeListStr: '6',
            pageNum: 1,
            pageSize: 2,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        this.shopList = res.data.data;
      },
      async closeVideo() {
        this.$refs.video_popup.close();
        let polyvPlayerContext = this.selectComponent('#polyv_player');
        let learningProgress = (polyvPlayerContext.rCurrentTime / polyvPlayerContext.rDuration).toFixed(2);
        let rCurrentTime = polyvPlayerContext.rCurrentTime;
        let _this = this;
        const res = await $http({
          url:
            'zx/wap/recorded/course/saveLeanProcess?courseType=' +
            this.courseType +
            '&learningProgress=' +
            learningProgress * 100 +
            '&courseId=' +
            this.videoInfo.id +
            '&studyTime=' +
            rCurrentTime,
          method: 'POST',
          data: {
            courseType: this.courseType,
            learningProgress: learningProgress * 100,
            courseId: this.videoInfo.id
          }
        });
        if (res) {
          this.getsociety();
          this.getsociety1();
        }
      },
      // 领取鼎币
      async goMoney(e, i) {
        uni.showLoading({
          title: '领取中...'
        });
        await $http({
          url: 'zx/wap/member/coin/gainCoin?id=' + e.id + '&userId=' + this.userId,
          method: 'post'
        });
        uni.hideLoading();
        if (i) {
          this.gomoreMoney();
        }
        this.getCoinByMonth();
      },
      async gomoreMoney() {
        let res = await $http({
          url: 'zx/wap/member/coin/list?userId=' + this.userId
        });
        this.allMoneyList = res.data;
        this.$refs.money.open();
      },
      // bindEnded(){
      // 	this.getGrowthValue('LEARN')
      // },
      bindloadedmetadata() {
        let polyvPlayerContext = this.selectComponent('#polyv_player');
        polyvPlayerContext.pause();
        polyvPlayerContext.seek(Number(this.studyTime));
      },
      async getsociety() {
        const res = await $http({
          url: 'zx/wap/recorded/course/findRecordedCoursesList',
          data: {
            courseType: 1,
            pageNum: 1,
            pageSize: 2
          }
        });
        if (res) {
          this.familyList = res.data.data[0];
          this.familyList.goodsCatalogueList = this.familyList.goodsCatalogueList.slice(0, 2);
        }
      },
      async getsociety1() {
        const res = await $http({
          url: 'zx/wap/recorded/course/findRecordedCoursesList',
          data: {
            courseType: 2,
            pageNum: 1,
            pageSize: 2
          }
        });
        if (res) {
          this.ReadList = res.data.data[0];
          this.ReadList.goodsCatalogueList = this.ReadList.goodsCatalogueList.slice(0, 2);
        }
      },
      goVideo(info, i) {
        console.log(info, i);
        // if(this.identityType==1){
        this.$refs.video_popup.open();
        this.videoInfo = info;
        this.courseType = i;
        this.studyTime = info.studyTime;
        let _this = this;
        this.$nextTick(() => {
          let polyvPlayerContext = _this.selectComponent('#polyv_player');
          console.log(polyvPlayerContext);
          const ts = new Date().getTime();
          const sign = MD5.md5(`${secretkey}${vid}${ts}`);
          polyvPlayerContext.changeVid({
            vid: _this.videoInfo.videoUrl,
            ts,
            sign
          });
        });
        // }else{
        // 	this.$refs.tipsContentPopupRefs.open()
        // 	this.tipsType=2
        // }
      },
      bindpause() {},
      skintap(url) {
        $navigationTo(url);
      },
      // 获取鼎币
      async getCoinByMonth() {
        let res = await $http({
          url: 'zx/wap/member/coin/getCoinByMonth?userId=' + this.userId
        });
        this.nowList = res.data;
      },
      closeTesting() {
        this.$refs.testing.close();
      },
      closeMonry() {
        this.$refs.money.close();
      },
      goDetails(item) {
        uni.navigateTo({
          url: '/shoppingMall/details?id=' + item.goodsId
        });
      },
      async testReport() {
        await this.$httpUser.get('zx/wap/talent/evaluation/single/detail?userId=' + this.userId).then((res) => {
          if (res.data.data.evaluationFileUrl === '') {
            uni.showToast({
              title: '报告未生成',
              icon: 'none'
            });
          } else {
            this.reportUrl = res.data.data.evaluationFileUrl;
            uni.downloadFile({
              url: this.reportUrl,
              success: function (res) {
                var filePath = res.tempFilePath;
                uni.openDocument({
                  filePath: filePath,
                  showMenu: true,
                  success: function (res) {
                    console.log('打开文档成功');
                  }
                });
              }
            });
            console.log(this.reportUrl, '1111');
          }
        });
      },
      goTest() {
        uni.navigateTo({
          url: '/aptitudeTesting/startTest'
        });
      },
      async startTest() {
        let that = this;
        await this.$httpUser
          .post('zx/wap/talent/evaluation/consume', {
            userId: uni.getStorageSync('user_id') || ''
          })
          .then((res) => {
            if (res.data.data && res.data.data.activationCode) {
              that.testCode = res.data.data.activationCode;
              that.$refs.testing.open();
            } else {
              uni.showToast({
                title: res.message,
                icon: 'none',
                duration: 2000
              });
            }
          });
      },
      copyCode() {
        uni.setClipboardData({
          data: this.testCode,
          success: function (res) {
            console.log(res);
            uni.getClipboardData({
              success: function (res) {
                uni.showToast({
                  title: '复制成功'
                });
              }
            });
          }
        });
      },
      async init() {
        this.array = [
          {
            value: '18',
            label: '幼儿园',
            ext: '',
            children: null
          },
          {
            value: '1',
            label: '一年级',
            ext: '',
            children: null
          },
          {
            value: '2',
            label: '二年级',
            ext: '',
            children: null
          },
          {
            value: '3',
            label: '三年级',
            ext: '',
            children: null
          },
          {
            value: '4',
            label: '四年级',
            ext: '',
            children: null
          },
          {
            value: '5',
            label: '五年级',
            ext: '',
            children: null
          },
          {
            value: '6',
            label: '六年级',
            ext: '',
            children: null
          },
          {
            value: '7',
            label: '七年级',
            ext: '',
            children: null
          },
          {
            value: '8',
            label: '八年级',
            ext: '',
            children: null
          },
          {
            value: '9',
            label: '九年级',
            ext: '',
            children: null
          },
          {
            value: '10',
            label: '高中一年级',
            ext: '',
            children: null
          },
          {
            value: '11',
            label: '高中二年级',
            ext: '',
            children: null
          },
          {
            value: '12',
            label: '高中三年级',
            ext: '',
            children: null
          }
        ];
        let res = await $http({
          url: 'zx/career/student/getList?parentMobile=' + this.mobile
        });
        this.careerList = res.data;
      },
      async getdiscount() {
        const res = await $http({
          url: 'zx/wap/goods/select/list',
          data: {
            // goodsCategoryId //商品分类id
            goodsTypeListStr: '2,3,4',
            pageNum: 1,
            pageSize: 2,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        if (res) {
          this.discountList = res.data.data;
        }
      },
      // 跳转去报告页
      goPlan(id) {
        uni.navigateTo({
          url: '/Coursedetails/Career/planning?id=' + id
        });
      },
      getElementPositionInParent(elementId) {
        return new Promise((resolve, reject) => {
          const query = uni.createSelectorQuery();

          // 查询父元素
          query.selectViewport().boundingClientRect();

          // 查询子元素
          query.select(`#${elementId}`).boundingClientRect();

          // 执行查询
          query.exec((results) => {
            if (results.length < 2) {
              reject(new Error('Failed to get element position.'));
              return;
            }

            const viewportRect = results[0];
            const elementRect = results[1];

            // 计算子元素在父元素中的位置
            const position = {
              top: elementRect.top - viewportRect.top,
              left: elementRect.left - viewportRect.left,
              width: elementRect.width,
              height: elementRect.height
            };

            resolve(position);
          });
        });
      },

      async goScroll(item, i) {
        this.active = i;
        const id = item.id;
        // console.log(res);
        // console.log(id);
        uni.pageScrollTo({
          scrollTop: item.top - 122,
          duration: 300
          // selector: "#" + id,
        });
      },
      career() {
        uni.navigateTo({
          url: '/Personalcenter/Career/enter'
        });
      },
      // 优惠券领取弹框
      async couponCollection() {
        let res = await $http({
          url: 'zx/wap/coupon/user/received/new/user',
          method: 'POST',
          data: {
            userId: uni.getStorageSync('user_id') || ''
          }
        });
        if (res.code === 20000 && res.data !== null) {
          this.couponsList = res.data.couponUserReceiveBriefList;
          this.$refs.coupon.open();
        } else {
          this.$util.alter(res.message);
        }
      },
      // 关闭拉新券弹框
      closeCouponPopun() {
        this.$refs.coupon.close();
        uni.navigateTo({
          url: '/coupons/CouponsList'
        });
      }
    },

    onPageScroll: function (e) {
      //nvue暂不支持滚动监听，可用bindingx代替
      console.log(e);
      if (e.scrollTop > 0) {
        this.scrollTop = false;
      } else {
        this.scrollTop = true;
      }
    }
  };
</script>
<style lang="scss" scoped>
  .fullImg {
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.6);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .regulations {
    height: 40rpx;
    line-height: 40rpx;
    margin: 32rpx 0;
    text-align: center;
    font-size: 28rpx;
    color: #cbb76d;
    font-family: AlibabaPuHuiTi_2_85_Bold;
  }

  .giftPacks {
    margin: 22rpx auto 0;
    width: 686rpx;
    height: 200rpx;
    background: #d8d8d8;
    border-radius: 16rpx;
    overflow: hidden;
  }

  .addressInfo {
    position: relative;
    min-height: 200rpx;
    background-color: #fff;
    padding: 34rpx;
    border-radius: 24rpx 24rpx 0rpx 0rpx;
    .addressTitle {
      height: 44rpx;
      line-height: 44rpx;
      font-family: AlibabaPuHuiTi_2_85_Bold;
      font-size: 32rpx;
      text-align: center;
      color: #333333;
      font-weight: bold;
      margin-bottom: 32rpx;
    }
    .addressTitle2 {
      height: 40rpx;
      line-height: 40rpx;
      margin-bottom: 24rpx;
    }
    .picImageItems {
      height: 220rpx;
      align-items: center;
      width: 682rpx;
      display: inline-flex;
      justify-content: space-between;
      .picImageItem {
        position: relative;
        height: 214rpx;
        width: 214rpx;
        border-radius: 14rpx;
        background-color: #555555;
        border: 4rpx solid #fff;
        overflow: hidden;
        .noHave {
          position: absolute;
          top: 0;
          left: 0;
          width: 214rpx;
          height: 214rpx;
          z-index: 999;
          background-color: rgba(0, 0, 0, 0.7);
          text-align: center;
          line-height: 214rpx;
          font-size: 24rpx;
          color: #fff;
        }
        .full {
          width: 48rpx;
          height: 48rpx;
          position: absolute;
          top: 14rpx;
          left: 14rpx;
          background: url('https://document.dxznjy.com/course/4679cc14090a440c8c7656e45b063f6f.png') no-repeat;
          background-size: contain;
        }

        .checked {
          width: 68rpx;
          height: 40rpx;
          position: absolute;
          top: -4rpx;
          right: -4rpx;
        }
      }
      .picActive {
        border: 4rpx solid #079881;
      }
    }
    .addressLine {
      display: inline-block;
      width: 6rpx;
      height: 24rpx;
      background: #11a98a;
      border-radius: 4rpx;
      margin-right: 8rpx;
    }
  }

  .paybtn {
    width: 100%;
    height: 90rpx;
    line-height: 90rpx;
    margin-top: 40rpx;
    text-align: center;
    border-radius: 45rpx;
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
  }

  .shadowMoney {
    position: relative;
    width: 648rpx;
    // height: 828rpx;
    background-color: #fff;
    border-radius: 24rpx;
    overflow: hidden;

    .moneyTitle {
      margin-top: 30rpx;
      margin-bottom: 30rpx;
      height: 44rpx;
      line-height: 44rpx;
      text-align: center;
      font-weight: bold;
      color: #333333;
      font-size: 32rpx;
    }

    .review_close {
      position: absolute;
      /* 固定在右上角 */
      top: 32rpx;
      right: 30rpx;
      z-index: 10;
      /* 确保在上层 */
    }

    .moneylist {
      height: 700rpx;
      width: 618rpx;
      overflow-y: auto;
      margin: 0 auto;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .item {
        width: 296rpx;
        height: 176rpx;
        padding-left: 24rpx;
        padding-top: 24rpx;
        box-sizing: border-box;
        // background-color: pink;
        margin-bottom: 16rpx;
        border-radius: 16rpx;
        // https://document.dxznjy.com/course/542cdefc8750463faf8659b143aa04ea.png
        //
      }

      .moneyActive {
        background: url('https://document.dxznjy.com/course/542cdefc8750463faf8659b143aa04ea.png') no-repeat;
        background-size: contain;

        .goMoney {
          margin-top: 4rpx;
          text-align: center;
          height: 36rpx;
          line-height: 36rpx;
          box-sizing: border-box;
          color: #ffffff;
          font-size: 24rpx;
          // font-weight: bold;
          width: 126rpx;
          border-radius: 40rpx;
          background: linear-gradient(270deg, #f4bc68 0%, #f79207 100%);
        }

        .noMoney {
          height: 36rpx;
          line-height: 36rpx;
          font-size: 24rpx;
          color: #ffffff;
          text-align: center;
          width: 126rpx;
          border-radius: 40rpx;
          background: #53cdb7;
        }

        .dingMonry {
          font-size: 48rpx;
          line-height: 54rpx;
          height: 54rpx;
          color: #fff;
        }
      }

      .moneyNo {
        background: url('https://document.dxznjy.com/course/d5ce31cbf9804130ac139a3b7b3d5dbd.png') no-repeat;
        background-size: contain;

        .dingMonry {
          font-size: 48rpx;
          line-height: 54rpx;
          height: 54rpx;
          color: #15af8d;
        }

        .goMoney {
          margin-top: 4rpx;
          text-align: center;
          height: 36rpx;
          line-height: 36rpx;
          box-sizing: border-box;
          color: #15af8d;
          font-size: 24rpx;
          // font-weight: bold;
          width: 204rpx;
          border-radius: 40rpx;
          background: #d1ede8;
        }
      }
    }
  }

  .return {
    .draw {
      height: 178rpx;
      width: 346rpx;
      padding-left: 24rpx;
      padding-top: 24rpx;
      box-sizing: border-box;
      background: url('https://document.dxznjy.com/course/acd5529f44be4ca390ef56e0b8c337af.png') no-repeat;
      background-size: contain;

      .dingMonry {
        font-size: 48rpx;
        line-height: 54rpx;
        height: 54rpx;
        color: #fff;
      }

      .goMoney {
        margin-top: 4rpx;
        text-align: center;
        height: 36rpx;
        line-height: 36rpx;
        box-sizing: border-box;
        color: #ffffff;
        font-size: 24rpx;
        // font-weight: bold;
        width: 126rpx;
        border-radius: 40rpx;
        background: linear-gradient(270deg, #f4bc68 0%, #f79207 100%);
      }

      .noMoney {
        height: 36rpx;
        line-height: 36rpx;
        font-size: 24rpx;
        color: #ffffff;
        text-align: center;
        width: 126rpx;
        border-radius: 40rpx;
        background: #53cdb7;
      }
    }

    .nodarw {
      background: url('https://document.dxznjy.com/course/6c2bad92896148408696956e4dc3f4fc.png') no-repeat;
      background-size: contain;

      .dingMonry {
        color: #099b83;
      }

      .goMoney {
        margin-top: 4rpx;
        text-align: center;
        height: 36rpx;
        line-height: 36rpx;
        box-sizing: border-box;
        color: #15af8d;
        font-size: 24rpx;
        // font-weight: bold;
        width: 204rpx;
        border-radius: 40rpx;
        background: #d1ede8;
      }
    }

    // "https://document.dxznjy.com/course/f108ab6c863f4dc28120f13c4d1e56c6.png
  }

  .video_content_css {
    height: 100vh;
    width: 750rpx;
    position: relative;
    z-index: 5;

    .video_css {
      width: 750rpx;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }

    .tips_content_close {
      position: absolute;
      right: 32rpx;
      top: 40rpx;
      z-index: 3;
    }
  }

  .moress {
    height: 34rpx;
    text-align: center;
    line-height: 34rpx;
    font-size: 24rpx;
    color: #339378;

    .right {
      display: inline-block;
      margin-left: 8rpx;
      font-size: 18rpx;
    }
  }

  .banans {
    width: 686rpx;
    height: 280rpx;
    border-radius: 16rpx;
    overflow: hidden;
    margin: 0 auto 48rpx;
  }

  .title {
    height: 44rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 32rpx;
    color: #333333;
    font-weight: bold;
    margin-bottom: 16rpx;

    .titlef {
      display: flex;
      align-items: center;
    }

    .linew {
      display: inline-block;
      height: 30rpx;
      width: 12rpx;
      background-color: #0b957a;
      border-radius: 6rpx;
      margin-right: 6rpx;
    }

    .mores {
      width: 110rpx;
      height: 40rpx;
      background: linear-gradient(90deg, #fdfdfd 0%, #b1ded1 100%);
      border-radius: 20rpx;
      color: #0f5b4e;
      text-align: center;
      font-size: 24rpx;
      line-height: 40rpx;
    }
  }

  .image_left_content {
    .imagele {
      width: 260rpx;
      height: 146rpx;
    }

    .video_play {
      width: 64rpx;
      height: 64rpx;
      left: 96rpx;
      top: 50rpx;
    }
  }

  .right_record_css {
    width: 410rpx;
  }

  .active {
    color: #fff !important;
  }

  .escription {
    margin-bottom: 24rpx;
    line-height: 40rpx;
    // height: 40rpx;
    color: #959595;
    font-size: 28rpx;
  }

  //弹窗
  .shareCard {
    position: relative;
    height: 600rpx;
    background: #ffffff;
    color: #000;
    padding-top: 50upx;
    box-sizing: border-box;
    overflow: hidden;
    width: 90vw;
  }

  .activityRule {
    text-align: center;
    font-weight: bold;
    position: absolute;
    top: 40rpx;
    left: 0;
    width: 100%;
    background: white;
    z-index: 10;
  }

  .review_close {
    position: absolute;
    /* 固定在右上角 */
    top: 40rpx;
    right: 20rpx;
    z-index: 10;
    /* 确保在上层 */
  }

  .rule {
    padding: 20rpx;
    line-height: 50rpx;
    margin-top: 60rpx;
    /* 确保不被固定元素遮挡 */
    overflow-y: auto;
    /* 允许滚动 */
    height: calc(100% - 40px);
    /* 适应滚动区域的高度 */
  }

  .scroll-view_H {
    position: sticky;
    top: 0;
    /* 当元素滚动到顶部时变为固定定位 */
    z-index: 1;
    /* 确保吸顶元素覆盖其他内容 */
    white-space: nowrap;
    width: 100%;
    padding: 32rpx 0;
    display: flex;
    justify-content: space-between;
    // height: 238rpx;
    background: linear-gradient(107deg, #084137 0%, #03251f 63%, #023b31 100%);

    .item {
      display: inline-block;
      margin: 0 40rpx;

      &:first-child {
        margin-left: 32rpx;
      }

      &:last-child {
        margin-right: 32rpx;
      }

      .icon {
        height: 112rpx;
        width: 112rpx;
        border-radius: 112rpx;
        margin: 0 auto 24rpx;
        background-color: pink;
      }

      .text {
        line-height: 40rpx;
        height: 40rpx;
        text-align: center;
        font-size: 28rpx;
        color: #dac3a4;
        font-weight: bold;
      }
    }
  }

  .tifry {
  }

  .swiper {
    width: 666rpx;
    // height: 220rpx;
    margin: 40rpx auto 0;

    .card {
      // width: 600rpx;
      // height: 256rpx;
      margin: 0 12rpx;
      background-color: #fff;
      border-radius: 24rpx;
      padding: 34rpx 24rpx;
      box-sizing: border-box;
      overflow: hidden;

      .cardBtn {
        color: #fff;
        font-size: 28rpx;
        font-weight: bold;
        width: 500rpx;
        margin: 40rpx auto 0;
        text-align: center;
        line-height: 60rpx;
        height: 60rpx;
        background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
        border-radius: 60rpx;
      }

      .avater {
        width: 82rpx;
        height: 82rpx;
        border-radius: 82rpx;
        margin-right: 48rpx;
      }

      .name {
        height: 44rpx;
        line-height: 44rpx;
        font-size: 32rpx;
        color: #555555;
        font-weight: bold;
        font-family: AlibabaPuHuiTi_2_85_Bold;
      }

      .old {
        height: 32rpx;
        background: rgba(51, 147, 120, 0.06);
        border-radius: 20rpx;
        border: 2rpx solid #aacac1;
        font-size: 24rpx;
        line-height: 34rpx;
        color: #2e836b;
        padding: 0 24rpx;
        text-align: center;
      }

      .glard {
        font-size: 28rpx;
        color: #a7a7a7;
        margin-left: 24rpx;
      }

      .type {
        margin-left: 12rpx;
        width: 120rpx;
        height: 34rpx;
        line-height: 34rpx;
        font-size: 24rpx;
        color: #ffffff;
        text-align: center;
        background: #33cb9e;
        border-radius: 30rpx;
      }
    }
  }

  .discount {
    padding: 18rpx;
    box-sizing: border-box;
    margin: 32rpx 24rpx;
    background: url('https://document.dxznjy.com/course/7da877c6f9464807a1f68daa54c30a54.png') no-repeat;

    // background-size: contain;
    .discountItems {
      // height: 346rpx;
      display: flex;
      justify-content: space-around;
    }
  }

  .talent {
    .talentBtn {
      width: 350rpx;
      height: 178rpx;
      background: url('https://document.dxznjy.com/course/6a559938caf2408a9185e3b6b8d2bbde.png') no-repeat;
      background-size: contain;
    }

    .talentBtn1 {
      width: 350rpx;
      height: 178rpx;
      background: url('https://document.dxznjy.com/course/43c3a9b43f9a47618779dc05bf9e4013.png') no-repeat;
      background-size: contain;
    }
  }

  .career {
    .careerbtn {
      width: 690rpx;
      box-sizing: border-box;
      // line-height: 96rpx;
      height: 130rpx;
      margin: 40rpx auto 0;
      // font-size: 32rpx;
      // font-weight: bold;
      // text-align: center;
      // border-radius: 16rpx;
      // background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
      background: url('https://document.dxznjy.com/course/8613e8e47373489a8c72c738e6185a07.png') no-repeat;
      background-size: contain;
    }
  }

  .shop {
    // height: 400px;
  }

  .earnings {
    .earning {
      height: 200rpx;
      border-radius: 16rpx;
      background: #d8d8d8;
      overflow: hidden;
    }
  }

  .common {
    background: #ffffff;
    border-radius: 24rpx;
    margin: 24rpx 0;
    padding: 20rpx 24rpx;
  }

  .society {
    margin-bottom: 40rpx;

    .societys {
      height: 200rpx;
      overflow: hidden;
      border-radius: 16rpx;
      background: #d8d8d8;
    }

    .societyTitle {
      display: flex;
      align-items: center;
      margin: 48rpx 0 24rpx;
      height: 44rpx;
      line-height: 44rpx;
      color: #3c574d;
      font-size: 32rpx;
      font-weight: bold;

      .rhomboid {
        height: 14rpx;
        width: 14rpx;
        transform: rotate(45deg);
        background-color: #cbb76d;
        margin-right: 14rpx;
      }

      // padding-left: 42rpx;
      // text-align: center;
    }

    .societyBan {
      height: 200rpx;
      border-radius: 16rpx;
      background: #d8d8d8;
      overflow: hidden;
      margin: 48rpx 0 0;
    }
  }
  .coupon_box {
    margin: 22rpx auto 0;
    width: 686rpx;
    height: 202rpx;
    border-radius: 16rpx;
    overflow: hidden;
  }
  .coupon_popup {
    position: relative;
    box-sizing: border-box;
    width: 542rpx;
    height: 764rpx;
    padding: 208rpx 32rpx 16rpx 28rpx;
    // background-color: pink;
    background: url('https://document.dxznjy.com/course/a7766070552b4155b25eda6ef62a91fc.png') no-repeat;
    background-size: contain;
  }
  .item-coupons {
    // display: flex;
    // width: 686rpx;
    background: #ffffff;
    border-radius: 16rpx;
    margin-top: 16rpx;
    padding: 20rpx 30rpx 20rpx 0;

    .activity-type {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 158rpx;

      .activity-name {
        font-family: AlibabaPuHuiTi_2_85_Bold;
        font-size: 26rpx;
        color: #489981;
        line-height: 56rpx;
        text-align: left;
        font-style: normal;
        font-weight: bold;
        margin-bottom: 4rpx;
      }

      .activity-limit {
        font-family: AlibabaPuHuiTi_2_55_Regular;
        font-size: 24rpx;
        color: #489981;
        line-height: 34rpx;
        text-align: right;
        font-style: normal;
      }
    }

    .activity-content {
      width: 358rpx;

      .title {
        font-family: AlibabaPuHuiTi_2_85_Bold;
        font-size: 28rpx;
        color: #555555;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
        font-weight: bold;
        margin-bottom: 8rpx;
      }

      .time {
        // height: 40rpx;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        font-size: 24rpx;
        color: #c5c4c4;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
        margin-bottom: 18rpx;
      }

      .rules {
        display: flex;
        align-items: center;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        font-size: 24rpx;
        color: #8f8e8e;
        line-height: 34rpx;
        text-align: left;
        font-style: normal;

        .expand-icon {
          width: 28rpx;
          height: 28rpx;
          margin-left: 8rpx;

          .icon-down {
            width: 28rpx;
            height: 28rpx;
          }
        }
      }
    }

    .activity-use {
      width: 120rpx !important;
      height: 48rpx !important;
      background: #339378 !important;
      border-radius: 24rpx !important;
      margin-left: 20rpx !important;
      font-weight: bold !important;
      font-size: 24rpx !important;
      color: #ffffff !important;
      line-height: 48rpx !important;
      text-align: center !important;
    }

    .rules-text {
      font-family: AlibabaPuHuiTi_2_55_Regular;
      font-size: 24rpx;
      color: #c5c4c4;
      line-height: 34rpx;
      text-align: left;
      font-style: normal;
      margin: 16rpx 0 0 156rpx;
    }
  }
</style>
