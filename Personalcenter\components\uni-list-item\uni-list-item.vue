<template>
	<!-- #ifdef APP-NVUE -->
	<cell>
		<!-- #endif -->
		<view :class="disabled ? 'uni-list-item--disabled' : ''" :hover-class="disabled || showSwitch ? '' : 'uni-list-item--hover'"
		 class="uni-list-item" @click="onClick">
			<view class="uni-list-item__container" :class="{'uni-list-item--first':isFirstChild}">
				<view v-if="thumb" class="uni-list-item__icon">
					<image :src="thumb" class="uni-list-item__icon-img" />
				</view>
				<view v-if="thumbsmall" class="uni-list-item__icon">
					<image :src="thumbsmall" class="uni-list-item__icon-imgsamll" />
				</view>
				<radio-group v-if="showRadio"  @change="onRadioChange" :radiodata="radiodata">
					<label class="uni-list-cell uni-list-cell-pd" v-for="(item, index) in radiodata" :key="item.value">
						<view>
							<radio :value="item.value" :color="radiocolor"  />
						</view>
						<view>{{item.name}}</view>
					</label>
				</radio-group>
				<view v-else-if="showExtraIcon" class="uni-list-item__icon">
					<uni-icons :color="extraIcon.color" :size="extraIcon.size" :type="extraIcon.type" class="uni-icon-wrapper" />
				</view>
				
				<view class="uni-list-item__content" v-if="!showLeftradio">
					<slot />
					<text class="uni-list-item__content-title">{{ title }}</text>
					<text v-if="note" class="uni-list-item__content-note">{{ note }}</text>
					<view v-if="contentthumb" class="uni-list-item__content-text">
						<text>{{contenttext}}</text> 
						<image :src="contentthumb" mode=""></image>
					</view>
				</view>
				<view class="uni-list-item__extra">
					<text v-if="rightText" class="uni-list-item__extra-text">{{rightText}}</text>
					<uni-badge v-if="showBadge" :type="badgeType" :text="badgeText" />
					<switch v-if="showSwitch" :disabled="disabled" :checked="switchChecked" @change="onSwitchChange" />
					<slot name="right"></slot>
					<uni-icons v-if="showArrow" :size="14" class="uni-icon-wrapper" color="#bbb" type="arrowright" />
				</view>
				
				<radio-group v-if="showLeftradio"  @change="onLeftRadioChange" :radiodata="radioLeftdata" class='radiogroup'>
					<label class="uni-list-cell uni-list-cell-pd list-between" v-for="(item, index) in radioLeftdata" :key="item.value">
						<view>
							<image :src="item.iconurl" mode=""></image>
							<view>{{item.name}}</view>
						</view>
						
						<view>
							<radio :value="item.value" :checked='item.check' :color="radiocolor"  />
						</view>
					</label>
				</radio-group>
			</view>
		</view>
		<!-- #ifdef APP-NVUE -->
	</cell>
	<!-- #endif -->
</template>

<script>
	// import uniIcons from '../uni-icons/uni-icons.vue'
	import uniBadge from '../uni-badge/uni-badge.vue'

	/**
	 * ListItem 列表子组件
	 * @description 列表子组件
	 * @tutorial https://ext.dcloud.net.cn/plugin?id=24
	 * @property {String} title 标题
	 * @property {String} note 描述
	 * @property {String} thumb 左侧缩略图，若thumb有值，则不会显示扩展图标
	 * @property {String} badgeText 数字角标内容
	 * @property {String} badgeType 数字角标类型，参考[uni-icons](https://ext.dcloud.net.cn/plugin?id=21)
	 * @property {String} rightText 右侧文字内容
	 * @property {Boolean} disabled = [true|false]是否禁用
	 * @property {Boolean} showArrow = [true|false] 是否显示箭头图标
	 * @property {Boolean} showBadge = [true|false] 是否显示数字角标
	 * @property {Boolean} showSwitch = [true|false] 是否显示Switch
	 * @property {Boolean} switchChecked = [true|false] Switch是否被选中
	 * @property {Boolean} showExtraIcon = [true|false] 左侧是否显示扩展图标
	 * @property {Boolean} scrollY = [true|false] 允许纵向滚动，需要显式的设置其宽高
	 * @property {Object} extraIcon 扩展图标参数，格式为 {color: '#4cd964',size: '22',type: 'spinner'}
	 * @event {Function} click 点击 uniListItem 触发事件
	 * @event {Function} switchChange 点击切换 Switch 时触发
	 */
	export default {
		name: 'UniListItem',
		components: {
			// uniIcons,
			uniBadge
		},
		props: {
			title: {
				type: String,
				default: ''
			}, // 列表标题
			note: {
				type: String,
				default: ''
			}, // 列表描述
			contenttext: {
				type: String,
				default: ''
			}, // 列表标题后边图标文字
			disabled: {
				// 是否禁用
				type: [Boolean, String],
				default: false
			},
			showArrow: {
				// 是否显示箭头
				type: [Boolean, String],
				default: true
			},
			showBadge: {
				// 是否显示数字角标
				type: [Boolean, String],
				default: false
			},
			showRadio: {
				// 是否显示radio
				type: [Boolean, String],
				default: false
			},
			radiodata:Array,  //单选框的数组
			radiocolor:'',		//单选框颜色
			
			showSwitch: {
				// 是否显示Switch
				type: [Boolean, String],
				default: false
			},
			switchChecked: {
				// Switch是否被选中
				type: [Boolean, String],
				default: false
			},
			showLeftradio: {
				// 是否显示radio
				type: [Boolean, String],
				default: false
			},
			radioLeftdata:Array,  //单选框的数组
			badgeText: {
				// badge内容
				type: String,
				default: ''
			},
			badgeType: {
				// badge类型
				type: String,
				default: 'success'
			},
			rightText: {
				// 右侧文字内容
				type: String,
				default: ''
			},
			thumbsmall: {
				// 缩略图
				type: String,
				default: ''
			},
			thumb: {
				// 缩略图
				type: String,
				default: ''
			},
			contentthumb: {
				// 缩略图
				type: String,
				default: ''
			},
			showExtraIcon: {
				// 是否显示扩展图标
				type: [Boolean, String],
				default: false
			},
			extraIcon: {
				type: Object,
				default () {
					return {
						type: 'contact',
						color: '#000000',
						size: 20
					}
				}
			}
		},
		inject: ['list'],
		data() {
			return {
				isFirstChild: false
			}
		},
		mounted() {
			// console.log(this.list)
			if (!this.list.firstChildAppend) {
				this.list.firstChildAppend = true
				this.isFirstChild = true
			}
		},
		methods: {
			onClick() {
				this.$emit('click')
			},
			onRadioChange(e) {
				this.$emit('radioChange', e.detail)
			},
			onLeftRadioChange(e) {
				this.$emit('radioLeftChange', e.detail)
			},
			onSwitchChange(e) {
				this.$emit('switchChange', e.detail)
			}
		}
	}
</script>

<style scoped>
	.uni-list-item {
		font-size: 16;
		position: relative;
		flex-direction: column;
		justify-content: space-between;
		padding-left: 15px;
	}
	
	.uni-list-item__icon-imgsamll{
		width: 16upx;
		height: 16upx;
		margin-right: 10upx;
	}
	
	.uni-list-item__content-text { position: absolute;display: inline-block;left: 100upx;padding: 2upx 18upx;text-align: center;line-height: 36upx;margin-top: 2upx; }
	.uni-list-item__content-text text { position: relative; z-index: 22;font-size: 24upx;color: #FFFFFF; }
	.uni-list-item__content-text image { width: 100%;height: 100%;position: absolute;left: 0;top: 0; }

	.uni-list-item--disabled {
		opacity: 0.3;
	}
	
	.radiogroup { width: 100%; }

	.uni-list-item--hover {
		background-color: #f1f1f1;
	}

	.uni-list-item__container {
		position: relative;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		padding: 14px 15px;
		padding-left: 0;
		flex: 1;
		position: relative;
		justify-content: space-between;
		align-items: center;
		/* #ifdef APP-PLUS */
		border-top-color: #e5e5e5;
		border-top-style: solid;
		border-top-width: 0.5px;
		/* #endif */
	}

	.uni-list-item--first {
		border-top-width: 0px;
	}

	/* #ifndef APP-NVUE */
	.uni-list-item__container:after {
		position: absolute;
		top: 0;
		right: 0;
		left: 0;
		height: 1px;
		content: '';
		-webkit-transform: scaleY(.5);
		transform: scaleY(.5);
		background-color: #e5e5e5;
	}

	.uni-list-item--first:after {
		height: 0px;
	}

	/* #endif */





	.uni-list-item__content {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex: 1;
		overflow: hidden;
		flex-direction: column;
		color: #3b4144;

	}

	.uni-list-item__content-title {
		font-size: 14px;
		color: #3b4144;
		overflow: hidden;
	}

	.uni-list-item__content-note {
		margin-top: 8rpx;
		color: #999;
		font-size: 14px;
		overflow: hidden;
	}

	.uni-list-item__extra {
		/* width: 25%;
 */
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		justify-content: flex-end;
		align-items: center;
	}

	.uni-list-item__icon {
		margin-right: 18rpx;
		flex-direction: row;
		justify-content: center;
		align-items: center;
	}

	.uni-list-item__icon-img {
		width: 36rpx;
		height: 28rpx;
	}

	.uni-list-item__extra-text {
		color: #999;
		font-size: 14px;
	}
	
	
	/* 左侧新增单选框 */
	uni-radio-group { display: block;width: 100%; }
	.uni-label-pointer {
	    cursor: pointer;
	    display: flex;
	    padding: 12px 0px;
		position: relative;
	}
	.uni-label-pointer:after {
	    position: absolute;
	    bottom: 0;
	    right: 0;
	    left: 0;
	    height: 1px;
	    content: '';
	    -webkit-transform: scaleY(.5);
	    transform: scaleY(.5);
	    background-color: #e5e5e5;
	}
	.uni-label-pointer:last-child:after {
	    height: 0px;
	}
	
	/* 右侧添加单选框 */
	.list-between { display: flex;width: 100%; justify-content: space-between; }
	.list-between>view { display: inline-flex; }
	.list-between image { width: 48rpx;height: 48rpx;margin-right: 40rpx; }
	
</style>
