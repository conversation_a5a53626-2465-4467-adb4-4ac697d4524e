<template>
  <view class="partner f-28">
    <view class="top p-30">
      <view class="search-box">
        <view class="search-title">学员号码</view>
        <view class="search-content flex">
          <view class="search-content-input">
            <u-input
              v-model.trim="phoneNumber"
              placeholder="请输入学员手机号……"
              :style="{ width: '500rpx', height: '88rpx', background: '#f3f8fc', borderRadius: '32rpx' }"
              :border="false"
              :padding="0"
              type="number"
              :maxlength="11"
              backgroundColor="#f3f8fc"
              @blur="handleBlur"
            ></u-input>
          </view>

          <view class="search-content-button f-28">
            <u-button type="primary" :hairline="false" :disabled="disabledSearch" color="#f3f3f3" shape="circle" size="small" text="搜索" @click="searchStudentBtn"></u-button>
          </view>
        </view>
      </view>

      <!-- 学员认领信息 -->
      <StudentMessage :searchResult="searchResult" :showContentStatus="showContentStatus" @claimSubmitBtn="claimSubmitBtn" @claimBtn="claimBtn" />

      <u-tabs
        :current="currentIndex"
        :list="tabList"
        lineWidth="40"
        lineHeight="12"
        :activeStyle="{ color: '#333333', fontWeight: 'bold' }"
        :inactiveStyle="{
          color: '#5A5A5A ',
          transform: 'scale(1)',
          fontSize: '28rpx'
        }"
        itemStyle="padding-left:20rpx;height: 80rpx;"
        :lineColor="`url(${lineBg}) 100% 110%`"
        @click="tabsClick"
        keyName="name"
      ></u-tabs>
    </view>

    <div v-if="auditList && auditList.length > 0">
      <scroll-view
        class="p-30"
        scroll-y="true"
        lower-threshold="100"
        style="height: calc(100vh - 464rpx - 44px)"
        :scroll-y="true"
        :scroll-with-animation="true"
        @scrolltolower="handleScrollToLower"
      >
        <view class="mb-20" v-for="item in auditList" :key="item.appealId" @click="jumpToDetail(item)">
          <AuditCard :cardItem="item" />
        </view>
      </scroll-view>
    </div>

    <view v-else class="no-data pt-30 pb-55 f-28">
      <image class="no-data-image" src="https://document.dxznjy.com/course/ac587707bf314badadb28a158852c77d.png"></image>
      <view class="c-66 f-32">暂无数据</view>
    </view>

    <!-- 弹窗 -->
    <u-popup ref="code" :show="showClaimModal" type="center" :showConfirmButton="false" mode="center" :safeAreaInsetBottom="false" :round="40">
      <view class="modal-content">
        <view class="close-icon" @click="closeBtn"></view>
        <view v-if="auditStatus == 1" class="show-title c-55 mb-40 mt-40 f-36">确认领取</view>
        <view v-if="auditStatus == 1" class="show-student c-55 mb-50 f-32">学员{{ searchResult.nickName }} {{ searchResult.mobile }} 吗？</view>

        <view v-if="auditStatus == 1" class="submit-btn c-ff f-32">
          <u-button
            v-if="auditStatus == 1"
            shape="circle"
            class="submit-btn c-ff f-32"
            text="确定"
            :loading="isLoading"
            :disabled="isLoading"
            loadingSize="28"
            @click="confirmClaim"
            color="linear-gradient(90deg, #ef9d4f 0%, #ea643e 100%)"
          ></u-button>
        </view>
        <image v-if="auditStatus == 2" class="smile-box w-80" src="https://document.dxznjy.com/dxSelect/752cee88-c41e-42d8-83f2-44b356050e0f.png"></image>
        <view v-if="auditStatus == 2" class="show-title c-55 mt-16 f-36">认领成功</view>
        <image v-if="auditStatus == 3" class="smile-box w-80 mt-50" src="https://document.dxznjy.com/dxSelect/6bf16448-6589-4d0b-8d16-07b25c6a00ab.png"></image>
        <view v-if="auditStatus == 3" class="show-title c-55 mt-16 f-36">很抱歉，学员认领失败</view>
        <view v-if="auditStatus == 3" class="show-desc mt-12 f-36">重新搜索一下学员信息试试吧</view>
      </view>
    </u-popup>
  </view>
</template>

<script>
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  const { $navigationTo, $http } = require('@/util/methods.js');
  import AuditCard from './components/auditCard.vue';
  import StudentMessage from './components/StudentMessage.vue';
  export default {
    name: 'Students',
    components: {
      AuditCard,
      StudentMessage
    },

    data() {
      return {
        name: '',
        userId: '',
        tabList: [
          {
            name: '记录',
            index: 0
          },
          {
            name: '已认领',
            index: 1
          },
          {
            name: '申诉中',
            index: 2
          },
          {
            name: '申诉成功',
            index: 3
          },
          {
            name: '申诉失败',
            index: 4
          }
        ],
        lineBg: 'https://document.dxznjy.com/course/01c96465d9ea46f69aa97465b1201aef.png',
        auditList: [], //认领信息
        phoneNumber: '',
        showClaimModal: false, // 是否显示弹窗
        currentIndex: 0,
        auditStatus: 1, //显示弹窗的状态 1-认领 2-认领成功 3-认领失败
        finished: false, // 是否加载完成
        pageNum: 1, //
        pageSize: 10, //
        total: 0, //
        showContentStatus: '5', // 1.搜索到学员信息 0-没有搜索到学员信息
        searchResult: {
          nickName: '', // 昵称
          mobile: '', // 手机号
          currentPartnerRealName: '', // 前合伙人姓名
          currentPartnerMerchantCode: '' // 当前合伙人编码
        }, // 搜索结果
        disabledSearch: false, // 是否禁用搜索按钮
        isLoading: false, // 加载中
        isPassLoading: false, // 是否阻止加载
        isClaimLoading: false //
      };
    },

    onShow() {
      // 获取当前选中的 tab
      const currentStatus = uni.getStorageSync('currentStatus');
      this.currentIndex = currentStatus;
      this.pageNum = 1;
      this.auditList = [];
      this.getData();
      this.debouncedClaimHandler = this.debounce(this.claimSubmitBtnHandler, 1000);
    },
    methods: {
      // 防抖函数实现
      debounce(fn, delay) {
        let timer = null;
        return function (...args) {
          if (timer) clearTimeout(timer);
          timer = setTimeout(() => {
            fn.apply(this, args);
          }, delay);
        };
      },
      /**
       * 关闭弹窗 认领成功后 刷新列表的数据
       */
      closeBtn() {
        this.showClaimModal = false;
        this.getData();
        this.phoneNumber = '';
        this.showContentStatus = '5';
        this.auditStatus = '';
      },

      /**
       * 失去焦点
       * 如果没有输入手机号, 显示暂无数据
       */
      handleBlur() {
        if (!this.phoneNumber) {
          this.showContentStatus = '5';
          return;
        }
      },
      showLoading() {
        uni.showLoading({
          title: '加载中',
          mask: true
        });
      },

      /**
       * 滚动到底部时触发
       *
       * 如果 finished == true, 则不再加载更多数据
       * 如果当前页数大于总页数时，停止加载更多数据
       * 否则，加载更多数据
       */
      handleScrollToLower() {
        if (this.finished) {
          return;
        }
        this.pageNum++;
        // 当前页数大于总页数时，停止加载更多数据
        if (this.pageNum > Math.ceil(this.total / this.pageSize)) {
          this.finished = true;
          return;
        }
        // this.showLoading();
        this.getData(true);
      },

      claimSubmitBtnHandler() {
        if (this.isClaimLoading) return;
        this.isClaimLoading = true;
        console.log('申诉按钮被点击了');
        httpUser
          .get('zx/wap/claim/appeal/can-appeal', {
            userId: this.searchResult.userId
          })
          .then((res) => {
            console.log('🚀 ~ .then ~ res:', res);
            if (res.data.success) {
              this.isClaimLoading = false;
              if (res.data.data.canAppeal == 1) {
                console.log('申诉按钮');
                $navigationTo(`partnerApplication/auditDetail?info=${JSON.stringify(this.searchResult)}`);
                setTimeout(() => {
                  this.phoneNumber = '';
                  this.showContentStatus = '5';
                }, 1000);
                return;
              }

              uni.showModal({
                title: '温馨提示',
                content: res.data.data.message,
                showCancel: false
              });
            }
          })
          .catch((error) => {
            console.log('🚀🥶💩~ error', error);
            this.isClaimLoading = false;
          });
      },

      /**
       * 申诉按钮
       * @description
       * 1. 跳转到申诉页面
       * 2. 1s后清空输入框，显示暂无数据
       */
      claimSubmitBtn() {
        // 调用已经初始化好的防抖函数
        this.debouncedClaimHandler();
      },

      /**
       * 搜索学员信息
       * @description
       * 1. 校验手机号
       * 2. 调用接口搜索学员信息
       */
      searchStudentBtn() {
        // 校验手机号
        if (!this.phoneNumber) {
          uni.showToast({
            title: '请输入手机号',
            icon: 'none'
          });
          return;
        }
        if (!/^1[3-9]\d{9}$/.test(this.phoneNumber)) {
          uni.showToast({
            title: '请输入正确的手机号',
            icon: 'none'
          });
          return;
        }
        // 调用接口搜索学员信息
        console.log('搜索学员信息11111', this.phoneNumber);
        if (this.disabledSearch) return;
        this.disabledSearch = true;
        httpUser
          .get('zx/wap/claim/user-info', {
            mobile: this.phoneNumber
          })
          .then((res) => {
            console.log('🚀 ~ .then ~ res:', res);
            Object.assign(this.searchResult, res.data.data);
            if (res.data.data == null) {
              this.showContentStatus = '6';
              this.disabledSearch = false;
              return;
            }
            this.showContentStatus = this.searchResult.canClaim;
            this.auditStatus = this.searchResult.canClaim;

            console.log('111🚀🥶💩~ this.showContentStatus', this.showContentStatus);
            this.disabledSearch = false;
            console.log('🚀 ~ .then ~ this.disabledSearch :', this.disabledSearch);
            console.log('111🚀 ~ .then ~ this.searchResult.canClaim:', this.searchResult.canClaim);
          });
      },
      /**
       * * 跳转到详情页
       * @param {Object} item - 学员信息对象
       */
      jumpToDetail(item) {
        console.log('跳转到详情页', item);
        if (!item.appealId) return;
        $navigationTo(`partnerApplication/auditResult?info=${JSON.stringify(item)}`);
      },
      /**
       * * 认领按钮点击事件
       */
      claimBtn() {
        this.showClaimModal = true;
      },

      /**
       * * 确认认领
       * @description
       *   1. 点击认领按钮
       *   2. 调用认领接口
       *   3. 认领成功则关闭 Modal
       *   4. 认领失败则提示
       */
      async confirmClaim() {
        try {
          this.isLoading = true;
          httpUser
            .get('zx/wap/claim/appeal/claim-user', {
              userId: this.searchResult.userId
            })
            .then((res) => {
              console.log('认领成功:', res);
              //  根据返回值状态 判断显示  认领成功 、 认领失败
              if (res.data.code === 20000) {
                this.auditStatus = 2; // 认领成功
                this.isLoading = false;
                return;
              }
              if (res.data.code === 50001) {
                this.auditStatus = 3; // 认领失败
                this.isLoading = false;
                return;
              }
              if (res.data.code === 50002 || res.data.code === 50003) {
                uni.showModal({
                  title: '温馨提示',
                  content: res.data.message,
                  showCancel: false
                });
                this.isLoading = false;
              }
            });
        } catch (error) {
          console.error('认领失败:', error);
          // this.auditStatus = 3; // 认领失败
          this.showClaimModal = false;
          this.isLoading = false;
        }
      },

      cancelClaim() {
        uni.showToast({
          title: '认领失败',
          icon: 'error'
        });
        this.showClaimModal = false;
      },
      /**
       * 获取审核信息
       *  */
      async getData(isPage) {
        try {
          console.log('开始🚀 ~ getData ~ this.isPassLoading:', this.isPassLoading);
          if (this.isPassLoading) return;
          this.isPassLoading = true;
          const res = await httpUser.get('zx/wap/claim/records', {
            pageNum: this.pageNum,
            pageSize: this.pageSize,
            type: this.currentIndex
          });
          console.log('哈哈哈🚀 ~ getData ~ res:', res);
          if (res.data.data.data.length === 0) {
            this.finished = true;
            this.auditList = [];
            this.total = 0;
            this.isPassLoading = false;
            console.log('结束-没有数据🚀 ~ getData ~ this.isPassLoading:', this.isPassLoading);
            return;
          }
          // console.log(newData, '有数据的开始-newData');
          const statusMap = {
            1: 1,
            '2_0': 2,
            '2_1': 3,
            '2_2': 4
          };
          const newData = res.data.data.data.map((item) => ({
            ...item,
            applyTime: item.applyTime?.substring(0, 10),
            auditTime: item.auditTime?.substring(0, 10),
            showStatus: statusMap[item.claimType === '1' ? '1' : `${item.claimType}_${item.appealStatus}`] || 0
          }));
          if (isPage) {
            this.auditList = [...this.auditList, ...newData];
          } else {
            this.auditList = newData;
          }
          this.total = res.data.data.totalItems;
          this.isPassLoading = false;
          this.$forceUpdate();
          console.log('🚀 ~ getData ~ this.auditList:', this.auditList);

          console.log('结束2🚀 ~ getData ~ this.isPassLoading:', this.isPassLoading);
          // return res;
        } catch (error) {
          if (error.message !== '任务已取消') {
            console.error('请求失败:', error);
            this.isPassLoading = false;
            this.auditList = []; // 数据加载失败时清空列表或设置默认值
          }
        }
      },

      async tabsClick(item) {
        this.currentIndex = item.index;
        console.log('🚀 ~ tabsClick ~ this.currentIndex:', this.currentIndex);
        this.pageNum = 1;
        this.auditList = [];
        this.finished = false;
        uni.setStorageSync('currentStatus', this.currentIndex);
        await this.getData();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .partner {
    width: 100%;
    height: 100vh;
    box-sizing: border-box;
    overflow-x: hidden;
  }

  .modal-content {
    width: 622rpx;
    height: 344rpx;
    background: url('https://document.dxznjy.com/dxSelect/5f09dfac-9808-4c2c-a474-0cb702a1df5d.png') no-repeat center / 100%;
    padding: 0;
    border-radius: 40rpx;
    box-sizing: border-box;

    .close-icon {
      width: 40rpx;
      height: 40rpx;
      background: url('https://document.dxznjy.com/dxSelect/55db650b-f99e-40dd-8dec-3cd85746c65c.png') no-repeat center / 100%;
      position: absolute;
      top: 24rpx;
      right: 24rpx;
    }

    .smile-box {
      width: 112rpx;
      height: 112rpx;
      display: block;
      margin: 80rpx auto 0;
    }

    .show-title {
      height: 50rpx;
      color: #3f4a2f;
      line-height: 50rpx;
      text-align: center;
    }

    .show-desc {
      height: 50rpx;
      color: #a4a59a;
      line-height: 50rpx;
      text-align: center;
    }

    .show-student {
      height: 44rpx;
      line-height: 44rpx;
      text-align: center;
    }

    .submit-btn {
      width: 368rpx;
      height: 84rpx;
      line-height: 84rpx;
      text-align: center;
      background: linear-gradient(90deg, #ef9d4f 0%, #ea643e 100%);
      border-radius: 42rpx;
      margin: 0 auto;
    }
  }

  .top {
    width: 100vw;
    width: 100%;
    height: 464rpx;
    box-sizing: border-box;

    .search-box {
      width: 100%;
      height: 138rpx;
      margin: 0 auto 24rpx;
      background: #f3f8fc;
      border-radius: 32rpx;
      box-sizing: border-box;
      background: url('https://document.dxznjy.com/dxSelect/8f5bc0e2-ddaa-45e9-807d-3530f437a86c.png') no-repeat center / 100%;

      .search-title {
        height: 48rpx;
        line-height: 48rpx;
        font-size: 28rpx;
        width: 112rpx;
        font-family: AlibabaPuHuiTi_3_85_Bold;
        font-size: 28rpx;
        color: #3c725e;
        font-style: normal;
        margin-left: 30rpx;
      }

      .search-content {
        height: 88rpx;
        padding: 0 10rpx 0 24rpx;

        .search-content-input {
          width: 500rpx;
        }

        .search-content-button {
          width: 144rpx;
          height: 66rpx;
          border-radius: 24rpx;
          font-family: AlibabaPuHuiTi_3_85_Bold;
          color: #333333;
          line-height: 66rpx;
          text-align: center;
          font-style: normal;

          /deep/ .u-reset-button {
            color: #000000 !important;
          }
        }
      }
    }
  }

  .no-data {
    position: relative;
    width: 710rpx;
    margin: auto;
    text-align: center;

    .no-data-image {
      width: 74rpx;
      height: 76rpx;
      display: block;
      margin: 16rpx auto;
    }
  }

  .jumpToDetail {
    width: 686rpx;
    line-height: 74rpx;
    margin: 0 auto;
    background: #339378;
    border-radius: 38rpx;
    position: fixed;
    bottom: 32rpx;
  }
</style>
<style>
  .u-tabs__wrapper__nav__line {
    left: 14rpx !important;
  }
</style>
