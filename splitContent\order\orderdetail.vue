<template>
  <view class="plr-30 pb-20">
    <view v-if="detail_s.payStatus == 0" class="f-26 t-c radius-8 tobe_paid button_right_css">
      {{ detail_s.payStatusName }}
    </view>
    <view v-if="detail_s.payStatus == 1" class="f-26 radius-8 t-c paid button_right_css">{{ detail_s.payStatusName }}</view>
    <view v-if="detail_s.payStatus == 2" class="f-26 radius-8 t-c button_right_css completed">
      {{ detail_s.payStatusName }}
    </view>
    <view v-if="detail_s.payStatus == 3" class="c-ff f-26 t-c button_right_css refunded">{{ detail_s.payStatusName }}</view>
    <view v-if="detail_s.payStatus == 4" class="f-26 t-c button_right_css completed">{{ detail_s.payStatusName }}</view>
    <view v-if="detail_s.payStatus == 5" class="c-ff f-26 t-c button_right_css paid">{{ detail_s.payStatusName }}</view>
    <view v-if="detail_s.payStatus == 6" class="f-26 t-c tobe_paid button_right_css">{{ detail_s.payStatusName }}</view>
    <view v-if="detail_s.payStatus == 7" class="f-26 t-c paid button_right_css">{{ detail_s.payStatusName }}</view>
    <view v-if="detail_s.payStatus == 8" class="f-26 t-c paid button_right_css">{{ detail_s.payStatusName }}</view>
    <view v-if="detail_s.payStatus == 9" class="c-ff f-26 t-c button_right_css refunded">{{ detail_s.payStatusName }}</view>
    <view v-if="detail_s.payStatus == 10" class="c-ff f-26 t-c button_right_css refunded">{{ detail_s.payStatusName }}</view>
    <view v-if="detail_s.payStatus == 11" class="c-ff f-26 t-c button_right_css tobe_paid">{{ detail_s.payStatusName }}</view>
    <view v-if="detail_s.payStatus == 12" class="c-ff f-26 t-c button_right_css paid">{{ detail_s.payStatusName }}</view>
    <view v-if="detail_s.payStatus == 13" class="f-26 radius-8 t-c paid button_right_css">{{ detail_s.payStatusName }}</view>
    <view class="p-30 radius-20 mb-30 bg-ff" v-if="detail_s != null">
      <view class="f-28 bold mb-30" style="line-height: 76rpx; border-bottom: 1px solid #e7e7e7">
        <text>订单编号:</text>
        <text>{{ detail_s != null ? detail_s.orderNo : '' }}</text>
      </view>
      <view class="flex-dir-row" v-if="orderType == 0">
        <view class="image_order_css radius-15 mr-20">
          <!-- :detail_s.mealInfo.mealImage -->
          <image :src="detail_s.piUserOrderGoodsList[0].goodsPicUrl" class="wh100"></image>
        </view>
        <view class="flex-box f-28">
          <view>
            <!-- :detail_s.mealInfo.mealName  -->
            <view class="goods_name mb-15">类别: {{ detail_s.piUserOrderGoodsList[0].goodsName }}</view>
            <view class="mb-15" v-if="detail_s.piUserOrderGoodsList[0].goodsType == 3">
              <text v-if="detail_s.piUserOrderGoodsList[0].specPriceQuantity > 0">
                <text>自有课程：&nbsp;&nbsp;&nbsp;</text>
                <text>{{ detail_s.piUserOrderGoodsList[0].specPriceQuantity }}</text>
                ；
              </text>
              <text v-if="detail_s.piUserOrderGoodsList[0].specPriceTwoQuantity >= 0">
                <text>交付课程：&nbsp;&nbsp;&nbsp;</text>
                <text>{{ detail_s.piUserOrderGoodsList[0].specPriceTwoQuantity }}</text>
                ；
              </text>
            </view>
            <view class="mb-15" v-else>
              <text v-if="detail_s.piUserOrderGoodsList[0].specLevelOneType">
                <text>{{ detail_s.piUserOrderGoodsList[0].specLevelOneType }}:</text>
                <text>{{ detail_s.piUserOrderGoodsList[0].specLevelOneName }}</text>
                ；
              </text>
              <text v-if="detail_s.piUserOrderGoodsList[0].specLevelTwoType">
                <text>{{ detail_s.piUserOrderGoodsList[0].specLevelTwoType }}:</text>
                <text>{{ detail_s.piUserOrderGoodsList[0].specLevelTwoName }}</text>
                ；
              </text>
              <text>
                <text>数量: x {{ detail_s.piUserOrderGoodsList[0].purchaseQuantity }}</text>
              </text>
            </view>
            <view v-if="detail_s.piUserOrderGoodsList[0].goodsType != 6" class="f-24 price_color mt-8">
              ￥
              <text class="f-32 bold">{{ detail_s.payAmount }}</text>
            </view>
            <view v-else class="f-24 price_color mt-8">
              <text class="f-32 bold">{{ detail_s.payAmount }}</text>
              <text class="c-55">鼎币</text>
            </view>
          </view>
        </view>
      </view>
      <view class="flex-dir-row" v-if="orderType == 1">
        <view class="image_order_css radius-15 mr-20">
          <!-- :detail_s.mealInfo.mealImage -->
          <image :src="detail_s.goodsPicUrl" class="wh100"></image>
        </view>
        <view class="flex-box f-28">
          <view>
            <!-- :detail_s.mealInfo.mealName  -->
            <view class="goods_name mb-15">类别: {{ detail_s.commissionMealName }}</view>
            <view class="mb-15">
              <text>
                <text>数量: x {{ detail_s.mealNum }}</text>
              </text>
            </view>
            <view class="mt-8">
              <text>金额: ￥{{ detail_s.commissionMealPrice }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="bg-ff p-30 f-24 radius-15" v-if="detail_s != null && detail_s.remark != '' && detail_s.remark != undefined">
      <view class="c-8896 bold mb-15">订单备注</view>
      <view class="bg-f7 p-30 radius-10">
        <view class="f-30">{{ detail_s != null ? detail_s.remark : '无' }}</view>
      </view>
    </view>
    <view
      class="mt-30 bg-ff p-30 order_css f-24 radius-15 c-33 lh-36"
      :style="{ height: detail_s != null && detail_s.remark ? useHeight + 'rpx' : noRamrkHeight + 'rpx' }"
      v-if="orderType == 1"
    >
      <view class="f-28">
        <view class="flex-a-c flex-x-s">
          <text class="mtb-25">创建时间: {{ detail_s != null ? detail_s.createdTime : '' }}</text>
        </view>
        <view class="flex-a-c flex-x-s">
          <text>有效期: {{ detail_s != null ? detail_s.mealNum : '' }}年</text>
        </view>
        <view class="flex-a-c flex-x-s">
          <text class="mtb-25">套餐生效时间: {{ detail_s != null ? detail_s.mealStartTime : '' }}</text>
        </view>
        <view class="flex-a-c flex-x-s">
          <text class="mb-25">套餐到期时间: {{ detail_s != null ? detail_s.mealEndTime : '' }}</text>
        </view>
      </view>
    </view>
    <view
      class="mt-30 bg-ff p-30 order_css f-24 radius-15 c-33 lh-36"
      :style="{ height: detail_s != null && detail_s.remark ? useHeight + 'rpx' : noRamrkHeight + 'rpx' }"
      v-if="orderType == 0"
    >
      <view class="f-28" style="border-bottom: 1px solid #e7e7e7" v-if="detail_s.piUserOrderGoodsList[0].goodsType != 1">
        <view class="flex-a-c flex-x-s">
          <text class="mtb-25">创建时间: {{ detail_s != null ? detail_s.createdTime : '' }}</text>
        </view>
      </view>
      <view v-else>
        <view class="f-28" style="margin-top: 30rpx; margin-bottom: 24rpx; border-left: 4rpx solid #339378">
          <text class="pl-20 bold">收货信息</text>
        </view>
        <view class="flex-a-c flex-x-s f-28">
          <text class="mb-25">收货人: {{ detail_s != null ? detail_s.buyerName : '' }}</text>
        </view>
        <view class="flex-a-c flex-x-s f-28">
          <text class="mb-25">收货人手机号: {{ detail_s != null ? detail_s.buyerPhone : '' }}</text>
        </view>
        <view class="flex-a-c flex-x-s f-28">
          <text class="mb-25">收货人地址: {{ detail_s != null ? detail_s.address : '' }}</text>
        </view>
      </view>
      <!--       <view class="flex-dir-row flex-a-c mt-24" v-if="showTrialClass">
                <text class="c-8896">推荐人姓名</text>
                <text class="c-55">{{ detail_s != null?detail_s.referrerName:"" }}</text>
            </view> -->
    </view>

    <!-- 商品推荐 -->
    <!-- <view class="mt-40 flex-c">
        	<u-line color="#ccc" length="36%" dashed></u-line>
        	<text class="mlr-20 f-32 bold">热门推荐</text>
        	<u-line color="#ccc" length="36%" dashed></u-line>
        </view>
        <view class="courseList pt-30">
        	<block v-for="(item,index) in infoLists.list" :key="index">
        		<view class="courseItem radius-20 pb-10 positionRelative"
        			@tap.stop="skintap('pages/index/courseDetail?id='+item.courseId)">
        			<view class="courseimg relative">
        				<image :src="item.courseImage" class="wh100" mode="widthFix"></image>
        			</view>
        			<view class="positionAbsolute courseItem_tig">
        				<image v-if="item.courseId=='f2cf0e76538473a7179c993674e09bdf'" :src="imgHost+'alading/correcting/mustBuy_icon.png'" class="wh100" mode="widthFix"></image>
        				<image v-if="item.courseId=='c499556f9da64dce19b723296c884bc4'" :src="imgHost+'alading/correcting/recommend_icon.png'" class="wh100" mode="widthFix"></image>
        			</view>
        			<view class="mtb-20 pl-20 pr-20">
        				<view class="bold f-28">{{ item.courseName }}</view>
        				<view class="color_red font12 mtb-16">会员价 <span class="bold f-34">￥{{item.memberPrice}}</span></view>
        				<view class="displayflex color_grey f-24" style="justify-content: space-between;">
        					<view class="">原价<text style="text-decoration: line-through;">￥{{item.originalPrice}}</text></view>
        					<view class="">{{item.studyNumber}}+人付款</view>
        				</view>
        			</view>
        
        		</view>
        	</block>
        	<view v-if="no_more && infoLists.list.length>0" style="width: 100%;text-align: center;">
        		<u-divider text="到底了"></u-divider>
        	</view>
        </view> -->
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  import Sumperman from '@/common/superman.js';
  export default {
    data() {
      return {
        pageShow: true,
        tabindex: 0,
        orderId: null,
        detail_s: null,
        codeimg: null,
        showTrialClass: false, //是否展示推荐人（根据是否是39.9这个商品id来判断）
        useHeight: 0, //除头部之外高度
        noRamrkHeight: 0, // 无订单备注的高度
        type: 1, // 1课程 2学习超人
        infoLists: {}, // 课程
        page: 1,
        no_more: false,
        orderType: 0
      };
    },
    onLoad(e) {
      this.orderId = e.orderId;
      this.type = e.type;
      // this.course();
      if (uni.getStorageSync('orderDetail')) {
        this.detail_s = JSON.parse(uni.getStorageSync('orderDetail'));
        console.log(this.detail_s);
      }
      if (uni.getStorageSync('orderType')) {
        this.orderType = uni.getStorageSync('orderType');
      }
      // ,JSON.stringify(item)
    },
    onShow() {
      // this.orderdetail();
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 610;
          this.noRamrkHeight = h - 365;
        }
      });
    },
    onReachBottom() {
      if (this.page >= this.infoLists.totalPage) {
        this.no_more = true;
        return false;
      }
      this.course(true, ++this.page);
    },
    methods: {
      async orderdetail() {
        let _this = this;
        let res;
        if (this.type == 1) {
          res = await $http({
            url: 'zx/order/userCourseOrderDetail',
            data: {
              orderId: _this.orderId
            }
          });
        } else {
          res = await $http({
            url: 'zx/order/mealOrderDetail',
            data: {
              orderMealId: _this.orderId
            }
          });
        }
        if (res) {
          _this.detail_s = res.data;
          let courseLabel;
          if (this.type == 1) {
            courseLabel = await Sumperman.getCourseLabel(_this.detail_s.orderCourseList[0].courseId);
          }
          if (courseLabel == 1) {
            _this.showTrialClass = true;
          } else {
            _this.showTrialClass = false;
          }
        }
      },
      async code() {
        let _this = this;
        const res = await $http({
          url: 'zx/order/getOrderCheckQr',
          data: {
            orderId: _this.id
          }
        });
        if (res) {
          _this.codeimg = res.data;
        }
      },

      async course(isPage, page) {
        let _this = this;
        const res = await $http({
          url: 'zx/course/courseList',
          data: {
            indexShow: 1,
            cityCode: '',
            cateId: '',
            page: page || 1,
            cateType: 1
          }
        });
        if (res) {
          if (isPage) {
            let old = _this.infoLists.list;
            _this.infoLists.list = [...old, ...res.data.list];
          } else {
            _this.infoLists = res.data;
          }
        }
      },
      // 状态切换
      changetab(index) {
        this.tabindex = index;
      }
    }
  };
</script>

<style lang="scss" scoped>
  .goods_name {
    -webkit-line-clamp: 2;
    overflow: hidden;
    -webkit-box-orient: vertical;
    display: -webkit-box;
    word-break: break-all;
  }

  .codebg {
    width: 296upx;
    height: 296upx;
  }

  .c-8896 {
    color: #888896;
  }

  .w480 {
    width: 480rpx;
    display: inline-block;
  }

  .order_css {
    .flex-dir-row {
      justify-content: space-between;
    }
  }

  .image_order_css {
    width: 144rpx;
    height: 160rpx;
  }

  .codeimg {
    position: absolute;
    width: 260upx;
    height: 260upx;
    top: 18upx;
    left: 18upx;
  }

  .courseList {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .courseItem {
    width: 330upx;
    border-radius: 20upx;
    background-color: #fff;
    margin-bottom: 30rpx;
  }

  .close_icon {
    width: 44rpx;
    position: absolute;
    bottom: 96rpx;
    left: 47%;
  }

  .after-Sales {
    width: 160rpx;
    height: 60rpx;
    color: #2e896f;
    font-size: 30rpx;
    text-align: center;
    line-height: 60rpx;
    border-radius: 35rpx;
    border: 1px solid #2e896f;
  }

  .button_right_css {
    width: 100%;
    line-height: 62rpx;
  }

  .tobe_paid {
    background-color: #fff0ec;
    color: #fa380e;
  }

  .paid {
    background-color: #fff2e3;
    color: #fd9b29;
  }

  .completed {
    background-color: #eaffe7;
    color: #469880;
  }

  .channel {
    background-color: #c6c6c6;
  }

  .refund {
    background-color: #ea6031;
  }

  .drawback {
    background-color: #bf61de;
  }

  .refunded {
    background-color: #f7f7f7;
    color: #888896;
  }
</style>
