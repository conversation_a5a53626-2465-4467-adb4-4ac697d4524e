<template>
  <view class="content">
    <image src="https://document.dxznjy.com/dxSelect/loading.gif" style="width: 300upx" mode="widthFix"></image>
  </view>
</template>

<script>
const { $http, $navigationTo } = require('@/util/methods.js');
export default {
  components: {},
  data() {
    return {
      type: '1', //分享进入类型 1 商品   2超人俱乐部会员  3俱乐部  9会议 6新商品  8e签宝合同10活动页
      contractId: '',
      courseId: '', //超人会员id或者商品id
      invitationCode: '', //超人分享码
      secCode: '',
      userId: '', //分享人code
      imgLookId: '', //分享海报进入id,根据当前参数查询type等值
      isNeedUser: '', //二维码是否需要用户信息
      QRInfo: {}, //根据二维码获取的信息
      userinfo: {},
      optionsInfo: {},
      shareId: '', //分享id
      invitationInfo: {}, // 邀请信息
      shareSource: '' // 分享来源
    };
  },
  onLoad(options) {
    let token = uni.getStorageSync('token');
    if (!token) {
      uni.setStorageSync('optionsInfo', JSON.stringify(options));
      $navigationTo('Personalcenter/login/login');
    } else {
      this.bannerId = options.bannerId || '';
      // app分享到小程序的商品引导下载app
      this.appSource = options.source || '';
      this.shareSource = options.shareSource || '';
      this.getShard(options);
    }
  },
  onShow() {
    let token = uni.getStorageSync('token');
    if (uni.getStorageSync('optionsInfo') && token) {
      this.optionsInfo = JSON.parse(uni.getStorageSync('optionsInfo'));
      this.shareSource = this.optionsInfo.shareSource || '';
      this.bannerId = this.optionsInfo.bannerId || '';
      uni.removeStorageSync('optionsInfo');
      this.getShard(this.optionsInfo);
    }
  },

  methods: {
    getShard(options) {
      this.imgLookId = options.scene;
      if (options.type == undefined) {
        //没有type进入的是分享海报进入
        this.getProductDetail();
      } else {
        this.type = options.type;
        if (this.type == '8') {
          this.contractId = options.contractId;
          this.toPageByType();
        } else if (this.type == '10') {
          console.log(options)
          console.log('*************************************')
          var path = `${options.path}?shareUserCode=${options.shareUserCode}&activityId=${options.activityId}&bannerId=${options.bannerId}`;
          this.toPageByType(path);
        } else {
          this.userId = options.scene;
          if (options.id != undefined) {
            this.courseId = options.id;
          }
          if (options.type == 6) {
            this.getUserInfoByUserId();
          } else {
            this.setUserId();
          }
        }
      }
    },
    // 设置userId
    async setUserId() {
      let data = {
        userId: this.userId,
        invitationCode: this.invitationCode,
        secCode: this.secCode
      };
      await uni.setStorageSync('invitationInfo', data);
      this.toPageByType();
    },

    // 分享海报进入根据编码获取信息
    async getProductDetail() {
      let _this = this;
      const res = await $http({
        url: 'zx/user/getSceneValueNew',
        data: {
          scene: _this.imgLookId
        }
      });
      if (res) {
        _this.QRInfo = res.data;
        _this.type = res.data.type;
        _this.invitationCode = res.data.userId;
        _this.secCode = _this.QRInfo.secCode;
        _this.userId = res.data.userId;
        _this.courseId = res.data.courseId;
        _this.isNeedUser = res.data.isNeedUser;
        _this.setUserId();
        if (_this.type == 6 || _this.type == 9) {
          let shardInfo = { mobile: res.data.mobile, nickName: res.data.nickName, userId: res.data.userId, enterType: 'qrcode' };
          uni.setStorageSync('shardInfo', shardInfo);
        }
      }
    },
    //根据UserId 获取分享人信息
    async getUserInfoByUserId() {
      let _this = this;
      const res = await $http({
        url: 'zx/user/getUserInfoByUserId',
        data: {
          userId: _this.imgLookId
        }
      });
      if (res) {
        _this.setUserId();
        if (_this.type == 6) {
          let shardInfo = { mobile: res.data.mobile, nickName: res.data.nickName, userId: res.data.userId, enterType: 'link' };
          uni.setStorageSync('shardInfo', shardInfo);
        }
      }
    },
    // 根据type  1跳转详情界面   2,3,4跳转H5
    async toPageByType(url) {
      console.log(this.type);
      let path = '';
      if (this.type == '1') {
        await uni.setStorageSync('user_id', this.userId);
        path = `/Coursedetails/productDetils?scene=${this.userId}&type=${this.type}&id=${this.courseId}&bannerId=${this.bannerId}&appSource=${this.appSource}&shareSource=${this.shareSource}`;
      } else if (this.type == '4') {
        uni.reLaunch({
          url: `/Recharge/payment/payment?type=${this.type}&id=${this.courseId}`
        });
      } else if (this.type == 9) {
        await uni.setStorageSync('user_id', this.userId);
        await uni.setStorageSync('course_id', this.courseId);
        path = `/meeting/meetIndex?id=${this.courseId}`;
        uni.reLaunch({
          url: `${path}`
        });
        return;
      } else if (this.type == 6) {
        path = `/Coursedetails/productDetils?id=${this.courseId}&bannerId=${this.bannerId}&isNeedUser=${this.isNeedUser}&appSource=${this.appSource}&shareSource=${this.shareSource}`;
        uni.reLaunch({
          url: `${path}`
        });
        return;
      } else if (this.type == '8') {
        path = `/signature/contract/manageSigning?id=${this.contractId}`;
      } else if (this.type == '10') {
        path = url
        uni.reLaunch({
          url: `${path}`
        });
      } else {
        await uni.setStorageSync('secCode', this.secCode);
        uni.reLaunch({
          url: `/supermanClub/superman/superman?type=${this.type}&secCode=${this.secCode}`
        });
        // path = `/Coursedetails/mealDetail?scene=${uni.getStorageSync('user_id')}&type=${this.type}&id=${this.courseId}?invitationCode=${this.invitationCode}`;
      }
      // uni.redirectTo({
      uni.reLaunch({
        url: `${path}`
      });
    },

    // 获取首页信息
    async homeData() {
      let _this = this;
      const res = await $http({
        url: 'zx/user/userInfoNew'
      });
      if (res) {
        _this.userinfo = res.data;
      }
    }
  }
};
</script>

<style>
.content {
  width: 100%;
  height: 100vh;
  background-color: #fbfbfb;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>