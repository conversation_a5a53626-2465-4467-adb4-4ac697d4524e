<template>
  <view>
	  <view class="box-bg flex-x" :style="{ paddingTop: statusBarHeight + 'rpx'}">
		<uni-icons type="left" size="22" color="#000" @click="backClick"></uni-icons>
		<view style="margin-left: 37%;"  class="f-32">
			付款
		</view>
	  </view>
    <view class="center-card positionRelative mlr-30 mb-30" :style="{ height: useHeight + 'rpx' }" v-if="show">
      <view class="logobox pb-55">
        <image :src="imgHost + 'dxSelect/recharge/pay-success.png'" class="success-img"></image>
        <view>充值成功</view>
      </view>
      <view class="studentinfo">
        <view style="margin-top: 120rpx; margin-left: 100rpx">
          <view class="studentinfo-text">
            <text>学员姓名：</text>
            <text>{{ paylist.studentName }}</text>
          </view>
          <view class="studentinfo-text">
            <text>充值类型：</text>
            <text>{{ paylist.type == 1 ? '充值软件使用费' : '充值课程包' }}</text>
          </view>
          <view class="studentinfo-text" v-if="paylist.type == 2">
            <text>充值课程包：</text>
            <text>{{ paylist.packageName }}</text>
          </view>
          <view class="studentinfo-text" v-if="paylist.type == 1">
            <text>充值学时：</text>
            <text>{{ paylist.courseNum }}节</text>
          </view>
          <view class="studentinfo-text" v-if="paylist.deliverNum != 0 && schoolType == 3">
            <text>充值交付学时：</text>
            <text>{{ paylist.deliverNum }}节</text>
          </view>

          <view class="studentinfo-text" v-if="paylist.selfDeliverNum != '' && paylist.selfDeliverNum > 0 && paylist.deliverNum < 1">
            <text>自行交付学时：</text>
            <text>{{ paylist.selfDeliverNum }}节</text>
          </view>

          <view class="studentinfo-text">
            <text>充值说明：</text>
            <text class="lh-50">{{ paylist.description }}</text>
          </view>
        </view>

        <view v-if="!isShowFillBtn">
          <view style="width: 100%; display: flex; justify-content: center; margin-top: 60rpx">
            <view class="suceesave" style="margin-bottom: 244rpx" @click="saveImage">
              <image src="https://document.dxznjy.com/dxSelect/image/download.png" class="link-img"></image>
              <text>保存付款记录</text>
            </view>
          </view>
        </view>
        <view v-else>
          <view style="width: 100%; display: flex; justify-content: center; margin-top: 60rpx">
            <view class="suceesave" style="margin-bottom: 40rpx" @click="saveImage">
              <image src="https://document.dxznjy.com/dxSelect/image/download.png" class="link-img"></image>
              <text>保存付款记录</text>
            </view>
          </view>

          <view style="width: 100%; display: flex; justify-content: center">
            <view class="fill-view" @click="fillClassTable()">填写上课信息对接表</view>
          </view>
        </view>

        <view class="footercode">
          <view class="footercode-left">
            <image src="https://document.dxznjy.com/dxSelect/recharge/logo.jpg" style="width: 100rpx; height: 100rpx"></image>
          </view>
          <view class="footercode-right">关注鼎校公众号，高效学习倡导者，帮助K1 2学员优化学习策略，让学习更简单！</view>
        </view>
      </view>
      <uni-popup ref="payPopup" type="top" mask-background-color="rgba(0,0,0,0)">
        <view class="plr-60">
          <view class="t-c bg-ff flex-c ptb-25 radius-50 mt-80 notify">
            <image :src="imgHost + 'dxSelect/image/success.png'" class="hint-icon"></image>
            <view class="f-34 ml-15">保存收款码成功</view>
          </view>
        </view>
      </uni-popup>
    </view>

    <view v-if="!show">
      <image :src="path" mode="widthFix" style="width: 100%" :show-menu-by-longpress="true"></image>
      <l-painter isCanvasToTempFilePath custom-style="position: fixed; left: 200%" ref="painter" @success="path = $event">
        <l-painter-view css="">
          <l-painter-view css="background: #fff; padding: 30rpx; color: #333;borderRadius: 15rpx;">
            <l-painter-view css="text-align: center;">
              <l-painter-image :src="imgHost + 'dxSelect/recharge/pay-success.png'" css="margin-top: 90rpx; width: 100rpx;height: 100rpx;" />
            </l-painter-view>

            <l-painter-view css="text-align: center;">
              <l-painter-text text="付款成功" css="padding-bottom: 10rpx; color: #000; font-size: 30rpx;margin-top: 20rpx;" />
            </l-painter-view>

            <l-painter-view css="border-top:1px solid #EFEFEF;margin-top:120rpx;"></l-painter-view>

            <l-painter-view css="margin-top:65rpx;margin-left:120rpx;">
              <l-painter-text :text="'学员姓名:' + paylist.studentName" css="color: #333; font-size: 30rpx;display: block;" />
              <l-painter-text
                v-if="schoolType != 3"
                :text="'充值类型:' + paylist.type == 1 ? '充值软件使用费' : '充值课程包'"
                css="color: #333; font-size: 30rpx;margin-top:30rpx;display: block;"
              />
              <l-painter-text :text="'充值学时:' + paylist.courseNum + '节'" css="color: #333; font-size: 30rpx;margin-top:30rpx;display: block;" />
              <l-painter-text
                v-if="paylist.deliverNum != 0 && schoolType == 3"
                :text="'充值交付学时:' + paylist.deliverNum + '节'"
                css="color: #333; font-size: 30rpx;margin-top:30rpx;display: block;"
              />
              <l-painter-text
                v-if="paylist.selfDeliverNum != '' && paylist.selfDeliverNum > 0 && paylist.deliverNum"
                :text="'自行交付学时:' + paylist.selfDeliverNum + '节'"
                css="color: #333; font-size: 30rpx;margin-top:30rpx;display: block;"
              />
              <l-painter-text v-if="paylist.type == 2" :text="'充值课程包：' + paylist.packageName" css="color: #333; font-size: 30rpx;margin-top:30rpx;display: block;" />
              <l-painter-text :text="'支付金额:￥' + paylist.price" css="color: #333; font-size: 30rpx;margin-top:30rpx;display: block;" />
              <l-painter-text :text="'充值说明:' + paylist.description" css="color: #333; font-size: 30rpx;margin-top:30rpx;display: block;" />
            </l-painter-view>

            <l-painter-view css="margin-top:245rpx;border-top:1px solid #EFEFEF;padding-top:60rpx;position: relative;">
              <l-painter-image
                src="https://document.dxznjy.com/dxSelect/recharge/logo.jpg"
                css="width: 80rpx;height: 80rpx;display: block;position: absolute;top:0rpx;left:0rpx;"
              />
              <l-painter-text text="关注鼎校公众号，高效学习倡导者，帮助" css="color: #666; font-size: 28rpx;display: block;padding-left:100rpx;" />
              <l-painter-text text="K1 2学员优化学习策略，让学习更简单！" css="color: #666; font-size: 28rpx;display: block;padding-left:100rpx;" />
            </l-painter-view>
          </l-painter-view>
        </l-painter-view>
      </l-painter>
    </view>
	<!-- 处理所有返回 -->
	<!-- <page-container v-if="showPage" :show="showPage" :duration="false" :overlay="false" @afterleave="beforeleave"></page-container> -->
  </view>
</template>

<script>
  const { $navigationTo, $http } = require('@/util/methods.js');
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  export default {
    data() {
      return {
        useHeight: 0,
        type: '',
        imgHost: getApp().globalData.imgsomeHost,
        level: false,
        screenHeight: '', // 屏幕高度
        screenWidth: '', // 屏幕高度
		navBarHeight: '', // 设备导航栏高度
		statusBarHeight: '', // 设备状态栏高度
        tempFilePath: '', //
        show: true, // 是否保存付款记录按钮
        payImg: '',
        saveImg: '',
        orderId: '', // 订单id
        sharePay: false,
        paylist: {}, // 支付信息
        orderlist: {}, // 订单支付信息
        flag: false, // 防止重复点击
        schoolType: '',
        path: '',

        //填写上课对接信息表
        studentCode: '',
        isShowFillBtn: false,
        fillId: null,
		
		showPage: true
      };
    },
    onLoad(options) {
      uni.setStorageSync('clearData', true);
      this.orderId = options.orderId;
      this.schoolType = options.schoolType;
      this.type = options.type;
      this.studentCode = options.studentCode;
      this.deliverLen = options.deliverLen;

      if (options.info != undefined) {
        let data = JSON.parse(options.info);
        this.paylist.studentName = options.name;
        this.status = 2;
        this.show = true;
        this.paylist.type = 1;
        this.paylist.courseNum = data.courseLength;
        this.paylist.price = data.totalPrice;
        this.paylist.description = '给' + this.paylist.studentName + '充' + this.paylist.courseNum + '节' + '软件使用费';
        this.paylist.selfDeliverNum = data.selfDeliverCourseLength;
        this.schoolType = options.schoolType;
      } else {
        if (this.schoolType == 3 || this.type == 4 || (this.orderId != undefined && this.orderId != '')) {
          this.getOrderInfo();
        }
      }
    },

    onReady() {
      uni.getSystemInfo({
        success: (res) => {
		  this.statusBarHeight = res.statusBarHeight + 50;
		  this.navBarHeight = this.statusBarHeight + 56;
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          if (this.screenHeight <= 688 && this.screenHeight > 619) {
            // this.useHeight = h - 40;
            this.useHeight = h - (this.navBarHeight + 66);
          } else if (this.screenHeight <= 619) {
            // this.useHeight = h - 80;
            this.useHeight = h - (this.navBarHeight + 106);
          } else if (this.screenHeight > 688 && this.screenHeight <= 736) {
            // this.useHeight = h - 50;
            this.useHeight = h - (this.navBarHeight + 76);
          } else {
            this.level = true;
            // this.useHeight = h - 240;
            this.useHeight = h - (this.navBarHeight + 266);
          }
        }
      });
    },

    onShow() {
      //新门店才需要
      if (this.schoolType == 3) {
        //判断是否第一次充值
        let isFirsyPay = false;
        let isFirsyPayData = uni.getStorageSync('isFirsyPay');
        if (isFirsyPayData) {
          for (let i = 0; i < isFirsyPayData.length; i++) {
            if (isFirsyPayData[i].studentCode == this.studentCode) {
              isFirsyPay = isFirsyPayData[i].isFirsyPay;
            }
          }
        }
        if (isFirsyPay1) {
          this.getFillBtnShow();
        }
        this.isShowFillBtn = isFirsyPay;
      }
    },
    methods: {
		// 自定义导航栏，返回按钮点击事件
		// backClick() {
		// 	this.beforeleave();
		// },
		// beforeleave() {
		// 	this.showPage = false;
		// 	uni.redirectTo({
		// 	  url: '/Coursedetails/tips/lessonTips?offStatus=true'
		// 	});
		// },
      //填写按钮显示
      async getFillBtnShow() {
        let _this = this;
        if (_this.studentCode && _this.studentCode != '') {
          let res = await $http({
            url: 'deliver/web/student/contact/info/getStudentContactInfoInit',
            method: 'get',
            data: {
              studentCode: _this.studentCode,
              orderNo: _this.orderId,
              rechargeHour: _this.deliverLen
            }
          });
          if (res && res.data) {
            _this.fillId = res.data;
          } else {
            _this.fillId = '';
          }
        }
      },
      getHeight() {
        // 获取系统信息
        const systemInfo = uni.getSystemInfoSync();
        // 获取屏幕高度
        this.screenHeight = systemInfo.windowHeight;
        this.screenWidth = systemInfo.windowWidth - 60;
        // 打印屏幕高度
        console.log(this.screenHeight);
      },

      // 获取订单信息
      async getOrderInfo() {
        let res = await $http({
          url: 'znyy/school/recharge/getRechargeOrderInfo',
          method: 'get',
          data: {
            orderId: this.orderId
          }
        });
        console.log(res);
        if (res) {
          this.paylist = res.data;
        }
      },

      saveImage() {
        this.show = false;
        setTimeout(() => {
          this.$util.alter('请长按保存');
        }, 1000);
      },

      fillClassTable() {
        uni.navigateTo({
          // url: '/Recharge/onlineJoinTable/onlineClass?orderId='+this.fillId+'&payStatus='+ 0
          url: `/Recharge/onlineJoinTable/onlineClass?orderId=${this.fillId}&&payStatus=0&&isEdit=false`
        });
      },

      getImg() {
        this.show = false;
        this.$refs.painter.canvasToTempFilePathSync({
          fileType: 'jpg',
          // 如果返回的是base64是无法使用 saveImageToPhotosAlbum，需要设置 pathType为url
          pathType: 'url',
          quality: 1,
          success: (res) => {
            console.log(res.tempFilePath);
            // 非H5 保存到相册
            // H5 提示用户长按图另存
            uni.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: function () {
                console.log('save success');
              }
            });
          }
        });
      },

      // 生成海报
      longPress() {
        //长按保存
        uni.showLoading();
        if (this.path !== '') {
          uni.hideLoading();
          // #ifdef MP-WEIXIN
          uni.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              uni.saveImageToPhotosAlbum({
                filePath: this.path,
                success: () => {
                  uni.showModal({
                    title: '保存成功',
                    content: '图片已成功保存到相册，快去分享到您的圈子吧',
                    showCancel: false
                  });
                }
              });
            },
            fail() {
              uni.showModal({
                title: '保存失败',
                content: '您没有授权，无法保存到相册',
                showCancel: false
              });
            }
          });
          // #endif

          // #ifdef APP-PLUS
          this.openAppOption();
          // #endif
          uni.hideLoading();
        } else {
          uni.showModal({
            title: '提示',
            content: '生成海报失败,请重试',
            showCancel: false
          });
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  page {
    padding: 60rpx 30rpx 30rpx 30rpx;

    // 顶部文字和返回箭头
    .top-title {
      position: fixed;
      top: 80rpx;
      margin-top: 80rpx;
      margin-bottom: 25rpx;
      display: flex;
      align-items: center;
      // padding-left: 30rpx;

      .page_title {
        font-size: 34rpx;
        font-family: AlibabaPuHuiTiM;
        color: #000000;
        margin-left: 38.5%;
      }
    }

    .marginT190 {
      margin-top: 190rpx;
    }

    .marginT90 {
      margin-top: 90rpx;
    }

    // 中间白色卡片
    .center-card {
      // width: 630rpx;
      padding: 30rpx;
      background: #ffffff;
      border-radius: 14rpx;
      font-size: 30rpx;
      font-family: AlibabaPuHuiTiR;
      color: #000000;

      // 顶部logo部分
      .logobox {
        text-align: center;

        .logo-img {
          margin-top: 130rpx;
          margin-bottom: 140rpx;
          width: 130rpx;
          height: 58rpx;
        }

        .price {
          font-size: 38rpx;
          font-weight: bold;
          margin-top: 15rpx;
        }
      }
    }

    // 付款成功
    .Paysuccess {
      text-align: center;
      height: 400rpx;
    }

    .success-img {
      width: 100rpx;
      height: 100rpx;
      margin-top: 100rpx;
      margin-bottom: 20rpx;
    }

    // 学员信息部分
    .studentinfo {
      padding-top: 20rpx;
      padding-bottom: 60rpx;
      // margin-left: 120rpx;
      // padding-left: 100rpx;
      // height: 356rpx;
      border-top: 1rpx solid #efefef;

      .studentinfo-text {
        margin-top: 30rpx;
        color: #333333;
      }
    }

    // 收款方
    .Payee {
      border-top: 1rpx solid #efefef;
      border-bottom: 1rpx solid #efefef;
      height: 110rpx;
      display: flex;
      justify-content: space-between;
      line-height: 110rpx;
    }

    // 付款成功保存付款记录按钮
    .suceesave {
      margin-top: 60rpx auto;
      border-radius: 45rpx;
      width: 290rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      font-size: 30rpx;
      font-family: AlibabaPuHuiTiR;
      border: 1rpx solid #2e896f;
      color: #2e896f;
      display: flex;
      align-items: center;
      justify-content: center;

      .link-img {
        width: 42rpx;
        height: 42rpx;
        margin-right: 14rpx;
      }
    }

    .fill-view {
      margin-top: 60rpx auto;
      margin-bottom: 200rpx;
      border-radius: 45rpx;
      width: 290rpx;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      font-size: 28rpx;
      font-family: AlibabaPuHuiTiR;
      background-color: #2e896f;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    // 底部支付按钮
    .aplay-footer-btn {
      // margin-top: 160rpx;

      .buttonFn {
        margin: 0 auto;
        border-radius: 45rpx;
        height: 80rpx;
        line-height: 80rpx;
        text-align: center;
        font-size: 30rpx;
        font-family: AlibabaPuHuiTiR;
        background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
        color: #ffffff;
      }
    }

    // 付款成功底部关注公众号二维码
    .footercode {
      height: 180rpx;
      border-top: 1rpx solid #efefef;
      display: flex;
      align-items: center;
      // margin-top: 200rpx;

      .footercode-left {
        width: 80rpx;
        height: 80rpx;
        background-color: pink;
        margin-right: 20rpx;
      }

      .footercode-right {
        flex: 1;
        font-size: 28rpx;
        font-family: AlibabaPuHuiTiR;
        color: #666666;
      }
    }
  }

  .img_s {
    width: 160rpx;
    height: 160rpx;
  }

  .notify {
    box-shadow: 0rpx 0rpx 20rpx #e0e0e0;
  }

  .hint-icon {
    width: 38rpx;
    height: 38rpx;
  }

  .pay-success {
    width: 100rpx;
    height: 100rpx;
    padding-top: 90rpx;
  }

  .marginB120 {
    margin-bottom: 120rpx;
  }

  .pay-save {
    position: absolute;
    top: 50%;
    z-index: 9;
  }

  .review_btn {
    width: 250upx;
    height: 80upx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45upx;
    font-size: 28upx;
    color: #ffffff;
    line-height: 80upx;
    justify-content: center;
    text-align: center;
  }

  .close_btn {
    width: 250upx;
    height: 80upx;
    color: #2e896f;
    font-size: 28upx;
    line-height: 80upx;
    text-align: center;
    border-radius: 45upx;
    box-sizing: border-box;
    border: 1px solid #2e896f;
  }
  .box-bg {
  	width: 100%;
  	// z-index: 9;
  	// position: fixed;
	padding-left: 24rpx;
	padding-bottom: 30rpx;
	box-sizing: border-box;
	background-color: transparent;
  }
  .flex-x {
  	display: flex;
  	align-items: center;
  }
</style>
