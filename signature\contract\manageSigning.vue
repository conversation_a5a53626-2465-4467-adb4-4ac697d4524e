<template>
  <view class="content f-28">
    <view class="">
      <view class="list_view" v-if="hasValues(listS)">
        <view class="list_head">
          {{ listS.contractTemplateName }}
          <view class="lc_yellow" v-if="listS.status === 1">签署中</view>
          <view class="lc_yellow lc_g" v-if="listS.status === 2">已完成</view>
          <view class="lc_yellow lc_gray" v-if="listS.status === 3">已撤销</view>
          <view class="lc_yellow lc_gray" v-if="listS.status === 4">已过期</view>
          <view class="lc_yellow lc_gray" v-if="listS.status === 5">已拒签</view>
        </view>
        <view class="list_context">
          <text class="list_name">生成时间：</text>
          <view class="bg_green">
            {{ listS.createTime }}
          </view>
        </view>
        <view class="list_context">
          <text class="list_name">发起方：</text>
          <view class="bg_green">
            {{ listS.creatorName }}
          </view>
        </view>
        <view class="list_context">
          <text class="list_name">接收方：</text>
          <view class="bg_green">
            {{ listS.signerName }}
          </view>
        </view>
      </view>
    </view>
    <view class="m-45">
      <button class="nextstep" form-type="submit" @click="submit">查看并签署</button>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        numC: 120,
        selectIndex: 0,
        isContractCreator: '', //是否合同发起人
        page: 1,
        no_more: false,
        listS: {},
        id: ''
      };
    },
    onLoad(option) {
      if (option != null) {
        this.id = option.id;
      }
    },
    onShow() {
      this.fetchContractList();
    },
    methods: {
      async fetchContractList() {
        uni.showLoading({
          title: '加载中...'
        });
        const res = await $http({
          url: 'zx/wap/contract/basic',
          data: {
            contractId: this.id
          }
        });
        if (res) {
          this.listS = res.data;
          uni.hideLoading();
        }
      },
      submit() {
        if (this.listS.signerSignUrl == '') {
          uni.showToast({
            title: '暂无签署链接'
          });
        } else {
          uni.navigateTo({
            url: '/signature/contract/signingPage?url=' + encodeURIComponent(JSON.stringify(this.listS.signerSignUrl))
          });
        }
      },
      // 判断对象是否有值
      hasValues(obj) {
        return Object.keys(obj).some((key) => obj[key] !== null && obj[key] !== undefined && obj[key] !== '');
      }
    }
  };
</script>

<style scoped>
  .content {
    padding: 32rpx;
  }

  .head_tab {
    height: 46rpx;
    padding: 0 10rpx;
    color: #5a5a5a;
    font-size: 28rpx;
    box-sizing: border-box;
    margin: 32rpx 0;
    display: flex;
    justify-content: space-between;
  }

  .active {
    border-bottom: 8rpx solid #339378;
    color: #333333;
    font-weight: 900;
    border-radius: 2rpx;
  }

  .list_view {
    width: 686rpx;
    border-radius: 24rpx;
    padding: 32rpx 24rpx;
    background-color: #ffffff;
    box-sizing: border-box;
    margin-bottom: 32rpx;
    letter-spacing: 3rpx;
  }

  .list_head {
    color: #333333;
    font-size: 32rpx;
    font-weight: 800;
    display: flex;
    justify-content: space-between;
    padding: 0 0 24rpx 0;
    border-bottom: 0.5px solid #f6f7f9;
  }

  ,
  .list_context {
    color: #555555;
    margin-top: 36rpx;
    display: flex;
  }

  .list_name {
    width: 30%;
  }

  .list_context text {
    /* width:172rpx ;
	background-color: pink; */
  }

  .lc_yellow {
    color: #fd9b2a;
    text-align: center;
    font-weight: 300;
    font-size: 26rpx;
    width: 116rpx;
    border: 1px solid #ffe1be;
    background-color: #fdf6ed;
    border-radius: 8rpx;
    margin-right: 16rpx;
  }

  .lc_green {
    color: #006f57;
  }

  .lc_g {
    color: #81e2af;
    border: 1px solid #81e2af;
    background-color: #ecfbf3;
  }

  .lc_gray {
    color: #cccccc;
    border: 1px solid #cccccc;
    background-color: #f7f7f7;
  }

  .list_bot {
    margin-top: 62rpx;
    display: flex;
    flex-direction: row-reverse;
  }

  .btn_b {
    width: 196rpx;
    height: 60rpx;
    border-radius: 60rpx;
    line-height: 60rpx;
    text-align: center;
    margin-left: 32rpx;
  }

  .bg_green {
    flex: 1;
  }

  .b_l {
    background-color: #fff;
    color: #4e9f87;
    border: 1px solid #7baea0;
    margin-left: 32rpx;
  }

  .b_r {
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    color: #ffffff;
  }

  .curriculum_css_no {
    position: relative;
    width: 710rpx;
    margin: auto;
    text-align: center;
  }

  .curriculum_image {
    width: 74rpx;
    height: 76rpx;
    display: block;
    margin: 16rpx auto;
  }
</style>
