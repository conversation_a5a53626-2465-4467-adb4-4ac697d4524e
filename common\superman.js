import { $http } from '@/util/methods.js'; // 全局挂载引入
const { httpUser } = require('@/util/luch-request/indexUser.js');

// 获取用户信息
async function getUserInfo() {
  let data;
  const res = await $http({
    url: 'zx/user/userInfoNew'
  });
  if (res) {
    console.log(111, '用户数据');
    data = res.data;
    uni.setStorageSync('log_userCode', res.data.userCode);
    console.log(data, '用户数据');
  }
  return data;
}

// 获取俱乐部可用等级    end 从最后一个下标删除几个数字
async function getClubLevel() {
  let data;
  const res = await $http({
    url: 'zx/common/moisteningSettingList',
    method: 'get'
  });
  if (res) {
    data = res.data;
    // let levelIndex = -1;
    // for(let i=0;i<res.data.length;i++){
    // 	let item = res.data[i];
    // 	if(item.gradeLevel == nowLevel){
    // 		levelIndex = i;
    // 	}
    // }
    //    data = res.data.splice(0,levelIndex);
  }
  return data;
}

// 进货申请
async function applyOutCode(num) {
  let data = false;
  const res = await $http({
    url: `zx/invitation/code/applyOutCode?num=${num}`
  });
  if (res && res.status == 1) {
    data = true;
  }
  return data;
}

// 取消进货
async function cancelApplyOutCode(outCodeApplyId) {
  let data = false;
  const res = await $http({
    url: `zx/invitation/code/cancelApplyOutCode?outCodeApplyId=${outCodeApplyId}`
  });
  if (res && res.status == 1) {
    data = true;
  }
  return data;
}

//判断上级超人码是否足够
async function ifEnoughCodeNumMerchant(num) {
  let data = false;
  const res = await $http({
    url: `zx/invitation/code/ifEnoughCodeNumMerchant?num=${num}`
  });
  if (res && res.status == 1) {
    data = true;
  }
  return data;
}

//获取上级联系方式
async function getMerchantPhone(merchantCode) {
  let data = '';
  const res = await $http({
    url: `zx/invitation/code/getMerchantPhone?merchantCode=${merchantCode}`
  });
  if (res && res.status == 1) {
    data = res.data;
  }
  return data;
}

// 根据userId 获取邀请码
async function getInvitationCodeByUserId(userId) {
  let data = '';
  const res = await $http({
    url: `zx/user/getOneInvitationCode?userId=${userId}`
  });
  if (res && res.status == 1) {
    data = res.data;
  }
  return data;
}

// 获取俱乐部金额
async function getAccount(type, item) {
  let data = '';
  console.log(type, item);
  let res = await httpUser.get('mps/account/list/login/code');
  if (res.data.success) {
    if (type == 0) {
      for (let i = 0; i < res.data.data.length; i++) {
        if (res.data.data[i].userCode == item.superCode) {
          data = res.data.data[i];
        }
      }
    } else {
      for (let i = 0; i < res.data.data.length; i++) {
        if (res.data.data[i].userCode == item.merchantCode) {
          data = res.data.data[i];
          console.log('*************');
          console.log(data);
          console.log('*************');
        }
      }
    }
  }
  console.log('data');
  console.log(data);
  console.log('data');
  return data;
}

// 判断当前课程是否是体验课 1体验课  2正式课（默认正式课）
async function getCourseLabel(courseId) {
  let courseLabel = 2;
  const res = await $http({
    url: 'zx/course/courseDetail',
    data: {
      courseId: courseId
    }
  });
  if (res) {
    courseLabel = res.data.courseLabel;
  }
  return courseLabel;
}

//甄选小程序跳鼎学习小程序传参
async function postDirect(data) {
  let response;
  const res = await $http({
    url: 'train/web/common/noSecret/direct',
    method: 'post',
    data: data // 在请求中使用传递的参数
  });
  if (res) {
    response = res.data;
  }
  return response;
}

// 扫码进入小程序---绑定合伙人
async function bindMerchantByScan(data) {
	let response;
	const res = await $http({
	  url: 'zx/merchantQr/bindUser',
	  method: 'post',
	  data: data, // 在请求中使用传递的参数
	  header: {
	  	'Content-Type': 'application/x-www-form-urlencoded'
	  }
	});
	if (res) {
	  response = res.data;
	}
	return response;
}

module.exports = {
  getUserInfo: getUserInfo,
  getClubLevel: getClubLevel,
  applyOutCode: applyOutCode,
  cancelApplyOutCode: cancelApplyOutCode,
  ifEnoughCodeNumMerchant: ifEnoughCodeNumMerchant,
  getMerchantPhone: getMerchantPhone,
  getInvitationCodeByUserId: getInvitationCodeByUserId,
  getAccount: getAccount,
  getCourseLabel: getCourseLabel,
  postDirect: postDirect,
  bindMerchantByScan
};
