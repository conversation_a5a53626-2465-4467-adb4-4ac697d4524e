import App from './App';
import uView from 'uview-ui';
Vue.use(uView);
import { httpUser } from './util/luch-request/indexUser.js'; // 全局挂载引入
import { playVoice } from '@/common/playVoice.js';
import { $payTlian, $tlpayResult } from './util/methods/common.js';
import { noMultipleClicks } from './util/util.js';
Vue.prototype.$noMultipleClicks = noMultipleClicks;

// 读取中/英文干绕项txt
uni.$payTlian = $payTlian;
uni.$tlpayResult = $tlpayResult;
// 读取中/英文干绕项txt
const fetchFile = (isEnglish) => {
  //如果读取英文干扰项并且有了数据则不执行下面的方法
  if (isEnglish == 1 && Vue.prototype.$distrubEnglish != undefined) {
    // console.log("我有英文数据了");
    return;
  }
  //如果读取中文干扰项并且有了数据则不执行下面的方法
  if (isEnglish == 0 && Vue.prototype.$distrubChinese != undefined) {
    // console.log("我有中文数据了");
    return;
  }
  httpUser.get('znyy/course/random/word?count=800&en=' + isEnglish).then((result) => {
    if (result.data.success) {
      var neww = [];
      result.data.data.forEach((item) => {
        if (isEnglish == 1) {
          if (item.word != null && item.word.length > 0) {
            neww.push(item.word);
          }
        } else {
          if (item.translation != null && item.translation.length > 0) {
            neww.push(item.translation);
          }
        }
      });
      neww = randomSort(neww);
      if (isEnglish == 1) {
        Vue.prototype.$distrubEnglish = neww;
      } else {
        Vue.prototype.$distrubChinese = neww;
      }
    } else {
      uni.showToast({
        title: result.data.message,
        icon: 'none'
      });
    }
  });
};

//数组随机排序
const randomSort = (arr) => {
  let len = arr.length,
    neww = [];
  for (let i = 0; i < len; i++) {
    var rand = Math.floor(Math.random() * len);
    neww.push(arr[rand]);
  }
  return neww;
};
const shuffleArray = (array) => {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
};

//字符串切数组（拼拼乐）
const wordRandoms = (word) => {
  if (word == '') {
    return null;
  }
  let len = word.length;
  let wordCount = wordSplitCount(word);
  if (wordCount == 0) {
    return null;
  }
  let randStr = [];

  if (len == wordCount) {
    for (let i = 0; i < wordCount; i++) {
      randStr.push(word.substring(i, i + 1));
    }
    return randStr;
  }

  let nowSeed = 0;
  let nextSeed = 0;
  for (let i = 0; i < wordCount; i++) {
    if (i == wordCount - 1) {
      randStr.push(word.substring(nowSeed));
    } else {
      let ra = 0;
      if (len - (wordCount - i) - nowSeed == 1) {
        ra = 1;
      } else {
        while (ra == 0) {
          let max = len - (wordCount - i) - nowSeed,
            min = 1;
          ra = Math.floor(Math.random() * (max - min + 1) + min);
        }
      }
      nextSeed = nowSeed + ra;
      let newStr = word.substring(nowSeed, nextSeed);
      if (newStr == ' ') {
        nextSeed += 1;
        if (nextSeed > len) {
          nextSeed = len;
        }
        newStr = word.substring(nowSeed, nextSeed);
        // console.log("=newStr-",newStr);
      }
      randStr.push(newStr);
      nowSeed = nextSeed;
    }
  }
  'randStr', randStr;
  return randStr;
};

const wordSplitCount = (word) => {
  if (word == null) {
    return 0;
  }
  // #ifdef MP-WEIXIN
  let word_long = word.replaceAll(' ', '').length;
  // #endif

  // #ifdef APP-PLUS
  let word_long = word.replace(/\s*/g, '').length;
  // #endif

  if (!word == '') {
    if (word_long == 1) {
      return 1;
    } else if (word_long >= 2 && word_long <= 5) {
      return 2;
    } else if (word_long >= 6 && word_long <= 10) {
      return 3;
    } else {
      return 4;
    }
  }
  return 0;
};

//从txt文档中随机取一个数切割返回
const randomWordStr = () => {
  var word = randomSort(Vue.prototype.$distrubEnglish).slice(0, 1);
  var strs = wordRandoms(word[0]);
  let max = strs.length,
    min = 1;
  let index1 = Math.floor(Math.random() * (max - min + 1) + min);
  return strs[index1];
};

// 跳转通联小程序
// 电子签约倒计时
Vue.prototype.signTime = 120;
Vue.prototype.isBeagin = false;
Vue.prototype.signCountdown;
const signCount = (showTime) => {
  Vue.prototype.isBeagin = true;
  Vue.prototype.signCountdown = setInterval(() => {
    showTime--;
    Vue.prototype.signTime = showTime;
    // console.log(showTime);
    if (showTime < 1) {
      Vue.prototype.isBeagin = false;
      clearInterval(Vue.prototype.signCountdown);
      return;
    }
  }, 1000);
};
const clearCount = () => {
  Vue.prototype.isBeagin = false;
  clearInterval(Vue.prototype.signCountdown);
};

const toTLMini = (murl) => {
  uni.navigateToMiniProgram({
    appId: 'wx89164566cfdabfdf',
    path: 'pages/merchantAddress/merchantAddress',
    extraData: {
      targetUrl: murl
    },
    success(res) {
      console.log('res');
      console.log('res');
      // 打开成功
    },
    fail(err) {
      console.log('err');
      console.log('err');
    }
  });
};

const alter = (title, icon = 'none', duration = 2000, mask = false) => {
  //统一提示方便全局修改
  if (Boolean(title) === false) {
    return;
  }
  uni.showToast({
    title,
    duration,
    mask,
    icon
  });
};

const goPrePage = () => {
  var pages = getCurrentPages(); // 当前页面
  var beforePage = pages[pages.length - 2]; // 前一个页面
  if (beforePage != null) {
    uni.navigateBack({
      delta: 1
    });
  } else {
    uni.switchTab({
      url: '../index/index'
    });
  }
};

//像素转rpx
const pxTorpx = (needchangeHeight) => {
  let bs = 0;
  let lastHeight = 0;
  uni.getSystemInfo({
    success: function (res) {
      let bs = 0;
      if (res.windowWidth <= 750) {
        bs = parseInt((750 * 100) / res.windowWidth);
        lastHeight = parseInt((res.windowHeight * bs) / 100);
      } else {
        bs = parseInt((res.windowWidth * 100) / 750);
        lastHeight = parseInt((res.windowHeight * 100) / bs);
      }
    }
  });

  return lastHeight;
};

Vue.prototype.$playVoice = playVoice;

// #ifndef VUE3
import Vue from 'vue';
Vue.config.productionTip = false;
App.mpType = 'app';
const app = new Vue({
  ...App
});
Vue.prototype.$httpUser = httpUser;
Vue.prototype.$util = {
  fetchFile,
  randomSort,
  randomWordStr,
  shuffleArray,
  alter,
  goPrePage,
  signCount,
  clearCount,
  toTLMini,
  wordRandoms,
  pxTorpx
};

app.$mount();
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue';
export function createApp() {
  const app = createSSRApp(App);
  return {
    app
  };
}
// #endif

// 调用setConfig方法，方法内部会进行对象属性深度合并，可以放心嵌套配置
// 需要在Vue.use(uView)之后执行
uni.$u.setConfig({
  // 修改$u.config对象的属性
  config: {
    // 修改默认单位为rpx，相当于执行 uni.$u.config.unit = 'rpx'
    unit: 'rpx',
    // #ifdef APP-PLUS
    unit: 'px'
    // #endif
  },
  // 修改$u.props对象的属性
  props: {
    // 修改radio组件的size参数的默认值，相当于执行 uni.$u.props.radio.size = 30
    radio: {
      size: 15
    }
    // 其他组件属性配置
    // ......
  }
});
