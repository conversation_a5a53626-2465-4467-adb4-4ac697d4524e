<template>
  <view class="plr-32">
    <!--<view class="scroll_tab_content">
			<view v-for="(item,index) in collectList" :key="item.id" class="bg-ff collect_item"  >
				<view class="flex-nowrap">
					<view class="width_css ml-25">
						<image class="w100" mode="widthFix" :src="item.goodsPicUrl"></image>
					</view>
					<view class="ml-25  w467">
						<view class="f-28 c-33 bold collect_item_title">{{item.goodsName}}</view>
						<view class="mt-24 c-FA380E">
							<text class="f-24">￥</text>
							<text class="f-32 bold">{{item.goodsVipPrice}}</text>
						</view>
					</view>
					<view v-if="item.show" class="close_collection bold" @tap="collection(item)">
						<text class="close_collection_text">取消 收藏</text>
					</view>
				</view>			
			</view>
		</view> -->
    <view class="scroll_tab_content">
      <u-swipe-action v-if="collectList.length > 0">
        <u-swipe-action-item :options="options1" @click="collection(item)" v-for="item in collectList" :key="item.id">
          <view class="bg-ff collect_item">
            <view class="swipe-action">
              <view class="swipe-action__content">
                <view class="flex-nowrap">
                  <view class="width_css ml-25">
                    <image class="w100" mode="widthFix" :src="item.goodsPicUrl"></image>
                  </view>
                  <view class="ml-25 w467">
                    <view class="f-28 c-33 bold collect_item_title twolist">{{ item.goodsName }}</view>
                    <view class="mt-24 c-FA380E">
                      <text class="f-24">￥</text>
                      <text class="f-32 bold">{{ identityType == 4 ? item.goodsVipPrice : item.goodsOriginalPrice }}</text>
                    </view>
                  </view>
                  <!--<view v-if="item.show" class="close_collection bold" @tap="collection(item)">
								<text class="close_collection_text">取消 收藏</text>
							</view> -->
                </view>
              </view>
            </view>
          </view>
        </u-swipe-action-item>
      </u-swipe-action>
      <view v-if="collectList.length == 0" class="pt-110">
        <u-empty mode="favor" textSize="24" width="300" height="300" icon="http://cdn.uviewui.com/uview/empty/data.png" text="暂无收藏"></u-empty>
      </view>
    </view>
    <view v-if="collectList.length > 0" @tap="collectionManage" class="button_css c-ff f-28">管理</view>
  </view>
</template>

<script>
  const { $navigationTo, $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        collectList: [],
        identityType: uni.getStorageSync('identityType'),
        show: false,
        startX: 0,
        options1: [
          {
            text: '取消 收藏'
          }
        ]
      };
    },
    onShow() {
      this.getCollectList();
    },
    methods: {
      // /
      async getCollectList() {
        // let userId = uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '';
        const res = await $http({
          url: 'zx/wap/goods/collect/list',
          data: {
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '',
            pageNum: 1,
            pageSize: 15
          }
        });
        if (res) {
          this.collectList = res.data.data;
          this.collectList.forEach((item) => {
            item.show = false;
          });
        }
      },
      async collection(info) {
        let _this = this;
        const res = await $http({
          url: 'zx/wap/goods/collect/cancel',
          method: 'post',
          data: {
            goodsIdList: [info.goodsId],
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        if (res) {
          this.getCollectList();
        }
      },
      collectionManage() {
        $navigationTo('Personalcenter/my/collectionManage');
      },
      /**
       * 触摸开始
       * @param {Object} e
       */
      touchStart: function (e) {
        if (e.touches.length == 1) {
          //设置触摸起始点水平方向位置
          this.startX = e.touches[0].clientX;
        }
      },
      /**
       * 触摸结束
       * @param {Object} e
       */
      touchEnd: function (e, index) {
        if (e.changedTouches.length == 1) {
          //手指移动结束后水平位置
          var endX = e.changedTouches[0].clientX;
          let diff = endX - this.startX;
          if (Math.abs(diff) > 20) {
            if (diff > 0) {
              console.log('左滑...');
              this.$set(this.collectList[index], 'show', false);
            } else {
              console.log('右滑...');
              this.$set(this.collectList[index], 'show', true);
            }
            console.log(this.collectList[index]);
            console.log(this.collectList);
          }
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .width_css {
    width: 144rpx;
    height: 160rpx;
  }
  .collect_item_title {
    max-height: 80rpx;
    overflow: hidden;
    width: 460rpx;
  }
  .scroll_tab_content {
    height: calc(100vh - 130rpx);
    overflow-y: scroll;
  }
  .collect_item {
    border-radius: 16rpx;
    height: 260rpx;
    .flex-nowrap {
      padding-top: 24rpx;
      // width: 800rpx;
      position: relative;
      height: 260rpx;
    }
  }
  .c-FA380E {
    color: #fa380e;
  }
  .w467 {
    width: 476rpx;
  }
  /deep/.u-swipe-action-item__right__button__wrapper {
    background-color: #ffedd6 !important;
    width: 86rpx;
    padding: 0 !important;
    line-height: 36rpx;
    height: 260rpx;
    text-align: center;

    // .close_collection_text{
    // 	display: block;
    // 	margin-top: 90rpx;
    // }
  }
  /deep/.u-swipe-action-item__right__button__wrapper__text {
    color: #f98603 !important;
    font-size: 28rpx !important;
  }
  /deep/.u-line-1 {
    -webkit-line-clamp: 2;
  }
  /deep/.u-swipe-action-item {
    margin-top: 20rpx;
  }
  .button_css {
    width: 686rpx;
    height: 74rpx;
    line-height: 74rpx;
    border-radius: 38rpx;
    background-color: #339378;
    position: absolute;
    left: 32rpx;
    bottom: 35rpx;
    text-align: center;
  }
</style>
