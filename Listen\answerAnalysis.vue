<template>
  <view class="contain" :style="{ height: useHeight + 'rpx' }">
    <view class="title">
      <view class="introduction">{{ all.title }}</view>
      <view class="percent">
        <span style="color: #428a6f">{{ all.index }}</span>
        /{{ all.allNum }}
      </view>
    </view>
    <view class="line"></view>
    <view class="reviewCount" v-if="reviewCount">
      做过
      <span style="color: #339378; margin: 0 10rpx">{{ reviewCount - 1 }}</span>
      次
    </view>
    <view class="topic" v-if="data">
      <span style="font-size: 34rpx; font-weight: bold">{{ data.questionText.replaceAll('##', '__') }}</span>
    </view>
    <view class="topicInterpret" v-if="data" style="margin-bottom: 68rpx; margin-top: 42rpx">
      {{ data.questionTextTranslation.replaceAll('##', '__') }}
    </view>
    <view class="options" v-for="item in data.optionDetailVoList" :key="item.id" :class="item.choiceOption == 'A' ? 'mt68' : ''">
      <!-- item.choiceOption == data.studyAnswer ? (right ? 'correct' : 'error') : '' -->
      <view class="option" :class="item.choiceOption == data.correctAnswer ? 'correct' : item.choiceOption == data.studyAnswer ? 'error' : ''">
        {{ item.choiceOption }}.{{ item.content }}
      </view>
      <view class="topicInterpret">{{ item.contentTranslation }}</view>
    </view>
    <view class="answer" :class="right ? 'answerRight' : 'answerError'">
      {{ right ? '回答正确：' : '回答错误：' }}
    </view>
    <view class="rightAnswer">
      正确答案：
      <span v-if="!right" style="color: #428a6f; font-weight: bold">{{ data.correctAnswer }}</span>
      <span v-else style="color: #428a6f; font-weight: bold">{{ data.correctAnswer }}</span>
      <span v-if="!right" style="margin-left: 20rpx">
        你的答案：
        <span style="color: #f97244; font-weight: bold">{{ data.studyAnswer }}</span>
      </span>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        data: null,
        all: '',
        useHeight: 0,
        right: true,
        reviewCount: 0
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          this.useHeight = res.windowHeight * (750 / res.windowWidth);
        }
      });
    },
    onLoad(option) {
      let a = decodeURIComponent(option.data);
      this.data = JSON.parse(a);
      let b = decodeURIComponent(option.all);
      this.all = JSON.parse(b);
      this.right = this.data.studyAnswer == this.data.correctAnswer;
      this.reviewCount = option.reviewCount;
    }
  };
</script>

<style lang="scss" scoped>
  .contain {
    box-sizing: border-box;
    padding: 32rpx;
    background-color: #fff;
    overflow-y: auto;
    .rightAnswer {
      height: 60rpx;
      line-height: 60rpx;
      // font-size: 28rpx;
    }
    .reviewCount {
      margin: 0 auto;
      width: 262rpx;
      height: 56rpx;
      background: #fafafa;
      border-radius: 36rpx;
      // font-size: 24rpx;
      color: #939393;
      font-weight: bold;
      line-height: 56rpx;
      text-align: center;
    }
    .title {
      height: 40rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      // font-size: 28rpx;
    }
    .topicInterpret {
      margin-top: 6rpx;
      // font-size: 24rpx;
      color: #939393;
      line-height: 1.5;
    }
    .answer {
      // width: 138rpx;
      height: 40rpx;
      font-weight: bold;
      // font-size: 28rpx;
      margin: 32rpx 0 16rpx;
      line-height: 40rpx;
    }
    .answerRight {
    }
    .answerError {
    }
    .mt68 {
      margin-top: 68rpx !important;
    }
    .introduction {
      font-weight: bold;
      color: #428a6f;
    }
    .options {
      margin-top: 32rpx;
    }
    .option {
      padding-left: 40rpx;
      min-height: 96rpx;
      line-height: 54rpx;
      border-radius: 16rpx;
      border: 2rpx solid #efeff0;
      display: flex;
      align-items: center;
    }
    .line {
      height: 2rpx;
      background-color: #e1e3e4;
      margin: 30rpx 0;
    }
    .correct {
      border: 2rpx solid #94e6c7;
      background: rgba(148, 230, 199, 0.15);
      color: #31cf93;
    }
    .interpretFlag {
      height: 40rpx;
      // width: 200rpx;
      position: absolute;
      bottom: -40rpx;
      line-height: 40rpx;
      color: #428a6f;
      // font-size: 24rpx;
      text-align: center;
      right: 0;
      border-bottom: 2rpx solid #428a6f;
    }
    .topic {
      position: relative;
      line-height: 50rpx;
      color: #555555;
      // font-size: 28rpx;
    }
    .error {
      border: 2rpx solid #ffaf85;
      background: rgba(255, 172, 129, 0.1);
      color: #ffac80;
    }
    .btn {
      width: 686rpx;
      height: 74rpx;
      background: #339378;
      border-radius: 38rpx;
      line-height: 74rpx;
      text-align: center;
      color: #fff;
      margin: 20rpx auto 0;
    }
    .disabled {
      width: 686rpx;
      height: 74rpx;
      background: #d3d2d7;
      border-radius: 38rpx;
      line-height: 74rpx;
      text-align: center;
      color: #8a8a8e;
      margin: 20rpx auto 0;
    }
  }
</style>
