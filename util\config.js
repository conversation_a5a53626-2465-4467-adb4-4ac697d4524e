let DXHost = 'https://test-k8s.ngrok.dxznjy.com/'; //上线
// let DXHost = 'https://linetest.dxznjy.com/'; // 预发布环境

// let DXHost = 'https://sxl.ngrok.dxznjy.com/';
// let DXHost = 'http://test221.ngrok.dxznjy.com/';
// let DXHost = 'https://test176.ngrok.dxznjy.com/';
// let DXHost = 'https://lpy.ngrok.dxznjy.com/dyf/';

// scrm 地址
// let DXSCRMHost = "https://applet.dxznjy.com/";

let DXSCRMHost = 'https://scrm.dxznjy.com/';
// 版本号
const wxAppVersion = '2.0.0';
// 鼎校甄选APP下载h5页面
let zxDownloadH5Url = 'https://zx-share.dxznjy.com/#/about';
// 试课奖励课程大类id（鼎英语）
let trialRewardCurriculumId = '1244777039673577472'; // 正式环境
// let trialRewardCurriculumId = '1222597307584499712'; // 测试环境
// 鼎校甄选
let ImgsomeHost = 'https://document.dxznjy.com/';
let ImguseHost = 'https://document.dxznjy.com/applet/';
let contactId = 'wwcc79054d80e112df'; //微信客服id
let contactUrl = 'https://work.weixin.qq.com/kfid/kfc67db783b65cf6f20';

let miniOriginalId = 'gh_d43ada85807b';
let webUrl = 'http://www.dxznjy.com/';
let webNewUrl = 'https://document.dxznjy.com/dxSelectH5/';
// let webNewUrl = "https://document.dxznjy.com/dxSelectH5-Second/"
let AppwebNewUrl = 'https://document.dxznjy.com/appDXSelectH5/';

let supermanShareImage = 'https://document.dxznjy.com/dxSelect/fourthEdition/superman_share.png';
let supermanClubShareImage = 'https://document.dxznjy.com/dxSelect/fourthEdition/supermanClub_share.png';

let ClassStyle = [
  {
    key: '1',
    courseName: '英语',
    color: '#94D6F3'
  },
  {
    key: '2',
    courseName: '概率论',
    color: '#CCB2FF'
  },
  {
    key: '3',
    courseName: '选修课',
    color: '#F0C762'
  },
  {
    key: '4',
    courseName: '创业基础',
    color: '#FDCDC5'
  },
  {
    key: '5',
    courseName: '生物化学',
    color: '#FFA7C5'
  },
  {
    key: '6',
    courseName: '马克思主义',
    color: '#CF93D8'
  },
  {
    key: '7',
    courseName: '形式与策略',
    color: '#92B5E6'
  }
];

let AmapKey = 'b5c3eae1d3a4990c61c73da366593720';
let XKTCurriculumCodeArr = [
  'XKT',
  'XKT_CZHX1',
  'XKT_CZSX1',
  'XKT_CZWL1',
  'XKT_CZYW1',
  'XKT_CZYY1',
  'XKT_GZDL',
  'XKT_GZHX',
  'XKT_GZLS',
  'XKT_GZSW',
  'XKT_GZSX',
  'XKT_GZWL',
  'XKT_GZYW',
  'XKT_GZYY',
  'XKT_GZZZ'
]; //学考通相关课程大类
let XKTNCurriculumCodeArr = [
  'XKT_CZHXN',
  'XKT_CZSXN',
  'XKT_CZWLN',
  'XKT_CZYWN',
  'XKT_CZYYN',
  'XKT_GZDLN',
  'XKT_GZHXN',
  'XKT_GZLSN',
  'XKT_GZSWN',
  'XKT_GZSXN',
  'XKT_GZWLN',
  'XKT_GZYWN',
  'XKT_GZYYN',
  'XKT_GZZZN'
]; //学考通一对多课程大类
let XSMCurriculumCodeArr = ['XSM_CZDL1', 'XSM_CZLS1', 'XSM_CZSW1', 'XSM_CZZZ1', 'XSM_SDHK', 'XSM_ZSZK', 'XSM_JXDK10', 'XSM_JXSHK10', 'XSM_JXSK10']; //小四门相关课程大类
let XKTAndXSMCurriculumCodeArr = [...XKTCurriculumCodeArr, ...XSMCurriculumCodeArr]; //学考通、小四门所有课程大类
let MATHCurriculumCodeArr = ['MATH', 'MATH_HKB', 'MATH_BSD', 'MATH_ZXK', 'MATH_STK']; //数学相关课程大类
let CurriculumCodeArr = [...XKTCurriculumCodeArr, ...XSMCurriculumCodeArr, ...MATHCurriculumCodeArr]; //学考通、小四门所有课程大类
module.exports = {
  // ApiHost: ApiHost,
  // ImgHost: ImgHost,
  AmapKey: AmapKey,
  DXHost: DXHost,
  DXSCRMHost: DXSCRMHost,
  ImgsomeHost: ImgsomeHost,
  ImguseHost: ImguseHost,
  ClassStyle: ClassStyle,

  contactId: contactId,
  contactUrl: contactUrl,
  miniOriginalId: miniOriginalId,
  webUrl: webUrl,
  webNewUrl: webNewUrl,
  AppwebNewUrl: AppwebNewUrl,
  supermanShareImage: supermanShareImage,
  supermanClubShareImage: supermanClubShareImage,
  zxDownloadH5Url,
  XKTCurriculumCodeArr,
  XSMCurriculumCodeArr,
  XKTAndXSMCurriculumCodeArr,
  CurriculumCodeArr,
  MATHCurriculumCodeArr,
  trialRewardCurriculumId,
  wxAppVersion,
  XKTNCurriculumCodeArr
};
