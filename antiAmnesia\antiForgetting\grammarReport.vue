<template>
  <view class="" style="overflow: hidden" :style="'height: ' + bodyHeight + 'rpx;'">
    <view style="position: relative">
      <image class="rate_bgImageG" src="https://document.dxznjy.com/applet/interesting/grammarBG.png" mode="" style="height: 68vh"></image>
      <view class="review_rate">
        <view class="header">
          <text class="fuxitext">{{ showData.reportDate }} 复习报告</text>
        </view>
        <view class="review_taskG">——— 本场作业完成情况统计 ———</view>
        <view class="rate_numG">
          {{ showData.completionRate }}
          <text>%</text>
          <view class="review_rate_text">完成率</view>
        </view>
        <view class="student-name-code">
          <view>学生姓名：{{ showData.studentName }}</view>
          <view style="margin-top: 14rpx">学生编号：{{ urlStundentCode }}</view>
        </view>
        <view class="over_review_census" v-if="isType == 0">
          <view>
            <view>
              <view class="flex-a-c flex-x-s">
                <view class="box-10 radius-allh" style="background-color: #9b8469"></view>
                <view style="color: #9b8469" class="f-24 m-10">需复习</view>
              </view>
              <view class="review_litleTitle review_color11">
                <view style="color: #c97513" class="f-30">{{ showData.needReviewNum }}个 知识点</view>
              </view>
            </view>
            <view>
              <view class="flex-a-c flex-x-s">
                <view class="box-10 radius-allh" style="background-color: #90c0b0"></view>
                <view style="color: #90c0b0" class="f-24 m-10">已复习</view>
              </view>
              <view class="review_litleTitle review_color22">
                <view style="color: #078970" class="f-30">{{ showData.reviewedNum }}个 知识点</view>
              </view>
            </view>
            <view>
              <view class="flex-a-c flex-x-s">
                <view class="box-10 radius-allh" style="background-color: #949fbd"></view>
                <view style="color: #949fbd" class="f-24 m-10">未复习</view>
              </view>
              <view class="review_litleTitle review_color33">
                <view style="color: #365197" class="f-30">{{ showData.notReviewedNum }}个 知识点</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="study_records bg-f3" :style="'min-height: ' + recordHeight + 'rpx;'">
      <view class="study_record_titles pl-40">今日完成知识点记录</view>

      <view>
        <view class="listBorder">
          <span>复习语法</span>
          <span>阶段</span>
          <span>复习时间</span>
        </view>
        <view v-if="knowledgeReview.length > 0" class="pb-20">
          <view class="listData" v-for="(item, index) in knowledgeReview" :key="index">
            <span style="width: 130rpx">{{ truncatedKnowledgeName(item.knowledgeName) }}</span>
            <span class="boxl-40 c-81 lh-30 b-g f-24 t-c radius-6" style="margin-right: 58rpx">{{ item.phase }}</span>
            <span style="margin-right: 44rpx; width: 50rpx">{{ item.useTime }}s</span>
          </view>
        </view>
        <view class="bg-ff radius-15 plr-20 mt-30 t-c flex-col" v-else :style="{ height: useHeight + 'rpx' }" style="position: relative">
          <image src="/static/index/<EMAIL>" mode="widthFix" class="mb-20 img_s"></image>
          <view style="color: #bdbdbd">暂无数据</view>
        </view>
      </view>
    </view>

    <view class="reviewBottom fixed-bottom-review" v-if="!isShare">
      <view class="reviewLeft_btn_1 reviewBtn1" @click="goUrl(`/antiAmnesia/review/history?studentCode=${showData.studentCode}&selectedType=${'grammar'}&antiForgetting=2`)">
        往期复习
      </view>
      <button class="reviewRight_btn reviewBtn" hover-class="none" open-type="share">分享给家长</button>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        src: 'http://*************:5000/v/b88b6ff1fa28125e',
        showData: {}, //复习字段
        echartsList: 0, //echarts数据
        wrongList: [],
        postImage: getApp().globalData.postHost,
        recordHeight: 0,
        bodyHeight: 0,
        urlStundentCode: '',
        recordInnerHeight: 250,

        isplay: false,
        isShare: false,
        isType: 0, //1刚复习  0复习报告
        // studentCode: '6231217888',
        studentCode: '',
        studyTime: null,
        knowledgeId: '',
        knowledgeReview: [],
        reportId: ''
      };
    },

    onShareAppMessage() {
      return {
        title: '21天抗遗忘打卡',
        imageUrl: '', //分享封面
        path: '/antiAmnesia/antiForgetting/grammarReport?reportId=' + this.reportId + '&isShare=true&isType=0'
      };
    },
    onLoad(options) {
      console.log(options, '11111111111111');
      this.studentCode = options.studentCode;
      this.knowledgeId = options.knowledgeId;
      // uni.setNavigationBarTitle({
      //   title: options.title,
      // })
      this.reportId = options.hanoutId ? options.hanoutId : options.reportId;
      this.studyTime = options.studyTime;
      this.isShare = options.isShare == 'true' ? true : false;
      this.isType = options.isType == '1' ? 1 : 0;
      let nowScreenHeight = this.$util.pxTorpx();
      this.bodyHeight = nowScreenHeight - 240 + 600;
      this.recordHeight = nowScreenHeight - 940;
      this.recordInnerHeight = nowScreenHeight - 1000;
      this.getReviewReport(this.reportId);
    },
    methods: {
      async getReviewReport(id) {
        let result;
        // 如果 id 存在，则调用接口 antiForgettingQuery
        console.log(this.reportId, 'antiForgettingQuery');
        result = await this.$httpUser.get('dyf/wap/applet/antiForgettingQuery', { reportId: this.reportId });

        if (result && result.data && result.data.data) {
          this.showData = result.data.data;
          this.urlStundentCode = result.data.data.studentCode;
          this.echartsList = result.data.data.echartsList;
          this.knowledgeReview = result.data.data.recordList;
        }
      },
      truncatedKnowledgeName(name) {
        if (name.length > 4) {
          return name.slice(0, 3) + '....';
        }
        return name;
      },
      //返回上一页
      back1() {
        if (this.isShare) {
          uni.switchTab({
            url: '/pages/index/index'
          });
        } else {
          uni.navigateBack({
            delta: 1
          });
        }
      },
      goUrl(url) {
        uni.redirectTo({
          url: url
        });
      },
      rightClick() {
        // console.log('返回今日复习')
        uni.redirectTo({
          url: '/antiAmnesia/review/allWords'
        });
      },

      //获取海报图片
      shareReviewReport() {
        // console.log("分享海报");
        let that = this;
        uni.request({
          url: that.postImage + 'api/link',
          data: {
            'zt#syh_rightRate': that.showData.rate,
            'aRig_zt#syh_alreadyReview': that.showData.reviewWordVM.length,
            'aRig_zt#syh_noReview': that.showData.wordCount - that.showData.reviewWordVM.length,
            'aRig_zt#syh_needReview': that.showData.wordCount,
            accessKey: 'ApfrIzxCoK1DwNZO',
            secretKey: 'EJCwlrnv6QZ0PCdvrWGi',
            posterId: 7
          },
          method: 'POST',
          header: {
            'Content-Type': 'application/json'
          },
          success: (res) => {
            // console.log(res.data);
            let urlString = res.data.url;
            that.src = that.postImage + 'v/' + urlString.split('/v/')[1];
            uni.navigateTo({
              url: '/interestModule/playbill?url=' + that.src
            });
          }
        });
      }
    }
  };
</script>

<style lang="less">
  page {
    height: 100%;
  }

  .student-name-code {
    width: 100%;
    text-align: left;
    position: absolute;
    font-family: PingFang-SC, PingFang-SC;
    color: #555555;
    bottom: 260rpx;
    font-size: 28rpx;
    right: -289rpx;
  }

  .review_litleTitle-1 {
    // width: 290rpx;
    // height: 160rpx;
    border-radius: 14rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
  }

  .review_litleTitle {
    width: 300rpx;
    height: 64rpx;
    line-height: 64rpx;
    border-radius: 8rpx;
  }

  .study_records {
    // width: 710rpx;
    padding-top: 190rpx;
    margin-top: 40rpx;
    box-sizing: border-box;
    margin: 0 auto;
    overflow-y: auto;
  }

  .study_record_titles {
    font-size: 32rpx;
    font-family: AlibabaPuHuiTiM;
    color: #428a6f;
    font-weight: bold;
    margin: 20rpx 0;
  }

  .header {
    padding-top: 40rpx;
    margin-left: -95rpx;
    font-size: 40rpx;
    box-sizing: border-box;
    text-align: center;
    position: relative;
  }

  .backIcon {
    position: absolute;
    left: 30rpx;
    top: 90rpx;
  }

  .over_review_census {
    margin: 0 auto;
    display: flex;
    justify-content: flex-start;
    padding-top: 230rpx;
    padding-left: 285rpx;
  }

  .fuxitext {
    color: #ffffff;
  }

  .listBorder {
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex-flow: row wrap;
    font-size: 30rpx;
    height: 100rpx;
    background: #ebf4fb;
    margin: 0 32rpx;
    border-radius: 8rpx;
  }

  .listData {
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex-flow: row wrap;
    font-size: 30rpx;
    height: 100rpx;
    margin: 0 30rpx;
    border-radius: 8rpx;
    border-bottom: 2rpx solid #e7eaed;
  }

  .fixed-bottom-review {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: white;
    z-index: 999;
    padding: 10rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: space-around;
  }
</style>
