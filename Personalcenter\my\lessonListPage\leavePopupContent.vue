<template>
  <view class="m_leave_content">
    <!-- 标题 -->
    <view class="popup_title">
      <text>请假</text>
    </view>
    <!-- 表单内容 -->
    <view class="form-content">
      <view class="form-item">
        <text>课程：</text>
        <text>{{ columnLeave.courseName }}</text>
      </view>
      <view class="form-item">
        <text>班级名称：</text>
        <text>{{ leaveInfo.className || '' }}</text>
      </view>
      <view class="form-item">
        <text>上课时间：</text>
        <text>{{ leaveInfo.courseTime || columnLeave.courseTime }}</text>
      </view>
      <view class="form-item">
        <text>学员：</text>
        <text>{{ columnLeave.studentName }}</text>
      </view>
      <view class="form-item">
        <text>剩余请假次数：</text>
        <text>{{ leaveInfo.surplusLeaveNum || 0 }}次</text>
      </view>

      <!-- 试课提示 -->
      <view class="warn-tip">
        <u-icon top="-16" name="error-circle-fill" color="#e57534" size="30" />
        <view class="u-tip">试课请假后将退出班级重新成班，当前课程将被删除！</view>
      </view>
    </view>

    <!-- 按钮组 -->
    <view class="btn-group">
      <view @click="onCancel">取消</view>
      <view class="sure_btn" @click="onConfirm">确认</view>
    </view>
  </view>
</template>
<script>
  export default {
    name: 'leavePopupContent',
    props: {
      columnLeave: {
        type: Object,
        default: () => ({})
      },
      leaveInfo: {
        type: Object,
        default: () => ({})
      }
    },
    data() {
      return {
        infoData: {},
        phone: ''
      };
    },
    methods: {
      // 取消按钮
      onCancel() {
        this.$emit('cancel');
      },
      // 确定按钮
      async onConfirm() {
        this.phone = uni.getStorageSync('phone');
        try {
          const res = await this.$httpUser.post('deliver/class/studentLeave/app/parentLeave', {
            courseId: this.columnLeave.planStudyId,
            phone: this.phone,
            grade: this.infoData.grade || ''
          });
          if (res.data.code == 20000) {
            uni.showToast({ title: '请假申请提交成功', icon: 'none' });
            this.$emit('submit');
          }
        } catch (e) {
          console.log(e);
        } finally {
          this.$emit('cancel');
        }
      }
    }
  };
</script>
<style scoped>
  .m_leave_content {
    display: flex;
    flex-direction: column;
    width: 560rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    padding: 40rpx 20rpx;
    box-sizing: border-box;
  }
  .popup_title {
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 32rpx;
    color: #333333;
    margin-bottom: 24rpx;
    font-weight: 600;
  }
  .form-item {
    font-size: 28rpx;
    color: #555555;
    margin-bottom: 24rpx;
  }
  .btn-group {
    display: flex;
    align-items: center;
    justify-content: space-around;
    font-size: 32rpx;
    min-width: 64rpx;
    height: 60rpx;
    color: #a39e9e;
  }
  .sure_btn {
    color: #428a6f;
  }
  .warn-tip {
    display: flex;
    align-items: center;
    color: #8a8a8a;
    font-size: 28rpx;
    margin-bottom: 20rpx;
  }
  .u-tip {
    margin-left: 6rpx;
  }
</style>
