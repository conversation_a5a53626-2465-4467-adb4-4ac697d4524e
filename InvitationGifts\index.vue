<template>
  <page-meta :page-style="'overflow:' + (show ? 'hidden' : 'visible')"></page-meta>
  <view class="activity">
    <view class="pb-20">
      <view class="activityTop">
        <p @click="acticityRule" class="activityToprule">邀请规则</p>
      </view>
      <view :class="showEquity ? 'activityMiddle' : 'activityMiddle2'">
        <view class="activityInfo">
          <p style="font-size: 32rpx; color: #024c37; font-weight: 700">
            <span>我的积分：{{ userCredit }}</span>
            <span style="margin: 0 20rpx">我的鼎币：{{ userDingbi }}</span>
          </p>
          <p @click="InvitationRecord" class="Activityrecord">邀请记录</p>
        </view>
        <view class="noticebar">
          <swiper circular :autoplay="autoplay" :interval="interval" :duration="duration" previous-margin="150rpx" next-margin="150rpx" :current="current" @change="swiperChange">
            <swiper-item v-for="(item, index) in rollList" :key="index" :class="{ 'gray-item': index !== current }">
              <view>{{ item }}</view>
            </swiper-item>
          </swiper>
        </view>
        <view :class="showEquity ? 'activityGiftPoints' : 'activityGiftPoints2'">
          <template v-if="showEquity">
            <view class="Nonmembe" v-for="(value, index) in NonmembeDetail" :key="index">
              <view class="NonmembeRegister">
                <p class="wode">我得</p>
                <p class="activityChild activityChild1" v-if="value.registerRewardType === 0">+{{ value.registerGiftPoints }}积分</p>
                <p class="activityChild activityChild1" v-else>+{{ value.registerGiftDingCoins }}鼎币</p>
                <p style="font-size: 28rpx; color: #555555">邀请注册</p>
              </view>
              <view class="NonmembeMember">
                <p class="wode">我得</p>
                <p class="activityChild activityChild1" v-if="value.memberActivateRewardType === 0">+{{ value.memberActivateGiftPoints }}积分</p>
                <p class="activityChild activityChild1" v-else>+{{ value.memberActivateGiftDingCoins }}鼎币</p>
                <p style="font-size: 28rpx; color: #555555">邀请开通会员</p>
              </view>
            </view>
          </template>
          <view v-for="(value, index) in Equitydetail" :key="index">
            <view class="member">
              <view class="memberzhuan">会员专属</view>
              <view class="memberGift">
                <view class="membeRegister">
                  <p class="activityChild activityChild2" v-if="value.registerRewardType === 0">+{{ value.registerGiftPoints }} 积分</p>
                  <p class="activityChild activityChild2" v-else>+{{ value.registerGiftDingCoins }} 鼎币</p>
                  <p style="font-size: 28rpx; color: #c18a4b">邀请注册</p>
                </view>
                <view class="membeMember">
                  <p class="activityChild activityChild2" v-if="value.memberActivateRewardType === 0">+{{ value.memberActivateGiftPoints }} 积分</p>
                  <p class="activityChild activityChild2" v-else>+{{ value.memberActivateGiftDingCoins }} 鼎币</p>
                  <p style="font-size: 28rpx; color: #c18a4b">邀请开通会员</p>
                </view>
              </view>
              <!-- 2024-11-6 紧急修改 购买超级会员修改成购买家长会员 隐藏 -->
              <view class="huiyuan" @click="ToEquity" v-if="false"></view>
              <!-- 							<view class="huiyuan" @click="ToEquity" v-if="showEquity"></view> -->
            </view>
          </view>
        </view>
      </view>
    </view>
    <view>
      <view class="ExchangeGood plr-32" :class="showEquity ? '' : ''">
        <view class="ExchangeGoodHead"></view>
        <view class="goodsBox">
          <scroll-view
            v-if="leftList.length > 0"
            @scrolltolower="scrolltolower"
            :show-scrollbar="false"
            bounces
            :throttle="false"
            scroll-with-animation
            scroll-anchoring
            scroll-y
            enhanced
          >
            <view class="mt-12">
              <view class="selection_list">
                <view class="waterfall-box h-flex-x h-flex-2">
                  <view>
                    <helang-waterfall v-for="(item, index) in leftList" :key="index" :item="item" tag="left" :index="index" @tap="goDetails(item)"></helang-waterfall>
                  </view>
                  <view style="margin-left: 24rpx">
                    <helang-waterfall v-for="(item, index) in rightList" :key="index" :item="item" @tap="goDetails(item)" tag="right" :index="index"></helang-waterfall>
                  </view>
                </view>
              </view>
              <view v-if="no_more && infoLists.list && infoLists.list.length > 0" style="width: 100%; text-align: center">
                <u-divider text="到底了"></u-divider>
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
      <!-- 2024-11-6 紧急修改 购买超级会员修改成购买家长会员 隐藏 -->
      <view class="activityBottom" v-if="false">
        <view @click="activityShare" class="shareFriends"></view>
      </view>
    </view>

    <!-- 活动规则弹窗 -->
    <uni-popup ref="acticityRule" type="center" @change="change">
      <view class="shareCard">
        <view class="activityRule">活动规则</view>
        <view class="review_close" @click="closeacticityRule">
          <uni-icons type="clear" size="26" color="#B1B1B1"></uni-icons>
        </view>
        <view class="rule" v-html="activityRuleText"></view>
      </view>
    </uni-popup>
    <!-- 分享弹窗 -->
    <uni-popup ref="sharePopup" type="bottom" style="padding: 0">
      <view class="shareCardFriend">
        <view class="reviewTitle bold">立即分享给好友</view>
        <view class="review_close" @click="closeDialog">
          <uni-icons type="clear" size="26" color="#B1B1B1"></uni-icons>
        </view>
        <view class="displayflexbetween mt-30 ptb-30" style="justify-content: space-around">
          <view class="t-c" @click="sharePoster">
            <image :src="imgHost + 'dxSelect/share_img.png'" class="shareIcon" mode=""></image>
            <view class="f-26 mt-10 color_grey66">海报分享</view>
          </view>
          <!-- #ifdef MP-WEIXIN -->
          <view class="t-c">
            <button class="fillButton" open-type="share">
              <image :src="imgHost + 'dxSelect/share_lj.png'" class="shareIcon" mode=""></image>
              <view class="f-26 mt-10 color_grey66">链接分享</view>
            </button>
          </view>
          <!-- #endif -->

          <!-- #ifdef APP-PLUS -->
          <view class="t-c" @click="linkShare">
            <button class="fillButton">
              <image :src="imgHost + 'dxSelect/share_lj.png'" class="shareIcon" mode=""></image>
              <view class="f-26 mt-10 color_grey66">链接分享</view>
            </button>
          </view>
          <!-- #endif -->
        </view>
        <view class="bd-ee"></view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  // import uni-table from "
  //  import uniDatetimePicker from '../compontents/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.vue'
  import helangWaterfall from './components/helang-waterfall/helang-waterfall';
  import uniTable from './components/uni-table/components/uni-table/uni-table.vue';
  import uniTd from './components/uni-table/components/uni-td/uni-td.vue';
  import uniTr from './components/uni-table/components/uni-tr/uni-tr.vue';
  import uniTh from './components/uni-table/components/uni-th/uni-th.vue';
  const { $http } = require('@/util/methods.js');
  export default {
    components: {
      uniTable,
      uniTr,
      uniTd,
      uniTh,
      helangWaterfall
    },
    data() {
      return {
        current: 0,
        lastIndex: 0, // 上一次的索引
        imgHost: getApp().globalData.imgsomeHost,
        data: [],
        list: [122, 2, 3, 4, 5],
        indicatorDots: true,
        autoplay: true,
        interval: 2000,
        duration: 500,
        activityList: [],
        count: 30,
        showNum: 3,
        lineHeight: 60,
        animationScroll: 800,
        animation: 2000,
        rollList: [],
        rollLists: [],
        rollListLength: 0,
        animationData: {},
        activityRuleText: '', //规则文本
        userDingbi: '', //用户鼎币
        Equitydetail: [], //会员活动详情
        NonmembeDetail: [], //非会员活动详情
        showEquity: false, //是否展示会员活动
        activityId: '', //活动id
        userCredit: '', //用户积分
        finished: false,
        leftList: [],
        rightList: [],
        leftHeight: 0,
        rightHeight: 0,
        infoLists: {},
        no_more: false,
        activitypageValue: 1,
        userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '',
        phoneHeight: '',
        poster: '',
        show: false //弹窗穿透
      };
    },
    onLoad(e) {
      this.activityId = e.activityid;
    },
    onShow() {
      this.fetchActivity();
      this.fetchDingbi();
      this.activityDetail();
      this.fetchRewardDetail();
      this.getGiftsList();
      this.fetchMycredit();
      this.fetchPoster();
      const info = uni.getSystemInfoSync();
      this.phoneHeight = info.windowHeight;
    },
    onShareAppMessage() {
      return {
        title: '好友邀请您参加活动',
        imageUrl: this.poster, //分享封面
        //如果有参数的情况可以写path
        path: '/Personalcenter/my/nomyEquity?userId=' + this.userId + '&activityId=' + this.activityId
      };
    },
    onReady() {},
    methods: {
      copyCode() {
        uni.setClipboardData({
          data: '15454254',
          success: function (res) {
            console.log(res);
            uni.getClipboardData({
              success: function (res) {
                uni.showToast({
                  title: '复制成功'
                });
              }
            });
          }
        });
      },
      //弹窗穿透
      change(e) {
        this.show = e.show;
      },
      scrolltolower() {
        if (this.page >= Number(this.infoLists.totalPage)) {
          this.no_more = true;
          return false;
        }
      },
      //查询我的鼎币
      fetchDingbi() {
        this.$httpUser.get('zx/wap/invite/getDingBi').then((res) => {
          this.userDingbi = res.data.data;
        });
      },
      // 活动详情
      activityDetail() {
        this.$httpUser.get('zx/wap/invite/getActivityDetail?activityId=' + this.activityId).then((res) => {
          res.data.data.forEach((value) => {
            if (value.userType === 0) {
              this.showEquity = true;
              this.NonmembeDetail = [];
              this.NonmembeDetail.push(value);
            } else if (value.userType === 1) {
              this.Equitydetail = [];
              this.Equitydetail.push(value);
            }
          });
        });
      },
      //获取海报
      fetchPoster() {
        this.$httpUser.get('zx/user/getTestActivityCode?activityId=' + this.activityId).then((res) => {
          this.poster = res.data.data.bgImageUrl[0];
        });
      },
      //积分滚动内容
      async fetchRewardDetail() {
        this.rollList = [];
        await this.$httpUser.get('zx/wap/invite/getRewardDetail?pageSize=10&pageNum=1&activityId=' + this.activityId).then((res) => {
          this.rollList = res.data.data.data;
          this.rollListLength = res.data.data.data.length;
        });
      },
      //更新积分滚动内容
      async fetchRewardDetails() {
        this.rollLists = [];
        await this.$httpUser.get('zx/wap/invite/getRewardDetail?pageSize=10&pageNum=1&activityId=' + this.activityId).then((res) => {
          this.rollLists = res.data.data.data;
        });
      },
      // 获取好物列表
      async getGiftsList() {
        let _this = this;
        const res = await $http({
          url: 'zx/wap/goods/select/list',
          data: {
            goodsTypeListStr: 6,
            orderBy: '',
            orderType: '',
            pageNum: 1,
            pageSize: 10,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        if (res) {
          this.infoLists = res.data;
          // this.selectionList=res.data.data
          let i = this.leftHeight > this.rightHeight ? 1 : 0;
          let [left, right] = [[], []];
          // 左右列表高度的差
          let differ = this.leftHeight - this.rightHeight;

          // 将数据源分为左右两个列表，容错差值请自行根据项目中的数据情况调节
          let list = res.data.data;
          list.forEach((item, index) => {
            /* 左侧高度大于右侧超过 600px 时，则前3条数据都插入到右边 */
            if (differ >= 600 && index < 3) {
              right.push(item);
              return;
            }

            /* 右侧高度大于左侧超过 600px 时，则前3条数据都插入到左边 */
            if (differ <= -600 && index < 3) {
              left.push(item);
              return;
            }

            /* 左侧高度大于右侧超过 350px 时，则前2条数据都插入到右边 */
            if (differ >= 350 && index < 2) {
              right.push(item);
              return;
            }
            /* 右侧高度大于左侧超过 350px 时，则前2条数据都插入到左边 */
            if (differ <= -350 && index < 2) {
              left.push(item);
              return;
            }

            /* 当前数据序号为偶数时，则插入到左边 */
            if (i % 2 == 0) {
              left.push(item);
            } else {
              /* 当前数据序号为奇数时，则插入到右边 */
              right.push(item);
            }
            i++;
          });
          this.leftList = left;
          this.rightList = right;
        }
      },
      //跳转详情
      goDetails(item) {
        uni.navigateTo({
          url: '/shoppingMall/details?id=' + item.goodsId
        });
      },
      async swiperChange(e) {
        this.current = e.detail.current;
        if (this.current == this.rollListLength - 1) {
          await this.fetchRewardDetails();
          this.rollList = this.rollLists;
        }
      },
      //获取我的积分
      fetchMycredit() {
        this.$httpUser.get('zx/wap/credit/getCreditCount?userId=' + this.userId).then((res) => {
          this.userCredit = res.data.data;
        });
      },
      // 成为会员
      ToEquity() {
        uni.navigateTo({
          url: '/Personalcenter/my/nomyEquity'
        });
      },
      //请求活动规则
      fetchActivity() {
        this.$httpUser.get('zx/wap/invite/getActivityRule?activityId=' + this.activityId).then((res) => {
          this.activityRuleText = res.data.data;
        });
      },
      //活动规则
      acticityRule() {
        this.$refs.acticityRule.open();
      },
      closeacticityRule() {
        this.$refs.acticityRule.close();
      },
      //邀请记录
      InvitationRecord() {
        uni.navigateTo({
          url: '/InvitationGifts/components/invitationDetai?activityId=' + this.activityId
        });
      },
      //点击好友分享
      activityShare() {
        this.$refs.sharePopup.open();
      },
      closeDialog() {
        this.$refs.sharePopup.close();
      },
      // 分享海报
      sharePoster() {
        this.$refs.sharePopup.close();
        uni.navigateTo({
          url: '/InvitationGifts/components/invitationPoster?activityId=' + this.activityId
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .gray-item {
    color: grey;
  }

  .line {
    height: 30rpx;
    line-height: 30rpx;
  }

  .activity {
    width: 100vw;
    font-size: 26rpx;
    padding-bottom: 130rpx;
    display: flex;
    flex-direction: column;
    position: relative;
    background: url('https://document.dxznjy.com/course/6a5ccbb8a7f645a282630a8f0df97ab4.png') no-repeat;
    background-size: 100vw auto;
    background-color: #fff;
  }

  .activityTop {
    display: flex;
    flex-direction: column;
    padding: 0 20rpx;
    box-sizing: border-box;
    margin-top: 10rpx;
    margin-bottom: 200rpx;
    color: white;
  }

  .activityToprule {
    width: 128rpx;
    line-height: 48rpx;
    text-align: center;
    align-self: flex-end;
    background-color: #0ba582;
    border-radius: 20rpx;
    margin-top: 20rpx;
  }

  .activityMiddle {
    margin: 0 40rpx;
    margin-top: 20rpx;
    height: 760rpx;
    padding-top: 48rpx;
    box-sizing: border-box;
    background: url('https://document.dxznjy.com/course/e37aa82d6e094ea2bb65e0ae5ff5d60b.png') no-repeat;
    position: relative;
    background-size: 100%;
  }

  .activityMiddle2 {
    margin: 0 40rpx;
    margin-top: 20rpx;
    height: 460rpx;
    padding-top: 48rpx;
    box-sizing: border-box;
    background: url('https://document.dxznjy.com/course/e37aa82d6e094ea2bb65e0ae5ff5d60b.png') no-repeat;
    position: relative;
    background-size: 100%;
  }

  .activityInfo {
    width: 90%;
    margin: auto;
    display: flex;
    margin-bottom: 20rpx;
    justify-content: space-around;
  }

  .Activityrecord {
    flex: 1;
    background-color: #e8ffd2;
    border-radius: 20rpx;
    color: #04845f;
    text-align: center;
    font-size: 24rpx;
    line-height: 48rpx;
  }

  .noticebar {
    width: 90%;
    margin: auto;
    height: 100rpx;
    line-height: 100rpx;
  }

  .memberChild {
    margin: 20rpx 0;
  }

  .memberoption {
    background-color: rgb(238, 179, 96);
    color: #b4683d;
    padding: 6rpx;
    box-sizing: border-box;
  }

  .activityGiftPoints {
    width: 622rpx;
    height: 426rpx;
    margin: auto;
    margin-top: 20rpx;
    background: url('https://document.dxznjy.com/course/9073dbdad9cb4707b9844c5f016c28d5.jpg');
    background-size: 100%;
  }

  .activityGiftPoints2 {
    width: 622rpx;
    height: 180rpx;
    margin: auto;
    margin-top: 30rpx;
    padding-top: 20rpx;
    background: url('https://document.dxznjy.com/course/9073dbdad9cb4707b9844c5f016c28d5.jpg');
    background-size: 100%;
  }

  .wode {
    width: 58rpx;
    line-height: 40rpx;
    font-weight: 700;
    background-color: #fee47d;
    position: absolute;
    top: 0;
    left: 0;
    font-size: 20rpx;
  }

  .huiyuan {
    width: 296rpx;
    height: 88rpx;
    background: url('https://document.dxznjy.com/course/5f4e69fc3226497f862af9d73781972f.png');
    background-size: 100%;
    position: absolute;
    left: 25%;
    bottom: -120rpx;
  }

  .member {
    width: 582rpx;
    height: 164rpx;
    background: url('https://document.dxznjy.com/course/f7f7e4cea06d4166a486dc88b9908a98.jpg');
    background-size: cover;
    margin: auto;
    margin-bottom: 20rpx;
    color: #be8748;
    position: relative;
  }

  .memberzhuan {
    width: 116rpx;
    text-align: center;
    line-height: 40rpx;
    background: linear-gradient(to right, #fce17b 0%, #d2a254 100%);
    color: #8e5616;
    font-size: 24rpx;
    border-radius: 20rpx;
    position: absolute;
    right: 0;
  }

  .Nonmembe {
    margin-bottom: 20rpx;
  }

  .Nonmembe,
  .memberGift {
    padding-top: 22rpx;
    box-sizing: border-box;
    display: flex;
    justify-content: space-evenly;
  }

  .NonmembeRegister,
  .NonmembeMember {
    position: relative;
    background-color: #fff;
    width: 266rpx;
    height: 160rpx;
  }

  .NonmembeRegister,
  .NonmembeMember,
  .membeRegister,
  .membeMember {
    width: 40%;
    height: 200rpx;
    text-align: center;
  }

  .activityChild1 {
    margin-top: 52rpx;
    font-weight: 700;
  }

  .activityChild2 {
    margin-top: 20rpx;
    font-size: 32rpx;
    font-weight: 700;
  }

  .activityChild {
    margin-bottom: 20rpx;
    color: #be8748;
    font-size: 32rpx;
  }

  .ExchangeGoodHead {
    width: 532rpx;
    height: 44rpx;
    margin: auto;
    margin-bottom: 20rpx;
    background: url('https://document.dxznjy.com/course/cf6f9e0fd53d4aae88320d7e58bfc653.png');
    background-size: 100%;
  }

  .ExchangeGood {
    width: 100%;
    margin-top: 40rpx;
    box-sizing: border-box;
  }

  .goodGoods {
    text-align: center;
    margin-bottom: 10rpx;
  }

  .giftposition {
    position: absolute;
    top: 766rpx;
  }

  .goodsBox {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
  }

  .goods {
    width: 48%;
    height: 700rpx;
  }

  .goodsImage {
    width: 100%;
  }

  .activityBottom {
    width: 750rpx;
    height: 120rpx;
    position: fixed;
    left: 0;
    bottom: 0;
    text-align: center;
    background-color: #fff;
  }

  .shareFriends {
    width: 686rpx;
    height: 74rpx;
    background: url('https://document.dxznjy.com/course/04ff984de521400a89d2e8289dec9f60.png') no-repeat;
    background-size: cover;
    margin: 23rpx auto;
  }

  // 活动规则弹窗
  .activityRule {
    text-align: center;
    font-weight: bold;
    position: absolute;
    top: 40rpx;
    left: 0;
    width: 100%;
    background: white;
    z-index: 10;
  }

  .activityRule1 {
    text-align: center;
    font-weight: bold;
    margin-bottom: 20rpx;
  }

  .scrollClass {
    height: calc(100vh - 100rpx);
  }

  /*分享弹窗样式*/
  .shareCard {
    position: relative;
    height: 1000rpx;
    background: #ffffff;
    color: #000;
    padding-top: 50upx;
    border-radius: 24upx;
    box-sizing: border-box;
    overflow: hidden;
    width: 90vw;
  }

  .shareCardRecord {
    position: relative;
    height: 100%;
    width: 100vw;
    background: #ffffff;
    color: #000;
    padding-top: 50upx;
    box-sizing: border-box;
    overflow: hidden;
    overflow-y: auto;
  }

  .shareCardFriend {
    position: relative;
    height: 100%;
    background: #ffffff;
    color: #000;
    padding-top: 50upx;
    box-sizing: border-box;
    width: 100%;
  }

  .rule {
    padding: 20rpx;
    line-height: 50rpx;
    margin-top: 60rpx;
    /* 确保不被固定元素遮挡 */
    overflow-y: auto;
    /* 允许滚动 */
    height: calc(100% - 40px);
    /* 适应滚动区域的高度 */
  }

  .shareIcon {
    width: 100upx;
    height: 100upx;
  }

  .shareCancelBox {
    background-color: #f3f8fc;
    width: 100%;
    height: 120upx;
    padding-top: 20upx;
    box-sizing: border-box;
  }

  .share_cancel {
    width: 100%;
    height: 100upx;
    line-height: 100upx;
    text-align: center;
    font-size: 30upx;
    background-color: #fff;
    color: #666666;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }

  .review_close {
    position: absolute;
    /* 固定在右上角 */
    top: 40rpx;
    right: 20rpx;
    z-index: 10;
    /* 确保在上层 */
  }
</style>
