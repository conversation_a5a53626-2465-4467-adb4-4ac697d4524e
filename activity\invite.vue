<template>
  <page-meta :page-style="'overflow:' + (show ? 'hidden' : 'visible')"></page-meta>
  <view>
    <u-sticky bgColor="#F3F8FC">
      <view class="pl-25 positionRelative">
        <u-tabs
          :list="tabList"
          :current="currentIndex"
          keyName="name"
          lineWidth="40"
          lineHeight="11"
          :activeStyle="{ color: '#333333', fontWeight: 'bold', fontSize: '28rpx' }"
          :inactiveStyle="{
            color: '#5A5A5A ',
            transform: 'scale(1)',
            fontSize: '28rpx'
          }"
          itemStyle="padding-left:5px; padding-right: 25px; height: 34px;"
          :lineColor="`url(${lineBg}) 100% 110%`"
          @click="tabsClick"
        ></u-tabs>
      </view>
      <!-- <view class="rule" @click="handleOpenRule">活动说明</view> -->
    </u-sticky>
    <view class="content" v-if="dataList && dataList.length > 0">
      <scroll-view scroll-y="true" @scrolltolower="handleScrollToLower" class="scrollClass">
        <view class="invite-item" v-for="(item, index) in dataList" :key="index">
          <view class="time">{{ item.createdTime }}</view>
          <view class="info">{{ item.content }}</view>
        </view>
      </scroll-view>
    </view>
    <view v-else class="no-data pt-30 pb-55 f-28">
      <image class="no-data-image" src="https://document.dxznjy.com/course/ac587707bf314badadb28a158852c77d.png"></image>
      <view class="c-66 f-32">暂无数据</view>
    </view>

    <!-- 温馨提示 -->
    <uni-popup ref="popupTips" type="center" @change="change">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold">规则说明</view>
            <view class="mb-10 ptb-5 mt-25 font14 desc">
              <view>
                1、本次活动为期两周，在活动期间内，鼎校甄选B端客户均可参与，按照活动规则推广相应课程的正式课即可。活动结束后会按照规则审核确认奖励获得人员。请所有客户遵守活动规则，共同维护一个公平、公正的活动环境。
              </view>
              <view>2、推荐人分享链接后，被邀请人需注册甄选平台（若已注册直接登录即可）之后点开链接查看，即计入邀请人数。</view>
              <view>3、推荐人分享链接后，被邀请人需注册甄选平台（若已注册直接登录即可）之后点开链接购买活动产品，即可计入支付人数</view>
            </view>
            <view class="review_btn" @click="handleOk">确认</view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    components: {},
    data() {
      return {
        show: false,
        dataList: [],
        activityId: '', //活动id
        finished: false,
        page: 1,
        userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '',
        phoneHeight: '',
        currentIndex: 0,
        lineBg: 'https://document.dxznjy.com/course/01c96465d9ea46f69aa97465b1201aef.png',
        tabList: [
          {
            name: '支付人数'
          },
          {
            name: '邀请人数'
          }
        ]
      };
    },
    onLoad(e) {
      this.activityId = e.activityId;
    },
    onShow() {
      const info = uni.getSystemInfoSync();
      this.phoneHeight = info.windowHeight;
      this.showLoading();
      this.currentIndex = 0;
      this.dataList = [];
      this.handleSearch();
      this.getInviteCount();
    },
    onHide() {
      this.dataList = [];
      this.page = 1;
    },
    onReady() {},
    methods: {
      change(e) {
        this.show = e.show;
      },
      closeDialog() {
        this.$refs.popupTips.close();
      },
      handleOk() {
        this.$refs.popupTips.close();
      },
      handleOpenRule() {
        this.$refs.popupTips.open();
      },
      getInviteCount() {
        let userCode = uni.getStorageSync('userCode');
        $http({
          url: 'zx/wap/activity/record/count',
          data: {
            activityId: this.activityId,
            shareUserCode: userCode,
            page: this.page,
            pageNum: 10
          }
        }).then((res) => {
          if (res.data && res.data.length > 0) {
            this.tabList[0].name = `支付人数：${res.data[1] || 0}`;
            this.tabList[1].name = `邀请人数：${res.data[0] || 0}`;
          }
        });
      },
      //获取邀请记录
      getInviteList() {
        let userCode = uni.getStorageSync('userCode');
        $http({
          url: 'zx/wap/activity/invitation/record/find',
          data: {
            activityId: this.activityId,
            shareUserCode: userCode,
            page: this.page,
            pageNum: 10
          }
        }).then((res) => {
          if (res.data && res.data.data.length === 0) {
            this.finished = true;
          } else {
            this.dataList = [...this.dataList, ...res.data.data];
          }
        });
      },
      getPayList() {
        let userCode = uni.getStorageSync('userCode');
        $http({
          url: 'zx/wap/activity/pay/record/find',
          data: {
            activityId: this.activityId,
            shareUserCode: userCode,
            page: this.page,
            pageNum: 20
          }
        }).then((res) => {
          if (res.data && res.data.data.length === 0) {
            this.finished = true;
          } else {
            this.dataList = [...this.dataList, ...res.data.data];
          }
        });
      },
      handleScrollToLower() {
        if (this.finished) {
          return;
        }
        this.page++;
        this.showLoading();
        if (this.currentIndex == 0) {
          this.getInviteList();
        } else {
          this.getPayList();
        }
      },
      showLoading() {
        uni.showLoading({
          title: '加载中',
          mask: true
        });
      },
      handleSearch() {
        this.page = 1;
        this.dataList = [];
        if (this.currentIndex == 0) {
          this.getPayList();
        } else if (this.currentIndex == 1) {
          this.getInviteList();
        }
      },
      tabsClick(e) {
        this.showLoading();
        this.currentIndex = e.index;
        this.finished = false;
        this.handleSearch();
        setTimeout(() => {
          this.getInviteCount();
        }, 500);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .goods {
    width: 48%;
    height: 700rpx;
  }

  .rule {
    position: absolute;
    top: 22rpx;
    right: 32rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #9c9c9c;
    line-height: 28rpx;
  }

  .scrollClass {
    height: calc(100vh - 100rpx);
  }

  .invite-item {
    background: #ffffff;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    margin-bottom: 24rpx;
    padding: 32rpx 24rpx 32rpx 32rpx;

    .time {
      font-weight: normal;
      font-size: 28rpx;
      color: #333333;
      line-height: 33rpx;
    }

    .info {
      margin-top: 24rpx;
      font-weight: 400;
      font-size: 28rpx;
      color: #9c9c9c;
      line-height: 42rpx;
    }
  }

  .content {
    position: relative;
    padding: 24rpx 32rpx;
    box-sizing: border-box;
    overflow: hidden;
    overflow-y: auto;
  }

  .no-data {
    position: relative;
    width: 710rpx;
    margin: auto;
    text-align: center;
    .no-data-image {
      width: 74rpx;
      height: 76rpx;
      display: block;
      margin: 16rpx auto;
    }
  }
  /* 弹窗样式 */
  .dialogBG {
    width: 100%;

    .reviewCard_box {
      width: 670rpx;
      position: relative;
    }

    .reviewCard_box image {
      width: 100%;
      height: 100%;
    }

    .reviewCard {
      position: relative;
      width: 100%;
      height: 100%;
      background: #ffffff;
      color: #000;
      border-radius: 24upx;
      padding: 50upx 55upx;
      box-sizing: border-box;
    }

    .cartoom_image {
      width: 420rpx;
      position: absolute;
      top: -250rpx;
      left: 145rpx;
      z-index: -1;
    }

    .review_close {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      z-index: 1;
    }

    .desc {
      color: #9c9c9c;

      > view {
        margin-bottom: 5rpx;
      }
    }

    .reviewTitle {
      width: 100%;
      text-align: center;
      font-size: 34upx;
      display: flex;
      justify-content: center;
    }

    .dialogContent {
      box-sizing: border-box;
      font-size: 32upx;
      line-height: 45upx;
      text-align: center;
      margin-top: 40rpx;
    }

    .review_btn {
      width: 240upx;
      height: 80upx;
      background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
      border-radius: 45upx;
      font-size: 30upx;
      color: #ffffff;
      line-height: 80upx;
      margin: 60rpx auto 0 auto;
      justify-content: center;
      text-align: center;
    }
  }
</style>
