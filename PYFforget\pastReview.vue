<template>
  <view class="contaiter">
    <view class="bg-h">
      <view class="positioning" @click="goback">
        <uni-icons type="left" size="24" color="#000"></uni-icons>
      </view>
      <view class="word-position t-c col-12" style="">
        <view class="f-34">往期复习</view>
      </view>
    </view>
    <view class="top">
      <text>复习课程</text>
      <text>日期</text>
      <text>查看详情</text>
    </view>
    <view class="history-item">
      <view class="list" v-for="(item, index) in historylist" :key="index">
        <view class="courseLevel" v-if="item.courseLevel">{{ courseLevelList.find((e) => e.value == item.courseLevel).label }}</view>
        <text>{{ item.pdCourseName }}</text>
        <text>{{ item.reviewTime.split(' ')[0] }}</text>
        <image src="https://document.dxznjy.com/course/1cebe1a9493c416db68cea8233704f95.png" mode="" @click="goUrl(item.id)" class="list-img"></image>
      </view>
      <uni-load-more class="mb-65" :status="loadingType"></uni-load-more>
      <!-- <view class="zanwu" :status="loadingType">没有更多数据了~</view> -->
    </view>
    <view v-if="historylist.length == 0" class="t-c flex-col mt-30 bg-ff radius-15 mlr-30" :style="{ height: useHeight + 'rpx' }">
      <image src="https://document.dxznjy.com/alading/correcting/no_data.png" class="mb-20" style="width: 160rpx" mode="widthFix"></image>
      <view style="color: #bdbdbd">暂无数据</view>
    </view>
  </view>
</template>

<script>
  import uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';
  export default {
    components: {
      uniLoadMore
    },
    onLoad(options) {
      console.log('options', options);
      this.studentCode = uni.getStorageSync('pyfStudentCode');
      console.log(this.studentCode);
      this.loadMyMember();
    },
    data() {
      return {
        aa: true,
        // aa:false,
        loadingType: 'more', //加载前
        historylist: [],
        pageindex: 1, //当前页码
        pageSize: 20,
        studentCode: '',
        useHeight: 0, //除头部之外高度
        courseLevelList: [
          { value: '0', label: '低年级' },
          { value: '1', label: '基础' },
          { value: '2', label: '高年级' }
        ]
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 460;
        }
      });
    },
    methods: {
      goback() {
        // uni.navigateBack()
        uni.navigateTo({
          url: '/PYFforget/forgetReview'
        });
      },
      async loadMyMember(type = 'add', loading) {
        if (type === 'add') {
          if (this.loadingType === 'no-more') {
            return;
          }
          this.loadingType = 'loading'; //加载中
        } else {
          this.loadingType = 'more'; //加载前
        }
        var mindex = this.pageindex;
        // let result = await this.$httpUser.get('znyy/review/query/student/word/review/' + mindex + '/' + this
        // 	.pageSize + '/' + this.studentCode)
        let result = await this.$httpUser.post('znyy/pd/planReview/queryReviewedParents', {
          pageNum: this.pageindex,
          pageSize: this.pageSize,
          studentCode: this.studentCode
          // studentCode:'111'
        });
        if (result) {
          if (type === 'refresh') {
            this.historylist = [];
          }

          // if (mindex <= 1 && result.data.data.data.length == 0) {
          if (result.data.data.data.length == 0) {
            this.loadingType = 'no-more';
          } else {
            if (result.data.data.data.length) {
              this.historylist = this.historylist.concat(result.data.data.data);
            }
            this.loadingType = this.pageindex >= result.data.data.totalPage ? 'no-more' : 'more';
          }
        }
      },
      goUrl(id) {
        // redirectTo关闭当前页面,跳转到其他页面
        console.log(id);
        uni.redirectTo({
          url: `/PYFforget/reviewReport?planReviewId=${id}`
        });
      }
    },
    onPageScroll(e) {
      if (e.scrollTop >= 0) {
        this.headerPosition = 'fixed';
      } else {
        this.headerPosition = 'absolute';
      }
    },
    onPullDownRefresh() {
      console.log('下拉刷新触发');
      this.pageindex = 1;
      this.loadMyMember('refresh');
    },
    //加载更多
    onReachBottom() {
      this.pageindex++;
      this.loadMyMember();
    }
  };
</script>

<style>
  .contaiter {
    margin: 0 auto;
    /* padding-bottom: 100rpx; */
    /* 		margin-top: 10rpx; */
    width: 690rpx;
    /* height: 92vh; */
    /* '		height: 1311rpx;' */
    background: #ffffff;
    padding: 20rpx 20rpx 100rpx;
    border-radius: 14rpx;
  }

  page {
    background: #f3f8fc !important;
  }

  .bg-h {
    position: fixed; /* 确保背景颜色显示 */
    top: 0; /* 固定在页面顶部 */
    left: 0;
    width: 100%; /* 使背景颜色覆盖整个宽度 */
    z-index: 8; /* 高于其他内容 */
  }
  .positioning {
    position: fixed;
    top: 110rpx;
    left: 30rpx;
    z-index: 9;
  }

  .word-position {
    position: fixed;
    top: 0;
    left: 0;
    background-color: #f3f8fc;
    height: 190rpx;
    padding-top: 110rpx;
    box-sizing: border-box;
  }
  .top {
    margin-top: 180rpx;
    height: 80rpx;
    display: flex;
    align-items: center;
    justify-content: space-around;
    font-size: 30rpx;
    background: #fbfbfb;
    font-weight: bold;
    /* border-bottom: 1px solid #e5e5e5; */
    padding-left: 70rpx;
  }

  .list {
    position: relative;
    display: flex;
    align-items: center;
    font-size: 30rpx;
    margin-top: 18rpx;
    min-height: 131rpx;
    background-color: #f9f9f9;
    /* height: 300rpx; */
    color: #666;
    padding-left: 100rpx;
  }

  .list-img {
    width: 52rpx;
    height: 52rpx;
    margin-left: 88rpx;
  }

  .list text {
    display: block;
    width: 33%;
    text-align: center;
  }
  .courseLevel {
    position: absolute;
    width: 89rpx;
    height: 52rpx;
    left: 0;
    top: 0;
    background: url('https://document.dxznjy.com/course/fdb49a412f4c483e80b1c69e005b3a8d.png') no-repeat;
    background-size: contain;
    color: #fff;
    font-size: 23rpx;
    line-height: 52rpx;
    text-align: center;
  }
  .zanwu {
    margin: 0 auto;
    margin-top: 20rpx;
    margin-bottom: 20rpx;
    text-align: center;
    font-size: 28rpx;
    color: #b3b7ba;
  }

  .timeFn {
    width: 80rpx;
    margin-left: 40rpx;
  }

  .wordsFn {
    margin-left: 39rpx;
  }

  .list-timer {
    width: 182rpx !important;
  }

  .detail_btn {
    width: 140rpx;
    height: 58rpx;
    background-color: #007aff;
    text-align: center;
    line-height: 58rpx;
    color: #fff;
    border-radius: 40rpx;
  }
  .mt-180 {
    margin-top: 180rpx;
  }
</style>
