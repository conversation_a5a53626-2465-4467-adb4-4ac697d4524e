<!-- 学习超人或者超人俱乐部界面 -->
<template>
  <page-meta :page-style="'overflow:' + (rollShow ? 'hidden' : 'visible')"></page-meta>
  <view class="positionRelative">
    <view class="title w100">
      <view class="positionAbsolute back" @click="goBack">
        <uni-icons type="back" size="25"></uni-icons>
      </view>
      <view class="t-c f-32" style="margin: 110rpx 0 30rpx">{{ type != 2 ? '超人俱乐部' : '学习超人' }}</view>
    </view>
    <!-- <web-view :src="weburl" @message="getMessage"></web-view> -->
    <image v-if="type == 3 || type == 4" :src="imgHost + 'dxSelect/three/images/club_detail.jpg'" class="img-club"></image>
    <image v-if="type == 2" :src="imgHost + 'dxSelect/three/images/superman_detal.jpg'" mode="widthFix" class="img-superman"></image>
    <view class="foot-content">
      <view class="learningmore" @click="getMessage('learnMore')">了解更多</view>
      <view class="join" @click="getMessage('joinNow')">立即加入</view>
    </view>

    <u-toast ref="uToast"></u-toast>

    <uni-popup ref="popup" type="bottom" @change="change">
      <view class="content-popup">
        <view class="p-30">
          <view class="icon-clear" @click="closePopup">
            <uni-icons type="clear" size="30" color="#B1B1B1"></uni-icons>
          </view>
          <view class="p-30 flex bg-ff radius-15 mt-30" @tap="selectadd">
            <image :src="imgHost + 'dxSelect/address_icon.png'" class="icon_dw"></image>
            <view class="flex-box mlr-20">
              <view class="f-30 mb-20" v-if="addressInfo.buyerName">
                <text class="mr-30">{{ addressInfo.buyerName }}</text>
                <text>{{ addressInfo.buyerPhone }}</text>
              </view>
              <view class="f-30 mr-30" v-else>请选择收货地址</view>
              <view class="f-30 c-88">{{ addressInfo.address }}</view>
            </view>
            <image :src="imgHost + 'dxSelect/image/right.png'" class="arrow" mode="widthFix"></image>
          </view>

          <view class="plr-30 pb-30 bg-ff radius-15 mt-30">
            <view>
              <view class="ptb-30 f-32">订购内容:超级会员套餐</view>
            </view>
            <view>
              <view class="ptb-30 f-32">应付金额:￥{{ cartTotalPrice }}元</view>
            </view>
            <view style="display: flex; align-items: center">
              <view class="ptb-30 f-32">会员开始时间:{{ memberStartTime }}</view>
              <view class="ptb-30 f-32" style="font-size: 24rpx; color: darkgray">(具体以实际付款为准)</view>
            </view>
            <view>
              <view class="ptb-30 f-32">有效期:1年</view>
            </view>

            <view class="plr-30 flex">
              <view class="paybtn f-32 c-ff" @click="onSubmitOrder">
                立即支付
                <text class="ml-10">￥{{ cartTotalPrice }}</text>
              </view>
              <!-- <button class="paybtn f-32 c-ff" v-if="userinfo.mobile" :disabled="disabled"
								@click="onSubmitOrder">立即付款</button>
							<button class="paybtn f-32 c-ff" v-else open-type="getPhoneNumber"
								@getphonenumber="onGetPhoneNumber">立即支付 ￥{{cartTotalPrice}}</button> -->
            </view>
          </view>

          <!-- 		<view class="plr-30 pb-30 bg-ff radius-15 mt-30">
						<view>
							<view class="ptb-30 f-32 bold">订单备注</view>
							<view class="p-30 bg-f7">
								<textarea placeholder="请输入" placeholder-style="color:#999" @input="inputtext" class="remark f-30"></textarea>
							</view>
						</view>
					</view>
					
					<view class="ptb-30 plr-30 bg-ff radius-15 flex_s mtb-30 f-30 ">
						<view>超人码：</view>
						<input class="c-99 flex_1 pl-20" type="number" v-model="invitationInfo.invitationCode" disabled/>
					</view> -->
        </view>
      </view>
    </uni-popup>

    <uni-popup ref="sharePopup" type="center">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="mt-40 bold t-c">提示</view>
            <view class="reviewTitle pb-20 mt-40" v-if="renewType == 1">学习超人未到期，不能从非上级处续费</view>
            <view class="reviewTitle pb-20 mt-40" v-if="renewType == 2">从该链接购买学习超人会更换上级，换绑上级会失去所有的下级家长，是否确认在此链接购买？</view>

            <view class="flex-s mt-60">
              <view class="review_btn" @click="changeShareType">确定</view>
              <view class="close_btn" @click="shutDialog">取消</view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  const { $http, $showMsg } = require('@/util/methods.js');
  import Superman from '@/common/superman.js';
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  export default {
    data() {
      return {
        payInfo: {},
        flag1: false,
        type: '', //type 2 学习超人    3  超人俱乐部  4 超人俱乐部升级
        userId: '', //分享人id
        weburl: '',
        webviewStyles: {
          progress: {
            color: '#FF3333'
          },
          height: '80%'
        },
        clickType: '', //点击  learnMore 了解更多    joinNow 加入学习超人/超人俱乐部  upNow 俱乐部升级
        secCode: '', // 判断用户是否从分享进入
        userinfo: {},
        shareId: '', //分享id
        invitationInfo: {}, // 邀请信息
        sharelist: {},
        memberStartTime: '',
        imgHost: getApp().globalData.imgsomeHost,

        addressInfo: {
          address: '',
          buyerName: '',
          buyerPhone: '',
          addressId: ''
        },
        params: {
          type: 'default',
          message: '支付成功',
          duration: 3000,
          url: '/Personalcenter/my/partnerUnion'
        },
        cartTotalPrice: 0,
        useCoin: false,
        remark: '', // 订单备注
        code1: '',
        // shareId:'',		//分享id
        // invitationInfo:{}, // 邀请信息
        recommendInfo: {
          //推荐人信息
          mobile: '无',
          name: ''
        },
        trialclassStudent: '', //试课学员姓名
        orderId: '',
        parentMobile: '', //家长联系方式
        parentFocuse: false,
        referrerFocuse: false,
        showTrialClass: false, //是否展示试课单

        radio: 0, // 1使用鼎学币 0 不使用
        range: [
          {
            text: '',
            value: 1
          }
        ],

        renewType: '', // 0继续进行 1未到期，从非上级分享链接点击进入 2已到期，从非上级分享链接点击进入
        renreShow: false,
        // secCode:""

        rollShow: false, //禁止滚动穿透
        list: {} // 用户是否有申请、升级
      };
    },

    onReady() {
      // #ifdef APP-PLUS
      this.clearMuiBack();
      // #endif
    },
    async onShow() {
      if (this.flag1) {
        uni.$tlpayResult(this.sucees, this.fail, this.payInfo.orderId);
      }
      // this.$refs.sharePopup.open()
      let _this = this;
      // if(uni.getStorageSync('token')){
      // 	await _this.homeData()

      // }
      let startTime = new Date();
      let year = startTime.getFullYear();
      let month = (startTime.getMonth() + 1).toString().padStart(2, '0');
      let day = startTime.getDate().toString().padStart(2, '0');
      _this.memberStartTime = `${year}-${month}-${day}`;
      if (uni.getStorageSync('token')) {
        _this.getUserIfApply();
        await _this.homeData();
        uni.$once('updateAddress', (data) => {
          console.log(data);
          _this.addressInfo.buyerName = data.plate.consigneeName;
          _this.addressInfo.buyerPhone = data.plate.consigneePhone;
          _this.addressInfo.addressId = data.plate.addressId;
          _this.addressInfo.address = data.plate.provinceName + data.plate.cityName + data.plate.districtName + data.plate.address;
        });
      }
      _this.disabled = false;
      uni.login({
        success(res) {
          _this.code1 = res.code;
        }
      });
    },
    onReady() {},

    onLoad(options) {
      let a = uni.getStorageSync('club');
      if (a) {
        uni.switchTab({
          url: '/pages/home/<USER>/index'
        });
      }
      let _this = this;
      // debugger
      _this.type = options.type;
      console.log(_this.type);
      this.shareId = options.userId;
      // 判断推荐人信息
      // _this.getInvitation(options);
      if (options.secCode != '' && options.secCode != undefined) {
        _this.secCode = options.secCode;
      }
      if (uni.getStorageSync('secCode')) {
        _this.secCode = uni.getStorageSync('secCode');
        // 取过值之后立马清除本地缓存
        uni.removeStorageSync('secCode');
      }
    },
    methods: {
      async sucees() {
        this.flag1 = false;
        let shareid = uni.getStorageSync('shareId') ? uni.getStorageSync('shareId') : '';
        if (shareid === '') {
          return;
        }
        let userinfo = await Superman.getUserInfo();
        let localip = uni.getStorageSync('localip') ? uni.getStorageSync('localip') : '';
        let activityid = uni.getStorageSync('activityId') ? uni.getStorageSync('activityId') : '';
        console.log(userinfo, '邀请活动信息');
        console.log(localip, '邀请活动ip');
        this.$httpUser.post('zx/wap/invite/saveInviteData', {
          userId: shareid,
          activityId: activityid,
          type: '2',
          inviteeOpenId: userinfo.openId,
          inviteeNickName: userinfo.nickName,
          inviteePhone: userinfo.mobile,
          ipAddress: localip
        });
        console.log(shareid, '邀请活动上级id');
        console.log(userinfo.mobile, '邀请活动手机号');
        console.log(userinfo.nickName, '邀请活动昵称');
        console.log(userinfo.openId, '邀请活动oprnid');
        console.log(localip, '邀请活动ip');
        this.redirectToOrderIndex();
      },
      fail() {
        this.flag1 = false;
      },
      fails() {
        uni.showToast({
          title: '支付失败',
          icon: 'none',
          duration: 2000
        });
        this.flag1 = false;
      },
      //解决返回该界报错问题
      clearMuiBack() {
        var currentWebview = this.$scope.$getAppWebview().children()[0];
        //监听注入的js
        currentWebview.addEventListener('loaded', function () {
          currentWebview.evalJS('mui.init({keyEventBind: {backbutton: false }});');
        });
      },

      // 禁止滚动穿透
      change(e) {
        this.rollShow = e.show;
      },

      async getMessage(val) {
        this.clickType = val;
        let token = uni.getStorageSync('token');
        if (!token) {
          uni.navigateTo({
            url: `/Personalcenter/login/login`
          });
          return;
        }
        // this.
        // 学习超人
        let _this = this;
        if (_this.type == 2) {
          // 学习超人下单页初始执行逻辑
          await _this.studySupermanInit();
        }
        if (_this.type == '2') {
          // 了解更多
          if (_this.clickType == 'learnMore') {
            // #ifdef APP-PLUS
            uni.navigateTo({
              url: `/supermanClub/superman/learnMore?name=learningSuperman&type=${_this.type}`
            });
            // #endif

            // #ifdef MP-WEIXIN
            uni.redirectTo({
              url: `/supermanClub/superman/learnMore?name=learningSuperman&type=${_this.type}`
            });
            // #endif
          }
        } else {
          //3,4都是超人俱乐部
          //了解更多
          if (_this.clickType == 'learnMore') {
            // #ifdef APP-PLUS
            uni.navigateTo({
              url: `/supermanClub/superman/learnMore?name=supermanClub&type=${_this.type}`
            });
            // #endif

            // #ifdef MP-WEIXIN
            uni.redirectTo({
              url: `/supermanClub/superman/learnMore?name=supermanClub&type=${_this.type}`
            });
            // #endif
          }
          // 加入超人俱乐部
          else if (_this.type == '3') {
            if (_this.list.type == 0) {
              // #ifdef APP-PLUS
              uni.navigateTo({
                url: '/supermanClub/clubManagement/clubUpgrade?shareId=' + _this.shareId
              });
              // #endif

              // #ifdef MP-WEIXIN
              uni.redirectTo({
                url: '/supermanClub/clubManagement/clubUpgrade?shareId=' + _this.shareId
              });
              // #endif
            } else {
              // #ifdef APP-PLUS
              uni.navigateTo({
                url: '/supermanClub/clubApplication/applicationWaiting?list=' + JSON.stringify(this.list) + '&type=3'
              });
              // #endif

              // #ifdef MP-WEIXIN
              uni.navigateTo({
                url: '/supermanClub/clubApplication/applicationWaiting?list=' + JSON.stringify(this.list) + '&type=3'
              });
              // #endif
            }
          }

          //俱乐部升级
          else if (_this.type == '4') {
            console.log('立即升级');

            if (this.list.type == 0) {
              // #ifdef APP-PLUS
              uni.navigateTo({
                url: '/supermanClub/clubManagement/clubUpgrade'
              });
              // #endif

              // #ifdef MP-WEIXIN
              uni.navigateTo({
                url: '/supermanClub/clubManagement/clubUpgrade'
              });
              // #endif
            } else {
              // #ifdef APP-PLUS
              uni.navigateTo({
                url: '/supermanClub/clubApplication/applicationWaiting?list=' + JSON.stringify(this.list) + '&type=4'
              });
              // #endif

              // #ifdef MP-WEIXIN
              uni.navigateTo({
                url: '/supermanClub/clubApplication/applicationWaiting?list=' + JSON.stringify(this.list) + '&type=4'
              });
              // #endif
            }
          }
        }
      },

      closePopup() {
        this.$refs.popup.close();
        this.addressInfo = {};
      },

      goBack() {
        if (this.shareId) {
          uni.reLaunch({
            url: '/pages/index/index'
          });
        } else {
          uni.navigateBack();
        }
      },

      // 获取首页信息
      async homeData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.userinfo = res.data;
        }
      },

      // 初始获取分享人信息
      async getInvitation(e) {
        let _this = this;
        _this.invitationInfo = uni.getStorageSync('invitationInfo');

        if (_this.invitationInfo) {
          _this.shareId = _this.invitationInfo.userId;

          // 取过值之后立马清除本地缓存
          uni.removeStorageSync('invitationInfo');
        }
      },

      // 学习超人初始化
      async studySupermanInit() {
        let _this = this;
        _this.detail();
        // debugger
        // 判断自己是否是俱乐部，如果自己是俱乐部，那么邀请码只能是自己的
        if (_this.userinfo.identityType == 2) {
          _this.shareId = _this.userinfo.userId;
          _this.invitationInfo = await Sumperman.getInvitationCodeByUserId(_this.shareId);
        }
        // 自己不是俱乐部正常走流程
        else {
          // debugger
          // 有邀请码
          if (_this.invitationInfo != '' && _this.invitationInfo.invitationCode != '') {
            _this.invitationInfo = _this.invitationInfo;
          } else {
            // 无邀请码的时候根据分享人id获取邀请码
            if (_this.shareId) {
              _this.invitationInfo = await Sumperman.getInvitationCodeByUserId(_this.shareId);
            }
          }
        }
        await _this.getRenewal();
      },

      // 判断是否是续费
      async getRenewal() {
        let _this = this;
        const res = await $http({
          url: 'zx/meal/canRenew',
          data: {
            secCode: (_this.invitationInfo.secCode ? _this.invitationInfo.secCode : _this.secCode) || ''
          }
        });
        if (res) {
          this.renewType = res.data;
          console.log('000000000000000');
          console.log(res);
          console.log(this.renewType);
          // debugger
          if (this.renewType == 0) {
            this.$refs.popup.open();
          } else {
            this.$refs.sharePopup.open();
          }
          console.log('000000000000000');
        }
      },

      changeShareType() {
        this.renreShow = true;
        this.$refs.sharePopup.close();
      },

      closeDialog() {
        this.renreShow = false;
        this.$refs.sharePopup.close();
      },

      shutDialog() {
        uni.reLaunch({
          url: '/pages/index/index'
        });
      },

      selectadd() {
        uni.navigateTo({
          url: '/splitContent/address/list/list?from=goods' + '&addressId=' + this.addressInfo.addressId
        });
      },

      // 学习超人获取商品详情
      async detail() {
        let _this = this;
        let courseId = 'd7a4373a089525cb2d195840b3985788';
        const res = await $http({
          url: 'zx/meal/mealDetail',
          data: {
            mealId: courseId
          }
        });
        if (res) {
          _this.cartTotalPrice = res.data.mealPrice;
          console.log(_this.cartTotalPrice);
        }
      },

      inputtext(e) {
        console.log(e);
        this.remark = e.detail.value;
      },

      // 提交订单
      async onSubmitOrder() {
        let _this = this;
        // if(!_this.renreShow){
        // 	if(_this.invitationInfo.secCode!=''&&_this.invitationInfo.secCode!=undefined){
        // 		if(_this.renewType==1 || _this.renewType==2){
        // 			_this.$refs.sharePopup.open();
        // 			return;
        // 		}
        // 	}
        // }
        if (_this.disabled) {
          return false;
        }
        // 表单验证（暂时先去掉地址信息）
        if (!_this._onVerify()) {
          return false;
        }
        uni.showLoading({
          title: '提交中'
        });
        _this.disabled = true;

        _this.mealPay();
      },

      // 套餐支付
      async mealPay() {
        let _this = this;
        let params = {};
        let result = [
          {
            buyNum: 1,
            courseId: 'd7a4373a089525cb2d195840b3985788'
          }
        ];
        _this.id = result[0].courseId;
        console.log(_this.invitationInfo);
        console.log('支付信息');
        params.address = _this.addressInfo.address;
        params.buyerName = _this.addressInfo.buyerName;
        params.buyerPhone = _this.addressInfo.buyerPhone;
        params.invitationCode = _this.invitationInfo.invitationCode;
        params.secCode = _this.invitationInfo.secCode;
        params.mealId = _this.id;
        params.shareId = _this.shareId;
        params.remark = _this.remark;
        params.courseAndNumDtoList = result;
        const res = await $http({
          url: 'zx/meal/mealPlaceOrder',
          method: 'post',
          data: params
        });
        _this.disabled = false;
        if (res) {
          _this.payBtn(res.data);
        }
      },

      async payBtn(data) {
        let _this = this;
        let resdata = await httpUser.post('mps/line/collect/order/unified/collect', data);
        let res = resdata.data.data;
        _this.disabled = false;
        console.log(res);
        if (res) {
          if (res.openAllinPayMini) {
            this.flag1 = true;
            this.payInfo = res;
            uni.$payTlian(res);
          } else {
            uni.requestPayment({
              provider: 'wxpay',
              timeStamp: res.payInfo.timeStamp,
              nonceStr: res.payInfo.nonceStr,
              package: res.payInfo.packageX,
              signType: res.payInfo.signType,
              paySign: res.payInfo.paySign,
              success: function (ress) {
                console.log('支付成功');
                // _this.successpay(id);
                _this.redirectToOrderIndex();
              },
              fail: function (err) {
                uni.showToast({
                  title: '支付失败'
                });
                setTimeout(function () {
                  uni.redirectTo({
                    url: '/splitContent/order/order'
                  });
                }, 1500);
              }
            });
          }
        }
      },
      async redirectToOrderIndex() {
        let _this = this;
        _this.$refs.uToast.show({
          ..._this.params,
          complete() {
            console.log(_this.params);
            _this.params.url &&
              uni.redirectTo({
                url: _this.params.url + '?type=' + _this.type + '&id=' + _this.id
              });
          }
        });
      },
      _onVerify() {
        let _this = this;
        if (!_this.addressInfo.buyerName) {
          $showMsg('请选择地址');
          return false;
        }
        if (!_this.addressInfo.buyerPhone) {
          $showMsg('请选择地址');
          return false;
        }
        if (!_this.addressInfo.address) {
          $showMsg('请选择地址');
          return false;
        }

        return true;
      },

      // 判断登陆用户是否已经有申请俱乐部或者升级俱乐部
      async getUserIfApply() {
        const res = await $http({
          url: 'zx/merchant/getUserIfApplyOrUpMerchant'
        });
        // debugger
        if (res.status == 1) {
          this.list = res.data;
          console.log(res);
          console.log(this.list);
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .back {
    left: 10rpx;
    top: 106rpx;
  }
  .foot-content {
    position: fixed;
    display: flex;
    bottom: 0;
    width: 100%;
    z-index: 9;
    padding: 50rpx 0 60rpx;
    justify-content: center;
    background-color: #fff;
  }
  .learningmore {
    border: 1px solid #2e896f;
    border-radius: 45rpx;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    width: 250rpx;
    color: #2e896f;
    font-size: 30rpx;
    margin-right: 30rpx;
    box-sizing: border-box;
  }

  .join {
    width: 250upx;
    height: 90upx;
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 90upx;
    text-align: center;
    margin-left: 30rpx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
  }

  .title {
    position: fixed;
    top: 0;
    background-color: #fff;
  }

  .img-club {
    width: 100%;
    height: 7024rpx;
    margin-top: 170rpx;
    margin-bottom: 190rpx;
  }

  .img-superman {
    width: 100%;
    height: 2180rpx;
    margin-top: 170rpx;
    margin-bottom: 190rpx;
  }

  .content-popup {
    background-color: #f3f8fc;
    border-top-left-radius: 45rpx;
    border-top-right-radius: 45rpx;
  }

  .icon_dw {
    width: 30rpx;
    height: 32rpx;
  }

  .remark {
    height: 150rpx;
    width: 100%;
    overflow: scroll;
  }

  .icon-clear {
    display: flex;
    justify-content: flex-end;
  }

  .flex_s {
    display: flex;
    align-items: center;
  }

  .flex_1 {
    flex: 1;
  }

  .paybtn {
    width: 100%;
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    border-radius: 45rpx;
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
  }

  // 分享选项弹出层
  .dialogBG {
    width: 100%;
    /* height: 100%; */
  }

  /* 21天结束复习弹窗样式 */
  .reviewCard_box {
    width: 670rpx;
    /* height: 560rpx; */
    position: relative;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }

  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 45upx 65upx;
    box-sizing: border-box;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 145rpx;
    z-index: -1;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }

  .review_btn {
    width: 250upx;
    height: 80upx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 80upx;
    justify-content: center;
    text-align: center;
  }

  .close_btn {
    width: 250upx;
    height: 80upx;
    color: #2e896f;
    font-size: 30upx;
    line-height: 80upx;
    text-align: center;
    border-radius: 45upx;
    box-sizing: border-box;
    border: 1px solid #2e896f;
  }
</style>

<style>
  page {
    background-color: #f3f8fc;
  }
</style>
