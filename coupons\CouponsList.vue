<template>
  <view>
    <view class="list-tabs">
      <view @click="switchTab(1)" class="item-tab" :class="{ 'is-active': active === 1 }">
        <view :class="{ 'rounded-border': active === 1 }">{{ `未使用(${total}张)` }}</view>
      </view>
      <view class="border-box"></view>
      <view @click="switchTab(2)" class="item-tab" :class="{ 'is-active': active === 2 }">
        <view :class="{ 'rounded-border': active === 2 }">已使用</view>
      </view>
      <view class="border-box"></view>
      <view @click="switchTab(3)" class="item-tab" :class="{ 'is-active': active === 3 }">
        <view :class="{ 'rounded-border': active === 3 }">已过期</view>
      </view>
    </view>
    <view class="coupons">
      <!-- <view v-if="active === 1" class="share-coupons" @click="getShareList">可分享优惠券</view> -->
      <template v-if="couponsList && couponsList.length > 0">
        <view v-for="(item, index) in couponsList" class="item-coupons">
          <view class="coupon-main">
            <view class="activity-type">
              <!-- 优惠券类型 1:满减，2:折扣 -->
              <view class="activity-name" :class="{ '.used-color': active === 2 || active === 3 }">
                {{ item.couponType === 1 ? `${item.couponDiscount}` : `${item.couponDiscount}` }}
              </view>
              <!-- 是否有门槛 1:有，0:无 -->
              <view class="activity-limit" :class="{ 'used-color': active === 2 || active === 3 }">
                {{ item.couponHasCondition === 1 ? `满${item.couponCondition}减` : '无门槛' }}
              </view>
            </view>
            <view class="activity-content">
              <view class="title">{{ item.couponName }}</view>
              <view class="time">有效期至：{{ item.effectiveEndTime }}</view>
            </view>
            <view v-if="active == 1 && !isShare" class="activity-use" @click="skintap(item)">去使用</view>
            <view v-else-if="active == 2" class="used-status">{{ item.useStatus == 2 ? '已使用' : item.useStatus == 5 ? '已分享' : '' }}</view>
          </view>
          <view class="rules">
            <view style="display: flex; align-items: center; width: 318rpx" @click="switchExpand(index)">
              <text>使用规则</text>
              <view class="expand-icon">
                <image v-if="expandList[index]" src="../static/index/icon_shou.png" class="icon-down"></image>
                <image v-else src="../static/index/icon_xia.png" class="icon-down"></image>
              </view>
            </view>
            <view v-if="active == 1 && item.shareable == 1">
              <!-- 需要生成兑换码为true createRedemptionCode=1 则不在这里分享 -->
              <button class="activity-share" @click="handleShare(item)" :open-type="item.createRedemptionCode == 1 ? '' : 'share'">去分享</button>
            </view>
          </view>
          <view class="rules-text" v-if="expandList[index]">
            <!-- <text v-if="item.couponTaskType == 1"> -->
            <!-- 【券有效期】获得券当日起7个自然日内有效，过期作废 -->
            <!-- 【使用说明】 （1）本券不兑换现金、不设找零，不退不换 （2）本券不支持与其他优惠活动同享 （3）本券仅支持被转赠人使用
              （4）用户不得以不合法、不正当、不诚信的方式领券（包括但不限于批量注册、利用作弊工具或者通过违法技术手段参与领券、恶意套取优惠券、虚假交易、刷单等），否则鼎校甄选有权撒销用户的参与资格，并有权收回用户已取得的优惠券权益；
              （5）成功领取后，用户可在”【鼎校甄选小程序】【我的】-【优惠券】“中查看券使用范围等具体使用规则并使用相关权益。
            </text>
            <text v-else-if="item.couponType == 1 || item.couponType == 2"> -->
            <!-- 【券有效期】获得券当日起7个自然日内有效，过期作废 -->
            <text>【使用说明】</text>
            <text>（1）本券不兑换现金、不设找零，不退不换；</text>
            <text>（2）本券不支持与其他优惠活动同享；</text>
            <text>
              （3）用户不得以不合法、不正当、不诚信的方式领券（包括但不限于批量注册、利用作弊工具或者通过违法技术手段参与领券、恶意套取优惠券、虚假交易、刷单等)，否则鼎校甄选有权撤销用户的参与资格，并有权收回用户已取得的优惠权益；
              （4）成功领取后，用户可在”【我的】-【优惠券】“中查看券使用范围等具体使用规则并使用相关权益。
            </text>
          </view>
        </view>
      </template>
      <view v-else class="t-c flex-col" :style="{ height: 500 + 'rpx' }">
        <image :src="imgHost + 'alading/correcting/no_data.png'" style="width: 160rpx" class="mb-20 img_s" mode="widthFix"></image>
        <view style="color: #bdbdbd">暂无数据</view>
      </view>
    </view>
    <RedeemCodeVue ref="codePopup" />
  </view>
</template>

<script>
  const { $http, $navigationTo } = require('@/util/methods.js');
  import RedeemCodeVue from './RedeemCode.vue';
  export default {
    components: {
      RedeemCodeVue
    },
    data() {
      return {
        active: 1,
        pageNum: 1,
        pageSize: 10,
        couponsList: [],
        couponsTotal: 0,
        total: 0,
        isShare: false,
        expandList: [],
        shareGoodsId: '',
        imgHost: getApp().globalData.imgsomeHost,
        shareDataInfo: {},
        couponCode: '',
        shareReceiveNo: ''
      };
    },
    onLoad(e) {
      if (!uni.getStorageSync('token')) {
        uni.navigateTo({
          url: '/Personalcenter/login/login'
        });
      }
      if (e.userId && e.couponId && e.receiveNo) {
        uni.setStorageSync('coupons_userId', e.userId);
        uni.setStorageSync('coupons_couponId', e.couponId);
        this.receivedCoupon(e.userId, e.couponId, e.wxShareCode, e.receiveNo);
      }

      if (e.userId && e.couponCode) {
        uni.setStorageSync('coupons_userId', e.userId);
        uni.setStorageSync('couponCode', e.couponCode);
        this.receivedCouponCode();
      }
    },
    onShow() {
      this.pageNum = 1;
      this.couponsList = [];
      this.isShare = false;
      this.getCouponsList();
    },
    onShareAppMessage() {
      const timestamp = new Date().getTime(); // 当前时间戳
      const randomNum = Math.floor(Math.random() * 100000000); // 生成一个较大的随机数

      return {
        title: '送您一份小心意',
        imageUrl: 'https://document.dxznjy.com/dxSelect/coupon.png', //分享封面
        //如果有参数的情况可以写path
        path: `/coupons/CouponsList?userId=${uni.getStorageSync('user_id')}&${
          this.shareDataInfo && this.shareDataInfo.createRedemptionCode == 1 ? 'couponCode=' + this.couponCode : 'couponId=' + this.shareGoodsId
        }&wxShareCode=${timestamp}_${randomNum}&receiveNo=${this.shareReceiveNo}`
      };
    },
    methods: {
      async handleShare(item) {
        this.shareGoodsId = item.couponId;
        this.shareReceiveNo = item.receiveNo;
        // 有生成兑换码则显示兑换码分享弹框
        if (item.createRedemptionCode == 1) {
          uni.showLoading({
            title: '获取兑换码中...',
            mask: true
          });
          const res = await $http({
            url: `zx/wap/coupon/shareable/redemption/code?piCouponUserReceiveId=${item.couponUserReceiveId}`
          });

          if (res.code === 20000) {
            this.couponCode = res.data;
            this.shareDataInfo = { ...item, isShow: true, couponCode: res.data };
            this.$refs.codePopup.open(this.shareDataInfo);
          }
        }
      },
      switchTab(tab) {
        this.isShare = false;
        this.active = tab;
        this.couponsList = [];
        this.pageNum = 1;
        console.log('tab', tab);
        this.getCouponsList(tab);
        console.log('this.couponsList', this.$refs.codePopup);
      },
      async getCouponsList(type) {
        uni.showLoading({
          title: '加载中'
        });
        let useStatusListStr = type ? type : 1;
        const res = await $http({
          url: 'zx/wap/coupon/user/list',
          data: {
            pageSize: this.pageSize,
            pageNum: this.pageNum,
            useStatus: Number(useStatusListStr),
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        console.log('resres', res);
        if (this.pageNum === 1) {
          this.couponsList = res.data.data;
        } else {
          this.couponsList = this.couponsList.concat(res.data.data);
        }
        this.couponsTotal = res.data.totalItems;
        if (useStatusListStr === 1) {
          this.total = this.couponsTotal;
        }
        this.expandList = [];
        this.couponsList.forEach((item) => {
          this.expandList.push(false);
        });
      },
      async getShareList() {
        if (!this.isShare) {
          this.couponsList = [];
        }
        this.isShare = true;
        this.pageNum = 1;
        uni.showLoading({
          title: '加载中'
        });
        const res = await $http({
          url: 'zx/wap/coupon/user/shareable/list',
          data: {
            pageSize: 10000,
            pageNum: this.pageNum,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        console.log('resres12', res);
        if (res.code === 20000) {
          this.couponsList = res.data;
        }
      },
      switchExpand(index) {
        this.$set(this.expandList, index, !this.expandList[index]);
      },
      skintap(item) {
        let _this = this;
        if (!uni.getStorageSync('token')) {
          uni.navigateTo({
            url: '/Personalcenter/login/login'
          });
        } else {
          if (item.couponLimitType === 2) {
            // 先查询是否存在可用商品
            $http({
              url: 'zx/wap/goods/findByCouponId/list',
              data: {
                pageSize: 20,
                pageNum: 1,
                couponId: item.couponId,
                userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
              }
            }).then((resp) => {
              if (resp.data.data && resp.data.data.length > 0) {
                uni.navigateTo({
                  url: '/coupons/CouponsGoodsList?id=' + item.couponId
                });
              } else {
                _this.$util.alter('当前优惠券暂无可使用商品！');
              }
            });
            // $navigationTo(url);
          } else {
            uni.switchTab({
              url: '/pages/index/index'
            });
          }
        }
      },
      // 用户接受分享优惠券
      async receivedCoupon(userId, couponId, wxShareCode, receiveNo) {
        const res = await $http({
          url: 'zx/wap/coupon/user/received/share',
          method: 'POST',
          data: {
            couponId: uni.getStorageSync('coupons_couponId'),
            shareUserId: uni.getStorageSync('coupons_userId'),
            wxShareCode: wxShareCode,
            // receivedUserId:'1293950340727443456'
            receivedUserId: uni.getStorageSync('user_id') || '',
            receiveNo: receiveNo
          }
        });
        if (res.code === 20000) {
          this.$util.alter(res.message || '领取成功');
          let _this = this;
          setTimeout(() => {
            _this.getCouponsList();
          }, 1500);
        } else {
          this.getCouponsList();
        }
      },
      // 用户接受分享优惠券兑换码
      async receivedCouponCode() {
        let receivedUserId = uni.getStorageSync('user_id') || '';
        let shareUserId = uni.getStorageSync('coupons_userId');
        const res = await $http({
          url: 'zx/wap/coupon/redemption/code/info?redemptionCode=' + uni.getStorageSync('couponCode') + '&receivedUserId=' + receivedUserId + '&shareUserId=' + shareUserId
        });
        console.log('res', res);
        if (res.code === 20000 && res.data.piCouponDto) {
          let shareDataInfo = { ...res.data.piCouponDto, isShow: false, couponCode: uni.getStorageSync('couponCode') };
          this.$refs.codePopup.open(shareDataInfo);
        }
      }
    },
    onReachBottom() {
      if (this.pageNum * 10 >= this.couponsTotal) {
        return;
      } else {
        this.pageNum++;
        if (this.isShare) {
          // this.getShareList()
        } else {
          this.getCouponsList();
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .list-tabs {
    display: flex;
    align-items: center;
    height: 96rpx;
    background: #f1f4f6;
    padding: 0 32rpx;

    .item-tab {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 226rpx;
      font-size: 28rpx;
      color: #5a5a5a;

      .rounded-border {
        width: auto;
        position: relative;
      }

      .rounded-border::after {
        content: '';
        position: absolute;
        bottom: 4rpx;
        left: 0;
        right: 0;
        height: 6rpx;
        background: rgba(51, 147, 120, 0.8);
        border-radius: 4rpx;
      }
    }

    .border-box {
      width: 2rpx;
      height: 32rpx;
      background-color: #e2e6e9;
    }

    .is-active {
      color: #333333;
      font-family: AlibabaPuHuiTi_3_55_Regular;
    }
  }

  .coupons {
    padding: 32rpx;

    .share-coupons {
      height: 34rpx;
      font-family: AlibabaPuHuiTi_2_85_Bold;
      font-size: 24rpx;
      color: #489981;
      line-height: 34rpx;
      text-align: right;
      font-style: normal;
      font-weight: bold;
    }

    .item-coupons {
      // display: flex;
      // width: 686rpx;
      background: #ffffff;
      border-radius: 16rpx;
      margin-top: 24rpx;
      padding: 20rpx 13rpx 20rpx 13rpx;
      &:first-of-type {
        margin-top: 0;
      }
      .coupon-main {
        display: flex;
        align-items: center;
        border-bottom: 2rpx dashed #e2e2e2;
        padding-bottom: 16rpx;
      }

      .activity-type {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 216rpx;

        .activity-name {
          font-family: AlibabaPuHuiTi_2_85_Bold;
          font-size: 52rpx;
          color: #489981;
          line-height: 74rpx;
          text-align: left;
          font-style: normal;
          font-weight: bold;
          margin-bottom: 4rpx;
        }

        .activity-limit {
          font-family: AlibabaPuHuiTi_2_55_Regular;
          font-size: 24rpx;
          color: #489981;
          line-height: 34rpx;
          text-align: right;
          font-style: normal;
        }
      }

      .activity-content {
        width: 304rpx;
        margin-right: 14rpx;

        .title {
          font-family: AlibabaPuHuiTi_2_85_Bold;
          font-size: 28rpx;
          color: #555555;
          line-height: 40rpx;
          text-align: left;
          font-style: normal;
          font-weight: bold;
          margin-bottom: 16rpx;
          overflow: hidden;
        }

        .time {
          // height: 40rpx;
          font-family: AlibabaPuHuiTi_2_55_Regular;
          font-size: 20rpx;
          color: #b4b4b4;
          line-height: 28rpx;
          text-align: left;
          font-style: normal;
          // margin-bottom: 28rpx;
        }
      }
      .rules {
        padding-left: 216rpx;
        padding-top: 20rpx;
        box-sizing: border-box;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-family: AlibabaPuHuiTi_3_55_Regular;
        font-size: 20rpx;
        color: #8f8e8e;
        line-height: 28rpx;
        text-align: left;
        font-style: normal;

        .expand-icon {
          width: 208rpx;
          height: 20rpx;
          margin-left: 4rpx;

          .icon-down {
            width: 20rpx;
            height: 20rpx;
          }
        }
      }

      .activity-use {
        font-family: AlibabaPuHuiTi_3_55_Regular;
        width: 108rpx !important;
        height: 40rpx !important;
        background: #2a896f !important;
        border-radius: 20rpx !important;
        // margin-left: 20rpx !important;
        font-size: 24rpx !important;
        color: #ffffff !important;
        line-height: 40rpx !important;
        text-align: center !important;
      }
      .activity-share {
        font-family: AlibabaPuHuiTi_3_55_Regular;
        width: 108rpx !important;
        height: 40rpx !important;
        border-radius: 20rpx !important;
        font-size: 24rpx !important;
        color: #2a896f !important;
        line-height: 38rpx !important;
        text-align: center !important;
        border: 1rpx solid #2a896f;
        box-sizing: border-box;
      }

      .rules-text {
        width: 422rpx;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        font-size: 20rpx;
        color: #999;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
        margin: 16rpx 0 20rpx 216rpx;
        display: flex;
        flex-direction: column;
      }
    }
  }

  .used-color {
    color: #9a9a9a !important;
  }
  .used-status {
    color: #9a9a9a;
    font-size: 26rpx;
    font-family: AlibabaPuHuiTi_2_55_Regular;
    flex: 1;
    text-align: center;
  }
</style>
