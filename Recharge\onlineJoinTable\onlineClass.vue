<template>
  <view>
    <page-meta :page-style="'overflow:' + (showDialog ? 'hidden' : 'visible')"></page-meta>
    <view class="plr-20 pb-30">
      <view class="bg-ff plr-20 pb-40 radius-15 positionRelative">
        <form id="#nform">
          <view class="information ptb-30 borderB">
            <view style="width: 40%" class="f-30 bold">学员姓名：</view>
            <view style="width: 60%; color: #666666">{{ infolist.studentName }}</view>
          </view>
          <view class="information ptb-30 borderB">
            <view style="width: 40%" class="f-30 bold">学员编号：</view>
            <view style="width: 60%; color: #666666">{{ infolist.studentCode }}</view>
          </view>
          <view class="information ptb-30 borderB">
            <view style="width: 40%" class="f-30 bold">课程类型：</view>
            <view style="width: 60%; color: #666666">{{ infolist.curriculumName }}</view>
          </view>
          <view class="information ptb-30 borderB">
            <view style="width: 40%" class="f-30 bold">联系方式：</view>
            <view style="width: 60%; color: #666666">{{ infolist.mobile }}</view>
          </view>
          <view class="information ptb-30 borderB">
            <view style="width: 40%" class="f-30 bold">年级：</view>
            <view style="width: 60%; color: #666666">{{ getGrade(infolist.grade) }}</view>
          </view>
          <view class="information ptb-30 borderB">
            <view style="width: 40%" class="f-30 bold">充值交付学时：</view>
            <view style="width: 60%; color: #666666">{{ infolist.rechargeHour }}</view>
          </view>
          <!-- 没有课程不需要课程规划 -->
          <view class="ptb-15 borderB" v-if="showLessonPlan">
            <view class="information">
              <view style="width: 73%" class="f-30 bold">课程规划：</view>
              <view class="uni-list-cell-db pr-20 positionRelative" @click="openCoursePlan" v-if="!isDisable">
                <view v-if="courseChoseList.length > 0" style="position: absolute; right: 20rpx">
                  <text style="font-size: 30rpx; color: #2e896f; line-height: 42rpx">编辑</text>
                </view>
                <view class="flex-s" v-else>
                  <view class="flex-a-c"><text style="float: left">请选择</text></view>
                  <view class="time-icon">
                    <u-icon v-if="!isDisable" name="arrow-right" color="#c7c7c7" size="30"></u-icon>
                  </view>
                </view>
              </view>
            </view>
            <view v-if="courseChoseList.length > 0 || isDisable">
              <view class="mt-30 mb-25" v-for="(item, index) in courseChoseList">
                <view class="course-bg-view positionRelative flex-a-c">
                  <view class="course">{{ item.courseName }}</view>
                  <view class="first-class-vocabulary" v-if="item.isFirstWordBase">首节课词库</view>
                </view>
              </view>
            </view>
          </view>
          <view class="ptb-15 borderB">
            <view class="information">
              <view style="width: 73%" class="f-30 bold">上课时间：</view>
              <view class="uni-list-cell-db flex-s pr-20 flex-s positionRelative" @click="openTime" v-if="!isDisable || isEdit">
                <view v-if="comfireCourseData.length > 0 || isEdit" style="position: absolute; right: 20rpx">
                  <text style="font-size: 30rpx; color: #2e896f; line-height: 42rpx">编辑</text>
                </view>
                <view class="flex-s" v-else>
                  <view class="flex-a-c"><text style="float: left">请选择</text></view>
                  <view class="time-icon">
                    <u-icon v-if="!isDisable" name="arrow-right" color="#c7c7c7" size="30"></u-icon>
                  </view>
                </view>
              </view>
            </view>
            <view v-if="comfireCourseData.length > 0 || isDisable">
              <view class="mt-30 mb-25 text-more-view" v-for="(item, index) in comfireCourseData">
                <view style="flex: 2" class="text-view">{{ getWeekName(item.week) }}</view>
                <view style="flex: 9; line-height: 50rpx" class="text-view">{{ getAllTimeShow(item.time) }}</view>
              </view>
            </view>
          </view>
          <view class="ptb-15 borderB" v-if="infolist.curriculumName == '鼎英语' || infolist.curriculumName == '拼音法'">
            <view class="information">
              <view style="width: 73%" class="f-30 bold">复习时间：</view>
              <view class="uni-list-cell-db pr-20 positionRelative" @click="openReviewTime" v-if="!isDisable || isEdit">
                <view
                  style="position: absolute; right: 20rpx"
                  v-if="
                    (comfireReviewData.week != undefined && comfireReviewData.week.length > 0 && comfireReviewData.startTime != undefined && comfireReviewData.startTime != '') ||
                    isEdit
                  "
                >
                  <text style="font-size: 30rpx; color: #2e896f; line-height: 42rpx">编辑</text>
                </view>
                <view class="flex-s" v-else>
                  <view class="flex-a-c"><text style="float: left">请选择</text></view>
                  <view class="time-icon">
                    <u-icon v-if="!isDisable" name="arrow-right" color="#c7c7c7" size="30"></u-icon>
                  </view>
                </view>
              </view>
            </view>
            <view
              v-if="
                (comfireReviewData.week != undefined && comfireReviewData.week.length > 0 && comfireReviewData.startTime != undefined && comfireReviewData.startTime != '') ||
                isDisable
              "
            >
              <view class="mt-30 mb-25" v-for="(item, index) in comfireReviewData.week">
                <text class="text-view">{{ getWeekName(item) }}</text>
                <text style="margin-left: 60rpx" class="text-view">{{ comfireReviewData.startTime }}</text>
              </view>
            </view>
          </view>
          <view class="borderB pt-30 pb-30" v-if="!isDisable || firstStudyTime">
            <view class="information">
              <view style="width: 40%">
                <view class="f-30 bold">首次上课时间：</view>
                <view class="c-99 f-24">限制24小时之后</view>
              </view>
              <view v-if="!isDisable || isEdit" class="c-66 f-30" style="width: 60%">
                {{ firstStudyTime ? firstStudyTime : '请先填写上课时间' }}
              </view>
              <view v-else style="width: 60%" class="c-00 f-30">{{ firstStudyTime }}</view>
            </view>
          </view>
          <view class="borderB pt-30 pb-30">
            <view class="flex-a-c f-30">
              <view class="bold" style="width: 63%">是否试课：</view>
              <view v-if="!isDisable || isEdit" class="w100 flex-a-c">
                <view class="flex-a-c mr-30">
                  <uni-icons :type="yesExp == 0 ? 'circle' : 'circle-filled'" :color="yesExp == 0 ? '#999' : '#2e896f'" size="22" @click="changeYesExp(1)"></uni-icons>
                  <span class="ml-15 c-66">是</span>
                </view>
                <view class="flex-a-c ml-40">
                  <uni-icons :type="noExp == 0 ? 'circle' : 'circle-filled'" :color="noExp == 0 ? '#999' : '#2e896f'" size="22" @click="changeNoExp(0)"></uni-icons>
                  <span class="ml-15 c-66">否</span>
                </view>
              </view>
              <view style="width: 96%" v-else>{{ infolist.isExp == 0 ? '否' : '是' }}</view>
            </view>
          </view>
          <view v-if="yesExp == 1 ? true : yesExp == 1 && infolist.isExp">
            <view class="information ptb-30 borderB" v-if="infolist.curriculumName == '鼎英语'">
              <view style="width: 40%" class="f-30 bold">词汇量检测：</view>
              <view style="width: 60%; color: #666666">{{ infolist.wordBase }}</view>
            </view>
          </view>
          <view class="flex-a-c borderB ptb-30 f-30">
            <view class="bold" style="width: 63%">是否新生：</view>
            <view v-if="!isDisable || isEdit" class="w100 flex-a-c">
              <view class="flex-a-c mr-30">
                <uni-icons :type="yesNew == 0 ? 'circle' : 'circle-filled'" :color="yesNew == 0 ? '#999' : '#2e896f'" size="22" @click="changeYesNew(1)"></uni-icons>
                <span class="ml-15 c-66">是</span>
              </view>
              <view class="flex-a-c ml-40">
                <uni-icons :type="noNew == 0 ? 'circle' : 'circle-filled'" :color="noNew == 0 ? '#999' : '#2e896f'" size="22" @click="changeNoNew(0)"></uni-icons>
                <span class="f">否</span>
              </view>
            </view>
            <view style="width: 96%" v-else>{{ infolist.isNewStudent == 0 ? '否' : '是' }}</view>
          </view>
          <!-- 学考通、小四门 -->
          <view v-if="XKTOrXSM">
            <!-- 联系人收货地址 address-->
            <view>
              <view class="flex-a-c pt-30 pb-10 f-30">
                <view class="bold" style="width: 63%">联系人收货地址：</view>
              </view>
              <view class="flex-a-c borderB f-30">
                <u--textarea v-model="address" :disabled="isDisable && !isEdit" placeholder="请输入联系人收货地址" autoHeight border="none" count maxlength="100"></u--textarea>
              </view>
            </view>
            <!-- 学段 educationStage：学段（1初中，2高中 -->
            <view class="flex-a-c borderB ptb-30 f-30">
              <view class="bold" style="width: 63%">学段：</view>
              <view v-if="!isDisable || isEdit" class="w100 flex-a-c">
                <view class="flex-a-c mr-30">
                  <uni-icons
                    :type="educationStage != 2 ? 'circle' : 'circle-filled'"
                    :color="educationStage != 2 ? '#999' : '#2e896f'"
                    size="22"
                    @click="changeEducationStage(2)"
                  ></uni-icons>
                  <span class="ml-15 c-66">初中</span>
                </view>
                <view class="flex-a-c ml-40">
                  <uni-icons
                    :type="educationStage != 1 ? 'circle' : 'circle-filled'"
                    :color="educationStage != 1 ? '#999' : '#2e896f'"
                    size="22"
                    @click="changeEducationStage(1)"
                  ></uni-icons>
                  <span class="ml-15 c-66">高中</span>
                </view>
              </view>
              <view style="width: 96%" v-else>{{ infolist.educationStage == 2 ? '初中' : '高中' }}</view>
            </view>
            <!-- 考试时间 examTime -->
            <view class="borderB pb-30 f-30">
              <view class="c-66 f-24 pt-20" v-if="!isDisable || isEdit">(考试时间会涉及录播视频学习时间，请谨慎选择)</view>
              <view class="flex-a-c f-30 pt-20">
                <view class="bold" style="width: 40%; flex-shrink: 0">考试时间：</view>
                <view v-if="!isDisable || isEdit" class="w100 flex-a-c flex-x-b" style="width: 55%; flex-shrink: 0">
                  <view class="c-66" @click="showExamTime = true">{{ examTime ? examTime : '请选择考试时间' }}</view>
                  <image @click="showExamTime = true" src="https://document.dxznjy.com/course/055f5dd5b3174b94938080129e93c4eb.png" style="width: 50rpx; height: 50rpx" />
                </view>
                <view style="width: 55%" v-else>{{ infolist.examTime }}</view>
              </view>
            </view>
          </view>
          <view class="ptb-30" style="padding-bottom: 60rpx">
            <view class="f-30 bold">备注：</view>
            <view class="mt-30 p-30 bg-f7 radius-15" v-if="!isDisable || isEdit">
              <textarea @input="inputRemark" maxlength="200" v-model="remark" placeholder-style="color:#999" placeholder="请输入" :disabled="isDisable && !isEdit" />
            </view>
            <view class="mt-30 remake-bg-view" v-else>
              {{ infolist.remark ? infolist.remark : '' }}
            </view>
          </view>
          <view class="tips" v-if="!isDisable || isEdit" :style="{ height: svHeight + 'px' }">
            <button class="phone-btn" @click="sendCourse" :disabled="disabled">确定</button>
          </view>
        </form>
      </view>
      <uni-popup ref="reviewShow" type="bottom" @maskClick="cancelAtion()">
        <view class="dialogBG pt-30 pb-40">
          <view class="top-close">
            <text>复习时间</text>
            <uni-icons type="clear" size="28" color="#B1B1B1" @click="cancelAtion"></uni-icons>
          </view>
          <view class="center-content">
            <view class="chose-week-text">
              <view
                class="chose-week-item"
                @click="reviewChoseWeekFunc(item, index)"
                v-for="(item, index) in normalWeekData"
                :class="reviewChoseWeek.indexOf(index) != -1 ? 'week-chose' : 'week-normal'"
              >
                {{ normalWeekData[index] }}
              </view>
            </view>
            <view class="start-time" @click="reviewStartTimeDialog()">
              <view>
                <text>开始时间</text>
                <text style="font-size: 32rpx; margin-left: 70rpx">
                  {{ rewviewdatevalue.length <= 0 ? '请选择' : rewviewdatevalue }}
                </text>
              </view>
              <u-icon name="arrow-right" color="#c7c7c7" size="30"></u-icon>
            </view>
          </view>
          <view class="top-button">
            <button class="radius-50 confirm-button" @click="reviewConfirm()">确定</button>
          </view>
        </view>
      </uni-popup>
      <u-datetime-picker
        ref="reviewtimePicker"
        :show="showReviewTime"
        v-model="rewviewdatevalue"
        mode="time"
        itemHeight="60"
        confirmColor="#2e896f"
        @cancel="cancel"
        @confirm="reviewDateChange"
        :immediateChange="true"
      ></u-datetime-picker>

      <uni-popup ref="courseShow" type="bottom" @maskClick="cancelCourse()">
        <view class="dialogBG pt-30 pb-40">
          <view class="top-close">
            <text>上课时间</text>
            <uni-icons type="clear" size="28" color="#B1B1B1" @click="cancelCourse"></uni-icons>
          </view>
          <view class="center-content">
            <view class="chose-week-text">
              <view
                class="chose-week-item"
                @click="ChoseWeekFunc(item, index)"
                v-for="(item, index) in normalWeekData"
                :class="getWeekForChoseIndex(index) != -1 ? 'week-chose' : 'week-normal'"
              >
                {{ normalWeekData[index] }}
              </view>
            </view>
            <scroll-view style="height: 540rpx" scroll-y="true">
              <view v-for="(item, index) in courseChoseWeek" class="week-view">
                <text>{{ getWeekName(item.week) }}</text>
                <view>
                  <view v-for="(childitem, childindex) in item.time" class="mb-30 time-view">
                    <view
                      :class="childitem.startTime == '开始时间' ? 'time-text-view-normal-1' : 'time-text-view'"
                      style="margin-right: 6rpx; text-align: center"
                      @click="showChoseCourseTime(item, childindex, true)"
                    >
                      {{ childitem.startTime.length <= 0 ? '请选择' : childitem.startTime == '开始时间' ? '00:00' : childitem.startTime }}
                    </view>

                    <uni-number-box v-model="childitem.hour" :min="1" :max="2" @change="bindChange($event, childitem)"></uni-number-box>

                    <view
                      :class="childitem.endTime == '结束时间' ? 'time-text-view-normal' : 'time-text-view'"
                      style="margin-left: 6rpx; text-align: center"
                      @click="showChoseCourseTimeEnd(childitem)"
                    >
                      {{ childitem.endTime.length <= 0 ? '结束时间' : childitem.endTime == '结束时间' ? '01:00' : childitem.endTime }}
                    </view>
                    <view v-if="childindex == item.time.length - 1">
                      <image v-if="childindex != 0" class="time-flag" @click="courseSub(item, childindex)" mode="widthFix" :src="imgHost + 'dxSelect/icon_time_sub.png'"></image>
                      <image class="time-flag" @click="courseAdd(item)" mode="widthFix" :src="imgHost + 'dxSelect/icon_time_add.png'"></image>
                      <image v-if="childindex == 0" class="time-flag"></image>
                    </view>
                    <view v-else>
                      <image class="time-flag" @click="courseSub(item, childindex)" mode="widthFix" :src="imgHost + 'dxSelect/icon_time_sub.png'"></image>
                      <image class="time-flag"></image>
                    </view>
                  </view>
                </view>
              </view>
            </scroll-view>
          </view>
          <view class="top-button">
            <button class="radius-50 confirm-button" @click="courseConfirm()">确定</button>
          </view>
        </view>
      </uni-popup>
      <u-datetime-picker
        ref="coursetimePicker"
        :show="showCourseTime"
        mode="time"
        itemHeight="60"
        confirmColor="#489981"
        @cancel="cancel"
        @confirm="courseDateChange"
        :immediateChange="true"
      ></u-datetime-picker>

      <a-calendar
        color="#489981"
        mode="single"
        :show="showExamTime"
        :minDate="lastYear"
        :monthNum="240"
        :defaultDate="examTime || today"
        @confirm="examTimeDateChange"
        @close="showExamTime = false"
      ></a-calendar>
      <!-- h5 -->
    </view>
    <web-view :src="urlAddress" @message="handleMessage" v-if="showWebview"></web-view>
  </view>
</template>

<script>
  const { $getSceneData, $showError, $showMsg, $http } = require('@/util/methods.js');
  import Util, { objForEach } from '@/util/util.js';
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  import aCalendar from '../components/uview-ui/components/u-calendar/u-calendar.vue';
  import dayjs from 'dayjs';
  import Config from '@/util/config.js';
  export default {
    components: { aCalendar },
    data() {
      return {
        svHeight: 50,
        useHeight: 0, //除头部之外高度

        infolist: {}, // 回显信息
        orderId: '',
        payStatus: '', //0填写   1查看
        isDisable: false, //是否禁用

        //是否试过课
        noExp: 0,
        yesExp: 0,
        isExp: '',
        //新生
        yesNew: 0,
        noNew: 0,
        isNewStudent: '',
        remark: '', // 体验需求

        flag: false, // 防止重复点击
        disabled: false,

        imgHost: getApp().globalData.imgsomeHost,

        localWordBase: -1,

        showDialog: false,
        normalWeekData: '周一_周二_周三_周四_周五_周六_周日'.split('_'),

        ///复习
        comfireReviewData: {},
        reviewChoseWeek: [], //复习选择的星期
        rewviewdatevalue: '',
        showReviewTime: false,

        //上课
        comfireCourseData: [],
        courseChoseWeek: [], //上课选择的星期 [{week:"周一", time:[{startTime:"",endTime:""}]},...]
        showCourseTime: false,

        curChoseItem: null,
        curChoseTimeIndex: null,
        curChoseStartFlag: false,
        showLessonPlan: true,

        showPlan: true, //课程规划
        //课程规划
        courseChoseList: [],

        gradeNameArr: '一年级_二年级_三年级_四年级_五年级_六年级_初一_初二_初三_高一_高二_高三_大一_大二_大三_大四_其他_幼儿园'.split('_'),

        isEdit: false,
        changeRemark: false,
        changeNewStudent: false,
        changeExp: false,
        changeReview: false,
        changeStudy: false,
        curriculumId: '',
        firstStudyTime: '',
        firstWeek: '',
        firstTime: '',

        showExamTime: false,
        address: '', //联系人收货地址
        educationStage: '', //年级类型（1初中，2高中）
        examTime: '', //考试时间
        today: dayjs().format('YYYY-MM-DD'),
        lastYear: dayjs().subtract(10, 'year').format('YYYY-MM-DD'), // 前年
        XKTOrXSM: false, // 是否是学考通或小四门课程
        CurriculumCodeArr: Config.CurriculumCodeArr, //学考通、小四门、数学课程code
        XKTAndXSMCurriculumCodeArr: Config.XKTAndXSMCurriculumCodeArr //学考通、小四门课程code
      };
    },
    watch: {
      isNewStudent(newVal, oldVal) {
        console.log('isNewStudent修改');
        if (this.isEdit && this.firstStudyTime) {
          this.changeNewStudent = this.isNewStudent != this.infolist.isNewStudent;
          this.setChangeFirstTime();
        }
      },

      infolist(newVal, oldVal) {
        if (newVal && newVal.curriculumId) {
          console.log('试课推荐111111111111:', newVal);
          if (this.showPlan) {
            if (newVal.curriculumName !== '鼎英语') {
              this.getCourseList(newVal.curriculumId);
            }
          } else {
            this.showLessonPlan = false;
          }
        }
      },
      isExp(newVal, oldVal) {
        console.log('isExp修改');
        if (this.isEdit && this.firstStudyTime) {
          this.changeExp = this.isExp != this.infolist.isExp;
          this.setChangeFirstTime();
        }
      },

      remark(newVal, oldVal) {
        console.log('remark修改');
        if (this.isEdit && this.firstStudyTime) {
          this.changeRemark = this.remark != this.infolist.remark;
          this.setChangeFirstTime();
        }
      }
    },

    onLoad(e) {
      //   console.log(e);
      if (this.CurriculumCodeArr.includes(e.curriculumCode)) {
        this.XKTOrXSM = this.XKTAndXSMCurriculumCodeArr?.includes(e.curriculumCode); // 是否是学考通或小四门课程
        // console.log('🚀 ~ this.XKTOrXSM :', this.XKTOrXSM, this.XKTAndXSMCurriculumCodeArr, e.curriculumCode);
        this.showPlan = false;
        console.log('🚀 ~ onLoad ~ this.showPlan:', this.showPlan);
      }
      this.orderId = e.orderId;
      this.payStatus = e.payStatus;
      this.isDisable = this.payStatus == 0 ? false : true;
      this.isEdit = e.isEdit == 'false' ? false : true;
      //标题
      let title = '上课信息对接表';
      if (this.isEdit) {
        title = '修改上课信息对接表';
      } else {
        let title = '查看上课信息对接表';
        if (this.payStatus == 0) {
          title = '填写上课信息对接表';
        }
      }
      uni.setNavigationBarTitle({
        title: title
      });
    },
    onReady() {
      let that = this;
      uni.getSystemInfo({
        //调用uni-app接口获取屏幕高度
        success(res) {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          that.useHeight = h - 65;
        }
      });
      // 微信小程序需要用此写法
      this.$refs.reviewtimePicker.setFormatter(this.formatter);
      this.$refs.coursetimePicker.setFormatter(this.formatter);
    },

    onShow() {
      let that = this;
      uni.$on('coursePlan', function (data) {
        that.courseChoseList = data;
        console.log('22222222222222');
        console.log(that.courseChoseList);
        console.log('22222222222222');
      });
      that.getCourseinfo();
    },

    methods: {
      setChangeFirstTime() {
        console.log('修改');
        let isneed = this.changeExp || this.changeNewStudent || this.changeRemark || this.changeReview || this.changeStudy;
        if (isneed) {
          this.getFirstStudyTime();
        } else {
          if (this.isEdit && this.firstStudyTime) {
            this.getFormatToService(this.infolist.firstTime, this.infolist.firstWeek);
          } else {
            this.firstTime = '';
            this.firstStudyTime = '';
            this.firstWeek = '';
          }
        }
      },
      formatter(type, value) {
        if (type === 'hour') {
          return `${value}时`;
        }
        if (type === 'minute') {
          return `${value}分`;
        }
        return value;
      },

      cancel() {
        this.showReviewTime = false;
        this.showCourseTime = false;
      },
      ////////Start////
      // 复习开始时间选择
      reviewDateChange: function (e) {
        this.showReviewTime = false;
        this.$forceUpdate();
      },
      //复习时间--开始时间
      reviewChoseWeekFunc(item, index) {
        let weelIndex = this.reviewChoseWeek.indexOf(index);
        if (weelIndex == -1) {
          this.reviewChoseWeek.push(index);
        } else {
          this.reviewChoseWeek.splice(weelIndex, 1);
        }
      },

      //显示时间
      reviewStartTimeDialog() {
        this.showReviewTime = true;
      },

      reviewConfirm() {
        if (this.reviewChoseWeek.length == 0 && this.rewviewdatevalue.length > 0) {
          uni.showToast({
            icon: 'none',
            title: '请选择星期'
          });
          return;
        }
        if (this.reviewChoseWeek.length > 0 && this.rewviewdatevalue.length <= 0) {
          uni.showToast({
            icon: 'none',
            title: '请选择开始时间'
          });
          return;
        }
        this.reviewChoseWeek.sort((a, b) => a - b);
        this.comfireReviewData = {
          week: this.reviewChoseWeek,
          startTime: this.rewviewdatevalue
        };
        this.$refs.reviewShow.close();
        this.showDialog = false;

        if (this.isEdit && this.firstStudyTime) {
          this.changeReview = true;
          this.setChangeFirstTime();
        }
      },
      openReviewTime() {
        this.$refs.reviewShow.open();
        this.showDialog = true;
      },
      cancelAtion() {
        this.$refs.reviewShow.close();
        this.showDialog = false;
      },
      ///复习时间/////End//////

      /////开始时间////START//////////////
      openTime() {
        this.$refs.courseShow.open();
        this.showDialog = true;
      },
      courseConfirm() {
        let copyArr = this.courseChoseWeek.slice();
        let arr = this.confirmJudgeChoseArr(copyArr);
        if (arr == null) {
          uni.showToast({
            icon: 'none',
            title: '选择的星期下至少需要有一个有效时间段'
          });
          return;
        }
        this.comfireCourseData = arr;
        this.$refs.courseShow.close();
        this.showDialog = false;
        if (!this.isDisable || (this.isEdit && this.firstStudyTime)) {
          this.changeStudy = true;
          this.setChangeFirstTime();
        }
      },
      cancelCourse() {
        this.$refs.courseShow.close();
        this.showDialog = false;
      },
      ChoseWeekFunc(item, index) {
        let weelIndex = this.getWeekForChoseIndex(index);
        if (weelIndex == -1) {
          let insertIndex = -1;
          for (let i = 0; i < this.courseChoseWeek.length; i++) {
            if (index > this.courseChoseWeek[i].weekIndex) {
              continue;
            }
            insertIndex = i;
            break;
          }
          let data = {
            weekIndex: index,
            week: index,
            time: [
              {
                hour: 1,
                startTime: '开始时间',
                endTime: '结束时间'
              }
            ]
          };
          if (insertIndex !== -1) {
            this.courseChoseWeek.splice(insertIndex, 0, data);
          } else {
            this.courseChoseWeek.push(data);
          }
        } else {
          this.courseChoseWeek.splice(weelIndex, 1);
        }
        console.log(this.courseChoseWeek);
        console.log(this.comfireCourseData);
      },

      getWeekForChoseIndex(item) {
        for (let i = 0; i < this.courseChoseWeek.length; i++) {
          let data = this.courseChoseWeek[i];
          if (data.week == item) {
            return i;
          }
        }
        return -1;
      },

      courseSub(item, index) {
        item.time.splice(index, 1);
      },

      courseAdd(item) {
        let data = {
          hour: 1,
          startTime: '开始时间',
          endTime: '结束时间'
        };
        item.time.push(data);
      },

      bindChange(e, item) {
        if (item.startTime == '开始时间') {
          return;
        }
        item.endTime = this.getAutoEndTime(item.startTime, item.hour);
        if (item.startTime != '开始时间' && item.endTime != '结束时间') {
          if (this.judgeAllBetween(item.startTime, item.endTime)) {
            item.endTime = '结束时间';
            uni.showToast({
              icon: 'none',
              title: '选择时间段与其他时间段重叠'
            });
          }
        }
      },

      //自动获取结束时间
      getAutoEndTime(startTime, addHour) {
        let timeParts = startTime.split(':');
        let hours = parseInt(timeParts[0], 10);
        let minutes = parseInt(timeParts[1], 10);

        hours += addHour;
        if (hours >= 24) {
          hours -= 24;
        }
        let endTime = `${hours < 10 ? '0' + hours : hours}:${minutes < 10 ? '0' + minutes : minutes}`;
        return endTime;
      },
      showChoseCourseTimeEnd(item) {
        if (item.startTime == '开始时间') {
          uni.showToast({
            icon: 'none',
            title: '请选择开始时间'
          });
        }
      },
      showChoseCourseTime(item, index, isStart) {
        this.showCourseTime = true;

        this.curChoseItem = item;
        this.curChoseTimeIndex = index;
        this.curChoseStartFlag = isStart;
      },
      // 考试时间选择
      examTimeDateChange(e) {
        this.examTime = e[0];
        this.showExamTime = false;
        console.log('🚀 ~ examTimeDateChange ~ e:', e, this.examTime);
      },
      //上课 开始/结束 时间选择
      courseDateChange: function (e) {
        if (this.judgeStartBetween(e.value)) {
          //判断是否在别的时间内 或者不等于1小时或者2小时
          uni.showToast({
            icon: 'none',
            title: '选择时间与其他时间段重叠'
          });
          return;
        }
        let oldVal = '';
        if (this.curChoseStartFlag) {
          oldVal = this.curChoseItem.time[this.curChoseTimeIndex].startTime;
          this.curChoseItem.time[this.curChoseTimeIndex].startTime = e.value;
          this.curChoseItem.time[this.curChoseTimeIndex].endTime = this.getAutoEndTime(e.value, this.curChoseItem.time[this.curChoseTimeIndex].hour);
        }
        // else{
        //     oldVal = this.curChoseItem.time[this.curChoseTimeIndex].endTime
        //     this.curChoseItem.time[this.curChoseTimeIndex].endTime = e.value
        // }
        let start = this.curChoseItem.time[this.curChoseTimeIndex].startTime;
        let end = this.curChoseItem.time[this.curChoseTimeIndex].endTime;
        if (start != '开始时间' && end != '结束时间') {
          if (this.judgeAllBetween(start, end)) {
            if (this.curChoseStartFlag) {
              if (oldVal != '') {
                this.curChoseItem.time[this.curChoseTimeIndex].startTime = oldVal;
                this.curChoseItem.time[this.curChoseTimeIndex].endTime = this.getAutoEndTime(oldVal, this.curChoseItem.time[this.curChoseTimeIndex].hour);
              } else {
                this.curChoseItem.time[this.curChoseTimeIndex].startTime = '开始时间';
                this.curChoseItem.time[this.curChoseTimeIndex].endTime = '结束时间';
              }
            }
            // else{
            //     if(oldVal != ""){
            //         this.curChoseItem.time[this.curChoseTimeIndex].endTime = oldVal
            //     }else{
            //         this.curChoseItem.time[this.curChoseTimeIndex].endTime = "结束时间"
            //     }
            // }
            uni.showToast({
              icon: 'none',
              title: '选择时间段与其他时间段重叠'
            });
            return;
          }
          // if(this.judgeHours(start,end)){
          //     if(this.curChoseStartFlag){
          //         if(oldVal != ""){
          //             this.curChoseItem.time[this.curChoseTimeIndex].startTime = oldVal
          //             this.curChoseItem.time[this.curChoseTimeIndex].endTime = this.getAutoEndTime(oldVal,this.curChoseItem.time[this.curChoseTimeIndex].hour);
          //         }else{
          //             this.curChoseItem.time[this.curChoseTimeIndex].startTime = "开始时间"
          //             this.curChoseItem.time[this.curChoseTimeIndex].endTime = "结束时间"
          //         }
          //     }
          //     // else{
          //     //     if(oldVal != ""){
          //     //         this.curChoseItem.time[this.curChoseTimeIndex].endTime = oldVal
          //     //     }else{
          //     //         this.curChoseItem.time[this.curChoseTimeIndex].endTime = "结束时间"
          //     //     }
          //     // }
          //     uni.showToast({
          //          icon: "none",
          //         title:"每次上课时间仅可以选择一小时或两小时"
          //     })
          //     return
          // }
        }
        this.showCourseTime = false;
      },
      async getCourseList(curriculumId) {
        let res = await $http({
          url: 'zxAdminCourse/web/course/student/course/subcategories',
          method: 'get',
          data: {
            curriculumId: curriculumId
          }
        });

        if (res) {
          this.showLessonPlan = res.data.length > 0;
        }
      },
      //开始时间是否在区间内
      judgeStartBetween(start) {
        let targetMinutes = this.timeToMinutes(start);
        for (let i = 0; i < this.curChoseItem.time.length; i++) {
          if (this.curChoseTimeIndex == i) {
            continue;
          }
          let startMinutes = this.timeToMinutes(this.curChoseItem.time[i].startTime);
          let endMinutes = this.timeToMinutes(this.curChoseItem.time[i].endTime);

          let isBetween = targetMinutes >= startMinutes && targetMinutes <= endMinutes;
          if (isBetween) {
            return true;
          }
        }
        return false;
      },
      //整个时间段是否重叠
      judgeAllBetween(start, end) {
        let s1 = this.timeToMinutes(start);
        let e1 = this.timeToMinutes(end);
        for (let i = 0; i < this.curChoseItem.time.length; i++) {
          if (this.curChoseTimeIndex == i) {
            continue;
          }
          let s2 = this.timeToMinutes(this.curChoseItem.time[i].startTime);
          let e2 = this.timeToMinutes(this.curChoseItem.time[i].endTime);

          if (s1 <= s2 && e1 >= e2) {
            return true;
          }
          if (s1 >= s2 && e1 <= e2) {
            return true;
          }
          if (s1 <= s2 && e1 <= e2 && s2 <= e1) {
            return true;
          }
          if (s1 >= s2 && e1 >= e2 && s1 <= e2) {
            return true;
          }
        }
        return false;
      },
      //是否是两小时、一小时
      judgeHours(start, end) {
        let startMinutes = this.timeToMinutes(start);
        let endMinutes = this.timeToMinutes(end);
        let timeDifferenceInMinutes = endMinutes - startMinutes;
        console.log(timeDifferenceInMinutes);
        if (timeDifferenceInMinutes != 60 && timeDifferenceInMinutes != 120) {
          return true;
        }
        return false;
      },
      // 将时间字符串转换为分钟数
      timeToMinutes(time) {
        let [hours, minutes] = time.split(':').map(Number);
        return hours * 60 + minutes;
      },
      confirmJudgeChoseArr(arr) {
        for (let i = 0; i < arr.length; i++) {
          let timeArr = arr[i].time;
          for (let j = timeArr.length - 1; j >= 0; j--) {
            if (timeArr.length > 1 && timeArr[j].startTime == '开始时间' && timeArr[j].endTime == '结束时间') {
              timeArr.splice(j, 1);
            } else if (timeArr[j].startTime == '开始时间' || timeArr[j].endTime == '结束时间') {
              return null;
            }
          }
        }
        return arr;
      },
      //时间展示
      getAllTimeShow(time) {
        let timeDetails = '';
        for (let i = 0; i < time.length; i++) {
          timeDetails += time[i].startTime + '~' + time[i].endTime + ' ';
        }
        return timeDetails;
      },
      /////开始时间////END//////////////

      /////课程规划///////////START////////
      openCoursePlan() {
        let curriculumIs = this.infolist.curriculumName == '鼎英语' ? 1 : 0;
        uni.navigateTo({
          url:
            '/Recharge/onlineJoinTable/coursePlan?choseList=' +
            encodeURIComponent(JSON.stringify(this.courseChoseList)) +
            '&curriculumId=' +
            this.infolist.curriculumId +
            '&curriculumIs=' +
            curriculumIs
        });
      },
      /////课程规划///////////END////////

      //是否试过课
      changeYesExp(value) {
        this.yesExp = 1;
        this.noExp = 0;
        this.isExp = value;
        if (this.localWordBase == -1) {
          this.getVocabulary();
        }
      },
      changeNoExp(e) {
        this.isExp = e;
        this.yesExp = 0;
        this.noExp = 1;
      },
      //是否新生
      changeYesNew(value) {
        this.yesNew = 1;
        this.noNew = 0;
        this.isNewStudent = value;
      },
      changeNoNew(e) {
        this.yesNew = 0;
        this.noNew = 1;
        this.isNewStudent = e;
      },
      // 修改年级类型（1初中，2高中）
      changeEducationStage(e) {
        this.educationStage = e;
      },
      inputRemark(e) {
        this.remark = e.detail.value;
      },

      //修改上课对接信息表
      async editOption() {
        let _this = this;
        uni.showLoading();
        let data = {
          firstTime: _this.firstTime,
          firstWeek: _this.firstWeek,
          id: _this.orderId,
          isExp: _this.isExp,
          isNewStudent: _this.isNewStudent,
          remark: _this.remark,
          studyTimeList: _this.getStudyTimeList(),
          wordBase: _this.infolist.wordBase,
          curriculumId: _this.infolist.curriculumId
        };
        if (_this.infolist.curriculumName == '鼎英语' || _this.infolist.curriculumName == '拼音法') {
          data.reviewTime = _this.comfireReviewData.startTime;
          data.reviewWeek = JSON.stringify(_this.comfireReviewData.week);
        }
        // 学考通和小四门校验
        if (this.XKTOrXSM) {
          // 学考通和小四门字段
          data.address = this.address;
          data.educationStage = this.educationStage;
          data.examTime = this.examTime;
        }
        // console.log('🚀 ~ editOption ~ data:', data);
        // return;
        try {
          let res;
          if (this.XKTOrXSM) {
            res = await $http({
              url: 'dyf/web/xkt/extend/updateStudentContactInfo',
              method: 'POST',
              data: {
                ...data,
                studentCode: _this.infolist.studentCode
              }
            });
          } else {
            res = await $http({
              url: 'deliver/web/student/contact/info/updateStudentContactInfo',
              method: 'POST',
              data: data
            });
          }
          // let res = await $http({
          //   url: 'deliver/web/student/contact/info/updateStudentContactInfo',
          //   method: 'POST',
          //   data: data
          // });
          if (res) {
            uni.reLaunch({
              //redirectTo
              url: '/Recharge/onlineJoinTable/onlineJoinTable'
            });
            _this.flag = false;
            _this.disabled = false;
          } else {
            _this.flag = false;
            _this.disabled = false;
          }
        } catch (e) {
          _this.flag = false;
          _this.disabled = false;
        }
        uni.hideLoading();
      },

      //新增上课对接信息表
      async sendCourse() {
        let _this = this;
        if (_this.flag) {
          return;
        }
        let data = {}; // 请求参数

        _this.flag = true;
        _this.disabled = true;
        if (_this.showLessonPlan && _this.courseChoseList.length <= 0 && this.infolist.isNeedDeliver) {
          _this.flag = false;
          _this.disabled = false;
          return $showError('请填写课程规划');
        }
        if (_this.comfireCourseData.length <= 0) {
          _this.flag = false;
          _this.disabled = false;
          return $showError('请填写上课时间');
        }
        // if (this.infolist.curriculumName == '鼎英语') {
        if ((this.CurriculumCodeArr.includes(this.infolist.curriculumCode) && this.infolist.curriculumName == '鼎英语') || this.infolist.curriculumName == '拼音法') {
          if (
            _this.comfireReviewData.week == undefined ||
            _this.comfireReviewData.week.length <= 0 ||
            _this.comfireReviewData.startTime == undefined ||
            _this.comfireReviewData.startTime == ''
          ) {
            _this.flag = false;
            _this.disabled = false;
            return $showError('请填写复习时间');
          }
        }

        if (_this.isExp === '') {
          _this.flag = false;
          _this.disabled = false;
          return $showError('请选择是否试过课');
        }
        if (_this.isNewStudent === '') {
          _this.flag = false;
          _this.disabled = false;
          return $showError('请选择是否是新生');
        }
        // 学考通和小四门校验
        if (this.XKTOrXSM) {
          let errorMsg = '';
          if (!this.address) {
            errorMsg = '请填写联系人收货地址';
          }
          if (!this.educationStage) {
            errorMsg = '请选择学段';
          }
          if (!this.examTime) {
            errorMsg = '请填写考试时间';
          }
          if (errorMsg) {
            _this.flag = false;
            _this.disabled = false;
            return $showError(errorMsg);
          }
          // 学考通和小四门字段
          data.address = this.address;
          data.educationStage = this.educationStage;
          data.examTime = this.examTime;
        }
        //修改上课信息对接表
        if (_this.isEdit) {
          _this.editOption();
          return;
        }
        uni.showLoading();
        data = {
          ...data,
          courseProject: JSON.stringify(_this.courseChoseList),
          id: _this.orderId,
          isExp: _this.isExp,
          isNewStudent: _this.isNewStudent,
          remark: _this.remark,

          studyTimeList: _this.getStudyTimeList(),
          wordBase: _this.infolist.wordBase,
          firstTime: _this.firstTime,
          firstWeek: _this.firstWeek,
          curriculumId: _this.infolist.curriculumId
        };
        if (_this.infolist.curriculumName == '鼎英语' || _this.infolist.curriculumName == '拼音法') {
          data.reviewTime = _this.comfireReviewData.startTime;
          data.reviewWeek = JSON.stringify(_this.comfireReviewData.week);
        }
        // console.log('🚀 ~ sendCourse ~ data:', data);
        try {
          let res;
          if (this.XKTOrXSM) {
            res = await $http({
              url: 'dyf/web/xkt/extend/submitStudentContactInfo',
              method: 'POST',
              data: {
                ...data,
                studentCode: _this.infolist.studentCode,
                type: ''
              }
            });
          } else {
            res = await $http({
              url: 'deliver/web/student/contact/info/submitStudentContactInfo',
              method: 'POST',
              data: data
            });
          }
          // let res = await $http({
          //   url: 'deliver/web/student/contact/info/submitStudentContactInfo',
          //   method: 'POST',
          //   data: data
          // });
          if (res) {
            uni.redirectTo({
              // url: '/Recharge/onlineJoinTable/friendlyReminder'
              url: '/Coursedetails/tips/lessonTips?schoolTable=true'
            });
            // uni.reLaunch({
            //   //redirectTo
            //   url: '/Recharge/onlineJoinTable/onlineJoinTable'
            // });
            _this.flag = false;
            _this.disabled = false;
          } else {
            _this.flag = false;
            _this.disabled = false;
          }
        } catch (e) {
          _this.flag = false;
          _this.disabled = false;
        }
        uni.hideLoading();
      },
      getStudyTimeList() {
        let dataArr = [];
        for (let i = 0; i < this.comfireCourseData.length; i++) {
          for (let j = 0; j < this.comfireCourseData[i].time.length; j++) {
            let startTimeArr = this.getSplitHour(this.comfireCourseData[i].time[j].startTime, ':');
            let endTimeArr = this.getSplitHour(this.comfireCourseData[i].time[j].endTime, ':');
            let data = {
              endTime: '',
              startTime: '',
              usableHourEnd: Number(endTimeArr[0]),
              usableMinuteEnd: Number(endTimeArr[1]),
              usableHourStart: Number(startTimeArr[0]),
              usableMinuteStart: Number(startTimeArr[1]),
              usableWeek: this.comfireCourseData[i].week
            };
            dataArr.push(data);
          }
        }
        return dataArr;
      },
      getCourseDataFormServer() {
        let dataArr = [];
        for (let i = 0; i < this.infolist.studyTimeList.length; i++) {
          console.log(this.infolist.studyTimeList[i]);
          let weekIndex = this.getChoseIndex(dataArr, this.infolist.studyTimeList[i].usableWeek);
          if (weekIndex == -1) {
            let date = {
              week: this.infolist.studyTimeList[i].usableWeek,
              time: [
                {
                  startTime: this.infolist.studyTimeList[i].startTime,
                  endTime: this.infolist.studyTimeList[i].endTime,
                  hour: this.getHourForService(this.infolist.studyTimeList[i])
                }
              ]
            };
            dataArr.push(date);
          } else {
            let timeData = {
              startTime: this.infolist.studyTimeList[i].startTime,
              endTime: this.infolist.studyTimeList[i].endTime,
              hour: this.getHourForService(this.infolist.studyTimeList[i])
            };
            dataArr[weekIndex].time.push(timeData);
          }
        }
        console.log(dataArr);
        this.comfireCourseData = dataArr;
        this.courseChoseWeek = dataArr;
      },

      getHourForService(timeData) {
        let timePartstart = timeData.startTime.split(':');
        let startHours = parseInt(timePartstart[0], 10);
        let timePartend = timeData.endTime.split(':');
        let endHours = parseInt(timePartend[0], 10) + 24;
        let diff = endHours - startHours;
        if (diff > 24) {
          return diff - 24;
        } else {
          return diff;
        }
      },

      getChoseIndex(arr, item) {
        for (let i = 0; i < arr.length; i++) {
          let data = arr[i];
          if (data.week == item) {
            return i;
          }
        }
        return -1;
      },

      getSplitHour(time, str) {
        let arr = time.split(str);
        return arr;
      },

      getWeekName(week) {
        return this.normalWeekData[week];
      },

      async getCourseinfo() {
        let _this = this;
        uni.showLoading();
        let res;
        if (this.XKTOrXSM) {
          res = await $http({
            url: 'dyf/web/xkt/extend/queryStudentContactInfoDetail',
            method: 'get',
            data: {
              id: _this.orderId,
              type: ''
            }
          });
        } else {
          res = await $http({
            url: 'deliver/web/student/contact/info/getStudentContactInfoDetail',
            method: 'get',
            data: {
              id: _this.orderId
            }
          });
        }
        // const res = await $http({
        //   url: 'deliver/web/student/contact/info/getStudentContactInfoDetail',
        //   method: 'get',
        //   data: {
        //     id: _this.orderId
        //   }
        // });
        uni.hideLoading();
        if (res && res.data) {
          _this.isDisable = res.data.isSubmit;
          _this.infolist = res.data;

          if (_this.infolist.isSubmit) {
            _this.getNormalExp(_this.infolist.isExp);
            _this.getNormalNew(_this.infolist.isNewStudent);
            _this.educationStage = _this.infolist.educationStage; // 年级类型（1初中，2高中）
            _this.address = _this.infolist.address; // 联系人收货地址
            _this.examTime = _this.infolist.examTime; // 考试时间
            _this.remark = _this.infolist.remark;
            _this.getCourseDataFormServer();
            if (_this.infolist.courseProject) {
              _this.courseChoseList = JSON.parse(_this.infolist.courseProject);
            }
            _this.getNormalReviewData(_this.infolist);
            _this.getFormatToService(_this.infolist.firstTime, _this.infolist.firstWeek);
          }
        }
      },

      getFormatToService(date, week) {
        if (date) {
          this.firstTime = date;
          let str = dayjs(date).format('MM月DD日& HH:mm');
          let allStr = str;
          if (week) {
            allStr = str.replace('&', this.getWeekName(week));
            this.firstWeek = week;
          } else {
            allStr = str.replace('&', '');
          }
          this.firstStudyTime = allStr;
        }
      },

      getNormalReviewData(infolist) {
        this.reviewChoseWeek = JSON.parse(infolist.reviewWeek);
        this.rewviewdatevalue = infolist.reviewTime;
        this.comfireReviewData = {
          startTime: this.rewviewdatevalue,
          week: this.reviewChoseWeek
        };
      },

      getNormalExp(val) {
        this.isExp = val;
        this.yesExp = this.isExp ? 1 : 0;
        this.noExp = this.isExp ? 0 : 1;
      },
      getNormalNew(val) {
        this.isNewStudent = val;
        this.yesNew = this.isNewStudent ? 1 : 0;
        this.noNew = this.isNewStudent ? 0 : 1;
      },
      getGrade(val) {
        if (val) {
          val -= 1;
          let index = 0;
          if (val <= 0) {
            index = 0;
          } else if (val >= this.gradeNameArr.length) {
            index = this.gradeNameArr.length - 1;
          } else {
            index = val;
          }
          return this.gradeNameArr[index];
        }
        return this.gradeNameArr[this.gradeNameArr.length - 1];
      },

      //获取词汇
      async getVocabulary() {
        let _this = this;
        uni.showLoading();
        const res = await $http({
          url: 'znyy/course/sel/last/test',
          method: 'get',
          data: {
            studentCode: _this.infolist.studentCode
          }
        });
        uni.hideLoading();
        if (res && res.data) {
          _this.localWordBase = res.data.vocabulary;
          _this.infolist.wordBase = res.data.vocabulary;
        }
      },

      //计算首次上课时间
      getFirstStudyTime() {
        console.log('计算');
        if (this.comfireCourseData.length == 0) {
          this.firstWeek = '';
          this.firstTime = '';
          this.firstStudyTime = '';
          return;
        }
        let dateArr = [];
        let dateMinArr = [];
        let nowDate = dayjs().format('YYYY-MM-DD HH:mm');
        for (var i = 0; i < this.comfireCourseData.length; i++) {
          for (let j = 0; j < this.comfireCourseData[i].time.length; j++) {
            let date = this.getChoseDataToOption(this.comfireCourseData[i].week, this.comfireCourseData[i].time[j]);
            dateArr.push(date);
            let dayjs1 = dayjs(date);
            dateMinArr.push(dayjs1.diff(nowDate, 'minute'));
          }
        }
        if (dateArr.length === 0) {
          this.firstWeek = '';
          this.firstTime = '';
          this.abutmentList.firstStudyTime = '';
          return;
        }
        let needIndex = -1;
        let minMinVal = Infinity;
        for (let i = 0; i < dateMinArr.length; i++) {
          if (dateMinArr[i] > 24 * 60 && dateMinArr[i] < minMinVal) {
            needIndex = i;
            minMinVal = dateMinArr[i];
          }
        }
        if (needIndex != -1) {
          this.firstStudyTime = this.getFormatToShow(dateArr[needIndex]);
        } else {
          let minIndex = dateMinArr.indexOf(Math.min(...dateMinArr));
          let lastDate = dayjs(dateArr[minIndex]).add(7, 'day');
          this.firstStudyTime = this.getFormatToShow(lastDate);
        }
      },

      getFormatToShow(date) {
        let str = dayjs(date).format('MM月DD日& HH:mm');
        let weekIndex = this.getLocalTypeWeek(dayjs(date).day());
        this.firstTime = dayjs(date).format('YYYY-MM-DD HH:mm');
        this.firstWeek = weekIndex;
        let allStr = str.replace('&', this.getWeekName(weekIndex));
        return allStr;
      },

      getChoseDataToOption(week, data) {
        let date = this.getDateForWeek(week);
        return date + ' ' + data.startTime;
      },

      getDateForWeek(week) {
        let nowWeek = this.getLocalTypeWeek(dayjs().day());
        let diff = week - nowWeek;
        let date = '';
        if (diff >= 0) {
          date = dayjs().add(Math.abs(diff), 'day').format('YYYY-MM-DD');
        } else {
          date = dayjs().subtract(Math.abs(diff), 'day').format('YYYY-MM-DD');
        }
        return date;
      },

      getLocalTypeWeek(nowWeek) {
        return (nowWeek + 6) % 7;
      }
    }
  };
</script>

<style lang="scss" scoped>
  page {
    background-color: #fff;
  }

  .information {
    display: flex;
    justify-content: space-between;
    align-items: center;

    /deep/.uni-icons {
      color: #fff !important;
    }
  }

  .phone-input {
    background: #fff;
    border-radius: 8rpx;
    width: 100%;
    height: 70rpx;
    font-size: 28rpx;
    color: #999;
    display: flex;
    padding-left: 30rpx;
    align-items: center;
  }

  .uni-list-cell-db {
    background: #fff;
    border-radius: 8rpx;
    width: 100%;
    height: 70rpx;
    font-size: 28rpx;
    color: #999;
    display: flex;
    padding-left: 20rpx;
    align-items: center;
  }

  /deep/.date_color {
    color: #000 !important;
  }

  /deep/.regions {
    color: #999 !important;
    font-size: 30upx;
  }

  /deep/.phone-btn {
    width: 586rpx;
    height: 80rpx;
    position: absolute;
    bottom: 40rpx;
    left: 54rpx;
    line-height: 80rpx;
    border-radius: 45rpx;
    font-size: 30rpx;
    color: #fff !important;
    background: linear-gradient(to bottom, #88cfba, #1d755c);
  }

  /deep/.uni-select {
    padding: 0 10rpx 0 0;
    border: 0;
  }

  /deep/.uni-select__input-placeholder {
    font-size: 28rpx;
  }

  /deep/.uni-select--disabled {
    background-color: #fff;
  }

  /deep/.uni-stat__select {
    height: 60rpx !important;
  }

  .borderB {
    border-bottom: 1px solid #efefef;
  }

  /deep/.uni-select__input-placeholder {
    color: #999 !important;
    font-size: 30rpx !important;
  }

  .icon_x {
    position: absolute;
    top: 28rpx;
    right: 0;
    z-index: 1;
  }

  .time-icon {
    /deep/.u-icon--right {
      position: absolute;
      right: 0;
      top: 20rpx;
    }
  }

  /deep/.u-picker__view {
    height: 600rpx !important;
  }

  .dialogBG {
    margin: 0 20rpx 20rpx 20rpx;
    height: 961rpx;
    background-color: #fff;
    border-radius: 12rpx;
  }

  .top-close {
    color: black;
    font-size: 30rpx;
    margin: 0 35rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .center-content {
    margin-top: 40rpx;
    height: 760rpx;
  }

  .start-time {
    margin: 30rpx 35rpx 0rpx 35rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: black;
  }

  .top-button {
    text-align: center;
    height: 80rpx;
    display: flex;
    justify-content: center;
  }

  .confirm-button {
    width: 250rpx;
    height: 80rpx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    color: #fff;
    font-size: 32rpx;
    display: flex;
    justify-content: center;
    /* 文本水平居中对齐 */
    align-items: center;
    /* 文本垂直居中对齐 */
  }

  .chose-week-text {
    width: 100%;
    margin: 0 15rpx;
    display: flex;
    align-content: flex-start;
    flex-flow: row wrap;
  }

  .chose-week-item {
    margin: 0 20rpx 30rpx 20rpx;
  }

  .week-normal {
    width: 120rpx;
    height: 65rpx;
    background: rgba(177, 177, 177, 0.2);
    border-radius: 12rpx;
    border: 1rpx solid #b1b1b1;
    color: #000000;
    font-size: 30rpx;
    display: flex;
    align-items: center;
    /* 垂直居中 */
    justify-content: center;
    /* 水平居中 */
  }

  .week-chose {
    width: 120rpx;
    height: 65rpx;
    background: rgba(46, 137, 111, 0.1);
    border-radius: 12rpx;
    border: 1rpx solid #2e896f;
    color: #2e896f;
    font-size: 30rpx;
    display: flex;
    align-items: center;
    /* 垂直居中 */
    justify-content: center;
    /* 水平居中 */
  }

  .text-view {
    font-size: 32rpx;
    color: #333333;
  }

  .text-more-view {
    display: flex;
    flex-direction: row;
    /* 设置为水平排列 */
  }

  .course-bg-view {
    width: 530rpx;
    height: 60rpx;
    line-height: 60rpx;
    background: rgba(153, 153, 153, 0.1);
    border-radius: 12rpx;
    // padding-top: 24rpx;
    padding-left: 24rpx;
    padding-right: 24rpx;
    font-size: 26rpx;
    color: #666666;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .remake-bg-view {
    background: rgba(153, 153, 153, 0.1);
    border-radius: 12rpx;
    padding: 24rpx 35rpx;
    font-size: 28rpx;
    color: #000;
  }

  .time-flag {
    width: 38rpx;
    height: 38rpx;
    margin-top: 5rpx;
    margin-left: 20rpx;
  }

  .week-view {
    margin-left: 35rpx;
    margin-right: 24rpx;
    display: flex;
    justify-content: space-between;
  }

  .time-view {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .time-text-view-normal {
    color: #999999;
    width: 116rpx;
    font-size: 28rpx;
  }

  .time-text-view-normal-1 {
    border-radius: 12rpx;
    border: 1px solid #c8c8c8;
    background-color: #f4f4f4;
    color: #000;
    width: 116rpx;
    padding: 10rpx 0;
    font-size: 28rpx;
  }

  .time-text-view {
    color: #000000;
    width: 116rpx;
    font-size: 30rpx;
  }

  .thesaurus {
    background-color: #1d755c;
    padding: 5rpx 8rpx;
  }

  .first-class-vocabulary {
    position: absolute;
    top: 10rpx;
    right: 2rpx;
    color: #fff;
    height: 40rpx;
    font-size: 24rpx;
    line-height: 40rpx;
    padding: 0 5rpx;
    background-color: #2f8069;
    border-radius: 10rpx 0 0 10rpx;
  }

  .course {
    width: 420rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  ::v-deep {
    .u-textarea.u-textarea {
      padding: 9px 0 20px;
    }
  }
</style>
