<template>
  <view class="bg-ff read_main_css">
    <view class="w100 read_banner_main plr-32">
      <swiper
        :autoplay="true"
        indicator-color="rgba(227, 231, 230, 1)"
        indicator-active-color="#04614B"
        class="swiper_css"
        :indicator-dots="indicatorDots"
        :current="bannerIndex"
        :interval="3000"
        :duration="1000"
      >
        <block v-for="(item, index) in bannerList" :key="index">
          <swiper-item class="flex-c radius-16 swiper_css_item">
            <image :src="item.bannerPicUrl" mode="aspectFill" class="wh100" lazy-load="true" @tap="bannerTab(item)"></image>
          </swiper-item>
          <!-- #04614B -->
        </block>
      </swiper>
    </view>
    <view class="read_content_css">
      <view class="plr-32">
        <u-tabs
          :list="readList"
          :current="readCurrent"
          keyName="name"
          lineWidth="40"
          lineHeight="11"
          :activeStyle="{ color: '#333333', fontWeight: 'bold', fontSize: '28rpx' }"
          :inactiveStyle="{ color: '#5A5A5A ', transform: 'scale(1)', fontSize: '28rpx' }"
          itemStyle="padding-left:1px; padding-right: 18px; height: 42px;"
          :lineColor="`url(${lineBg}) 100% 110%`"
          @click="readClick"
        ></u-tabs>
      </view>
      <scroll-view
        :scroll-top="scrollTop"
        class="read-content-style"
        @scrolltolower="scrolltolower"
        @scroll="scroll"
        :show-scrollbar="false"
        bounces
        :throttle="false"
        scroll-with-animation
        scroll-anchoring
        scroll-y
        enhanced
      >
        <view v-if="readCurrent == 0">
          <view v-for="(item, index) in recordedLessons" class="plr-32" :key="index">
            <view class="f-28 c-55 lh-40 mt-35">
              {{ item.goodsName }}
            </view>
            <view v-for="(info, i) in item.goodsCatalogueList" :key="i" class="flex-x-s flex-a-c mt-24" @click="goVideo(info)">
              <view class="image_left_content positionRelative">
                <image class="imagele radius-8" :src="item.goodsPicUrl"></image>
                <image class="video_play positionAbsolute" src="https://document.dxznjy.com/course/25d0f57150cf4aeaac8cd9a7489b7a9d.png"></image>
              </view>
              <view class="ml-15 right_record_css">
                <view class="f-28 c-55 lh-40">{{ item.goodsName }}</view>
                <view class="f-24 c-55 lh-36">{{ info.catalogueName }}</view>
                <view class="mt-15">
                  <u-line-progress activeColor="#339378" :showText="false" :percentage="info.learningProgress" height="16"></u-line-progress>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view v-if="readCurrent == 1">
          <view class="pb-70">
            <view v-for="item in releaseSelecte" :key="item.id" class="plr-32 pb-20 pt-24 border_color">
              <releaseItem
                releaseType="3"
                @showImage="showImage"
                :showLike="true"
                @addThumb="addThumb"
                @getRelease="getRelease"
                :releaseStyle="releaseStyle"
                :releaseInfo="item"
              ></releaseItem>
            </view>
            <suspensionBtn @release="release" :showIcon="true" :fixedText="fixedText"></suspensionBtn>
          </view>
        </view>
        <view class="pt-28" v-if="(readCurrent == 0 && recordedLessons.length == 0) || (readCurrent == 1 && releaseSelecte.length == 0)">
          <emptyPage></emptyPage>
        </view>
      </scroll-view>
    </view>
    <uni-popup ref="video_popup" type="center" style="padding: 0">
      <view class="bg-00 video_content_css">
        <view class="tips_content_close" @click="closeVideo()">
          <u-icon name="close-circle-fill" color="#B1B1B1" size="38"></u-icon>
        </view>
        <polyv-player
          id="polyv_player"
          @loadedmetadata="bindloadedmetadata"
          @pause="bindpause"
          @ended="bindEnded"
          :autoplay="true"
          :playerId="playerIdcont"
          :vid="videoInfo.videoUrl"
          :width="width"
          :height="height"
          :ts="ts"
          :sign="sign"
        ></polyv-player>
      </view>
    </uni-popup>
    <tipsContentPopup ref="tipsContentPopupRefs" :tipsType="tipsType"></tipsContentPopup>
    <growthPopup ref="growthPopupRefs"></growthPopup>
  </view>
</template>

<script>
  import releaseItem from '../components/releaseItem.vue';
  import suspensionBtn from '../components/suspensionBtn.vue';
  import growthPopup from '../components/growthPopup.vue';
  import emptyPage from '../components/emptyPage.vue';
  import tipsContentPopup from '../components/tipsContentPopup.vue';
  const { $navigationTo, $http } = require('@/util/methods.js');
  let ts = new Date().getTime();
  let sign = '';
  export default {
    components: { releaseItem, suspensionBtn, growthPopup, tipsContentPopup, emptyPage },
    data() {
      return {
        readList: [
          { name: '读书文化录播课', key: 1 },
          { name: '心灵旅程', key: 2 }
        ],
        fixedText: '上传',
        lineBg: 'https://document.dxznjy.com/course/01c96465d9ea46f69aa97465b1201aef.png',
        readCurrent: 0,
        tipsType: 3,
        //心灵旅程
        releaseSelecte: [],
        releaseStyle: {
          leftImageWidth: '100rpx'
        },
        recordedLessons: [],
        bannerList: [],
        indicatorDots: true,
        identityType: uni.getStorageSync('identityType'),
        videoInfo: {},
        domId: 'polyvPlayer',
        playerIdcont: 'polyvPlayercont',
        ts: ts,
        sign: sign,
        width: '100%',
        height: '100%',
        studyTime: 0,
        scrollTop: 0,
        infoLists: {},
        scrollTopNum: 0,
        showImageType: false,
        page: 1
      };
    },
    onReachBottom() {
      console.log(this.infoLists);
      console.log(this.page);
      if (this.page * 10 >= this.infoLists.totalItems) {
        return false;
      }
      if (this.readCurrent == 1) {
        this.getCultureCircle(true, ++this.page);
      }
    },
    onLoad(e) {
      this.banner();
    },
    onShow() {
      this.page = 1;
      this.scrollTop = 0;
      if (this.showImageType) {
        this.showImageType = false;
        return;
      }
      this.readClick({ index: this.readCurrent, inKey: -1 });
    },
    onUnload() {
      if (this.$refs.video_popup.showPopup) {
        this.closeVideo();
      }
    },
    onReady() {},
    methods: {
      scrolltolower() {
        if (this.page * 10 >= this.infoLists.totalItems) {
          return false;
        }
        if (this.readCurrent == 1) {
          this.getCultureCircle(true, ++this.page);
        } else if (this.readCurrent == 0) {
          this.getFindRecordedCoursesList(true, ++this.page);
        }
      },
      scroll(e) {
        this.scrollTopNum = e.detail.scrollTop;
      },
      async banner() {
        let _this = this;
        const res = await $http({
          url: 'zx/wap/layout/banner/list',
          showLoading: true,
          data: {
            bannerPosition: 4
          }
        });
        if (res) {
          _this.bannerList = res.data || [];
        }
      },
      async getCultureCircle(isPage, page) {
        // page = page || 1;
        let _this = this;
        const res = await $http({
          url: 'zx/wap/CultureCircle',
          showLoading: true,
          data: {
            topicType: 'READ_CULTURE',
            pageNum: page || 1,
            pageSize: 10,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        if (res) {
          this.infoLists = res.data;
          if (isPage == -1) {
            _this.scrollTop = this.scrollTopNum;
            this.$nextTick(() => {
              _this.scrollTop = 0;
            });
          }
          if (isPage && isPage != -1) {
            this.releaseSelecte = [...this.releaseSelecte, ...res.data.data];
          } else {
            this.releaseSelecte = res.data.data || [];
          }
        }
      },
      async getGrowthValue(text) {
        if (this.identityType != 4) {
          return;
        }
        const res = await $http({
          url: 'zx/wap/CultureCircle/getGrowthValue?eventType=' + text + '&userId=' + uni.getStorageSync('user_id'),
          method: 'POST',
          data: {}
        });
        if (res) {
          if (Number(res.data)) {
            this.$refs.growthPopupRefs.open(res.data);
          }
        }
      },
      showImage(list, index) {
        this.showImageType = true;
        let photoList = list.map((item) => {
          return item.url;
        });
        uni.previewImage({
          urls: photoList,
          current: index,
          indicator: 'default'
        });
      },
      bannerTab(item) {
        if (item.needLogin == 0 && !uni.getStorageSync('token')) {
          uni.navigateTo({
            url: '/Personalcenter/login/login'
          });
        } else {
          if (item.goodsId) {
            $navigationTo('Coursedetails/productDetils?id=' + item.goodsId);
          } else {
            $navigationTo(item.bannerLinkUrl);
          }
        }
      },
      readClick(e) {
        this.page = 1;
        this.readCurrent = e.index;
        if (this.readCurrent == 1) {
          this.getCultureCircle(e.inKey);
        } else if (this.readCurrent == 0) {
          let that = this;
          setTimeout(() => {
            that.getFindRecordedCoursesList(-1);
          }, 300);
        }
      },
      async getFindRecordedCoursesList(isPage, page) {
        let _this = this;
        const res = await $http({
          url: 'zx/wap/recorded/course/findRecordedCoursesList',
          data: {
            courseType: 2,
            pageNum: page || 1,
            pageSize: 10
          }
        });
        if (res) {
          this.infoLists = res.data;
          if (isPage == -1) {
            _this.scrollTop = this.scrollTopNum;
            this.$nextTick(() => {
              _this.scrollTop = 0;
            });
          }
          if (isPage && isPage != -1) {
            this.recordedLessons = [...this.recordedLessons, ...res.data.data];
          } else {
            this.recordedLessons = res.data.data || [];
          }
        }
      },
      goVideo(info) {
        if (this.identityType == 4) {
          uni.navigateTo({
            url: '/Coursedetails/culturalType/palyVideo'
          });
          uni.setStorageSync('videoInfo', info);
          // this.$refs.video_popup.open()
          // this.videoInfo=info
          // this.studyTime=info.studyTime||0
          // let _this=this
          // this.$nextTick(()=>{
          // 	let polyvPlayerContext = _this.selectComponent('#polyv_player');
          // 	const ts = new Date().getTime()
          // 	const sign = MD5.md5(`${secretkey}${vid}${ts}`);
          // 	polyvPlayerContext.changeVid({
          // 		vid: _this.videoInfo.videoUrl,
          // 		ts,
          // 		sign
          // 	});
          // })
        } else {
          this.$refs.tipsContentPopupRefs.open();
          this.tipsType = 2;
        }
      },
      async closeVideo() {
        this.$refs.video_popup.close();
        let polyvPlayerContext = this.selectComponent('#polyv_player');
        let learningProgress = (polyvPlayerContext.rCurrentTime / polyvPlayerContext.rDuration).toFixed(2);
        console.log(this.videoInfo);
        console.log(learningProgress);
        let rCurrentTime = polyvPlayerContext.rCurrentTime.toFixed(2);
        //
        let _this = this;
        const res = await $http({
          url: 'zx/wap/recorded/course/saveLeanProcess?courseType=2&learningProgress=' + learningProgress * 100 + '&courseId=' + this.videoInfo.id + '&studyTime=' + rCurrentTime,
          method: 'POST',
          data: {
            courseType: 2,
            learningProgress: learningProgress * 100,
            courseId: this.videoInfo.id
          }
        });
        if (res) {
          this.getFindRecordedCoursesList();
        }
      },
      async getGrowthValue(text) {
        if (this.identityType != 4) {
          return;
        }
        const res = await $http({
          url: 'zx/wap/CultureCircle/getGrowthValue?eventType=' + text + '&userId=' + uni.getStorageSync('user_id'),
          method: 'POST',
          data: {}
        });
        if (res) {
          if (Number(res.data)) {
            this.$refs.growthPopupRefs.open(res.data);
          }
        }
      },
      bindpause() {},
      bindEnded() {
        this.getGrowthValue('LEARN');
      },
      bindloadedmetadata() {
        let polyvPlayerContext = this.selectComponent('#polyv_player');
        polyvPlayerContext.pause();
        polyvPlayerContext.seek(Number(this.studyTime));
      },
      release() {
        if (this.identityType == 4) {
          uni.navigateTo({
            url: '/memberCenter/releaseIndex?type=2'
          });
        } else {
          this.$refs.tipsContentPopupRefs.open();
          this.tipsType = 3;
        }
      },
      openMembership() {
        $navigationTo('Personalsscenter/my/nomyEquity?type=2');
      }
    }
  };
</script>

<style lang="scss" scoped>
  .read-content-style {
    height: calc(100vh - 480rpx);
  }
  .read_main_css {
    height: 100vh;
    overflow-x: hidden;
    overflow-y: scroll;
    .imagele {
      width: 100rpx;
      height: 100rpx;
    }
    .read_banner_main {
      padding-top: 24rpx;
      .swiper_css {
        height: 300rpx;
        width: 686rpx;
        .swiper_css_item {
          height: 240rpx !important;
        }
      }
    }
    .right_record_css {
      width: 430rpx;
    }
    .image_left_content {
      .imagele {
        width: 260rpx;
        height: 146rpx;
      }
      .video_play {
        width: 64rpx;
        height: 64rpx;
        left: 96rpx;
        top: 50rpx;
      }
    }
    .border_color {
      border-bottom: 1rpx solid #ecf0f4;
    }
    .tips_content_css {
      width: 560rpx;
      text-align: center;
    }
  }
  .video_content_css {
    height: 100vh;
    width: 750rpx;
    position: relative;
    z-index: 5;
    .video_css {
      width: 750rpx;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }
    .tips_content_close {
      position: absolute;
      right: 32rpx;
      top: 40rpx;
      z-index: 3;
    }
  }
</style>
