<template>
  <view style="min-height: 100vh; background: #fff">
    <view class="list_tabs">
      <view @click="switchTab(1)" class="item_tab" :class="{ is_active: active === 1 }">
        <view :class="{ rounded_border: active === 1 }">原密码修改</view>
      </view>
      <view class="border_box"></view>
      <view @click="switchTab(2)" class="item_tab" :class="{ is_active: active === 2 }">
        <view :class="{ rounded_border: active === 2 }">短信验证修改</view>
      </view>
    </view>
    <view class="password">
      <view class="form_item">
        <view class="item_title">登录{{ active === 1 ? '账号' : '手机号' }}</view>
        <input
          type="number"
          name="mobile"
          :placeholder="`请输入您的${active === 1 ? '账号' : '手机号'}`"
          v-model="mobile"
          maxlength="11"
          @input="this.mobile = this.mobile.replace(/[^0-9]/g, '')"
        />
      </view>
      <view class="form_item" v-if="active === 1">
        <view class="item_title">账号原始密码</view>
        <input type="number" placeholder="请输入原始密码" v-model="oldPassWord" @input="this.oldPassWord = this.oldPassWord.replace(/[^0-9]/g, '')" />
      </view>
      <view class="form_item" v-if="active === 2">
        <view class="item_title">验证码</view>
        <view style="display: flex; justify-content: space-between; width: 458rpx">
          <input type="number" style="width: 268rpx" placeholder="请输入验证码" v-model="smsCode" @input="this.smsCode = this.smsCode.replace(/[^0-9]/g, '')" />
          <view class="info_text" v-if="isCounting">{{ countdown + '秒' }}</view>
          <view class="info_text" v-else @click="getSmsCode">获取短信验证码</view>
        </view>
      </view>
      <view class="form_item">
        <view class="item_title">账号新密码</view>
        <input type="number" placeholder="请输入新的账号密码" v-model="newPassWord" @input="this.newPassWord = this.newPassWord.replace(/[^0-9]/g, '')" />
      </view>
      <view class="form_item">
        <view class="item_title">确认新密码</view>
        <input type="number" placeholder="请再次输入新的账号密码" v-model="comfirmPassWord" @input="this.comfirmPassWord = this.comfirmPassWord.replace(/[^0-9]/g, '')" />
      </view>
    </view>
    <view class="btn-box">
      <button class="btn_confirm" @click="confirmChange(active)">确定</button>
    </view>
    <view
      v-if="codeIsShow"
      style="height: 100vh; width: 100vw; background-color: rgba(0, 0, 0, 0.75); position: fixed; z-index: 199; top: 0; left: 0; padding-top: 45%"
      @click.prevent="closePopup"
    >
      <view class="code" style="background: #fff; margin: 10rpx; margin-top: -10rpx; padding-bottom: 20rpx; border-radius: 5%" @click.stop="clear">
        <view style="display: flex; align-items: center; height: 80rpx; padding: 0 48rpx; justify-content: space-between">
          <view style="margin-top: 4rpx; font-weight: bold; font-size: 26rpx">请输入图形验证码</view>
          <view @click.stop="closePopup">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
        </view>
        <view>
          <pt-images-verification
            :uuid="uuid"
            :imgHeight="codeObj.imgHeight"
            :imgWidth="codeObj.imgWidth"
            :left="codeObj.top"
            :bgImg="codeObj.bgImg"
            :maskImg="codeObj.maskImg"
            @refresh="refresh"
            @success="success"
          ></pt-images-verification>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  import ptImagesVerification from '../components/pt-images-verification.vue';
  export default {
    components: {
      ptImagesVerification
    },
    data() {
      return {
        codeObj: {
          bgImg: '',
          maskImg: '',
          imgHeight: '',
          imgWidth: '',
          top: 0 // 凹陷区距离背景图左边的距离
        },
        uuid: '',
        codeIsShow: false,
        active: 1,
        mobile: '',
        oldPassWord: '',
        smsCode: '',
        newPassWord: '',
        comfirmPassWord: '',
        isCounting: false,
        countdown: 60,
        timer: null,
        disabled: false
      };
    },
    onLoad(e) {},
    onShow() {
      // this.mobile = ''
      this.oldPassWord = '';
      // this.smsCode = ""
      this.newPassWord = '';
      this.comfirmPassWord = '';
      this.disabled = false;
      // this.active = 1
    },
    methods: {
      //验证表单
      validateForm: function () {
        if (this.mobile == '') {
          this.$util.alter('手机号码不能为空');
          return false;
        }
        if (this.mobile.length != 11) {
          this.$util.alter('手机号码必须是11位');
          return false;
        }
        if (this.active === 2 && this.smsCode == '') {
          this.$util.alter('短信验证码不能为空');
          return false;
        }
        if (this.active === 2 && this.smsCode.length != 6) {
          this.$util.alter('手机效验码必须是6位');
          return false;
        }
        if (this.active === 1 && this.oldPassWord == '') {
          this.$util.alter('原密码不能为空');
          return false;
        }
        if (this.active === 1 && this.oldPassWord.length < 6) {
          this.$util.alter('原密码不能少于6位');
          return false;
        }
        if (this.newPassWord == '') {
          this.$util.alter('新密码不能为空');
          return false;
        }
        if (this.newPassWord.length < 6) {
          this.$util.alter('新密码不能少于6位');
          return false;
        }
        if (this.comfirmPassWord == '') {
          this.$util.alter('确认密码不能为空');
          return false;
        }
        if (this.comfirmPassWord.length < 6) {
          this.$util.alter('确认密码不能少于6位');
          return false;
        }
        if (this.newPassWord != this.comfirmPassWord) {
          this.$util.alter('两次密码输入不一致');
          return false;
        }
        return true;
      },
      switchTab(tab) {
        this.active = tab;
        this.mobile = '';
        this.oldPassWord = '';
        this.smsCode = '';
        this.newPassWord = '';
        this.comfirmPassWord = '';
        this.isCounting = false;
      },
      async getSmsCode() {
        console.log(this.mobile, this.mobile.length);
        if (this.mobile.length != 11) {
          this.$util.alter('请输入手机号');
          return false;
        }
        await this.getNum();
        this.codeIsShow = true;
      },
      refresh() {
        // this.closePopup();
        this.getNum();
      },
      success(e) {
        console.log(e, 'e11111111111111111111111');
        this.closePopup();
        this.confirm(e);
      },
      closePopup() {
        // if (this.stop) return;
        this.codeIsShow = false;
      },
      clear(e) {},
      confirm(e) {
        let that = this;

        that.$httpUser.post('zx/common/checkRegister?mobile=' + that.mobile).then((res) => {
          if (res.data.status == 1) {
            that.$httpUser.post('new/security/sms/forget/' + that.mobile + '?code=' + e + '&uuid=' + that.uuid).then((res) => {
              if (res.data.success) {
                this.$util.alter('发送成功,请注意查收');
                this.downFun(); // 倒计时执行事件
              } else {
                this.$util.alter(res.data.message);
              }
            });
          } else {
            that.$util.alter(res.data.message);
          }
        });
      },
      // 倒计时执行事件
      downFun() {
        let TIME_COUNT = 60;
        if (!this.timer) {
          this.countdown = TIME_COUNT;
          this.isCounting = true;
          this.timer = setInterval(() => {
            if (this.countdown > 0 && this.countdown <= TIME_COUNT) {
              this.countdown--;
            } else {
              this.isCounting = false;
              clearInterval(this.timer);
              this.timer = null;
            }
          }, 1000);
        }
      },
      generateUUID() {
        var d = new Date().getTime(); //Timestamp
        // var d2 = (performance && performance.now && performance.now() * 1000) || 0; //Time in microseconds since page-load or 0 if unsupported
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
          var r = Math.random() * 16; //random number between 0 and 16
          // if (d > 0) {
          //Use timestamp until depleted
          r = (d + r) % 16 | 0;
          d = Math.floor(d / 16);
          // } else {
          // log
          // //Use microseconds since page-load if supported
          // r = (d2 + r) % 16 | 0;
          // d2 = Math.floor(d2 / 16);
          // }
          return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16);
        });
      },
      getNum() {
        let that = this;
        return new Promise((resolve, reject) => {
          that.uuid = that.generateUUID();
          that.$httpUser.get('new/security/captcha/image/slide?uuid=' + that.uuid).then((res) => {
            if (res.data.success) {
              let a = res.data.data;
              that.codeObj.maskImg = 'data:image/png;base64,' + a.cutImage;
              that.codeObj.bgImg = 'data:image/png;base64,' + a.oriImage;
              that.codeObj.imgWidth = a.cutImageWidth / 3.2 + '%';
              that.codeObj.imgHeight = a.cutImageHeight / 1.6 + '%';
              // that.codeObj.left = a.ypos;
              that.codeObj.top = a.ypos / 1.6 + 1;
              resolve();
            }
          });
        });
      },
      confirmChange() {
        if (this.disabled) {
          uni.showToast({
            title: '已提交确认，请勿重复点击',
            icon: 'none',
            duration: 2000
          });
          return;
        }
        if (!this.validateForm()) {
          return false;
        }
        this.disabled = true;
        const type = 9;
        if (this.active === 1) {
          $http({
            url: 'new/security/user/password/old?mobile=' + this.mobile + '&type=' + Number(type) + '&newPassword=' + this.newPassWord + '&oldPassword=' + this.oldPassWord,
            method: 'PUT'
          })
            .then((result) => {
              if (result.code === 20000) {
                uni.showToast({
                  title: '修改成功,请重新登录'
                });
                uni.redirectTo({
                  url: '/Personalcenter/login/login'
                });
              }
            })
            .catch((err) => {
              console.log('err', err);
            })
            .finally(() => {
              // this.disabled = false
              setTimeout(() => {
                this.disabled = false;
              }, 500);
            });
        } else if (this.active === 2) {
          $http({
            url: 'new/security/user/password/' + this.mobile + '?type=' + Number(type) + '&newPassword=' + this.newPassWord + '&smsCode=' + this.smsCode,
            method: 'PUT'
          })
            .then((res) => {
              if (res.code === 20000) {
                uni.showToast({
                  title: '修改成功,请重新登录'
                });
                uni.redirectTo({
                  url: '/Personalcenter/login/login'
                });
              } else {
                this.isCounting = false;
                this.countdown = 60;
              }
            })
            .catch((error) => {
              console.log('passerr', error);
            })
            .finally(() => {
              // this.disabled = false
              setTimeout(() => {
                this.disabled = false;
              }, 500);
            });
        }
      }
    },
    beforeDestroy() {
      // 清除定时器以防内存泄漏
      if (this.timer) {
        clearInterval(this.timer);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .list_tabs {
    display: flex;
    align-items: center;
    height: 96rpx;
    background: #f1f4f6;
    padding: 0 32rpx;

    .item_tab {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 374rpx;
      font-size: 28rpx;
      color: #5a5a5a;

      .rounded_border {
        width: auto;
        position: relative;
      }

      .rounded-border::after {
        content: '';
        position: absolute;
        bottom: 4rpx;
        left: 0;
        right: 0;
        height: 6rpx;
        background: #339378;
        border-radius: 4rpx;
      }
    }

    .border_box {
      width: 2rpx;
      height: 32rpx;
      background-color: #e2e6e9;
    }

    .is_active {
      color: #333333;
      font-family: AlibabaPuHuiTi_2_85_Bold;
      font-weight: bold;
    }
  }

  .password {
    padding: 40rpx 32rpx 0 32rpx;

    .form_item {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      height: 64rpx;
      margin-bottom: 32rpx;
      input {
        width: 458rpx;
        height: 64rpx;
        box-sizing: border-box;
        background: rgba(243, 248, 252, 0.31);
        border: 2rpx solid #f3f8fc;
        padding-left: 32rpx;
      }
    }

    .item_title {
      width: 228rpx;
      height: 42rpx;
      font-family: AlibabaPuHuiTi_2_85_Bold;
      font-size: 28rpx;
      color: #555555;
      line-height: 42rpx;
      text-align: left;
      font-style: normal;
      font-weight: bold;
    }

    .info_text {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 168rpx;
      font-size: 24rpx;
      color: #489981;
      font-weight: bold;
    }
  }

  .btn_confirm {
    width: 686rpx;
    height: 92rpx;
    background: #428a6f;
    border-radius: 46rpx;
    margin: 0 32rpx;
    position: fixed;
    bottom: 48rpx;
    font-family: AlibabaPuHuiTiR;
    font-size: 32rpx;
    color: #ffffff;
    line-height: 92rpx;
  }
</style>
