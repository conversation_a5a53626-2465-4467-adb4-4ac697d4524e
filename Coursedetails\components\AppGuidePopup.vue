<template>
  <!-- 引导下载弹窗 -->
  <uni-popup ref="appGuidePopup" type="center" style="padding: 0">
    <view class="dialogBG">
      <view class="reviewCard_box positionRelative">
        <view class="cartoom_image">
          <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
        </view>
        <view class="review_close" @click="closeDialog">
          <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
        </view>
        <view class="reviewCard">
          <view class="reviewTitle">请扫描二维码</view>
          <view class="reviewTitle">下载鼎校甄选APP</view>
          <view class="dis_center">
            <image class="imgCode" :src="codeForm.qrCode" show-menu-by-longpress="true" @tap="previewImage"></image>
          </view>
          <view class="review_btn" @click="saveCode()">下载二维码</view>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script>
  const { $navigationTo, $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        codeForm: {
          qrCode: 'https://document.dxznjy.com/course/054d1bdaa7cb4bc29d748ccf3dee2244.png'
        }
      };
    },
    methods: {
      saveCode() {
        let that = this;
        uni.downloadFile({
          url: that.codeForm.qrCode,
          success: (res) => {
            console.log('res', res);
            if (res.statusCode === 200) {
              // #ifdef MP-WEIXIN
              // 手动加后缀
              const filePath = res.tempFilePath;
              const newFilePath = `${wx.env.USER_DATA_PATH}/qrcode.png`; // 改成你想要的文件名和后缀
              console.log('filePath', filePath);
              console.log('newFilePath', newFilePath);
              // 使用 FileSystemManager 复制并加后缀
              const fs = wx.getFileSystemManager();
              fs.copyFile({
                srcPath: filePath,
                destPath: newFilePath,

                success: () => {
                  uni.saveImageToPhotosAlbum({
                    filePath: newFilePath,
                    success: () => {
                      uni.showToast({ title: '图片已保存', duration: 2000 });
                    },
                    fail: (err) => {
                      uni.showToast({ title: '保存失败', duration: 2000, icon: 'none' });
                    }
                  });
                },
                fail: (err) => {
                  console.error('文件重命名失败', err);
                  uni.showToast({ title: '处理失败', duration: 2000, icon: 'none' });
                }
              });
              // #endif
              // #ifndef MP-WEIXIN
              uni.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: function () {
                  uni.showToast({
                    title: '图片已保存',
                    duration: 2000
                  });
                },
                fail: function () {
                  uni.showToast({
                    title: '保存失败',
                    duration: 2000,
                    icon: 'none'
                  });
                }
              });
              // #endif
            }
          },
          fail: () => {
            uni.showToast({ title: '下载失败', duration: 2000, icon: 'none' });
          }
        });
      },
      previewImage() {
        uni.previewImage({
          current: this.codeForm.qrCode, // 当前显示图片的http链接
          urls: [this.codeForm.qrCode] // 需要预览的图片http链接列表
        });
      },
      handleClose() {
        this.$refs.appGuidePopup.close();
      },
      //关闭弹窗
      closeDialog() {
        this.$refs.appGuidePopup.close();
      },
      open() {
        this.$refs.appGuidePopup.open();
      }
    }
  };
</script>
<style lang="scss" scoped>
  /* 弹窗样式 */
  .dialogBG {
    width: 100%;
    /* height: 100%; */
  }

  /* 21天结束复习弹窗样式 */
  .reviewCard_box {
    width: 670rpx;
    /* height: 560rpx; */
    position: relative;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }

  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 145rpx;
    z-index: -1;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 28rpx;
    display: flex;
    justify-content: center;
  }
  .dis_center {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .imgCode {
    margin-top: 30rpx;
    height: 360rpx !important;
    width: 360rpx !important;
  }

  .dialogContent {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }

  .review_btn {
    width: 240upx;
    height: 80upx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 80upx;
    margin: 30rpx auto 0 auto;
    justify-content: center;
    text-align: center;
  }
</style>
