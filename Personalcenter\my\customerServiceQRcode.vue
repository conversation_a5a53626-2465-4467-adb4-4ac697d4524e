<!-- 添加客服二维码 -->
<!-- 展示员工企业微信名片 -->
<template>
  <view class="">
    <view class="content">
      <view class="userCard">
        <view class="tip_text">
          <view class="">想了解更多关于家长会员的事宜</view>
          <view class="">
            <span class="tip_red">长按识别二维码</span>
            添加鼎校甄选相关
          </view>
          人员企业微信了解
        </view>
        <image class="ewm" :src="codeImg" :show-menu-by-longpress="true" mode=""></image>
      </view>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        type: 5, // 家长会员
        wxUrl: 'https://open.work.weixin.qq.com/wwopen/userQRCode?vcode=vc4181b8fa39d70214',
        codeImg: '' // 二维码
      };
    },
    onLoad() {},
    onShow() {
      this.getQrCode();
    },
    methods: {
      // 获取客服二维码
      async getQrCode() {
        let res = await $http({
          url: 'zx/common/getKfCodeByType',
          data: {
            type: this.type
          }
        });
        console.log(res);
        this.codeImg = res.data.codeUrl;
      }
    }
  };
</script>

<style>
  .content {
    width: 100%;
    height: 100vh;
    text-align: center;
    background-color: rgb(244, 245, 247);
    justify-content: center;
    flex-wrap: wrap;
  }

  .userCard {
    height: 96vh;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    align-items: center;
    margin: 1vh auto;
  }

  .ewm {
    width: 400upx;
    height: 400upx;
    margin-bottom: 460upx;
  }

  .tip_text {
    font-size: 30upx;
    width: 500upx;
    line-height: 45upx;
    color: #333333;
    margin-top: 230upx;
  }

  .tip_red {
    color: #ea6031;
  }

  .trialStyle {
    line-height: 50upx;
    margin: 100upx auto 0 auto;
    width: 500upx;
    height: 120upx;
    color: #666666;
  }
</style>
