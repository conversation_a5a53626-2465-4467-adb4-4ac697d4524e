<template>
  <view class="container">
    <view class="flex-a-c nav-bar plr-20">
      <uni-icons type="left" size="24" @click="returnBack"></uni-icons>
      <view class="nav_title f-28">{{ title }}</view>
    </view>
    <view class="container-box">
      <view class="plr-30 single">
        <view class="f-28 mb-20 title">{{ title }}</view>
      </view>
      <view class="flex-c pt-20">
        <image show-menu-by-longpress="true" class="code-img" :src="codeImg" alt="" />
      </view>
    </view>
    <view class="text-btn dis_center" @click="saveCode">
      <text>下载二维码</text>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        codeImg: '',
        title: 'APP下载',
        type: 1,
        platform: '' // 系统信息
      };
    },
    onLoad(options) {
      this.type = options.type || 1;
      const { platform } = uni.getSystemInfoSync();
      // 系统信息
      this.platform = platform;
      // type 1 鼎校甄选  2 鼎星星
      if (this.type == 1) {
        // if (platform == 'ios') {
        //   this.title = '下载鼎校甄选IOS版';
        //   this.codeImg = 'https://img0.baidu.com/it/u=582752653,3386538207&fm=253&fmt=auto&app=138&f=GIF?w=500&h=500';
        //   return;
        // }
        this.title = '下载鼎校甄选App';
        this.codeImg = 'https://document.dxznjy.com/course/0878b6738cf04b228f333ef7e2e11298.png';
      } else {
        // if (platform == 'ios') {
        //   this.title = '下载鼎星星IOS版';
        //   this.codeImg = 'https://img0.baidu.com/it/u=582752653,3386538207&fm=253&fmt=auto&app=138&f=GIF?w=500&h=500';
        //   return;
        // }
        this.title = '下载鼎星星App';
        this.codeImg = 'https://document.dxznjy.com/course/455a9328423a4d8baaef2ba7cb99e66b.png';
      }
    },
    methods: {
      // 保存图片到本地
      // wx.env.USER_DATA_PATH 是小程序沙箱文件夹的路径
      // wx.getFileSystemManager() 是微信小程序原生 API
      // 我们通过复制的方式添加 .png 后缀，再保存,适配不同ios机型保存图片格式不正确问题
      saveCode() {
        const imgUrl = this.codeImg;
        // 确认框
        uni.showModal({
          title: '提示',
          content: '是否保存二维码图片到相册',
          success: function (res) {
            if (res.confirm) {
              uni.downloadFile({
                url: imgUrl,
                success: (res) => {
                  // console.log('res', res);
                  if (res.statusCode === 200) {
                    // #ifdef MP-WEIXIN
                    // 手动加后缀
                    const filePath = res.tempFilePath;
                    const newFilePath = `${wx.env.USER_DATA_PATH}/qrcode.png`; // 改成你想要的文件名和后缀
                    // console.log('filePath', filePath);
                    // console.log('newFilePath', newFilePath);
                    // 使用 FileSystemManager 复制并加后缀
                    const fs = wx.getFileSystemManager();
                    fs.copyFile({
                      srcPath: filePath,
                      destPath: newFilePath,

                      success: () => {
                        uni.saveImageToPhotosAlbum({
                          filePath: newFilePath,
                          success: () => {
                            uni.showToast({ title: '图片已保存', duration: 2000 });
                          },
                          fail: (err) => {
                            uni.showToast({ title: '保存失败', duration: 2000, icon: 'none' });
                          }
                        });
                      },
                      fail: (err) => {
                        // console.error('文件重命名失败', err);
                        uni.showToast({ title: '处理失败', duration: 2000, icon: 'none' });
                      }
                    });
                    // #endif
                    // #ifndef MP-WEIXIN
                    uni.saveImageToPhotosAlbum({
                      filePath: res.tempFilePath,
                      success: function () {
                        uni.showToast({
                          title: '图片已保存',
                          duration: 2000
                        });
                      },
                      fail: function () {
                        uni.showToast({
                          title: '保存失败',
                          duration: 2000,
                          icon: 'none'
                        });
                      }
                    });
                    // #endif
                  }
                },
                fail: (e) => {
                  console.log('🚀 ~ saveCode ~ e:', e);
                  uni.showToast({ title: '下载失败', duration: 2000, icon: 'none' });
                }
              });
            }
          }
        });
      },
      // 返回上一页
      returnBack() {
        uni.navigateBack({
          delta: 1
        });
      }
    }
  };
</script>
<style lang="scss" scoped>
  page {
    background: linear-gradient(180deg, #2f8c70 0%, #82beac 100%);
  }

  .container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 169rpx);
    background-image: url(https://document.dxznjy.com/course/e05e1d50791c4483ab06aed781e9aeb8.png);
    background-size: 100% 100%;
    font-family: 'AlibabaPuHuiTi-3-85-Bold', 'Alibaba PuHuiTi', sans-serif;
    position: relative;
    top: 169rpx;
  }

  .container-box {
    width: 70%;
    margin: 260rpx auto 0;
    border-radius: 10rpx;

    .title {
      font-size: 36rpx;
      font-weight: 550;
      color: #2a5d4d;
    }
  }

  .nav-bar {
    position: fixed;
    top: 0;
    background-color: #f3f8fc;
    width: 100%;
    height: 100rpx;
    padding-top: 50rpx;
    z-index: 9;
  }

  .nav_title {
    margin-left: 32%;
  }

  .single {
    text-align: center;
  }

  .code-img {
    margin-top: 200rpx;
    width: 370rpx;
    height: 370rpx;
  }

  .text-btn {
    height: 94.25rpx;
    width: 357.14rpx;
    background-color: #428a6f;
    border-radius: 47rpx;
    color: #ffffff;
    font-size: 36rpx;
    position: absolute;
    bottom: 5vh;
    left: 50%;
    /* 将元素的左边缘移动到父容器的中心点 */
    transform: translateX(-50%);
    /* 将元素向左移动自身宽度的一半 */
  }

  .dis_center {
    display: flex;
    align-items: center;
    justify-content: center;
  }
</style>
