<template>
  <view class="member_center_css">
    <view class="flex-a-c flex-x-b plr-32 bg-ff">
      <view class="flex-a-c flex-x-s ptb-35">
        <view class="f-32 c-55 tabs_item_css" v-for="(item, index) in categoryList" @click="tabsClick({ index: index })" :key="item.key">
          <view :class="tabsCurrent == index ? 'active_tabs_css' : ''">
            <view>
              <view class="tabs_name">{{ item.name }}</view>
              <view class="line_tabs_css"></view>
            </view>
          </view>
        </view>
      </view>
      <image class="top_home_right" @click="goMyHoem" src="https://document.dxznjy.com/course/9cd7833fcc144aecad821861b675de9e.png"></image>
    </view>
    <view class="member_back_css" v-if="tabsCurrent == 0">
      <view class="banner_top_centent" :class="identityType == 1 ? 'member_banner_css' : ''">
        <image class="banner_css" mode="widthFix" src="https://document.dxznjy.com/course/f14fb476f0bd4fb3bdcf795f56d2f8a4.png"></image>
      </view>
      <!-- 2024-11-6 紧急修改 购买超级会员修改成购买家长会员 隐藏 -->
      <view v-if="false" class="member_card_style" @click="openMembership">
        <!--      <view v-if="identityType != 4" class="member_card_style" @click="openMembership"> -->
        <image class="member_card_image" src="https://document.dxznjy.com/course/853dc616f6464f81a3c412b68687095f.png"></image>
      </view>
      <view class="tab_css mt-12" :class="identityType == 1 ? 'member_tab_css' : ''">
        <image class="image_tab_css" @click="goUrl('/memberCenter/medalIndex')" src="https://document.dxznjy.com/course/d7dfa135d56a44579f02f8688ecd46f2.png"></image>
        <image class="image_tab_css" @click="goUrl('/memberCenter/rankingList')" src="https://document.dxznjy.com/course/92ffc1cb943041acab7ac7596cd65cfb.png"></image>
        <image class="image_tab_css" @click="tipshowMsg" src="https://document.dxznjy.com/course/9e4215524483440ab1856fc5b0740b90.png"></image>
      </view>
      <view class="tab_content_css flex-x-s flex-wrap">
        <image
          @click="goUrl('/Coursedetails/culturalType/familyCulture')"
          class="item_content_css"
          src="https://document.dxznjy.com/course/bdcd1ab5d18047e9a1267d107541a1b7.png"
        ></image>
        <image
          @click="goUrl('/Coursedetails/culturalType/readCultural')"
          class="item_content_css"
          src="https://document.dxznjy.com/course/14ac14a441ed4ba7b402fbe16571d325.png"
        ></image>
        <image
          @click="goUrl('/memberCenter/culturalType/labourCulture')"
          class="item_content_css"
          src="https://document.dxznjy.com/course/2ee418f48b38484ba9a3f56ec8e8835c.png"
        ></image>
        <image
          @click="goUrl('/memberCenter/culturalType/sportsCulture')"
          class="item_content_css"
          src="https://document.dxznjy.com/course/f41b36ac765b4e47abbd591e72333a5f.png"
        ></image>
      </view>
    </view>
    <view v-else class="release_top_css">
      <view class="plr-25 flex-a-c flex-x-b">
        <u-tabs
          :list="culturalRingList"
          :current="culturalRing"
          keyName="name"
          lineWidth="40"
          lineHeight="11"
          :activeStyle="{ color: '#333333', fontWeight: 'bold', fontSize: '28rpx' }"
          :inactiveStyle="{ color: '#5A5A5A ', transform: 'scale(1)', fontSize: '28rpx' }"
          itemStyle="padding-left:5px; padding-right: 25px; height: 34px;"
          :lineColor="`url(${lineBg}) 100% 110%`"
          @click="tabsculturalClick"
        ></u-tabs>
        <image v-if="culturalRing == 0" @click="release(0)" class="fu_bu_css" src="https://document.dxznjy.com/course/67c685ce5bfc47d1b836c3555c3cae51.png"></image>
      </view>
      <view v-if="culturalRing == 0">
        <view v-for="item in releaseSelecte" :key="item.id" class="mt-40 pb-20 plr-32 pt-30 border_color">
          <releaseItem
            releaseType="1"
            @addCollect="addCollect"
            @addThumb="addThumb"
            @getRelease="getRelease"
            @showImage="showImage"
            :releaseStyle="releaseStyle"
            :releaseInfo="item"
          ></releaseItem>
        </view>
      </view>
      <view v-if="culturalRing == 1" class="qusetion_content_css plr-32">
        <view v-for="item in problemList" :key="item.id" class="mb-25">
          <problemItem problemType="1" @addThumb="addThumb" @goAnswer="goAnswer" :problemInfo="item" :problemStyle="problemStyle"></problemItem>
        </view>
      </view>
      <view v-if="culturalRing == 2" class="plr-32">
        <view v-for="item in collectList" :key="item.id" class="mt-24">
          <releaseItem releaseType="3" @getRelease="getRelease" @showImage="showImage" :releaseStyle="releaseStyle" :releaseInfo="item"></releaseItem>
        </view>
      </view>
      <view
        class="pt-28"
        v-if="(culturalRing == 0 && releaseSelecte.length == 0) || (culturalRing == 1 && problemList.length == 0) || (culturalRing == 2 && collectList.length == 0)"
      >
        <emptyPage></emptyPage>
      </view>
      <view class="bottom_button_css" v-if="tabsCurrent == 1 && culturalRing == 1">
        <button class="butoton_content_css c-ff lh-40 f-28" @click="release(2)">我也要提问</button>
      </view>
      <view class="release_top_bottom"></view>
    </view>
    <!-- 发布or提问 -->
    <tipsContentPopup ref="tipsContentPopupRefs" :tipsType="tipsType"></tipsContentPopup>
    <!-- <suspensionBtn :fixedText="fixedText"></suspensionBtn> -->
    <growthPopup ref="growthPopupRefs"></growthPopup>
  </view>
</template>
<script>
  // https://document.dxznjy.com/course/056e0594268b4a0cb4747d9db9c772fa.png
  import releaseItem from './components/releaseItem.vue';
  import problemItem from './components/problemItem.vue';
  import suspensionBtn from './components/suspensionBtn.vue';
  import growthPopup from './components/growthPopup.vue';
  import tipsContentPopup from './components/tipsContentPopup.vue';
  import emptyPage from './components/emptyPage.vue';
  const { $navigationTo, $getSceneData, $showError, $showMsg, $http } = require('@/util/methods.js');
  export default {
    components: {
      releaseItem,
      problemItem,
      suspensionBtn,
      growthPopup,
      tipsContentPopup,
      emptyPage
    },
    data() {
      return {
        tabsCurrent: 0,
        categoryList: [
          { name: '文化中心', key: 2 },
          { name: '文化圈', key: 1 }
        ],
        lineBg: 'https://document.dxznjy.com/course/01c96465d9ea46f69aa97465b1201aef.png',
        culturalRingList: [
          { name: '精选', key: 1 },
          { name: '问答', key: 2 },
          { name: '喜欢', key: 3 }
        ],
        culturalRing: 0,
        tipsType: 0,
        fixedText: '我也要提问',
        //精选tabs
        releaseSelecte: [],
        releaseStyle: {
          leftImageWidth: '82rpx'
        },
        addThumbShow: false,
        addCollectFalse: false,
        //问题tabs
        problemList: [],
        problemStyle: {
          leftImageWidth: '82rpx'
        },
        infoLists: {},
        identityType: uni.getStorageSync('identityType'),
        collectList: [],
        growthEventList: [],
        getCultureTypeList: [],
        page: 1,
        showImageType: false
      };
    },
    onReachBottom() {
      if (this.page * 10 >= this.infoLists.totalItems) {
        return false;
      }
      let codeType = '';
      if (this.culturalRing == 1) {
        codeType = this.getCultureCode('问答', this.getCultureTypeList);
      }
      if (this.culturalRing == 2) {
        this.getCollectList(true, ++this.page);
      } else {
        this.getCultureCircle(codeType, true, ++this.page);
      }
    },
    onLoad(e) {
      this.tabsCurrent = e.tabsCurrent;
      this.culturalRing = e.culturalRing || 0;
      this.getCultureType(e);
    },
    onShow() {
      this.page = 1;
      this.growthEventType();
      if (this.tabsCurrent == 1) {
        if (this.showImageType) {
          this.showImageType = false;
          return;
        }
        this.tabsculturalClick({ index: this.culturalRing, key: -1 });
      }
      if (uni.getStorageSync('showType') == 'addProblem') {
        uni.setStorageSync('showType', '');
        this.getGrowthValue('每日发布一个提问');
      }
    },
    methods: {
      goUrl(url) {
        if (!uni.getStorageSync('token')) {
          uni.navigateTo({
            url: '/Personalcenter/login/login'
          });
          return;
        }
        uni.navigateTo({
          url: url
        });
      },
      tipshowMsg() {
        $showMsg('开发中，敬请期待');
      },
      // 查询文化圈类型
      async getCultureType(e) {
        let token = uni.getStorageSync('token');
        if (!token) {
          return;
        }
        let _this = this;
        const res = await $http({
          url: 'zx/wap/CultureCircle/getCultureType',
          data: {}
        });
        if (res) {
          console.log(res, '查询文化圈类型');
          _this.getCultureTypeList = res.data;
          uni.setStorageSync('getCultureTypeList', JSON.stringify(res.data));
        }
      },
      async growthEventType(e) {
        let token = uni.getStorageSync('token');
        if (!token) {
          return;
        }
        let _this = this;
        const res = await $http({
          url: 'zx/wap/CultureCircle/growthEventType',
          data: {}
        });
        if (res) {
          this.growthEventList = res.data;
          uni.setStorageSync('growthEvent', JSON.stringify(res.data));
          this.getGrowthValue('进入文化中心板块');
        }
      },
      async getGrowthValue(text) {
        if (this.identityType != 4) {
          return;
        }
        const res = await $http({
          url: 'zx/wap/CultureCircle/getGrowthValue?eventType=' + this.getCultureCode(text, this.growthEventList) + '&userId=' + uni.getStorageSync('user_id'),
          method: 'POST',
          data: {}
        });
        if (res) {
          if (Number(res.data)) {
            this.$refs.growthPopupRefs.open(res.data);
          }
        }
      },
      // growthEventType
      getCultureCode(msg, list) {
        for (var i = 0; i < list.length; i++) {
          let item = list[i];
          if (item.msg == msg) {
            return item.code;
          }
        }
      },
      //获取全部文化圈
      async getCultureCircle(codeType, isPage, page) {
        let parm = {
          isPublic: 1,
          pageNum: page || 1,
          pageSize: 10,
          userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
        };
        if (codeType) {
          parm.topicType = codeType;
        }
        let _this = this;
        const res = await $http({
          url: 'zx/wap/CultureCircle',
          data: parm
        });
        if (res) {
          this.infoLists = res.data;
          if (this.culturalRing == 0) {
            if (isPage) {
              this.releaseSelecte = [...this.releaseSelecte, ...res.data.data];
            } else {
              this.releaseSelecte = res.data.data || [];
            }
          } else if (this.culturalRing == 1) {
            if (isPage && isPage != -1) {
              this.problemList = [...this.problemList, ...res.data.data];
            } else {
              this.problemList = res.data.data || [];
            }
            if (isPage == -1) {
              this.$nextTick(() => {
                _this.getElementScollTop();
              });
            }
          }
          this.addThumbShow = false;
          this.addCollectFalse = false;
          uni.hideLoading();
        }
      },
      getElementScollTop() {
        const query = uni.createSelectorQuery();
        query
          .select('.member_center_css')
          .boundingClientRect((data) => {
            console.log(data, 'data');
            let pageScrollTop = Math.round(data.top);
            uni.pageScrollTo({
              scrollTop: pageScrollTop, //滚动的距离
              duration: 0 //过渡时间
            });
          })
          .exec();
      },
      tabsClick(e) {
        if (!uni.getStorageSync('token')) {
          uni.navigateTo({
            url: '/Personalcenter/login/login'
          });
          return;
        }
        this.tabsCurrent = e.index;
        if (this.tabsCurrent == 1) {
          this.releaseSelecte = [];
          this.problemList = [];
          this.tabsculturalClick({ index: e.culturalRing || this.culturalRing });
        }
      },
      tabsculturalClick(e) {
        console.log(e);
        if (e.index != this.culturalRing) {
          this.releaseSelecte = [];
          this.problemList = [];
        }
        this.culturalRing = e.index;
        let codeType = '';
        uni.showLoading({
          title: '加载中...',
          mask: true
        });

        if (this.culturalRing == 0) {
          this.getCultureCircle(codeType);
        } else if (this.culturalRing == 1) {
          codeType = this.getCultureCode('问答', this.getCultureTypeList);
          this.getCultureCircle(codeType, e.key);
        } else if (this.culturalRing == 2) {
          this.getCollectList();
        }
      },
      async getCollectList(isPage, page) {
        let _this = this;
        const res = await $http({
          url: 'zx/wap/CultureCircle/getCollect',
          data: {
            pageNum: page || 1,
            pageSize: 10,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        if (res) {
          this.addThumbShow = false;
          this.addCollectFalse = false;
          this.infoLists = res.data;
          if (isPage) {
            this.collectList = [...this.collectList, ...res.data.data];
          } else {
            this.collectList = res.data.data || [];
          }
          uni.hideLoading();
        }
      },
      openMembership() {
        $navigationTo('Personalcenter/my/nomyEquity?type=2');
      },
      release(key) {
        if (this.identityType == 4) {
          if (key == 0) {
            uni.navigateTo({
              url: '/memberCenter/releaseIndex?codeType=' + this.getCultureCode('文化圈', this.getCultureTypeList)
            });
          } else {
            uni.navigateTo({
              url: '/memberCenter/addProblem?codeType=' + this.getCultureCode('问答', this.getCultureTypeList)
            });
          }
        } else {
          this.$refs.tipsContentPopupRefs.open();
          this.tipsType = 3;
        }
      },
      //点赞
      async addThumb(info, key) {
        if (this.addThumbShow) {
          return;
        }
        let url = '';
        if (key == 1) {
          url = 'zx/wap/CultureCircle/cancelThumb';
        } else {
          url = 'zx/wap/CultureCircle/thumb';
        }
        this.addThumbShow = true;
        const res = await $http({
          url: url,
          method: 'POST',
          data: {
            topicId: info.id,
            topicType: this.getCultureCode('文化圈', this.getCultureTypeList),
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        if (res) {
          this.tabsculturalClick({ index: this.culturalRing });
          if (key != 1) {
            this.getGrowthValue('每日点赞一篇动态');
          }
        }
      },
      //收藏
      async addCollect(info, key) {
        if (this.addCollectFalse) {
          return;
        }
        this.addCollectFalse = true;
        let url = '';
        if (key == 1) {
          url = 'zx/wap/CultureCircle/cancelCollect';
        } else {
          url = 'zx/wap/CultureCircle/collect';
        }
        const res = await $http({
          url: url + '?topicId=' + info.id + '&userId=' + uni.getStorageSync('user_id'),
          method: 'POST',
          data: {}
        });
        if (res) {
          this.tabsculturalClick({ index: this.culturalRing });
        }
      },
      showImage(list, index) {
        this.showImageType = true;
        let photoList = list.map((item) => {
          return item.url;
        });
        uni.previewImage({
          urls: photoList,
          current: index,
          indicator: 'default'
        });
      },
      getRelease(info) {
        if (this.identityType == 4) {
          uni.setStorageSync('releaseInfo', JSON.stringify(info));
          uni.navigateTo({
            url: '/memberCenter/detail/releaseDetail'
          });
        } else {
          this.$refs.tipsContentPopupRefs.open();
          this.tipsType = 3;
        }
      },
      goAnswer(info) {
        uni.setStorageSync('problemInfo', JSON.stringify(info));
        uni.navigateTo({
          url: '/memberCenter/detail/problemDetail'
        });
      },
      goMyHoem() {
        if (!uni.getStorageSync('token')) {
          uni.navigateTo({
            url: '/Personalcenter/login/login'
          });
          return;
        }
        uni.navigateTo({
          url: '/memberCenter/homepage'
        });
      }
    }
  };
</script>
<style lang="scss" scoped>
  .member_center_css {
    word-break: break-all;
    .top_home_right {
      width: 48rpx;
      height: 48rpx;
    }
    .release_top_css {
      border: 1px solid #ecf0f4;
      background-color: #fff;
      min-height: calc(100vh - 100rpx);
      overflow-x: hidden;
      overflow-y: scroll;
      padding-bottom: 180rpx;
      .release_top_bottom {
        height: 60rpx;
      }
      .fu_bu_css {
        width: 64rpx;
        height: 64rpx;
      }
      .qusetion_content_css {
        background-color: #f8f8f8;
        border-radius: 40rpx 40rpx 0rpx 0rpx;
        padding-top: 26rpx;
        margin-top: 24rpx;
        overflow-y: scroll;
        position: relative;
      }
    }
    .member_tab_css {
      padding-top: 20rpx !important;
    }
    .tab_css {
      display: flex;
      justify-content: space-around;
      padding: 0 14rpx;
      .image_tab_css {
        width: 216rpx;
        height: 144rpx;
      }
    }
    .tabs_item_css {
      margin-left: 2rpx;
      .active_tabs_css {
        font-weight: bold;
        color: #333;
        .tabs_name {
          z-index: 1;
          font-size: 32rpx;
        }
        .line_tabs_css {
          width: 100%;
          height: 8rpx;
          border-radius: 8rpx;
          background-color: #28a781;
          margin-top: -8rpx;
          z-index: 0;
        }
      }
      .tabs_name {
        font-size: 28rpx;
      }
    }
    .tabs_item_css:first-child {
      padding-right: 54rpx;
      position: relative;
    }
    .tabs_item_css:first-child::after {
      content: '';
      display: inline-block;
      height: 28rpx;
      border-right: 1rpx solid #d7dde3;
      position: absolute;
      right: 0;
      top: 10rpx;
    }
    .tabs_item_css:last-child {
      padding-left: 54rpx;
    }
    .member_back_css {
      background-image: url('https://document.dxznjy.com/course/7d9f7e6e8381464ebccf3031b306b525.png');
      background-repeat: no-repeat;
      background-size: 100%;
      height: calc(100vh - 120rpx);
      .member_card_style {
        position: relative;
        height: 240rpx;
      }
      .member_card_image {
        width: 686rpx;
        height: 255rpx;
        position: absolute;
        top: -32rpx;
        left: 32rpx;
      }
    }
    .banner_top_centent {
      .banner_css {
        width: 750rpx;
      }
    }
    .member_banner_css {
      height: 450rpx;
      overflow: hidden;
    }
    .border_color {
      border-bottom: 1rpx solid #ecf0f4;
    }
  }
  // .release_popup_css{
  //     height: calc(100vh - 300rpx);
  //     .release_content_css{
  // 	    height: calc(100vh - 500rpx);
  //     }
  // }
  // .problem_content_css{
  //     max-height: calc(100vh - 300rpx);
  // }
  .button_input_css {
    display: flex;
    justify-content: flex-start;
    .input_css {
      width: 560rpx;
    }
  }
  .tab_content_css {
    padding: 0 24rpx;
    margin-top: 36rpx;
    .item_content_css {
      width: 348rpx;
      height: 220rpx;
    }
  }
  .bottom_button_css {
    position: fixed;
    left: 0rpx;
    bottom: 40rpx;
    display: flex;
    justify-content: center;
    width: 750rpx;
    .butoton_content_css {
      width: 360rpx;
      height: 96rpx;
      background: #339378;
      box-shadow: 0rpx 4rpx 8rpx 0rpx #36a586;
      border-radius: 48rpx;
      text-align: center;
      line-height: 90rpx;
      margin: auto;
    }
  }
</style>
