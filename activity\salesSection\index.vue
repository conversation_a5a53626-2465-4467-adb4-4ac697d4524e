<!-- 鼎英语销课节活动页 -->
<template>
  <view class="act-content">
    <image class="wh100" src="https://document.dxznjy.com/course/2b83324c26ee43c296d5258ddad0b8ce.jpg" mode="widthFix" />
    <view class="foot-content">
      <view class="btn-apply" @click="handleApply">
        <image src="https://document.dxznjy.com/course/760e777fa8ac4242a76e83fa931258cc.png" style="width: 100%; height: 100%"></image>
      </view>
      <view class="btn-rank" @click="handleGoRank">
        <image src="https://document.dxznjy.com/course/9c4aef7bcbfb4bf1bdfb8f3064d2fd48.png" style="width: 100%; height: 100%"></image>
      </view>
    </view>
    <view class="footer">
      <button class="join" open-type="share"></button>
    </view>

    <!-- 选择学员弹窗 -->
    <uni-popup ref="popupChooseStudent" :mask-click="false" type="center" @change="change">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold pb-10">选择学员</view>
            <view
              class="dialogContent"
              @click="chooseStudentList(item, index)"
              v-for="(item, index) in arrayStudent"
              :key="index"
              :class="isActive == index ? 'addclass' : 'not-selected'"
            >
              {{ item.realName + '（' + item.studentCode + '）' }}
            </view>
            <view class="mask-footer">
              <button class="confirm-button" @click="confirmStudent()">确定</button>
              <button class="cancel-button" @click="closeDialog">取消</button>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <uni-popup ref="tipsRef" :mask-click="false" type="center" @change="change">
      <view class="tipsBg">
        <view class="tipsContent">
          <view class="tipsText" :style="{ paddingTop: tipsType == 0 ? '328rpx' : '228rpx' }">您已报名成功！！！</view>
          <view class="tipsBtn" @click="handleClose"></view>

          <view class="content" v-if="tipsType == 1">
            您的鼎英语正式课数量不足
            <text class="number">8</text>
            课时，请您联系您 的推荐人{{ referrerName }}帮您购买，如联系不上推荐人可联系官方客服：
            <text class="number" @click="handleCallPhone">************</text>
          </view>
          <view class="content" v-if="tipsType == 2">
            您的鼎英语正式课数量不足
            <text class="number">8</text>
            课时，请您联系官方客服：
            <text class="number" @click="handleCallPhone">************</text>
            帮您购买鼎英语正式课。
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  const { $http, $navigationTo } = require('@/util/methods.js');
  export default {
    data() {
      return {
        arrayStudent: [],
        isActive: -1,
        show: false,
        studentInfo: {},
        tipsType: 0,
        referrerName: ''
      };
    },

    methods: {
      // 400电话
      handleCallPhone() {
        uni.makePhoneCall({
          phoneNumber: '************',
          success: (res) => {
            console.log(res);
          },
          fail: (err) => {
            console.log(err);
          }
        });
      },
      change(e) {
        this.show = e.show;
      },
      closeDialog() {
        this.$refs.popupChooseStudent.close();
      },
      handleClose() {
        this.$refs.tipsRef.close();
      },
      async confirmStudent() {
        let mobile = uni.getStorageSync('phone');
        const res = await $http({
          url: 'zx/wap/dyy/student/apply',
          data: {
            mobile,
            studentCode: this.studentInfo.studentCode,
            studentName: this.studentInfo.realName,
            remainHours: this.studentInfo.haveDeliverNum
          }
        });
        if (res) {
          this.closeDialog();
          this.showTips();
        }
      },
      showTips() {
        // 不足8课时
        if (this.studentInfo.haveDeliverNum < 8 || this.studentInfo.haveDeliverNum == undefined) {
          if (this.referrerName) {
            // 有推荐人
            this.tipsType = 1;
          } else {
            this.tipsType = 2;
          }
          this.$refs.tipsRef.open();
        } else {
          this.tipsType = 0;
          this.$refs.tipsRef.open();
        }
      },
      // 获取推荐人信息
      async getReferrerInfo() {
        const res = await $http({
          url: `zx/wap/dyy/student/referrerUser?mobile=${uni.getStorageSync('phone')}`,
          showLoading: true
        });
        if (res) {
          console.log('🚀 ~ getReferrerInfo ~ res:', res);
          this.referrerName = res.data;
        }
      },
      // 报名成功
      applySuccess() {
        this.$refs.popupChooseStudent.close();
      },
      async handleApply() {
        getApp().sensors.track('activityApplyClick', {
          name: '我要报名'
        });
        // 报名
        const token = uni.getStorageSync('token');
        if (!token) {
          uni.navigateTo({
            url: `/Personalcenter/login/login`
          });
          return;
        }
        if (this.arrayStudent.length > 1) {
          // 选择学员
          this.isActive = 0;
          this.studentInfo = this.arrayStudent[0];
          this.referrerName = this.studentInfo.merchantRealName;
          this.$refs.popupChooseStudent.open();
        } else {
          let mobile = uni.getStorageSync('phone');
          if (this.arrayStudent.length == 1) {
            this.studentInfo = this.arrayStudent[0];
            this.referrerName = this.studentInfo.merchantRealName;
          }
          let params = { mobile };
          if (this.studentInfo.studentCode) {
            params.studentCode = this.studentInfo.studentCode;
            params.studentName = this.studentInfo.realName;
            params.remainHours = this.studentInfo.haveDeliverNum;
          }

          const res = await $http({
            url: 'zx/wap/dyy/student/apply',
            data: params
          });
          if (res) {
            this.showTips();
          }
        }
      },
      handleGoRank() {
        const token = uni.getStorageSync('token');
        if (!token) {
          uni.navigateTo({
            url: `/Personalcenter/login/login`
          });
          return;
        }

        uni.navigateTo({
          url: `/activity/salesSection/rankList`
        });
      },
      //点击选择学员
      chooseStudentList(item, index) {
        this.isActive = index;
        this.studentInfo = { ...item };
      },
      // 获取学员列表
      async getStudentList() {
        const res = await $http({
          url: 'znyy/review/query/my/student/hours',
          showLoading: true
        });

        if (res) {
          this.arrayStudent = res.data;
        }
      }
    },

    onShareAppMessage() {
      getApp().sensors.track('activityShareClick', {
        name: '分享'
      });
      return {
        title: '叮，你的好友敲了你一下，赶紧过来看看',
        imageUrl: 'https://document.dxznjy.com/course/cc6d6e023c7d4317af6d98bf83719d58.png',
        //如果有参数的情况可以写path
        // path: `/activity/salesSection/index?shareUserCode=${this.userInfo.userCode}&activityId=${this.activityId}&bannerId=${this.bannerId}`
        path: `/activity/salesSection/index`
      };
    },

    async onShow() {
      const token = uni.getStorageSync('token');
      if (token) {
        await this.getStudentList();
        // await this.getReferrerInfo();
      }
    },

    onLoad(options) {
      //埋点-活动详情页
      getApp().sensors.track('$MPViewScreen', {
        pageName: '活动详情页'
      });
    }
  };
</script>

<style lang="scss" scoped>
  .act-content {
    width: 100%;
    image {
      display: block;
    }
  }
  .footer {
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    bottom: 0;
    width: 100%;
    height: 140rpx;
    z-index: 9;
    justify-content: center;
    background: rgba(255, 255, 255, 0.5);
    .join {
      width: 686rpx;
      height: 104rpx;
      background: transparent;
      background-image: url('https://document.dxznjy.com/course/a7b59abd1d7c4896822affc650eb589a.png');
      background-size: 100% 100%;
    }
  }
  .foot-content {
    position: absolute;
    top: 1117rpx;
    left: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 62rpx;
    box-sizing: border-box;
    width: 100%;
    height: 95rpx;
    z-index: 9;

    .btn-apply {
      width: 334rpx;
      height: 95rpx;
    }
    .btn-rank {
      width: 254rpx;
      height: 95rpx;
    }
  }

  .dialogContent {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }

  /* 弹窗样式 */
  .dialogBG {
    width: 100%;
    /* height: 100%; */
  }

  .tipsBg {
    width: 686rpx;
    height: 734rpx;
    padding: 0 18rpx 20rpx;
    background: url('https://document.dxznjy.com/course/a050fbe3f56b45e9a91b5f013d271290.png') no-repeat;
    background-size: 100% 100%;

    .tipsText {
      text-align: center;
      padding-top: 328rpx;
      font-weight: normal;
      font-size: 52rpx;
      line-height: 66rpx;
      color: #0aa169;
      margin-top: 0 auto;
      padding-top: 40rpx;
    }

    .tipsBtn {
      margin: 40rpx auto 0;
      width: 540rpx;
      height: 110rpx;
      background: url('https://document.dxznjy.com/course/cff9f1fc353b4cd6bb4a1b628d502e39.png') no-repeat;
      background-size: 100% 100%;
    }

    .content {
      box-sizing: border-box;
      padding: 28rpx 20rpx 30rpx 28rpx;
      margin-top: 44rpx;
      width: 648rpx;
      height: 202rpx;
      background: #ecf7f1;
      border-radius: 22rpx;
      font-size: 28rpx;
      color: #929292;
      line-height: 48rpx;

      .number {
        font-size: 32rpx;
        margin: 0 8rpx;
        color: #0aa169;
      }
    }
  }

  /* 21天结束复习弹窗样式 */
  .reviewCard_box {
    width: 670rpx;
    /* height: 560rpx; */
    position: relative;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }

  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 145rpx;
    z-index: -1;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }

  .addclass {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #2e896f;
    border: 1px solid #2e896f;
    border-radius: 35rpx;
  }

  .not-selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #000;
    border: 1px solid #c8c8c8;
    border-radius: 35rpx;
  }

  .mask-footer {
    margin-top: 20px;
    display: flex;
    justify-content: space-around;
  }

  .mask-footer button {
    width: 250rpx;
    height: 80rpx;
    font-size: 30rpx;
    border-radius: 45rpx;
  }

  .confirm-button {
    display: flex;
    justify-content: space-around;
    align-items: center;
    /* 文本垂直居中对齐 */
    background: #2e896f;
    color: #ffffff !important;
  }

  .cancel-button {
    display: flex;
    justify-content: space-around;
    align-items: center;
    /* 文本垂直居中对齐 */
    color: #2e896f !important;
    border: 1rpx solid #2e896f !important;
  }
</style>
