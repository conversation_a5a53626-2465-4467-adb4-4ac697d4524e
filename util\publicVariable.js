import Config from "@/util/config.js"
module.exports = {
  host: Config.DXHost,
  // host: "http://************:1001/course/",
  // host: "https://dx.iyoutui.cn/shop/",
  upload_host: "https://ceshi.ngrok.dxznjy.com/zx/course/common/uploadFile",
  upload_host: Config.DXHost+"course/common/uploadFile",
  checkLogin: false,
  checkPhone: false,
  wxapp_id: "", 
  tabBarLinks: [
    'pages/index/index',
    'pages/home/<USER>/index',
    'pages/home/<USER>/index',
	  // 'pages/selection/index',
    'pages/selectCourse/selectCourse'
  ],
  throttle: true,
  QQMapWXKey: "KUTBZ-7MFCP-5RUDQ-VXJ7V-CUIIE-46B65",
  DeliveryTypeEnum : {
    EXPRESS: {
      name: '快递配送',
      value: 10
    },
    EXTRACT: {
      name: '上门自提',
      value: 20
    },
    DISTRIBUTE: {
      name: '商家配送',
      value: 30
    }
  },
  PayTypeEnum :{
    BALANCE: {
      name: '余额支付',
      value: 10
    },
    WECHAT: {
      name: '微信支付',
      value: 20
    },
    SCORE: {
      name: '积分兑换',
      value: 30
    }
  },
  comment_count:[
    {
      type: '-1',
      title: '全部',
      cate: "all",
    },{
      type: '10',
      title: '好评',
      cate: "good",
    },{
      type: '20',
      title: '中评',
      cate: "soso",
    },{
      type: '30',
      title: '差评',
      cate: "bad",
    },
  ],
  color_arr: ['#37B6FF','#FEAB1E','#E5460A','#70BB2B'],
}