<template>
  <view class="search-page">
    <view class="status_bar" :style="'height:' + barTop + 'px'">
      <view class="top_view"></view>
    </view>
    <!-- barTop -->
    <!-- <view class="navigationBar_css">
      <u-icon class="arrow-left" @tap="goBack" name="arrow-left" color="#575757" size="38"></u-icon>
      <text class="navigation_title">搜索</text>
    </view> -->
    <view class="search-css">
      <view class="flexbox search_style">
        <!-- <u--input
          customStyle="margin-left:10rpx;height:60rpx"
          @clear="clearInput"
          @input="getValue"
          style="margin-left: 10rpx"
          border="none"
          fontSize="28rpx"
          placeholder="搜索你想要的"
          v-model="searchValue"
          clearable
        ></u--input> -->
        <u-icon @tap="goBack" name="arrow-left" color="#575757" size="38"></u-icon>
        <u-search 
          placeholder="搜索你想要的" 
          v-model="searchValue" 
          :showAction="false"
          bgColor="#F4F4F6"
          height="60"
          searchIconSize="40"
          color="#666666"
          margin="0 24rpx 0 16rpx"
          clearabled
          placeholderColor="#B3B3B3"
          @clear="clearInput"
          @change="getValue"
        >
        </u-search>
        <view @tap.stop="searchClick" class="search_button">搜索</view>
      </view>
    </view>
    <view class="record_main" v-if="!(searchValue && searchShow)">
      <view class="record_style" v-if="this.historyList.list.length > 0">
        <view class="flexbox f-28">
          <view class="fontWeight history_title f-28">历史记录</view>
          <u-icon @tap="delectRecord" class="arrow-left" name="trash" size="32"></u-icon>
        </view>
        <view class="record_list">
          <view class="record_item f-28" @tap="getHistory(item)" v-for="(item, index) in historyList.list" :key="index">
            {{ item }}
          </view>
        </view>
      </view>
      <view class="record_style">
        <!-- 热门搜索 -->
        <view class="fontWeight history_title f-28" style="display: flex;">
          <image src="https://document.dxznjy.com/course/455e345585684e00aae13c724f19bb62.png" mode="scaleToFill" style="height: 28rpx;width: 118rpx"></image>
          <!-- <view style="color: #009B55;font-weight: bold">热门</view>
          <view style="font-weight: bold">搜索</view> -->
        </view>
        <view class="record_list">
          <view class="record_item f-28" style="background-color: #F6F6F6;display: flex;align-items: center;" @tap="getRecommended(item)" v-for="(item, index) in recommendedList" :key="index">
            <span class="record_name">{{ item.searchName }}</span>
            <image v-if="item.recommendationType == 1" class="image_css_hot" mode="widthFix" src="https://document.dxznjy.com/course/bc74224b9cb149639ad75e157ef99bb9.png"></image>
            <image v-if="item.recommendationType == 4" class="image_css" mode="widthFix" src="https://document.dxznjy.com/course/37ded001196c4526816dc5e4c1af6e7f.png"></image>
            <image v-if="item.recommendationType == 3" class="image_css" mode="widthFix" src="https://document.dxznjy.com/course/db89f35dbe054d66a1c30af8aa995492.png"></image>
          </view>
        </view>
      </view>
    </view>
    <view v-if="searchValue && searchShow">
      <view v-if="courseData.length > 0" class="p-30">
        <u-tabs
          :list="userList"
          lineWidth="36"
          lineHeight="4"
          lineColor="#009B55"
          :activeStyle="{ color: '#555555', fontSize: '28rpx', fontWeight: '500' }"
          :inactiveStyle="{
            color: '#999999',
            fontSize: '28rpx',
            fontWeight: '400'
          }"
          itemStyle="padding-left: 5px; padding-right: 25px; height: 30px;"
          @click="tabsClick"
        ></u-tabs>
        <view class="waterfall-box mt-25">
          <course-list 
            v-for="(item, index) in courseData" 
            :key="index" 
            :item="item" 
            :index="index"
						:width="'326rpx'"
						@click="skintap(item)"
          >
          </course-list>
          <!-- <view style="width: 50%">
            <helang-waterfall
              v-for="(item, index) in leftList"
              :key="index"
              :item="item"
              tag="left"
              :index="index"
              @shareVip="shareVip"
              @height="onHeight"
              @click="skintap(item)"
            ></helang-waterfall>
          </view>
          <view style="margin-left: 24rpx; width: 50%">
            <helang-waterfall
              v-for="(item, index) in rightList"
              :key="index"
              :item="item"
              @height="onHeight"
              @shareVip="shareVip"
              @click="skintap(item)"
              tag="right"
              :index="index"
            ></helang-waterfall>
          </view> -->
        </view>
      </view>
      <view v-else class="search_result">
        <view style="text-align: center;">
          <image class="img-none" mode="widthFix" src="https://document.dxznjy.com/course/8ce539aed0984c25bbaffa4e5294f873.png"></image>
          <view class="f-28 mb-12">抱歉，没有找到相关内容</view>
          <view class="f-28">您可以试试搜索学科，例如“物理”</view>
        </view>
      </view>
      <!-- 分享弹窗 -->
      <!-- shareContent -->
      <sharePopup ref="sharePopupRefs"></sharePopup>
    </view>
  </view>
</template>

<script>
  import courseList from '@/components/course-list/course-list.vue';
  import Config from '@/util/config.js';
  import sharePopup from '@/components/sharePopup.vue';
  const { $navigationTo, $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        imgHost: getApp().globalData.imgsomeHost,
        searchValue: '',
        historyList: {
          key: '',
          list: []
        },
        recommendedList: [],
        searchShow: false,
        leftHeight: 0,
        rightHeight: 0,
        userList: [
          {
            name: '销量',
            orderBy: 'goodsSales'
          },
          {
            name: '价格',
            orderBy: 'goodsOriginalPrice'
          }
        ],
        orderBy: ' ',
        lineBg: 'https://document.dxznjy.com/course/01c96465d9ea46f69aa97465b1201aef.png',
        leftList: [],
        rightList: [],
        page: 1,
        barHeight: 0,
        barTop: 0,
        // 分享内容
        shareContent: {},
        infoLists: {},
        no_more: false,
        courseData: [],
      };
    },
    components: {
      sharePopup,
      courseList
    },
    onReachBottom() {
      if (this.page * 20 >= this.infoLists.totalItems) {
        this.no_more = true;
        return false;
      }
      this.searchInfo(true, ++this.page);
    },
    onShareAppMessage(res) {
      let userId = uni.getStorageSync('user_id');
      let url = `/pages/beingShared/index?scene=${uni.getStorageSync('user_id')}&type=${this.shareContent.type}&id=${this.shareContent.id}`;
      console.log(url);
      if (res.from == 'menu') {
        return {
          title: '叮，你的好友敲了你一下，赶紧过来看看',
          path: `/pages/index/index`
        };
      } else {
        setTimeout(() => {
          this.$refs.sharePopup.close();
        }, 2000);
        if (!uni.getStorageSync('token')) {
          return {
            title: this.shareContent.title || '叮，你的好友敲了你一下，赶紧过来看看',
            imageUrl: this.shareContent.imgurl,
            // imageUrl: "https://document.dxznjy.com/dxSelect/superman_share.jpg",
            path: `Coursedetails/productDetils?id=${this.shareContent.id}`
          };
        }
        return {
          title: this.shareContent.title || '叮，你的好友敲了你一下，赶紧过来看看',
          imageUrl: this.shareContent.imgurl,
          // imageUrl: "https://document.dxznjy.com/dxSelect/superman_share.jpg",
          path: `/pages/beingShared/index?scene=${uni.getStorageSync('user_id')}&type=${this.shareContent.type}&id=${this.shareContent.id}`
        };
      }
    },
    onLoad() {
      let that = this;
      uni.getSystemInfo({
        //调用uni-app接口获取屏幕高度
        success(res) {
          that.barHeight = res.statusBarHeight;
          let borHeightInfo = uni.getMenuButtonBoundingClientRect();
          that.barTop = borHeightInfo.height + (borHeightInfo.top - that.barHeight) * 2;
        }
      });
    },
    onShow() {
      this.orderBy = 'goodsSales';
      this.getRecommendation();
      // data&&data.length>0&&JSON.parse(data).key==uni.getStorageSync('nickName');
      if (uni.getStorageSync('historyList') && JSON.parse(uni.getStorageSync('historyList')).key == uni.getStorageSync('nickName')) {
        this.historyList = JSON.parse(uni.getStorageSync('historyList'));
      } else {
        this.historyList.key = uni.getStorageSync('nickName');
      }
      //  console.log(this.historyList)
    },
    onUnload() {
      uni.setStorageSync('historyList', JSON.stringify(this.historyList));
    },
    methods: {
      shareVip(type, item) {
        console.log(222222);
        // if (!uni.getStorageSync('token')) {
        //   uni.navigateTo({
        //     url: '/Personalcenter/login/login'
        //   });
        //   return;
        // }
        this.shareContent.type = type;
        let id = '',
          imgurl = '';
        //type 1课程  2学习超人（会员） 3超人俱乐部
        if (type == '1') {
          id = item.courseId;
          imgurl = item.courseImage;
        } else {
          id = item.mealId;
          type == '2' ? (imgurl = Config.supermanShareImage) : (imgurl = Config.supermanClubShareImage);
        }
        this.shareContent.id = item.goodsId;
        if (type != 6) {
          this.shareContent.imgurl = imgurl;
        } else {
          this.shareContent.imgurl = item.goodsSharePoster;
          this.shareContent.title = item.goodsShareTextList[0] ? item.goodsShareTextList[0].shareText : null;
        }
        //
        this.$refs.sharePopupRefs.open(this.shareContent);
      },
      // /zx/operation/search/recommendation/enable/list
      async getRecommendation(page) {
        let _this = this;
        const res = await $http({
          url: 'zx/operation/search/recommendation/enable/list',
          data: {
            pageNum: page || 1,
            pageSize: 20
          }
        });
        if (res) {
          if (res.data.data.length > 0) {
            this.recommendedList = res.data.data;
          }
        }
      },
      searchClick() {
        getApp().sensors.track('resultSearchHandleClick', {
          name: this.searchValue
        });
        if (this.searchValue) {
          uni.showLoading({
            title: '搜索中'
          });
          this.searchShow = true;
          this.leftHeight = 0;
          this.rightHeight = 0;
          this.searchInfo();
        }
        if (this.searchValue && this.historyList.list.indexOf(this.searchValue) < 0) {
          if (this.historyList.list.length < 9) {
            this.historyList.list.unshift(this.searchValue);
          } else {
            this.historyList.list.splice(9, 1);
            this.historyList.list.unshift(this.searchValue);
          }
        }
      },
      goBack() {
        if (this.searchShow) {
          this.searchShow = false;
          this.searchValue = '';
        } else {
          uni.navigateBack({
            delta: 1
          });
        }
      },
      getHistory(item) {
        this.searchValue = item;
      },
      getRecommended(item) {
        if (item.redirectType == 1) {
          this.searchValue = item.searchName;
        } else if (item.redirectType == 2) {
          $navigationTo(item.redirectUrl);
        } else if (item.redirectType == 3) {
          $navigationTo('Coursedetails/productDetils?id=' + item.goodsId);
        }
      },
      skintap(item) {
        if (item.goodsType == 6) {
          getApp().sensors.track('goShopping', {
            name: '进入商城',
            refereePath: 'shoppingMall/details'
          });
          $navigationTo('shoppingMall/details?id=' + item.goodsId);
        } else {
          getApp().sensors.track('goProductDetils', {
            name: '查看商品',
            refereePath: 'Coursedetails/productDetils'
          });
          $navigationTo('Coursedetails/productDetils?id=' + item.goodsId);
        }
      },
      delectRecord() {
        this.historyList.list = [];
        uni.showToast({
          title: '已删除记录',
          icon: 'none'
        });
      },
      // 监听高度变化
      onHeight(height, tag) {
        if (tag == 'left') {
          this.leftHeight += height;
        } else {
          this.rightHeight += height;
        }
      },
      tabsClick(item) {
        this.orderBy = item.orderBy;
        this.searchInfo(false, 1);
      },
      getValue() {
        this.searchShow = false;
        this.leftList = [];
        this.rightList = [];
      },
      //清楚input
      clearInput() {
        this.searchShow = false;
        this.leftList = [];
        this.rightList = [];
      },
      async searchInfo(isPage, page) {
        let _this = this;
        const res = await $http({
          url: 'zx/wap/goods/search/list',
          data: {
            goodsName: this.searchValue,
            orderBy: this.orderBy,
            orderType: 'desc', //asc
            pageNum: page || 1,
            pageSize: 20,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        if (res) {
          if (isPage) {
            this.courseData = [...this.courseData, ...res.data.data];
          } else {
            _this.infoLists = res.data;
            this.courseData = res.data.data;
          }
        }
      }
      // async searchInfo(isPage, page) {
      //   let _this = this;
      //   const res = await $http({
      //     url: 'zx/wap/goods/search/list',
      //     data: {
      //       goodsName: this.searchValue,
      //       orderBy: this.orderBy,
      //       orderType: 'desc', //asc
      //       pageNum: page || 1,
      //       pageSize: 20,
      //       userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
      //     }
      //   });
      //   if (res) {
      //     // 初始化左右列表的数据
      //     let i = this.leftHeight > this.rightHeight ? 1 : 0;
      //     let [left, right] = [[], []];
      //     // 左右列表高度的差
      //     let differ = this.leftHeight - this.rightHeight;

      //     // 将数据源分为左右两个列表，容错差值请自行根据项目中的数据情况调节
      //     let list = res.data.data;
      //     list.forEach((item, index) => {
      //       /* 左侧高度大于右侧超过 600px 时，则前3条数据都插入到右边 */
      //       if (differ >= 600 && index < 3) {
      //         right.push(item);
      //         return;
      //       }

      //       /* 右侧高度大于左侧超过 600px 时，则前3条数据都插入到左边 */
      //       if (differ <= -600 && index < 3) {
      //         left.push(item);
      //         return;
      //       }

      //       /* 左侧高度大于右侧超过 350px 时，则前2条数据都插入到右边 */
      //       if (differ >= 350 && index < 2) {
      //         right.push(item);
      //         return;
      //       }
      //       /* 右侧高度大于左侧超过 350px 时，则前2条数据都插入到左边 */
      //       if (differ <= -350 && index < 2) {
      //         left.push(item);
      //         return;
      //       }

      //       /* 当前数据序号为偶数时，则插入到左边 */
      //       if (i % 2 == 0) {
      //         left.push(item);
      //       } else {
      //         /* 当前数据序号为奇数时，则插入到右边 */
      //         right.push(item);
      //       }
      //       i++;
      //     });
      //     if (isPage) {
      //       this.leftList = [...this.leftList, ...left];
      //       this.rightList = [...this.rightList, ...right];
      //     } else {
      //       _this.infoLists = res.data;
      //       this.leftList = left;
      //       this.rightList = right;
      //     }
      //   }
      // }
    }
  };
</script>

<style scoped lang="scss">
  .search-page {
    background-color: #f9fcff;
    min-height: 100vh;

    .navigationBar_css {
      display: flex;
      justify-content: flex-start;
      padding-left: 24rpx;

      .navigation_title {
        color: #333333;
        font-size: 36rpx;
        display: inline-block;
        margin-left: 16rpx;
        font-weight: 600;
      }
    }

    .search-css {
      margin-top: 90rpx;

      .search_style {
        width: 686rpx;
        margin-left: 32rpx;

        /deep/.u-input__content__field-wrapper__field {
          padding: 0 32rpx;
        }

        .search_button {
          width: 100rpx;
          height: 60rpx;
          border-radius: 32rpx;
          font-size: 28rpx;
          background-color: #009B55;
          text-align: center;
          line-height: 60rpx;
          color: #fff;
        }

        /deep/.input-placeholder {
          font-size: 28rpx;
          color: #969499;
        }
      }
    }

    .record_main {
      padding: 0 32rpx;
    }

    .record_style {
      margin-top: 42rpx;
      .history_title {
        color: #666666;
        font-weight: 500;
      }

      .record_list {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;

        .record_item {
          padding: 8rpx 14rpx;
          background-color: #f9fcff;
          border: 2rpx solid #EDEDED;
          border-radius: 34rpx;
          margin-top: 32rpx;
          margin-right: 24rpx;
          color: #7D7D7D;

          .image_css {
            width: 32rpx;
            height: 32rpx;
            vertical-align: middle;
          }

          .record_name {
            display: inline-block;
            margin-right: 8rpx;
            vertical-align: middle;
          }

          .image_css_hot {
            width: 28rpx;
            height: 28rpx;
            vertical-align: middle;
          }
        }

        .icon_css {
          display: inline-block;
          padding: 0 4rpx;
          background-color: #fa370e;
          margin-left: 5rpx;
          color: #fff;
        }

        /deep/.u-icon {
          display: inline-block;
          margin-left: 5rpx;
        }

        .icon_new {
          background-color: #0398ef;
        }
      }
    }

    .status_bar {
      height: var(--status-bar-height);
      width: 100%;
      background-color: #f9fcff;
    }

    .top_view {
      height: var(--status-bar-height);
      width: 100%;
      position: fixed;
      background-color: #f9fcff;
      top: 0;
      z-index: 999;
    }

    .search_result {
      color: #999999;
      margin-top: 38%;
      display: flex;
      justify-content: center;
      .img-none {
        width: 400rpx;
        height: 400rpx;
      }
    }

    .waterfall-box {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: space-between;
		  align-items: center;
    }
  }
</style>
