<template>
  <view class="partner p-30">
    <view class="top">
      <h3 class="top-title mb-25"><span class="ml-15">学员信息</span></h3>
      <div class="mb-25 c-55">学员名称：{{ info.nickName || '' }}</div>
      <div class="mb-40 c-55">学员手机号：{{ info.mobile || '' }}</div>
    </view>
    <view class="top mb-15">
      <h3 class="top-title mb-25"><span class="ml-15">合伙人信息</span></h3>
      <div class="mb-25 c-55">合伙人名称：{{ info.currentPartnerRealName || '' }}</div>
      <div class="mb-25 c-55">合伙人编码：{{ info.currentPartnerMerchantCode || '' }}</div>
      <div class="mb-40 c-55">合伙人手机号：{{ info.currentPartnerMobile || '' }}</div>
    </view>
    <view class="top mb-40">
      <h3 class="top-title mb-25"><span class="ml-15">文字描述</span></h3>
      <u--textarea v-model="appealReason" placeholder="请输入申诉内容" height="300" count maxlength="500" :autoHeight="false"></u--textarea>
    </view>
    <view class="top mb-15">
      <h3 class="top-title mb-25"><span class="ml-15">添加图片</span></h3>
      <div class="mb-25 c-55 image-upload">
        <u-upload
          :fileList="fileList"
          @afterRead="afterRead"
          @delete="deletePic"
          @oversize="oversize"
          :sizeType="compressed"
          multiple
          width="150"
          height="150"
          :maxCount="9"
          :maxSize="2097512"
        ></u-upload>
      </div>
    </view>
    <button class="submit f-30 t-c c-ff mt-40" form-type="submit" :disabled="disabledBtn" @click="submitBtn">提交</button>

    <!-- 弹窗 -->
    <u-popup ref="code" :show="showClaimModal" type="center" :showConfirmButton="false" mode="center" :safeAreaInsetBottom="false" :round="40">
      <view class="modal-content">
        <view class="close-icon" @click="closeBtn"></view>
        <image class="smile-box" src="https://document.dxznjy.com/dxSelect/752cee88-c41e-42d8-83f2-44b356050e0f.png"></image>
        <view class="show-title c-55 mt-8 f-32">提交成功！</view>
        <view class="show-desc f-28">提交成功后，会在1-3个工作日后给您返回信息，您注意查看申诉表的信息哦~</view>
      </view>
    </u-popup>
  </view>
</template>

<script>
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  import Config from '../util/config.js';

  export default {
    name: 'auditDetail',
    data() {
      return {
        auditInfo: {
          createTime: '',
          idCard: '',
          name: '',
          mobile: '', //学员手机号
          appealStatus: 1,
          applyTime: '',
          auditTime: '',
          partnerMobile: '' //合伙人手机号
        },
        info: {
          nickName: '', // 学员姓名
          mobile: '', // 学员手机号
          appealId: '', // 申诉id
          currentPartnerMobile: '', //合伙人手机号
          currentPartnerRealName: '', //当前合伙人名称
          currentPartnerMerchantCode: '' //合伙人编码
        },
        appealReason: '', // 输入框的值
        fileList: [],
        appealId: '', // 申诉id
        image: [],
        showClaimModal: false, // 是否显示弹窗
        submitLoading: false, // 提交loading
        disabledBtn: false // 提交loading
      };
    },
    onLoad(options) {
      Object.assign(this.info, JSON.parse(options.info) || {});
      console.log(this.info, 'this.info=======================');
      this.appealId = this.info.appealId || ''; // 申诉id
    },
    onShow() {},
    methods: {
      /**
       * 关闭 Modal 并返回上一页
       */
      closeBtn() {
        this.showClaimModal = false;
        uni.navigateBack({
          delta: 1
        });
      },

      /**
       * 提交申诉
       * @description
       *   1. 点击提交按钮
       *   2. 上传图片
       *   3. 发送申诉请求
       *   4. 申诉成功则关闭 Modal
       *   5. 申诉失败则提示
       */
      async submitBtn() {
        console.log(this.image, 'this.image');
        if (this.appealReason?.length === 0) {
          uni.showToast({
            title: '请输入申诉内容',
            icon: 'none'
          });
          return;
        } else if (this.image?.length === 0) {
          uni.showToast({
            title: '请上传图片',
            icon: 'none'
          });
          return;
        }
        const params = {
          appealReason: this.appealReason,
          evidenceImages: this.image,
          userId: this.info.userId
        };
        console.log('🚀 ~ submitBtn ~ params.this.image:', this.image);
        console.log('🚀 ~ submitBtn ~ params:', params);
        // return;

        if (this.submitLoading) {
          return;
        }

        this.submitLoading = true;
        await httpUser
          .post('zx/wap/claim/appeal/submit', params)
          .then((res) => {
            if (res.data.code === 20000) {
              this.showClaimModal = true;
              this.image = [];
              this.appealReason = '';
              this.submitLoading = false;
            } else if (res.data.code === 50003) {
              uni.showModal({
                title: '温馨提示',
                content: res.message,
                showCancel: false
              });
              this.submitLoading = false;
            }
          })
          .catch((err) => {
            this.submitLoading = false;
            console.log('提交失败:', err);
          });
      },

      // 新增图片
      async afterRead(event) {
        try {
          this.disabledBtn = true;
          this.showLoading();
          // 当设置 multiple 为 true 时, file 为数组格式，否则为对象格式
          let lists = [].concat(event.file);
          console.log('🚀 ~ afterRead ~ lists:', lists);
          let fileListLen = this[`fileList${event.name}`].length;
          console.log('🚀 ~ afterRead ~ fileListLen:', fileListLen);
          lists.map((item) => {
            this[`fileList${event.name}`].push({
              ...item,
              appealStatus: 'uploading',
              message: '上传中'
            });
          });
          for (let i = 0; i < lists.length; i++) {
            console.log(lists[i], 'lists[i]');
            const result = await this.uploadFilePromise(lists[i].url);
            // console.log('🚀 ~ afterRead ~ result:', result);
            let item = this[`fileList${event.name}`][fileListLen];
            this[`fileList${event.name}`].splice(
              fileListLen,
              1,
              Object.assign(item, {
                appealStatus: 'success',
                message: '',
                url: result
              })
            );
            fileListLen++;
          }
          this.disabledBtn = false;
          uni.hideLoading();
        } catch (error) {
          uni.hideLoading();
        }

        // console.log(fileListLen, 'fileListLen');
      },

      showLoading() {
        uni.showLoading({
          title: '图片上传中',
          mask: true
        });
      },

      uploadFilePromise(url) {
        console.log(url);
        let that = this;
        let arrimg = [];
        // that.image=[]
        return new Promise((resolve, reject) => {
          const uploadUrlBase = Config.DXHost + `zxAdminCourse/common/uploadFile`;
          let a = uni.uploadFile({
            url: uploadUrlBase,
            filePath: url,
            name: 'file',
            formData: {
              user: 'test'
            },
            header: {
              Token: uni.getStorageSync('token')
            },
            success: (res) => {
              setTimeout(() => {
                resolve(res.data.data);
                let data = JSON.parse(res.data);
                console.log(data, 5555);
                that.image.push(data.data.fileUrl);
                that.disabledBtn = false;
              }, 1000);
            },
            fail: (err) => {
              console.log('🚀 ~ uploadFilePromise ~ err11111111:', err);
              reject(err);
            }
          });
        });
      },
      previewImage(currentUrl) {
        // 如果要预览多张图片，可以传入 urls 数组
        uni.previewImage({
          current: currentUrl, // 当前显示图片的链接
          urls: [currentUrl] // 需要预览的图片链接列表
        });
      },
      deletePic(event) {
        console.log('🚀 ~ deletePic ~ event:', event);
        this[`fileList${event.name}`].splice(event.index, 1);
        this.image.splice(event.index, 1);
        console.log('🚀 ~ deletePic ~ this.image:', this.image);
      },
      oversize(event) {
        event.file.forEach((item) => {
          if (item.size > 1048576 * 2) {
            uni.showToast({
              title: '图片大小不能超过2M',
              icon: 'none'
            });
          }
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .partner {
    width: 100vw;
    height: 100vh;
    height: 100%;
    background-color: #fff;
    box-sizing: border-box;

    .top {
      .top-title {
        width: 80%;
        height: 40rpx;
        line-height: 40rpx;
        border-radius: 4rpx;
        border-left: 6rpx solid #339378;
      }

      .image-upload {
        min-height: 200rpx;
      }
    }
  }

  .uni-input {
    height: 64rpx;
    padding-left: 32rpx;
    background-color: #f3f8fc;
  }
  .submit {
    background: #339378;
    height: 88rpx;
    border-radius: 44upx;
    line-height: 88upx;
  }

  .modal-content {
    width: 622rpx;
    height: 344rpx;
    background: url('https://document.dxznjy.com/dxSelect/5f09dfac-9808-4c2c-a474-0cb702a1df5d.png') no-repeat center / 100%;
    padding: 0;
    border-radius: 40rpx;
    box-sizing: border-box;

    .close-icon {
      width: 40rpx;
      height: 40rpx;
      background: url('https://document.dxznjy.com/dxSelect/55db650b-f99e-40dd-8dec-3cd85746c65c.png') no-repeat center / 100%;
      position: absolute;
      top: 24rpx;
      right: 24rpx;
    }

    .smile-box {
      width: 112rpx;
      height: 112rpx;
      display: block;
      margin: 28rpx auto 0;
    }

    .show-title {
      height: 50rpx;
      color: #3f4a2f;
      line-height: 50rpx;
      text-align: center;
    }
    .show-desc {
      width: 490rpx;
      // height: 50rpx;
      margin: 12rpx auto 0;
      color: #a4a59a;
      // line-height: 50rpx;
      text-align: center;
      word-wrap: break-word; /* 旧版属性（兼容性更好） */
      overflow-wrap: break-word; /* 新版标准属性 */
    }

    .submit-btn {
      width: 368rpx;
      height: 84rpx;
      line-height: 84rpx;
      text-align: center;
      background: linear-gradient(90deg, #ef9d4f 0%, #ea643e 100%);
      border-radius: 42rpx;
      margin: 0 auto;
    }
  }

  .submitButton {
    width: 686rpx;
    line-height: 74rpx;
    margin: 0 auto;
    background: #339378;
    border-radius: 38rpx;
    position: fixed;
    bottom: 32rpx;
  }
</style>
