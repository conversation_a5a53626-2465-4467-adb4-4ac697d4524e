<!-- 学员信息登记表 -->
<template>
  <page-meta :page-style="'overflow:' + (show ? 'hidden' : 'visible')"></page-meta>
  <view>
    <image :src="actImage" mode="widthFix" class="img-act"></image>
    <view class="foot-content">
      <view class="join" @click="handleGo()">
        <image src="https://document.dxznjy.com/course/231b0294600249a1820bde00185b96a4.png" style="width: 100%; height: 100%"></image>
      </view>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        show: false, //禁止穿透
        actId: '',
        actInfo: {},
        actImage: '',
        show: false
      };
    },
    components: {},

    mounted() {},
    methods: {
      getActDetails() {
        $http({
          url: 'zx/course/courseDetail',
          data: {
            courseId: this.actId
          }
        }).then((res) => {
          this.actInfo = res.data;
          if (res.data.bannerImages[0]) {
            this.actImage = res.data.bannerImages[0];
          }
        });
      },
      handleGo() {
        $http({
          url: 'zxAdminCourse/course/nonstriker/student/info/verify',
          data: {
            courseId: this.actId,
            mobile: uni.getStorageSync('phone')
          }
        }).then((res) => {
          if (res.code == 20000) {
            uni.navigateTo({
              url: `/Coursedetails/my/studentInfo?id=${this.actId}&title=${this.actInfo.courseName}&qrCodeImage=${this.actInfo.qrCodeImage}`
            });
          }
        });
      }
    },
    onShow() {
      let token = uni.getStorageSync('token');
      if (!token) {
        uni.navigateTo({
          url: '/Personalcenter/login/login'
        });
      }
    },

    onLoad(options) {
      this.actId = options.id;
      this.getActDetails();
    }
  };
</script>
<style lang="scss" scoped>
  .img-act {
    width: 100%;
    height: 2565rpx;
    margin-bottom: 0;
    vertical-align: top;
  }
  .foot-content {
    position: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    bottom: 0;
    width: 100%;
    height: 140rpx;
    z-index: 1;
    justify-content: center;
    background: url('https://document.dxznjy.com/course/d4c0a9dbfa724de3936fa724d0db70ad.png') 100% 100% no-repeat;
    background-size: 100% 100%;

    .join {
      width: 500rpx;
      height: 101rpx;
    }
  }
</style>
