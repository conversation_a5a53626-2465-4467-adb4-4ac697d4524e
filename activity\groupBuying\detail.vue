<!-- 拼团详情 -->
<template>
  <view class="page-content">
    <view class="title">
      您已成功参团，再邀请
      <text class="number">{{ showNumber }}</text>
      人即可成团
    </view>
    <view class="time-down" v-if="!timeOver">
      <text class="text">拼团剩余时间</text>
      <view class="time">{{ countdown.hours }}</view>
      <view class="colon">:</view>
      <view class="time">{{ countdown.minutes }}</view>
      <view class="colon">:</view>
      <view class="time">{{ countdown.seconds }}</view>
    </view>
    <view v-else class="time-down">已结束</view>
    <view class="content">
      <view class="guide-title">新手指引</view>
      <view class="qa">
        <view class="question">问：是不是支付成功后就可以成团？</view>
        <view class="ask">答：发起的拼购需要通过分享给好友/群，用户下单满足人数才可以成团哦~</view>
        <view class="question">问：团长额外福利/成团福利如何领取？</view>
        <view class="ask">答：选择设置福利的商品开团，成团后，福利会自动发放至参团账号中。</view>
        <view class="question">问：拼购的商品不想要了怎么办？</view>
        <view class="ask">答：若活动结束，没有完成拼购，预付款退到您的账户。</view>

        <view class="qa-tips">拼购成功的商品不能进行退款退货，如有特殊情况 请联系在线客服。</view>
      </view>
    </view>

    <button open-type="share" :data-invite="true" class="invite" hover-class="none"></button>
  </view>
</template>

<script>
  const {
    $http,
    $navigationTo
  } = require('@/util/methods.js');
  export default {
    data() {
      return {
        countdown: {
          days: '00',
          hours: '00',
          minutes: '00',
          seconds: '00'
        },
        groupInstanceId: '',
        groupUserList: [], // 参团用户信息
        groupSize: 0,
        groupOverNum: 0,
        endTime: 0,
        groupInstanceInfo: {},
        timeOver: false,
        showNumber: 0 // 显示人数
      };
    },
    onShareAppMessage(res) {
      getApp().sensors.track('Click', {
        name: '拼团邀请好友',
        goodsId: this.groupInstanceInfo.goodsId
      });

      // 拼团邀请按钮
      console.log('🚀 ~ onShareAppMessage ~ res:', res);
      if (res.from == 'button' && res.target.dataset && res.target.dataset.invite) {
        return {
          title: this.groupInstanceInfo.groupShareText || '叮，你的好友敲了你一下，赶紧过来看看',
          imageUrl: this.groupInstanceInfo.groupShareImage || '', //分享封面
          //如果有参数的情况可以写path
          path: `/Coursedetails/productDetils?id=${this.groupInstanceInfo.goodsId}&groupActivityId=${this.groupInstanceInfo.groupActivityId}&groupInstanceId=${this.groupInstanceId}&isGroupBuyGood=1&isJoinGroup=1`
        };
      }
    },
    methods: {
      startCountdown() {
        const updateCountdown = () => {
          const now = new Date().getTime();
          const timeLeft = this.endTime - now;

          if (timeLeft <= 0) {
            clearInterval(this.countdownTimer);
            this.countdown = {
              days: '00',
              hours: '00',
              minutes: '00',
              seconds: '00',
              status: 'finish'
            };
            this.handleCountdownEnd();
            return;
          }

          const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
          const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)) + days * 24;
          const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
          const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

          this.countdown = {
            days: String(days).padStart(2, '0'),
            hours: String(hours).padStart(2, '0'),
            minutes: String(minutes).padStart(2, '0'),
            seconds: String(seconds).padStart(2, '0')
          };
        };

        updateCountdown();
        this.countdownTimer = setInterval(updateCountdown, 1000);
      },
      handleCountdownEnd() {
        // 倒计时结束时的处理逻辑
        console.log('倒计时已结束');
        this.timeOver = true;
      },
      async getGroupInstanceInfo() {
        uni.showLoading({
          title: '加载中...',
          mask: true
        });
        const res = await $http({
          url: 'zx/wap/group/instances/single',
          method: 'get',
          data: {
            instancesId: this.groupInstanceId
          }
        });

        if (res.data) {
          this.groupInstanceInfo = res.data;
          this.groupSize = res.data && res.data ? res.data.groupSize : 0;
          this.groupOverNum = res.data && res.data && res.data.groupOverNum > 0 ? res.data.groupOverNum : 0;
          // groupFlag 发起拼团团长 2、参与拼团用户  发起拼团不减1 参与拼团减1
          if (this.groupFlag == 1) {
            this.showNumber = this.groupOverNum;
          } else {
            this.showNumber = this.groupOverNum - 1 >= 0 ? this.groupOverNum - 1 : 0;
          }
          this.groupUserList = res.data.piGroupParticipantsVos || [];
          this.endTime = new Date(res.data.endTime).getTime();
          console.log('🚀 ~ getGroupInstanceInfo ~ endTime:', this.endTime);
          this.startCountdown();
        }
      }
    },

    onShow() {
      this.getGroupInstanceInfo();
    },

    onLoad(options) {
      this.groupInstanceId = options.groupInstanceId;
      this.groupFlag = options.groupFlag;
    },
    onUnload() {
      clearInterval(this.countdownTimer);
    }
  };
</script>

<style lang="scss" scoped>
  .page-content {
    width: 100%;
    height: 100vh;
    box-sizing: border-box;
    background-image: url('https://document.dxznjy.com/dxSelect/2fa9e32a-553b-4f4a-9b0b-85634c83e0ae.png');
    background-size: 100% 100%;

    .title {
      padding-top: 80rpx;
      margin-top: 0 auto;
      font-family: ZiZhiQuXiMaiTi;
      font-size: 36rpx;
      color: #34705e;
      line-height: 46rpx;
      text-align: center;

      .number {
        padding: 0 24rpx;
        font-size: 44rpx;
        color: #e9a240;
        line-height: 58rpx;
      }
    }

    .time-down {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 76rpx;
      margin-top: 6rpx;
      color: #333333;
      font-size: 28rpx;

      .text {
        margin-right: 16rpx;
      }

      .time {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 56rpx;
        height: 56rpx;
        font-size: 32rpx;
        color: #ffffff;
        background: #fd9b2a;
        border-radius: 10rpx;
      }

      .colon {
        font-size: 32rpx;
        padding: 0 14rpx;
      }
    }

    .content {
      margin: 210rpx auto 0;

      .guide-title {
        font-family: ZiZhiQuXiMaiTi;
        font-size: 36rpx;
        color: #34705e;
        line-height: 46rpx;
        text-align: center;
      }

      .qa {
        margin-top: 24rpx;
        padding: 0 104rpx 0 114rpx;
      }

      .question {
        margin-top: 24rpx;
        font-size: 28rpx;
        color: #34705e;
        line-height: 42rpx;
        font-family: AlibabaPuHuiTiBold;
        font-weight: bold;
      }

      .ask {
        margin-top: 16rpx;
        font-size: 28rpx;
        color: #555555;
        line-height: 44rpx;
      }

      .qa-tips {
        margin-top: 68rpx;
        font-size: 24rpx;
        color: #e89b48;
        line-height: 44rpx;
      }
    }

    .invite {
      position: fixed;
      bottom: 154rpx;
      left: 50%;
      width: 440rpx;
      height: 80rpx;
      margin-left: -220rpx;
      background-image: url('https://document.dxznjy.com/dxSelect/1760644b-b3d9-4790-882d-93cf777e9f8e.png');
      background-size: 100% 100%;
    }
  }
</style>