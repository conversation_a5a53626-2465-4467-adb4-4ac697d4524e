<template>
  <page-meta :page-style="'overflow:' + (show ? 'hidden' : 'visible')"></page-meta>
  <view style="padding-bottom: 84rpx; background-color: #f3faf9">
    <!-- <scroll-view class="scroll-view_H" scroll-x="true">
      <view class="item" v-for="(item, index) in topList" :key="index" @click="goScroll(item, index)">
        <view class="icon" v-if="scrollTop">
          <image :src="item.icon" style="width: 100%; height: 100%"></image>
        </view>
        <view class="text" :class="active == index ? 'active' : ''">{{ item.name }}</view>
      </view>
    </scroll-view> -->

    <view class="scroll-view_H title-view">
      <view class="item" v-for="(item, index) in topList" :key="index" @click="goScroll(item, index)">
        <view class="icon">
          <image :src="item.icon" style="width: 100%; height: 100%"></image>
        </view>
        <view class="text" :class="active == index ? 'active' : ''">{{ item.name }}</view>
      </view>
    </view>

    <view class="section-top">
      <!-- 权益一 -->
      <view class="banner" id="equity-1">
        <view class="top-title"></view>
        <view class="des">
          <view>价值29800元家族荣耀全年线上陪跑营：</view>
          <view>家长会员一年成长规划，让父母成为教育专家让</view>
          <view>孩子成为人生赢家，唤醒生命，实现家族荣耀旺三代</view>
        </view>
        <view class="banner-swiper">
          <view class="banner-swiper-content">
            <uni-swiper-dot :info="bannerList" :current="bannerIndex" mode="dot" field="content" :dotsStyles="dotsStyles">
              <swiper
                @change="(e) => (bannerIndex = e.detail.current)"
                style="height: 280rpx"
                :autoplay="true"
                :indicator-dots="false"
                circular
                :current="bannerIndex"
                :interval="5000"
                :duration="1000"
              >
                <block v-for="(item, index) in bannerList" :key="index">
                  <swiper-item class="flex-c radius-16 swiper_css_item">
                    <image :src="item" mode="aspectFill" class="wh100" lazy-load="true" @tap="handleBannerClick(item)"></image>
                  </swiper-item>
                </block>
              </swiper>
            </uni-swiper-dot>
          </view>
        </view>
      </view>

      <!-- 权益二 -->
      <view class="qh" id="equity-2">
        <view class="title-spec">清北学霸学习营</view>
        <view class="des-spec">获得3980元清北学霸学习营</view>
        <view class="pic" @click="handleGoStudy">
          <image src="https://document.dxznjy.com/course/a8c95265dfbf445aa1218acf3834e5c8.jpg" style="width: 100%; height: 100%"></image>
        </view>
      </view>
    </view>

    <!-- 权益三 -->
    <view class="talent common" id="equity-3">
      <view class="title">
        <view class="titleFlex">
          <span class="lineBlock"></span>
          天赋测评 发掘孩子超能力
        </view>
      </view>
      <view class="description">八大智能测评 | 发现孩子未来天赋 | 因材施教科学养育</view>
      <view style="display: flex; justify-content: space-between">
        <view class="talentBtn" @tap="startTest"></view>
        <view class="talentBtn1" @tap="testReport"></view>
      </view>
    </view>

    <!-- 权益四 -->
    <!-- <view class="discount common" id="equity-4">
      <view class="title">
        <view>会员折扣</view>
        <view class="mores" @click="skipTap('pages/selection/index')">更多></view>
      </view>
      <view class="description" style="color: #059880">全平台95折，畅享全学龄段优质教育产品</view>
      <view class="discountItems">
        <he-lang-waterfall
          v-for="(item, index) in discountList"
          :key="index"
          width="300rpx"
          :item="item"
          :share="false"
          @click="skipTap('Coursedetails/productDetils?id=' + item.goodsId)"
          tag="right"
          :index="index"
        ></he-lang-waterfall>
      </view>
    </view> -->

    <!-- 权益五 -->
    <!-- <view class="big-shot common" id="equity-5">
      <view class="title">
        <view class="titleFlex">
          <span class="lineBlock"></span>
          大咖线下课
        </view>
      </view>
      <view class="description">价值7980元家庭教育大咖课程线下学习名额一个学费全免，场地费另交，大咖导师现场答疑解惑</view>
      <view class="pic" @click="handleMessage">
        <image src="https://document.dxznjy.com/course/1bc3703759cc4f6d8870c6dd9fcc9ee1.png" style="width: 100%; height: 100%"></image>
      </view>
    </view> -->

    <!-- 权益六 -->
    <!-- <view class="career common" id="equity-6">
      <view class="title">
        <view class="titleFlex">
          <span class="lineBlock"></span>
          学业生涯规划 探索孩子未来
        </view>
      </view>
      <view class="description">成长、学科、升学规划，绘就孩子璀璨未来蓝图</view>
      <view class="careerBtn" @click="handleMessage"></view>
    </view> -->

    <!-- 权益七 -->
    <!-- <view class="equity-7 common" id="equity-7">
      <view class="title">
        <view class="titleFlex">教育金</view>
      </view>
      <view class="description">获得1000元教育金，享全平台全学龄段优质教育产品</view>
      <view class="pic" @click="handleGoCoupon">
        <image src="https://document.dxznjy.com/course/30fe9fe7ad8c4980a493642cbc11d228.png" style="width: 100%; height: 100%"></image>
      </view>
    </view> -->

    <view class="fullImg" v-if="fullShow" @click="fullShow = !fullShow">
      <image @click.stop="" style="width: 100%" mode="widthFix" :src="fullImages"></image>
    </view>

    <uni-popup ref="testing" type="center" @change="change">
      <view class="shareCard">
        <view class="activityRule" style="font-size: 32rpx">提醒</view>
        <view class="review_close" @click="closeTesting">
          <uni-icons type="clear" size="26" color="#B1B1B1"></uni-icons>
        </view>
        <view class="rule">
          <view style="color: #428a6f; font-size: 40rpx; display: flex; align-items: center; justify-content: center">
            {{ testCode }}
            <view
              style="
                background: url('https://document.dxznjy.com/course/c76e5247efa54ea79a52db0fba0dd104.png');
                background-size: 100%;
                width: 32rpx;
                height: 32rpx;
                margin-left: 10rpx;
              "
              @click="copyCode"
            ></view>
          </view>
          <view class="" style="width: 70%; margin: 20rpx auto; text-align: center; margin-bottom: 40rpx">
            恭喜您获得会员专属的激活码,在测评时请输入激活码，评测完成后约1-2个工作日生成报告
          </view>
          <view
            class=""
            style="color: #fff; background-color: #428a6f; width: 342rpx; line-height: 92rpx; margin: auto; font-size: 32rpx; text-align: center; border-radius: 46rpx"
            @tap="goTest"
          >
            前往测评
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 保存天赋测评弹窗 -->
    <uni-popup ref="testingQrCode" type="center" @change="change">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="review_close_qr" @click="closeQrCodeTesting">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold pb-10">长按保存图片</view>
            <view class="qrCodeRule">
              <image class="ewm" src="https://document.dxznjy.com/course/44f13e3a2eef46ecadb1c1f0312cc517.jpg" :show-menu-by-longpress="true" mode=""></image>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import HeLangWaterfall from '@/components/helang-waterfall/helang-waterfall';
  const { $http, $navigationTo, $showMsg } = require('@/util/methods.js');
  export default {
    data() {
      return {
        dotsStyles: {
          backgroundColor: '#CCEBE6',
          selectedBackgroundColor: '#054036'
        },
        scrollTop: true,
        mobile: null,
        careerList: [],
        show: false, //禁止穿透
        discountList: [],
        shopList: [],
        courseType: null,
        active: 0,
        playerIdContext: 'polyVPlayerCont',
        // 年级
        gradeList: [
          {
            value: '18',
            label: '幼儿园',
            ext: '',
            children: null
          },
          {
            value: '1',
            label: '一年级',
            ext: '',
            children: null
          },
          {
            value: '2',
            label: '二年级',
            ext: '',
            children: null
          },
          {
            value: '3',
            label: '三年级',
            ext: '',
            children: null
          },
          {
            value: '4',
            label: '四年级',
            ext: '',
            children: null
          },
          {
            value: '5',
            label: '五年级',
            ext: '',
            children: null
          },
          {
            value: '6',
            label: '六年级',
            ext: '',
            children: null
          },
          {
            value: '7',
            label: '七年级',
            ext: '',
            children: null
          },
          {
            value: '8',
            label: '八年级',
            ext: '',
            children: null
          },
          {
            value: '9',
            label: '九年级',
            ext: '',
            children: null
          },
          {
            value: '10',
            label: '高中一年级',
            ext: '',
            children: null
          },
          {
            value: '11',
            label: '高中二年级',
            ext: '',
            children: null
          },
          {
            value: '12',
            label: '高中三年级',
            ext: '',
            children: null
          }
        ],
        bannerIndex: 0,
        fullShow: false,
        fullImages: '',
        bannerList: ['https://document.dxznjy.com/course/b0168d09cb174ea18c228719d40b631c.jpg'],
        topList: [
          {
            name: '全年陪跑',
            id: 'equity-1',
            top: 55,
            icon: 'https://document.dxznjy.com/course/04377cdcfa0243669acb4660a50495a9.png'
          },
          {
            name: '清北学霸营',
            id: 'equity-2',
            top: 55,
            icon: 'https://document.dxznjy.com/course/331444f46ab444d48acaad99102285df.png'
          },
          {
            name: '天赋测评',
            id: 'equity-3',
            top: 55,
            icon: 'https://document.dxznjy.com/course/fc78dd7ccee64a9abebef30e955e4181.png'
          }
          // {
          //   name: '会员折扣',
          //   id: 'equity-4',
          //   top: 421,
          //   icon: 'https://document.dxznjy.com/course/982c099bb8bd4edea22e13ecc8ae2fb2.png'
          // },
          // {
          //   name: '大咖线下课',
          //   id: 'equity-5',
          //   top: 421,
          //   icon: 'https://document.dxznjy.com/course/0d8cd151577d450080a99b35ab3fae0a.png'
          // },
          // {
          //   name: '学业规划',
          //   id: 'equity-6',
          //   top: 555,
          //   icon: 'https://document.dxznjy.com/course/893b1e68ef1f4f87ba37f884228fa199.png'
          // },
          // {
          //   name: '教育金',
          //   id: 'equity-7',
          //   top: 1062,
          //   icon: 'https://document.dxznjy.com/course/35b7619200d246e3a19a22950a3ff342.png'
          // },
          // {
          //   name: '教育卡',
          //   id: 'equity-8',
          //   top: 1062,
          //   icon: 'https://document.dxznjy.com/course/2c4fdc1c837048b585ba936176c8beda.png'
          // }
        ],
        identityType: uni.getStorageSync('identityType'),
        imgHost: getApp().globalData.imgsomeHost,
        testCode: '',
        userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '',
        qhId: ''
      };
    },
    components: {
      HeLangWaterfall
    },
    async onLoad(e) {
      this.mobile = uni.getStorageSync('phone');
    },
    onShow() {
      this.init();
      this.getDiscountGoods();
      this.getShopList();

      let token = uni.getStorageSync('token');
      if (!token) {
        uni.navigateTo({
          url: '/Personalcenter/login/login'
        });
      }
    },
    mounted() {
      this.getTop();
      this.getQh();
    },
    methods: {
      handleMessage() {
        uni.showToast({
          title: '正在规划中，敬请期待...',
          icon: 'none'
        });
      },
      handleGoCoupon() {
        uni.navigateTo({
          url: `/coupons/CouponsList`
        });
      },
      // 获取清北学霸学习营id
      getQh() {
        $http({
          url: 'zxAdminCourse/web/goods/multi/query',
          data: {
            goodsIdsOrName: '清北学霸'
          }
        }).then((res) => {
          if (res.data.length > 0) {
            this.qhId = res.data[0].id;
          } else {
            this.qhId = '1311774637864411136';
          }
        });
      },
      // 陪跑营跳转
      handleBannerClick() {
        uni.navigateTo({
          url: `/Coursedetails/my/joggingCamp`
        });
      },
      // 查看大图
      lookFull(e) {
        this.fullImages = e;
        this.fullShow = true;
      },

      async getTop() {
        uni.showLoading({
          title: '加载中'
        });

        for (let index = 0; index < this.topList.length; index++) {
          const ele = this.topList[index];
          let p = await this.getElementPositionInParent(ele.id);
          ele.top = p.top;
          if (index == this.topList.length - 1) {
            uni.hideLoading();
          }
        }
      },
      // 禁止穿透滚动
      change(e) {
        this.show = e.show;
      },
      async getShopList() {
        const res = await $http({
          url: 'zx/wap/goods/select/list',
          data: {
            goodsTypeListStr: '6',
            pageNum: 1,
            pageSize: 2,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        this.shopList = res.data.data;
      },

      skipTap(url) {
        $navigationTo(url);
      },
      closeTesting() {
        this.$refs.testing.close();
      },
      closeQrCodeTesting() {
        this.$refs.testingQrCode.close();
      },
      goDetails(item) {
        uni.navigateTo({
          url: '/shoppingMall/details?id=' + item.goodsId
        });
      },
      async testReport() {
        await this.$httpUser.get('zx/wap/talent/evaluation/single/detail?userId=' + this.userId).then((res) => {
          if (res.data.data.evaluationFileUrl === '') {
            uni.showToast({
              title: '报告未生成',
              icon: 'none'
            });
          } else {
            this.reportUrl = res.data.data.evaluationFileUrl;
            uni.downloadFile({
              url: this.reportUrl,
              success: function (res) {
                var filePath = res.tempFilePath;
                uni.openDocument({
                  filePath: filePath,
                  showMenu: true,
                  success: function (res) {
                    console.log('打开文档成功');
                  }
                });
              }
            });
            console.log(this.reportUrl, '1111');
          }
        });
      },
      goTest() {
        this.$refs.testingQrCode.open();
        return;
        uni.navigateTo({
          url: '/aptitudeTesting/startTest'
        });
      },
      async startTest() {
        let that = this;
        await this.$httpUser
          .post('zx/wap/talent/evaluation/consume', {
            userId: uni.getStorageSync('user_id') || ''
          })
          .then((res) => {
            if (res.data.data && res.data.data.activationCode) {
              that.testCode = res.data.data.activationCode;
              that.$refs.testing.open();
            } else {
              uni.showToast({
                title: res.message,
                icon: 'none',
                duration: 2000
              });
            }
          });
      },
      copyCode() {
        uni.setClipboardData({
          data: this.testCode,
          success: function (res) {
            console.log(res);
            uni.getClipboardData({
              success: function (res) {
                uni.showToast({
                  title: '复制成功'
                });
              }
            });
          }
        });
      },
      async init() {
        let res = await $http({
          url: 'zx/career/student/getList?parentMobile=' + this.mobile
        });
        this.careerList = res.data || [];
      },
      async getDiscountGoods() {
        const res = await $http({
          url: 'zx/wap/goods/select/list',
          data: {
            // goodsCategoryId //商品分类id
            goodsTypeListStr: '2,3,4',
            pageNum: 1,
            pageSize: 2,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        if (res) {
          this.discountList = res.data.data;
        }
      },
      // 跳转去报告页
      goPlan(id) {
        uni.navigateTo({
          url: '/Coursedetails/Career/planning?id=' + id
        });
      },
      handleGoStudy() {
        if (this.qhId) {
          uni.navigateTo({
            url: `/Coursedetails/study/courseDetail?courseId=${this.qhId}&sourse=parentEquity`
          });
        } else {
          $showMsg('正在规划中，敬请期待...');
        }
      },
      getElementPositionInParent(elementId) {
        return new Promise((resolve, reject) => {
          const query = uni.createSelectorQuery();

          // 查询父元素
          query.selectViewport().boundingClientRect();

          // 查询子元素
          query.select(`#${elementId}`).boundingClientRect();

          // 执行查询
          query.exec((results) => {
            if (results.length < 2) {
              reject(new Error('Failed to get element position.'));
              return;
            }

            const viewportRect = results[0];
            const elementRect = results[1];

            // 计算子元素在父元素中的位置
            const position = {
              top: elementRect.top - viewportRect.top,
              left: elementRect.left - viewportRect.left,
              width: elementRect.width,
              height: elementRect.height
            };

            resolve(position);
          });
        });
      },

      async goScroll(item, i) {
        this.active = i;
        if (this.scrollTop && i == 0) {
          return;
        }
        if (i == 0) {
          uni.pageScrollTo({
            scrollTop: 1,
            duration: 300
          });
          return;
        }
        uni.pageScrollTo({
          scrollTop: item.top - 122,
          duration: 300
        });
      },
      career() {
        uni.navigateTo({
          url: '/Personalcenter/Career/enter'
        });
      }
    },

    onPageScroll: function (e) {
      //nvue暂不支持滚动监听，可用bindingx代替
      if (e.scrollTop > 0) {
        this.scrollTop = false;
      } else {
        this.scrollTop = true;
      }
    }
  };
</script>
<style lang="scss" scoped>
  .fullImg {
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.6);
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .moreBtn {
    height: 34rpx;
    text-align: center;
    line-height: 34rpx;
    font-size: 24rpx;
    color: #339378;

    .right {
      display: inline-block;
      margin-left: 8rpx;
      font-size: 18rpx;
    }
  }

  .pic {
    height: 200rpx;
    border-radius: 16rpx;
    background: #d8d8d8;
    overflow: hidden;
  }

  .banner {
    width: 686rpx;
    border-radius: 16rpx;
    margin: 0 auto 48rpx;
  }
  .qh {
    width: 686rpx;
    height: 492rpx;
    margin: 84rpx auto 0;
    box-sizing: border-box;
    background: url('https://document.dxznjy.com/course/4018a3f005f64c6bae2295c9de7af380.png') no-repeat;
    background-size: 100% 100%;
    padding: 36rpx 18rpx 34rpx;

    .pic {
      width: 654rpx;
      height: 306rpx;
    }
  }

  .title-spec {
    height: 44rpx;
    font-size: 32rpx;
    color: #0f5b4e;
    line-height: 44rpx;
    text-align: left;
    font-style: normal;
    font-weight: bold;
  }

  .des-spec {
    height: 40rpx;
    font-size: 28rpx;
    color: #059880;
    line-height: 40rpx;
    text-align: left;
    font-style: normal;
    margin: 16rpx 0;
  }

  .title {
    height: 44rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 32rpx;
    color: #333333;
    font-weight: bold;
    margin-bottom: 16rpx;

    .titleFlex {
      display: flex;
      align-items: center;
    }

    .lineBlock {
      display: inline-block;
      height: 30rpx;
      width: 12rpx;
      background-color: #0b957a;
      border-radius: 6rpx;
      margin-right: 8rpx;
    }

    .mores {
      width: 110rpx;
      height: 40rpx;
      background: linear-gradient(90deg, #fdfdfd 0%, #b1ded1 100%);
      border-radius: 20rpx;
      color: #0f5b4e;
      text-align: center;
      font-size: 24rpx;
      line-height: 40rpx;
    }
  }

  .active {
    color: #fff !important;
  }

  .description {
    margin-top: 16rpx;
    margin-bottom: 16rpx;
    line-height: 40rpx;
    color: #959595;
    font-size: 28rpx;
  }

  //弹窗
  .shareCard {
    position: relative;
    height: 600rpx;
    background: #ffffff;
    color: #000;
    padding-top: 50upx;
    box-sizing: border-box;
    overflow: hidden;
    width: 90vw;
  }

  .dialogBG {
    width: 100%;

    .reviewCard_box {
      width: 670rpx;
      position: relative;
    }
    .reviewCard_box image {
      width: 100%;
      height: 100%;
    }

    .cartoom_image {
      width: 420rpx;
      position: absolute;
      top: -250rpx;
      left: 145rpx;
      z-index: -1;
    }

    .review_close_qr {
      position: absolute;
      top: 20rpx;
      right: 20rpx;
      z-index: 2;
    }

    .reviewCard {
      position: relative;
      width: 100%;
      height: 100%;
      background: #ffffff;
      color: #000;
      border-radius: 24upx;
      padding: 50upx 55upx;
      box-sizing: border-box;
    }

    .reviewTitle {
      width: 100%;
      text-align: center;
      font-size: 34upx;
      display: flex;
      justify-content: center;
    }

    .qrCodeRule {
      padding: 20rpx;
      margin-top: 0rpx;
      display: flex;
      justify-content: center;
      align-items: center;
      image {
        width: 400upx;
        height: 400upx;
      }
    }
  }

  .activityRule {
    text-align: center;
    font-weight: bold;
    position: absolute;
    top: 40rpx;
    left: 0;
    width: 100%;
    background: white;
    z-index: 10;
  }

  .review_close {
    position: absolute;
    /* 固定在右上角 */
    top: 40rpx;
    right: 20rpx;
    z-index: 10;
    /* 确保在上层 */
  }

  .rule {
    padding: 20rpx;
    line-height: 50rpx;
    margin-top: 60rpx;
    /* 确保不被固定元素遮挡 */
    overflow-y: auto;
    /* 允许滚动 */
    height: calc(100% - 40px);
    /* 适应滚动区域的高度 */
  }

  .scroll-view_H {
    position: sticky;
    top: 0;
    /* 当元素滚动到顶部时变为固定定位 */
    z-index: 10;
    /* 确保吸顶元素覆盖其他内容 */
    white-space: nowrap;
    width: 100%;
    padding: 32rpx 0;
    display: flex;
    justify-content: space-around;
    // height: 238rpx;
    background: linear-gradient(107deg, #084137 0%, #03251f 63%, #023b31 100%);

    .item {
      display: inline-block;
      margin: 0 40rpx;

      &:first-child {
        margin-left: 32rpx;
      }

      &:last-child {
        margin-right: 32rpx;
      }

      .icon {
        height: 112rpx;
        width: 112rpx;
        border-radius: 112rpx;
        margin: 0 auto 24rpx;
        background-color: transparent;
      }

      .text {
        line-height: 40rpx;
        height: 40rpx;
        text-align: center;
        font-size: 28rpx;
        color: #dac3a4;
        font-weight: bold;
      }
    }
  }

  .title-view {
    width: 100%;
    padding: 32rpx 0;
    display: flex;
    justify-content: space-around;
    background: linear-gradient(107deg, #084137 0%, #03251f 63%, #023b31 100%);
  }

  .banner-swiper {
    width: 686rpx;
    height: 280rpx;
    margin: 14rpx auto 0;
    border-radius: 16rpx;
    box-shadow: 0rpx 4rpx 8rpx 0rpx #069b81;
    position: relative;
    .banner-swiper-content {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
    }

    .swiper_css_item {
      overflow: hidden;
    }

    /deep/.uni-swiper__dots-box {
      right: 14rpx !important;
      bottom: 14rpx !important;
      left: unset !important;
      > .uni-swiper__dots-item {
        width: 16rpx !important;
        height: 16rpx !important;
        border-color: transparent !important;
      }
    }
  }

  .swiper {
    width: 666rpx;
    // height: 220rpx;
    margin: 40rpx auto 0;

    .card {
      // width: 600rpx;
      // height: 256rpx;
      margin: 0 12rpx;
      background-color: #fff;
      border-radius: 24rpx;
      padding: 34rpx 24rpx;
      box-sizing: border-box;
      overflow: hidden;

      .cardBtn {
        color: #fff;
        font-size: 28rpx;
        font-weight: bold;
        width: 500rpx;
        margin: 40rpx auto 0;
        text-align: center;
        line-height: 60rpx;
        height: 60rpx;
        background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
        border-radius: 60rpx;
      }

      .avater {
        width: 82rpx;
        height: 82rpx;
        border-radius: 82rpx;
        margin-right: 48rpx;
      }

      .name {
        height: 44rpx;
        line-height: 44rpx;
        font-size: 32rpx;
        color: #555555;
        font-weight: bold;
        font-family: AlibabaPuHuiTi_2_85_Bold;
      }

      .old {
        height: 32rpx;
        background: rgba(51, 147, 120, 0.06);
        border-radius: 20rpx;
        border: 2rpx solid #aacac1;
        font-size: 24rpx;
        line-height: 34rpx;
        color: #2e836b;
        padding: 0 24rpx;
        text-align: center;
      }

      .glard {
        font-size: 28rpx;
        color: #a7a7a7;
        margin-left: 24rpx;
      }

      .type {
        margin-left: 12rpx;
        width: 120rpx;
        height: 34rpx;
        line-height: 34rpx;
        font-size: 24rpx;
        color: #ffffff;
        text-align: center;
        background: #33cb9e;
        border-radius: 30rpx;
      }
    }
  }

  .section-top {
    width: 100%;
    height: 1112rpx;
    background: url('https://document.dxznjy.com/course/950a7d4f4519417db866309d6196ab24.png') no-repeat;
    background-size: 100% 100%;
  }

  .banner {
    .top-title {
      width: 460rpx;
      height: 58rpx;
      margin: 0 auto;
      background: url('https://document.dxznjy.com/course/4fd4bd7795cb4d98ab3ac161d507b463.png') no-repeat;
      background-size: 100% 100%;
    }

    .des {
      width: 686rpx;
      height: 140rpx;
      box-sizing: border-box;
      padding-top: 10rpx;
      background: linear-gradient(90deg, rgba(225, 240, 239, 0.486) 0%, #bce5d8 28%, #bbe5d8 70%, rgba(245, 248, 250, 0.432) 100%);
      margin-top: 14rpx;
      font-size: 28rpx;
      color: #032c25;
      line-height: 40rpx;
      text-align: right;
      font-style: normal;
      text-align: center;
    }
  }

  .talent {
    .talentBtn {
      width: 350rpx;
      height: 178rpx;
      background: url('https://document.dxznjy.com/course/6a559938caf2408a9185e3b6b8d2bbde.png') no-repeat;
      background-size: contain;
    }

    .talentBtn1 {
      width: 350rpx;
      height: 178rpx;
      background: url('https://document.dxznjy.com/course/43c3a9b43f9a47618779dc05bf9e4013.png') no-repeat;
      background-size: contain;
    }
  }

  .career {
    .careerBtn {
      width: 686rpx;
      box-sizing: border-box;
      height: 144rpx;
      margin: 6rpx auto 0;
      background: url('https://document.dxznjy.com/course/ee88f5b40fea4715894108678d016ee1.png') no-repeat;
      background-size: contain;
    }
  }
  .common {
    background: #ffffff;
    border-radius: 24rpx;
    margin: 32rpx auto 0;
    padding: 24rpx;
  }
  .big-shot {
    .pic {
      width: 100%;
      height: 280rpx;
    }
  }

  .discount {
    padding: 18rpx;
    box-sizing: border-box;
    margin: 32rpx 24rpx;
    background-image: url('https://document.dxznjy.com/course/7da877c6f9464807a1f68daa54c30a54.png');
    background-repeat: no-repeat;
    background-size: contain;
    background-color: #f3f8fc;

    .discountItems {
      // height: 346rpx;
      display: flex;
      justify-content: space-around;
    }
  }

  .equity-7 {
    margin-top: 0;
    background: transparent;

    .pic {
      width: 100%;
      height: 202rpx;
    }
  }

  .equity-8 {
    width: 686rpx;
    height: 492rpx;
    margin: 32rpx auto 0;
    box-sizing: border-box;
    background: url('https://document.dxznjy.com/course/6a0e2c83eef4453788bd8e910cdc5ad0.png') no-repeat;
    background-size: 100% 100%;
    padding: 36rpx 18rpx 34rpx;

    .pic {
      width: 654rpx;
      height: 306rpx;
    }
  }

  .society {
    margin-bottom: 40rpx;

    .societys {
      height: 200rpx;
      overflow: hidden;
      border-radius: 16rpx;
      background: #d8d8d8;
    }

    .societyTitle {
      display: flex;
      align-items: center;
      margin: 48rpx 0 24rpx;
      height: 44rpx;
      line-height: 44rpx;
      color: #3c574d;
      font-size: 32rpx;
      font-weight: bold;

      .rhomboid {
        height: 14rpx;
        width: 14rpx;
        transform: rotate(45deg);
        background-color: #cbb76d;
        margin-right: 14rpx;
      }

      // padding-left: 42rpx;
      // text-align: center;
    }

    .societyBan {
      height: 200rpx;
      border-radius: 16rpx;
      background: #d8d8d8;
      overflow: hidden;
      margin: 48rpx 0 0;
    }
  }
  .coupon_box {
    margin: 22rpx auto 0;
    width: 686rpx;
    height: 202rpx;
    border-radius: 16rpx;
    overflow: hidden;
  }
  .coupon_popup {
    position: relative;
    box-sizing: border-box;
    width: 542rpx;
    height: 764rpx;
    padding: 208rpx 32rpx 16rpx 28rpx;
    // background-color: pink;
    background: url('https://document.dxznjy.com/course/a7766070552b4155b25eda6ef62a91fc.png') no-repeat;
    background-size: contain;
  }
  .item-coupons {
    // display: flex;
    // width: 686rpx;
    background: #ffffff;
    border-radius: 16rpx;
    margin-top: 16rpx;
    padding: 20rpx 30rpx 20rpx 0;

    .activity-type {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 158rpx;

      .activity-name {
        font-family: AlibabaPuHuiTi_2_85_Bold;
        font-size: 26rpx;
        color: #489981;
        line-height: 56rpx;
        text-align: left;
        font-style: normal;
        font-weight: bold;
        margin-bottom: 4rpx;
      }

      .activity-limit {
        font-family: AlibabaPuHuiTi_2_55_Regular;
        font-size: 24rpx;
        color: #489981;
        line-height: 34rpx;
        text-align: right;
        font-style: normal;
      }
    }

    .activity-content {
      width: 358rpx;

      .title {
        font-family: AlibabaPuHuiTi_2_85_Bold;
        font-size: 28rpx;
        color: #555555;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
        font-weight: bold;
        margin-bottom: 8rpx;
      }

      .time {
        // height: 40rpx;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        font-size: 24rpx;
        color: #c5c4c4;
        line-height: 40rpx;
        text-align: left;
        font-style: normal;
        margin-bottom: 18rpx;
      }

      .rules {
        display: flex;
        align-items: center;
        font-family: AlibabaPuHuiTi_2_55_Regular;
        font-size: 24rpx;
        color: #8f8e8e;
        line-height: 34rpx;
        text-align: left;
        font-style: normal;

        .expand-icon {
          width: 28rpx;
          height: 28rpx;
          margin-left: 8rpx;

          .icon-down {
            width: 28rpx;
            height: 28rpx;
          }
        }
      }
    }

    .activity-use {
      width: 120rpx !important;
      height: 48rpx !important;
      background: #339378 !important;
      border-radius: 24rpx !important;
      margin-left: 20rpx !important;
      font-weight: bold !important;
      font-size: 24rpx !important;
      color: #ffffff !important;
      line-height: 48rpx !important;
      text-align: center !important;
    }

    .rules-text {
      font-family: AlibabaPuHuiTi_2_55_Regular;
      font-size: 24rpx;
      color: #c5c4c4;
      line-height: 34rpx;
      text-align: left;
      font-style: normal;
      margin: 16rpx 0 0 156rpx;
    }
  }
</style>
