index/
<template>
  <view class="funContent">
    <interesting-head :title="titleText" @backPage="backPage" :hasTitleBg="true" :closeWhite="true" :hasRight="false"></interesting-head>

    <image class="interesting_car"></image>
    <!-- 趣味复习标题 -->
    <view class="interesting_title" @click="playWord(showListData[qIdIndex].word)"></view>

    <!-- 答案列表 -->
    <view class="answerListBox" v-if="showListData.length > 0 && showListData[qIdIndex].answerList != undefined && showListData[qIdIndex].answerList != null">
      <view class="answerList" v-for="(item, index) in showListData[qIdIndex].answerList" :key="index" @click="chooseAnswer(item, index)">
        <text style="color: #6c2401; font-weight: bold" :style="item.length > 6 ? 'line-height:30rpx;font-size:25rpx' : ''">{{ item }}</text>
        <image
          v-if="index == chooseAnswerIndex"
          class="answerIcon"
          :src="isRight ? imgHost + 'newInteresting/interesting_right.png' : imgHost + 'newInteresting/interesting_wrong.png'"
          mode=""
        ></image>
      </view>
    </view>

    <view class="userProgressbg">
      <view>
        <text class="interesting_countdown_tag">倒计时</text>
      </view>
      <view>
        <text class="interesting_countdown">{{ showDownTime == undefine || showDownTime < 0 ? 0 : showDownTime }}s</text>
      </view>
    </view>

    <!--结束弹窗 -->
    <uni-popup ref="popopPower" type="center" :maskClick="true" :classBG="''">
      <interesting-dialog
        :gradScreenWheel="3"
        :pageUrl="'interestingTyby'"
        :showData="showData"
        :play="play"
        :scheduleCode="showData.scheduleCode"
        @closeDialog="closeDialog1"
      ></interesting-dialog>
    </uni-popup>

    <!-- 引导 -->
    <uni-popup ref="guideOne" type="bottom" :maskClick="true" :classBG="''">
      <view style="position: relative">
        <image :style="{ height: screenHeight + 'rpx', width: screenWidth + 'rpx' }" src="https://document.dxznjy.com/applet/newInteresting/interesting_guide_tyby_one.png"></image>
        <image class="guide_btn_next" @click="guideNext()" src="https://document.dxznjy.com/applet/newInteresting/interesting_guide_next.png"></image>
      </view>
    </uni-popup>
    <uni-popup ref="guideTwo" type="bottom" :maskClick="true" :classBG="''">
      <view style="position: relative">
        <image :style="{ height: screenHeight + 'rpx', width: screenWidth + 'rpx' }" src="https://document.dxznjy.com/applet/newInteresting/interesting_guide_tyby_two.png"></image>
        <image class="guide_btn_close" @click="guideClose()" src="https://document.dxznjy.com/applet/newInteresting/interesting_guide_end.png"></image>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import interestingHead from './components/interesting-head/interestingHead.vue';
  import interestingDialog from './components/interesting-dialog/review.vue';
  import cmdProgress from './components/cmd-progress/cmd-progress.vue';

  export default {
    components: {
      interestingHead,
      interestingDialog,
      cmdProgress
    },
    data() {
      return {
        imgHost: getApp().globalData.imguseHost,
        titleText: '听音辨意',
        courseCode: '', //课程编号
        scheduleCode: '', //课程进度
        nowGroup: '',
        nowLevel: '',
        waitGroup: '',
        wordInfo: {
          roundId: null,
          scheduleCode: null,
          courseCode: null,
          nowGroup: null,
          wordCount: null,
          studentWordId: null,
          play: '2'
        },
        showListData: [], //当前课程玩法组所有的单词
        showData: {}, //当前课程所有数据
        distrubList: [], //干扰项列表数据
        qIdIndex: 0, //当前题目编号
        play: '2', //玩法
        otherWord: false, //除当前玩法其它玩法是否还有单词
        chooseAnswerIndex: -1, //选择第几个答案
        successList: [], //正确列表数据存储
        errorList: [], //错误列表数据存储
        correctRate: 0, //正确率
        gradScreenWheel: 1, //1组 2关 3轮  4轮但是其它组还没结束，没有正确率这些数据的时候给0
        isRight: null,

        downTimeCount: getApp().globalData.interestDownTime,
        showDownTime: this.downTimeCount,
        countdown: null,
        isEnd: false,

        isGuide: 0, //0未显示过引导-下一步 1知道了-已完成引导 2已完成引导
        screenHeight: 0,
        screenWidth: 0,

        timbre: 'W', // 音色默认女声 M  W
        pronunciationType: 0, // 1英式  0美式  默认美式
        playType: 2, // 版本
        linkUrl: ''
      };
    },
    onShow() {
      // this.$refs.popopPower.open();
    },

    onLoad(options) {
      this.getHeight();
      this.showData = JSON.parse(decodeURIComponent(options.params));
      // this.getHeight();
      //  console.log('玩法数据')
      //  console.log(this.showData)
      //  console.log('玩法数据')
      this.getWordversion();
      this.isGuide = uni.getStorageSync('tybyGuide');
      if (!this.isGuide) {
        this.isGuide = 0;
      }
      if (this.isGuide == 0) {
        this.$refs.guideOne.open();
      }
      if (this.isGuide == 1) {
        this.$refs.guideTwo.open();
      }
      if (this.isGuide == 2) {
        this.getShowData();
      }
    },
    methods: {
      getHeight() {
        // 获取系统信息
        const systemInfo = uni.getSystemInfoSync();

        // 获取屏幕高度
        this.screenHeight = systemInfo.windowHeight * 2;
        this.screenWidth = systemInfo.windowWidth * 2;
        // 打印屏幕高度
        //  console.log(this.screenHeight);
      },
      // 初始获取当前组所有单词
      getShowData() {
        let that = this;
        that.nowGroup = that.showData.levelGroup;
        that.nowLevel = that.showData.nowLevel;
        that.waitGroup = that.showData.levelTotalGroup - that.showData.levelGroup;
        that.wordInfo.nowGroup = that.showData.nowGroup;
        that.wordInfo.wordCount = that.showData.wordCount;
        that.wordInfo.nowLevel = that.showData.nowLevel;
        that.wordInfo.roundId = that.showData.roundId;
        that.wordInfo.scheduleCode = that.showData.scheduleCode;

        that.getNoLevelData();

        // // 判断除当前组之外其它组是否还有单词
        // if (that.showData.cos.play1.length == 0 && that.showData.cos.play3.length == 0 && that.showData.cos
        //     .play4.length ==
        //     0) {
        //     that.otherWord = false;
        // } else {
        //     that.otherWord = true;
        //     that.gradScreenWheel = 4;
        // }
        // if (!that.otherWord) {
        //     // 判断这组结束是组还是轮还是关
        //     if (!that.showData.status) {
        //         that.gradScreenWheel = 1;
        //     }
        //     if (that.showData.status && that.showData.nowLevel != that.showData.totalLevel) {
        //         that.gradScreenWheel = 2;
        //     } //当前关数==总关数 代表是最后一关，所以是一轮结束(其它组都为空的时候才可以开启下一轮)
        //     if (that.showData.status && that.showData.nowLevel == that.showData.totalLevel) {
        //         that.gradScreenWheel = 3;
        //     }
        // }
        // if (that.showData.groupEnd) {
        //     that.judgeNewGroup();
        // }
      },
      // 获取当前学员设置的语音版本
      getWordversion() {
        var that = this;
        that.$httpUser
          .get('znyy/course/info', {
            studentCode: that.showData.studentCode
          })
          .then((res) => {
            if (res.data.success) {
              let name = res.data.data.voiceModel.split('#');
              that.pronunciationType = name[1];
              that.timbre = name[2];
              that.playType = name[0];
            } else {
              that.$util.alter(res.data.message);
            }
          });
      },

      getNoLevelData() {
        let that = this;
        that.$httpUser.get('znyy/course/query/fun/words/byType?scheduleCode=' + that.showData.scheduleCode + '&type=2').then((res) => {
          if (res.data.success) {
            if (res.data.data && res.data.data.length != 0) {
              that.showListData = res.data.data;
              that.qIdIndex = 0;
              that.chooseAnswerIndex = -1;
              // 对答案进行操作
              that.wordExecute();
            } else {
              that.endDialog();
              return;
            }
          } else {
            uni.navigateBack({ delta: 1 });
            // uni.redirectTo({
            //     url: '/antiAmnesia/review/funReview?scheduleCode=' + that.showData.scheduleCode
            // })
            return;
          }
        });
      },
      //播放音频
      playWord(word) {
        this.$playVoice(word, false, this.timbre, this.pronunciationType, this.playType, this.showData.studentCode);
      },

      //选择答案
      chooseAnswer(item, index) {
        if (this.isEnd) {
          this.$util.alter('当前玩法已结束');
          return;
        }
        if (this.isRight) {
          return;
        }
        let that = this;
        that.chooseAnswerIndex = index;
        //是否正确
        if (that.showListData[that.qIdIndex].translation == that.showListData[that.qIdIndex].answerList[index]) {
          //  console.log("回答正确")
          that.$playVoice('task_success.mp3', true, that.timbre, that.pronunciationType, that.playType, that.showData.studentCode);
          that.isRight = true;
          that.doItRight();
          return;
        }

        //回答错误
        //  console.log("回答错误")
        that.$playVoice('ino.mp3', true, that.timbre, that.pronunciationType, that.playType, that.showData.studentCode);
        that.isRight = false;
        that.markWrongWord();
      },
      //答对一个单词
      doItRight() {
        let that = this;
        var errHas = that.errorList.some((item) => {
          if (that.showListData[that.qIdIndex].translation == item.translation) {
            return true;
          }
        });
        if (!errHas) {
          that.successList.push(that.showListData[that.qIdIndex]);
        }
        // that.markRightWord();
        that.newMarkRightWord();
        that.nextQuestion();
      },

      // 标记正确单词
      markRightWord() {
        //  console.log("标记正确单词")
        let that = this;
        that.wordInfo.studentWordId = that.showListData[that.qIdIndex].id;
        that.$httpUser.post(`znyy/course/fun/play/word`, that.wordInfo).then((res) => {
          if (!res.data.success) {
            that.$util.alter(res.data.message);
          }
        });
      },

      // 标记正确单词--new
      newMarkRightWord() {
        //  console.log("标记正确单词")
        let that = this;
        that.wordInfo.studentWordId = that.showListData[that.qIdIndex].id;
        that.$httpUser.post(`znyy/course/noLevel/fun/play/word/finish`, that.wordInfo).then((res) => {
          if (!res.data.success) {
            that.$util.alter(res.data.message);
          }
        });
      },

      // 标记错误单词
      markWrongWord() {
        //  console.log("标记错误单词")
        let that = this;
        that.wordInfo.studentWordId = that.showListData[that.qIdIndex].id;
        // that.$httpUser.post("znyy/course/mark/wrong/words", that.wordInfo).then((res) => {
        //     if (res.data.success) {
        // that.markRightWord();
        that.newMarkRightWord();
        var errHas = that.errorList.some((item) => {
          if (that.showListData[that.qIdIndex].word == item.word) {
            return true;
          }
        });
        var succHas = that.successList.some((item) => {
          if (that.showListData[that.qIdIndex].word == item.word) {
            return true;
          }
        });
        if (!errHas && !succHas) {
          that.errorList.push(that.showListData[that.qIdIndex]);
        }

        // 如果倒计时结束就标记玩过
        if (that.chooseAnswerIndex == -1) {
          // that.markRightWord()
          that.newMarkRightWord();
        }
        // } else {
        //     that.$util.alter(res.data.message)
        // }
        // })
      },

      // 下一题
      nextQuestion() {
        let that = this;
        clearInterval(that.countdown);
        that.countdown = null;
        if (that.qIdIndex < that.showListData.length - 1) {
          setTimeout(function () {
            that.qIdIndex++;
            that.chooseAnswerIndex = -1;
            that.wordExecute();
          }, 1000);
        } else {
          //提交趣味复习单词
          setTimeout(function () {
            that.getNoLevelData();
          }, 600);
        }
      },

      // 结束弹窗
      endDialog() {
        let that = this;
        that.isEnd = true;

        if (that.successList.length + that.errorList.length == 0) {
          that.correctRate = 0;
        } else if (that.successList.length != 0 && that.errorList.length == 0) {
          that.correctRate = 100;
        } else {
          that.correctRate = parseInt((that.successList.length * 100) / (that.successList.length + that.errorList.length));
          if (isNaN(that.correctRate)) {
            that.correctRate = 0;
          }
        }

        that.$refs.popopPower.open();
      },

      //题目执行
      wordExecute() {
        let that = this;
        that.downFun();
        that.showDownTime = that.downTimeCount;
        let newArr = [];
        that.isRight = null;
        that.distrubList = that.$util.randomSort(that.$distrubChinese).slice(0, 5);
        let answerListSingle = [];
        let forLen = 3; //干绕项去重
        for (var i = 0; i < forLen; i++) {
          if (that.distrubList[i] == that.showListData[that.qIdIndex].translation) {
            that.distrubList.splice(i, 1);
            forLen++;
          } else {
            answerListSingle.push(that.distrubList[i]);
          }
        }
        answerListSingle.push(that.showListData[that.qIdIndex].translation);
        //  console.log( that.showListData);
        //  console.log( that.qIdIndex);
        that.showListData[that.qIdIndex].answerList = answerListSingle.sort();

        // 读单词
        that.$playVoice(that.showListData[that.qIdIndex].word, false, that.timbre, that.pronunciationType, that.playType, that.showData.studentCode);
      },

      // 是否进入下一组
      judgeNewGroup() {
        this.endDialog();
      },

      refreshWord() {
        this.showListData = []; //当前课程玩法组所有的单词
        this.distrubList = []; //干扰项列表数据
        this.qIdIndex = 0; //当前题目编号
        this.chooseAnswerIndex = -1; //选择第几个答案
        this.successList = []; //正确列表数据存储
        this.errorList = []; //错误列表数据存储
        this.correctRate = 0; //正确率
        this.isRight = null;
        this.wordInfo = {
          roundId: null,
          scheduleCode: null,
          courseCode: null,
          nowGroup: null,
          wordCount: null,
          studentWordId: null,
          play: '2'
        };
        const sc = this.showData.scheduleCode;
        this.$httpUser.get(`znyy/course/query/fun/words?scheduleCode=${sc}`).then((res) => {
          if (res.data.success) {
            this.showData = {}; //当前课程所有数据
            this.showData = res.data.data;
            this.showData.scheduleCode = sc;
            this.getShowData();
            this.closeDialog();
          } else {
            this.$util.alter(res.data.message);
          }
        });
      },

      // 倒计时执行事件
      downFun() {
        let that = this;
        that.showDownTime = that.downTimeCount;
        if (that.countdown == null) {
          that.countdown = setInterval(() => {
            that.showDownTime--;
            if (this.showDownTime < 0) {
              clearInterval(that.countdown);
              that.countdown = null;
              return;
            }
            if (that.showDownTime < 1) {
              that.$playVoice('game_over.mp3', true, that.timbre, that.pronunciationType, that.playType, that.showData.studentCode);
              if (that.chooseAnswerIndex == -1) {
                that.markWrongWord();
              }
              that.nextQuestion();
            }
          }, 1000);
        }
      },

      //关闭弹窗
      closeDialog() {
        //  console.log("复习报告quxiao")
        this.$refs.popopPower.close();
      },
      //查看报告弹框关闭
      closeDialog1() {
        // #ifdef APP-PLUS
        this.backPage();
        // #endif
        this.$refs.popopPower.close();
      },

      // 返回上一页
      backPage() {
        clearInterval(this.countdown);
        this.countdown = null;
        //返回按钮
        uni.navigateBack({ delta: 1 });
        // uni.redirectTo({
        //     url: '/antiAmnesia/review/funReview?scheduleCode=' + this.showData.scheduleCode +
        //         '&merchantCode=' + this.showData.merchantCode
        // })
      },

      async guideNext() {
        this.isGuide = 1;
        await uni.setStorageSync('tybyGuide', this.isGuide);
        this.$refs.guideOne.close();
        this.$refs.guideTwo.open();
      },
      async guideClose() {
        this.isGuide = 2;
        await uni.setStorageSync('tybyGuide', this.isGuide);
        this.$refs.guideTwo.close();
        this.getShowData();
      }
    },
    onUnload() {
      // 页面关闭后销毁实例
      clearInterval(this.countdown);
      this.countdown = null;
    }
  };
</script>

<style>
  page {
    height: 100vh;
    padding: 0;
  }

  .funContent {
    width: 100%;
    padding: 0 30rpx;
    box-sizing: border-box;
    height: 100%;
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_tyby_bg.png') 100% 100% no-repeat;
    background-size: 100% 100%;
    position: relative;
  }

  .interesting_car {
    position: absolute;
    bottom: 100rpx;
    margin-left: -30rpx;
    width: 745rpx;
    height: 480rpx;
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_tyby_car.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  .interesting_title {
    position: absolute;
    bottom: 357rpx;
    left: 87rpx;
    width: 47rpx;
    height: 50rpx;
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_laba2.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  .answerListBox {
    width: 546rpx;
    position: absolute;
    bottom: 235rpx;
    left: 183rpx;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin: 0 auto 0 auto;
  }

  .answerList {
    text-align: center;
    width: 258rpx;
    height: 140rpx;
    line-height: 140rpx;
    background: #fcf6f6;
    border-radius: 19rpx;
    border: 4rpx solid #ffc500;
    margin-top: 15rpx;
    position: relative;
  }

  .answerIcon {
    position: absolute;
    top: 9rpx;
    right: 9rpx;
    width: 36rpx;
    height: 36rpx;
  }

  /* 趣味复习倒计时 */
  .userProgressbg {
    position: absolute;
    top: 39%;
    right: 20%;
    height: 87rpx;
    text-align: center;
  }
  .interesting_countdown {
    font-size: 38rpx;
    line-height: 62rpx;
    color: #6c2401;
  }
  .interesting_countdown_tag {
    font-size: 26rpx;
    color: #6c2401;
  }

  .guide_btn_next {
    position: absolute;
    bottom: 57rpx;
    right: 64rpx;
    width: 269rpx;
    height: 142rpx;
  }

  .guide_btn_close {
    position: absolute;
    bottom: 57rpx;
    right: 64rpx;
    width: 269rpx;
    height: 142rpx;
  }
</style>
