<template>
  <view class="funContent">
    <!-- 趣味复习标题 -->
    <interesting-head :title="titleText" @backPage="backPage()" :hasTitleBg="true" :closeWhite="false" :hasRight="false"></interesting-head>
    <!-- 倒计时 -->
    <pyf-progress :percent="((downTimeCount - showDownTime) * 100) / downTimeCount" :time="!showDownTime || showDownTime < 0 ? 0 : showDownTime"></pyf-progress>
    <view class="interesting_content">
      <!-- 问题 -->
      <view class="interesting_tip">你听到了什么？</view>
      <!-- 语音 -->
      <view class="interesting_title" v-show="showListData[qIdIndex]" @click="playWord(showListData[qIdIndex].wordSyllableAudioUrl, showListData[qIdIndex].splitList)"></view>

      <!-- 答案列表 -->
      <view class="answerListBox" v-show="showListData.length > 0">
        <!-- item && item.length > 10 ? '26rpx' : item && item.length > 10 ? '15rpx' : '' -->
        <view
          class="answerList"
          :class="index != chooseAnswerIndex ? (correctAnswer && correctAnswer == item ? 'answerRight' : 'answerNormal') : isRight ? 'answerRight' : 'answerWrang'"
          v-for="(item, index) in showListData[qIdIndex].answerList"
          :key="index"
          @click="chooseAnswer(item, index)"
        >
          {{ item }}
        </view>
      </view>

      <!-- 下一题 -->
      <!-- isSubmit/本题是否完成提交 --true完成 -->
      <!-- <view :class="isSubmit ? 'nextBtn right' : 'nextBtn disabled'" @click="nextQuestion()">{{ qIdIndex + 1 >= showListData.length ? '提交' : '继续' }}</view> -->
    </view>

    <!--结束弹窗 -->
    <uni-popup ref="popopPower" type="bottom" :maskClick="false" :classBG="''">
      <interesting-dialog
        :conclusionText="'太棒了，成功通关！看下战绩如何吧~'"
        :finishData="[
          { name: '题目数量', data: reportData.topicNum, iconIndex: 0 },
          { name: '正确率', data: (reportData.accuracy || 0).toFixed(1) + '%', iconIndex: 1 }
        ]"
        word="word"
        :errorList="reportData.errorWordList"
        :showData="showData"
        @close="closeDialog"
        @confirm="onceMore"
      ></interesting-dialog>
    </uni-popup>

    <!-- 退出弹框 -->
    <uni-popup ref="popopPowerExit" type="bottom" :maskClick="false" :classBG="''">
      <pyf-dialog-exit @close="closePopopPowerExit" @confirm="submitBackPage()"></pyf-dialog-exit>
    </uni-popup>

    <!-- 引导 -->
    <uni-popup ref="guideOne" type="bottom" :maskClick="true" :classBG="''">
      <view style="position: relative">
        <image style="height: 100vh; width: 100vw" src="https://document.dxznjy.com/course/c06237201c284d22ba89f4f0fb835f79.png"></image>
        <image class="guide_btn_next" @click="guideNext()" src="https://document.dxznjy.com/applet/newInteresting/interesting_guide_next.png"></image>
      </view>
    </uni-popup>
    <uni-popup ref="guideTwo" type="bottom" :maskClick="true" :classBG="''">
      <view style="position: relative">
        <image style="height: 100vh; width: 100vw" src="https://document.dxznjy.com/course/ecc63794738b47a9acf29e69bf98fc52.png"></image>
        <image class="guide_btn_close" @click="guideClose()" src="#"></image>
      </view>
    </uni-popup>
    <!-- 处理所有返回 -->
    <page-container v-if="showPage" :show="showPage" :duration="false" :overlay="false" @afterleave="beforeleave"></page-container>
  </view>
</template>

<script>
  import interestingHead from '../components/interesting-head/pyfInterestingHead.vue';
  import interestingDialog from '../components/interesting-dialog/pyfReview.vue';
  import pyfProgress from '../components/cmd-progress/pyf-progress.vue';
  // import pyfProgress from '../components/cmd-progress/cmd-progress.vue';
  import pyfDialogExit from '../components/interesting-dialog/pyfDialogExit.vue';
  // 一些共同方法
  import { countStudyDuration, submitData, getNoLevelData, getWordversion, getHeight, playWord, resetAudioContext, beforeleave, handleReset } from './pyfUtils';

  export default {
    components: {
      interestingHead,
      interestingDialog,
      pyfProgress,
      pyfDialogExit
    },
    data() {
      return {
        imgHost: getApp().globalData.imguseHost,
        titleText: '听音识词',
        courseCode: '', //课程编号
        scheduleCode: '', //课程进度
        showListData: [
          {
            answerList: []
          }
        ], //当前课程玩法组所有的单词
        showData: {}, //当前课程所有数据
        reportData: {}, // 报告数据

        distrubList: [], //干扰项列表数据
        qIdIndex: null, //当前题目编号

        play: '1', //玩法
        otherWord: false, //除当前玩法其它玩法是否还有单词
        chooseAnswerIndex: -1, //选择第几个答案
        successList: [], //正确列表数据存储
        errorList: [], //错误列表数据存储
        // correctRate: 0, //正确率
        isRight: null,

        correctAnswer: '', // 答错时显示的正确答案
        downTimeCount: 40, // 倒计时默认时间
        showDownTime: this.downTimeCount, // 倒计时显示时间
        countdown: null, // 倒计时定时器

        isEnd: false, //当前玩法是否结束
        isSended: false, //是否发送过请求
        isSubmit: false, //本题是否提交

        isGuide: 0, //0未显示过引导-下一步 1知道了-已完成引导 2已完成引导
        screenHeight: 0,
        screenWidth: 0,
        showPage: true, // 返回保护

        timbre: 'W', // 音色默认女声 M  W
        pronunciationType: 0, // 1英式  0美式  默认美式
        playType: 2, // 版本
        linkUrl: ''
      };
    },
    onLoad(options) {
      this.$util.fetchFile(1);
      this.$util.fetchFile(0);
      // console.log(options, 'options');
      // 获取系统信息
      getHeight(this);
      // 获取当前课程信息
      this.showData = options;

      // 获取当前学员设置的语音版本信息
      getWordversion(this);
      // 获取引导进度
      this.isGuide = uni.getStorageSync('PyfTyscGuide');
      // console.log(this.isGuide, 'this.isGuide');

      if (!this.isGuide) {
        this.isGuide = 0;
      }
      if (this.isGuide == 0) {
        this.$refs.guideOne.open();
      }
      if (this.isGuide == 1) {
        this.$refs.guideTwo.open();
      }
      if (this.isGuide == 2) {
        this.getShowData();
      }
    },
    methods: {
      getShowData() {
        let that = this;

        this.showData.studyStatus = this.showData.studyStatus * 1; // 学习状态： 0-开始学习；1-继续学习；2-再来一轮
        this.showData.passType = 1; // 关卡类型： 1-听音识词；2-拼拼乐；3-连连看；4-规则大闯关
        this.showData.studyStartTime = uni.$u.timeFormat(new Date(), 'yyyy-mm-dd hh:MM:ss'); // 学习开始时间
        // this.showData.studyDuration = uni.getStorageSync('PyfLlkStudyDuration') || 0; // 学习时长
        this.showData.studyDuration = 0; // 学习时长
        // 获取题目数据
        getNoLevelData(this);
      },

      //选择答案
      chooseAnswer(item, index) {
        let that = this;

        if (this.isEnd) {
          this.$util.alter('当前玩法已结束');
          return;
        }
        // 节流
        if (that.isLoading) {
          that.$util.alter('请等待单词加载完成~', 'none', 1000);
          return;
        }
        that.isLoading = true; // 加载中

        // 清空定时器
        clearInterval(that.countdown);
        that.countdown = null;
        // 选择第几个答案
        that.chooseAnswerIndex = index;
        that.isSubmit = true; // 是否提交
        //是否正确
        // console.log(item, index, 'item', that.showListData[that.qIdIndex].wordSyllable);

        if (that.showListData[that.qIdIndex].wordSyllable == item) {
          //  console.log("回答正确")
          that.$playVoice('task_success.mp3', true, that.timbre, that.pronunciationType, that.playType, that.showData.studentCode);
          that.isRight = true;
          // 标记正确单词
          that.markRightWord();
          return;
        }

        //回答错误
        //  console.log("回答错误")
        that.$playVoice('ino.mp3', true, that.timbre, that.pronunciationType, that.playType, that.showData.studentCode);
        that.isRight = false;
        // 标记错误单词
        that.markWrongWord();
      },

      // 标记正确单词--new
      markRightWord() {
        // console.log('标记正确单词');
        let that = this;

        // 正确列表中添加已答对的单词
        let successItem = that.showListData[that.qIdIndex];

        that.successList.push(successItem);
        // 保存答题数据
        that.showData.pdFunReviewStudyWordSaveDtoList[that.qIdIndex].studyStatus = 1; // 是否学习：0-未学 1-已学
        that.showData.pdFunReviewStudyWordSaveDtoList[that.qIdIndex].answerStatus = 1; // 做题对错状态： 0-错；1-正确

        that.nextQuestion(); // 下一题
      },

      // 标记错误单词
      markWrongWord() {
        let that = this;

        // 显示正确答案
        that.showAnswer();

        // 错误列表中添加已答错的单词
        let successItem = that.showListData[that.qIdIndex];
        that.errorList.push(successItem);
        // 保存答题数据
        that.showData.pdFunReviewStudyWordSaveDtoList[that.qIdIndex].studyStatus = 1; // 是否学习：0-未学 1-已学
        that.showData.pdFunReviewStudyWordSaveDtoList[that.qIdIndex].answerStatus = 0; // 做题对错状态： 0-错；1-正确
        // console.log('标记错误单词', that.errorList);

        that.nextQuestion(); // 下一题
      },
      // 显示正确答案
      showAnswer() {
        let that = this;
        that.correctAnswer = that.showListData[that.qIdIndex].wordSyllable;
        // console.log(that.correctAnswer, '正确答案');
      },

      // 下一题
      nextQuestion() {
        let that = this;
        if (!that.isSubmit) {
          return that.$util.alter('请先选择答案', 'none', 1000);
        }
        resetAudioContext(); // 重置音频上下文
        that.isSubmit = false; // 重置提交状态
        if (that.qIdIndex < that.showListData.length - 1) {
          setTimeout(function () {
            that.qIdIndex++;
            that.chooseAnswerIndex = -1;
            that.wordExecute(); // 执行题目
          }, 1000);
        } else {
          // 计算学习时长
          countStudyDuration(that, 2);

          setTimeout(function () {
            //提交趣味复习单词
            that.endDialog();
          }, 600);
        }
      },
      // 结束弹窗
      endDialog() {
        let that = this;
        that.isEnd = true;
        clearInterval(that.countdown);
        that.countdown = null;
        // 计算正确率 --弃用
        // if (that.successList.length + that.errorList.length == 0) {
        //   that.correctRate = 0;
        // } else if (that.successList.length != 0 && that.errorList.length == 0) {
        //   that.correctRate = 100;
        // } else
        //   that.correctRate = ((that.successList.length * 100) / (that.successList.length + that.errorList.length)).toFixed(1);
        //   console.log(that.correctRate, '正确率');
        //   console.log(that.successList.length, that.errorList.length);

        //   if (isNaN(that.correctRate)) {
        //     that.correctRate = 0;
        //   }
        // }
        // 提交答题数据
        submitData(this);
      },

      //题目执行 添加干绕项
      wordExecute() {
        let that = this;
        that.downFun(); // 倒计时

        // 清除正确答案
        that.correctAnswer = null;
        that.isRight = null;
        // 获取干扰项数据
        // that.distrubList = that.$util.randomSort(that.$distrubChinese).slice(0, 5);
        // 如果总数据不足4个，则随机补充干绕项
        if (that.showListData.length <= 4) {
          let wordList = that.$util.randomSort(that.$distrubEnglish).slice(0, 5);
          // console.log(wordList, 'wordList');

          wordList.forEach((item) =>
            that.distrubList.push({
              wordSyllable: item
            })
          );
        } else {
          let showListData = JSON.parse(JSON.stringify(that.showListData));
          that.distrubList = showListData.sort(() => Math.random() - 0.5).slice(0, 5);
        }

        let answerListSingle = [];
        let forLen = 3; //干绕项去重

        // 添加不重复的干扰项 // wordSyllable 改成
        for (var i = 0; i < forLen; i++) {
          if (that.distrubList[i].wordSyllable == that.showListData[that.qIdIndex].wordSyllable) {
            forLen++;
            continue;
          }
          // 不重复则添加
          answerListSingle.push(that.distrubList[i].wordSyllable);
        }
        // 添加正确答案
        answerListSingle.push(that.showListData[that.qIdIndex].wordSyllable);
        console.log(answerListSingle, '添加正确答案干扰项');
        // 随机排序
        // answerListSingle.sort(() => Math.random() - 0.5);
        that.$util.shuffleArray(answerListSingle);
        that.showListData[that.qIdIndex].answerList = answerListSingle;

        // console.log(answerListSingle, '随机');

        // 读单词
        that.playWord(that.showListData[that.qIdIndex].wordSyllableAudioUrl, that.showListData[that.qIdIndex].splitList);
        this.$nextTick(() => {
          that.isLoading = false; // 加载完成
        });
      },
      // 倒计时
      downFun() {
        let that = this;
        // 重置倒计时时间
        that.showDownTime = that.downTimeCount;
        // 倒计时
        if (that.countdown) {
          clearInterval(that.countdown);
          that.countdown = null;
        }
        that.countdown = setInterval(() => {
          that.showDownTime--;

          if (that.showDownTime < 0) {
            clearInterval(that.countdown);
            that.countdown = null;
            return;
          }
          // 播放 结束 音效
          if (that.showDownTime < 1) {
            that.$playVoice('game_over.mp3', true, that.timbre, that.pronunciationType, that.playType, that.showData.studentCode);
            that.isSubmit = true; // 重置提交状态
            that.markWrongWord();
          }
        }, 1000);
      },
      // 查看报告弹框关闭
      closeDialog() {
        this.showPage = false;
        uni.navigateBack({
          delta: 1
        });

        this.$refs.popopPower.close();
      },
      // 查看报告弹框确定/再来一局
      onceMore() {
        // 重置
        handleReset(this, this.showData.passType);
        this.showData.studyStatus = 2; // 学习状态： 0-开始学习；1-继续学习；2-再来一轮
        getNoLevelData(this); // 获取题目数据
        this.$refs.popopPower.close();
      },
      //播放音频
      playWord(word, wordList) {
        playWord(this, word, wordList);
      },
      // 关闭是否退出弹框
      closePopopPowerExit() {
        this.showPage = true;
        this.$refs.popopPowerExit.close();
      },
      // 返回上一页
      backPage() {
        uni.navigateBack({
          delta: 1
        });
      },
      /** 提交答题数据并返回上一页。
       * 提交答题数据并返回上一页。
       * 该方法调用 submitData 函数来处理数据提交。
       */
      submitBackPage() {
        // 计算学习时长
        countStudyDuration(this, 2);
        submitData(this, false); // 提交答题数据并返回上一页
      },
      // 返回
      beforeleave() {
        beforeleave(this);
      },
      // 引导
      async guideNext() {
        this.isGuide = 1;
        await uni.setStorageSync('PyfTyscGuide', this.isGuide);
        this.$refs.guideOne.close();
        this.$refs.guideTwo.open();
      },
      async guideClose() {
        this.isGuide = 2;
        await uni.setStorageSync('PyfTyscGuide', this.isGuide);
        this.$refs.guideTwo.close();
        this.getShowData();
      }
    },

    onUnload() {
      // console.log('--页面关闭后销毁实例--');
      // 页面关闭后销毁实例
      resetAudioContext(); // 重置音频上下文
      // 提交答题数据 /未提交 且 已作答 可以提交
      // if (!this.isSended && (this.errorList.length > 0 || this.successList.length > 0)) submitData(this);
      // 销毁定时器
      // 1.清除倒计时定时器
      clearInterval(this.countdown);
      this.countdown = null;
      // // 2.清除学习时长定时器
      // clearInterval(this.studyDurationTimer);
      // this.studyDurationTimer = null;
      // uni.setStorageSync('PyfLlkStudyDuration', this.showData.studyDuration);
    }
  };
</script>

<style>
  page {
    height: 100vh;
    padding: 0;
  }
</style>
<style lang="scss" scoped>
  .funContent {
    width: 100%;
    padding: 0 30rpx;
    box-sizing: border-box;
    height: 100%;
    // min-height: 1624rpx;

    background: url('https://document.dxznjy.com/course/d3684a6aa1294ce181ba6ea2fe3c986b.png') 100% 100% no-repeat;
    background-size: 100% 100%;
    position: relative;
    overflow: hidden;
  }

  .interesting_content {
    width: 686rpx;
    margin: 0 4rpx;
    height: 82.5vh;
    background: url('https://document.dxznjy.com/course/a51f843f13514c1f8107e65c37ba099e.png') 100% 100% no-repeat;
    background-size: 100% 100%;
    position: absolute;
    bottom: 30rpx;
  }

  .interesting_tip {
    position: absolute;
    top: 3%;
    left: 32rpx;
    width: 626rpx;
    height: 44rpx;
    line-height: 44rpx;
    font-size: 32rpx;
    font-family: Impact, Haettenschweiler, 'Arial Narrow Bold', sans-serif;
    font-weight: bold;
    color: #333333;
  }

  .interesting_title {
    width: 252rpx;
    // height: 156rpx;
    height: 9.5vh;
    min-height: 130rpx;
    background: url('https://document.dxznjy.com/course/eff22d6992674d7f9f5fa15a13e747f3.png') 100% 100% no-repeat;
    background-size: 100% 100%;

    display: grid;
    position: relative;
    z-index: 22;
    place-items: center;
    border-radius: 24rpx;
    // margin: 160rpx auto 100rpx;
    margin: 9vh auto 7.5vh;
  }

  .answerListWhiteText {
    color: white;
    font-size: 35rpx;
    font-weight: bold;
  }

  .answerListText {
    color: #a26a3d;
    font-size: 34rpx;
    font-weight: bold;
  }

  .title_icon {
    top: -90rpx;
    right: -50rpx;
    position: absolute;
    width: 60rpx;
    height: 60rpx;
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_sccg_laba.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  /* 趣味复习倒计时 */
  .userProgressbg {
    position: relative;
    width: 686rpx;
    height: 74rpx;
    margin: 35rpx 4rpx 0;
  }

  .guide_btn_next {
    position: absolute;
    bottom: 60rpx;
    right: 64rpx;
    width: 269rpx;
    height: 142rpx;
  }

  .guide_btn_close {
    position: absolute;
    bottom: 171rpx;
    right: 125rpx;
    width: 312rpx;
    height: 93rpx;
  }

  .answerListBox {
    width: 632rpx;
    height: auto;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    flex-wrap: wrap;
    margin: 0 auto;

    & :nth-child(2n + 1) {
      margin-right: 40rpx;
    }

    & :nth-child(n + 3) {
      margin-top: 60rpx;
    }
  }

  .answerRight {
    background: url('https://document.dxznjy.com/course/11375a5789ea45a3b1afba906e8977ce.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  .answerWrang {
    background: url('https://document.dxznjy.com/course/1d8d0180b6ca41059587f1ca8a81fa2c.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  .answerNormal {
    background: url('https://document.dxznjy.com/course/c7c6ec7f7aa34beea69fc4ebb659e031.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  .answerList {
    flex-shrink: 0;
    position: relative;
    width: 296rpx;
    // height: 304rpx;
    height: 19vh;
    text-align: center;
    font-size: 40rpx;
    // line-height: 220rpx;
    box-sizing: border-box;
    border-radius: 24rpx;
    display: grid;
    place-items: center;
    font-weight: bold;
    color: #444;
    padding: 20rpx;
    word-break: break-word;
    /* 允许单词内换行 */
    overflow-wrap: break-word;
    /* 标准属性，效果类似 */
  }

  .nextBtn {
    width: 632rpx;
    height: 90rpx;
    margin: 0 auto;
    display: grid;
    place-items: center;
    color: #fff;
    font-style: 28rpx;
    position: absolute;
    bottom: 42rpx;
    left: 26rpx;

    &.right {
      background: url('https://document.dxznjy.com/course/1c12f2db0daa4c1d80171e6cb6544ace.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }

    &.disabled {
      background-color: #cccccc;
      color: #666666;
      border-radius: 45rpx;
    }
  }

  @media (min-width: 500px) {
    .interesting_content {
      height: 78vh;
    }

    .interesting_title {
      width: 222rpx;
      margin: 6vh auto 4.5vh;
    }

    .answerList {
      width: 240rpx;
      height: 22vh;
    }
  }
</style>
