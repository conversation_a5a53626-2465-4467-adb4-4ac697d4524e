<template>
	<page-meta :page-style="'overflow:' + (rollShow || isLoading ? 'hidden' : 'visible')"></page-meta>
	<view v-if="isLoading" class="loading-container" @touchmove.prevent="preventTouchMove"
		@touchstart.prevent="preventTouchMove" @touchend.prevent="preventTouchMove">
		<view class="loading-content">
			<!-- <image src="https://document.dxznjy.com/course/loading.gif" class="loading-image" mode="widthFix"></image> -->
			<text class="loading-text">加载中，请稍候...</text>
		</view>
	</view>
	<view v-if="shopdetail != null" class="content positionRelative" :style="'height:' + height">
		<view class="top-tips" v-if="showTips">
			<image src="https://document.dxznjy.com/course/f46f51e47cb540e196735d174623f5e4.png" class="w100"
				mode="widthFix"></image>
			<view class="text-con">下载鼎校甄选APP 了解更多信息</view>
			<button @click="handleOpenDownload" class="btn_download">扫码下载</button>
		</view>
		<!-- 标题设置 -->
		<u-navbar bgColor="rgba(0,0,0,0)" @leftClick="leftClicks" :borderBottom="false">
			<!-- 左侧自定义返回按钮 -->
			<template #left>
				<div class="custom-left-btn">
					<u-icon name="arrow-left" color="#FFFFFF" size="30rpx"></u-icon>
				</div>
			</template>

			<!-- 右侧自定义图标（适配胶囊位置） -->
			<!-- <template #right>
				<div :style="{ paddingRight: rightPadding + 'px' }">
					<div class="custom-right-icon" @click="shareFriend">
						<u-icon name="share-square" color="#FFFFFF" size="30rpx"></u-icon>
					</div>
				</div>
			</template> -->
		</u-navbar>
		<!-- 页面 -->
		<view class="course-detail pb-200">
			<!-- 吸顶导航 -->
			<view v-show="showStickyNav" class="sticky-nav" :class="{ 'sticky-nav-show': showStickyNav}"
				:style="{ paddingTop: paddingTop + 'px' }">
				<view class="search-container">
					<view class="" @click="leftClicks">
						<u-icon name="arrow-left" size="45"></u-icon>
					</view>
					<view class="search-wrapper">
						<view class="search-css" @click="searchFocus">
							<view class="" style="margin-left: 24rpx">
								<u-icon name="search" class="search-image" color="#8A8A8A" size="40"></u-icon>
							</view>
							<view class="search-input" type="text" readonly>请输入</view>
						</view>
					</view>
					<view class="" @click="shareFriend">
						<u-icon name="share-square" size="45"></u-icon>
					</view>
				</view>
				<view class="sticky-nav-container">
					<view v-for="item in anchorList" :key="item.id" class="sticky-nav-item"
						:class="{ 'active': activeAnchor === item.id }" @click="scrollToAnchor(item.id, item.name)">
						{{ item.name }}
					</view>
				</view>
			</view>

			<u-back-top :right='60' :bottom='200' :scroll-top="scrollTop"
				icon="https://document.dxznjy.com/course/c7013ce0316846aabcf3abff80566d51.png"
				:icon-style="iconStyle"></u-back-top>

			<view id="goods" class="relative" v-if="shopdetail != null && shopdetail.goodsCarouselList != null">
				<swiper :indicator-dots="false" :autoplay="shopdetail.goodsVideoUrl ? false : true" circular="true"
					duration="500" indicator-active-color="#ffffff" class="swiper" @change="swiperChange"
					style="height: 840rpx;">
					<!-- goodsVideoUrl -->
					<block v-if="shopdetail.goodsVideoUrl">
						<swiper-item class="section_item" style="height: 840rpx;">
							<polyv-player id="polyv-player-id" @loadedmetadata="bindloadedmetadata" :autoplay="false"
								:playerId="playerIdcont" :vid="shopdetail.goodsVideoUrl" :width="width" height="100%"
								:ts="ts" :sign="sign"></polyv-player>
							<!-- <video
                id="myVideo"
                class="video_css_content"
                :src="shopdetail.goodsVideoUrl"
                :poster="shopdetail.goodsVideoUrl + '?x-oss-process=video/snapshot,t_0,f_jpg'"
                controls></video> -->
						</swiper-item>
					</block>
					<block v-for="(item, index) in shopdetail.goodsCarouselList" :key="index">
						<swiper-item class="section_items">
							<image v-if="shopdetail.goodsType == 1" :src="item.picUrl" class="swiperImage"
								mode="widthFix" style="background-color: #000;">
							</image>
							<image v-else :src="item.picUrl" class="swiperImage" mode="widthFix"
								style="background-color: #000;"></image>
						</swiper-item>
					</block>
				</swiper>
				<!-- 页码指示器：只有图片数量大于1时显示，视频页不显示 -->
				<view v-if="getImageCount() > 1 && isShowingImage()" class="swiper-indicator">
					{{ getImageIndex() }}/{{ getImageCount() }}
				</view>

				<!-- 弹幕效果购买轮播 -->
				<view class="swiper-barrage" v-if="getImageCount() && isShowingImage()">
					<view class="barrage-container">
						<swiper display-multiple-items="3" circular vertical autoplay interval="3000" duration="1000"
							@change="swiperChangeindex" style="height: 150rpx;">
							<swiper-item v-for="(item, index) in buyDataList" :key="index">
								<view class=""
									:style="{ backgroundColor: `rgba(33, 35, 30, ${getOpacity(index)})`, borderRadius: '8rpx', padding: ' 5rpx 10rpx'}">
									<text class="" style="font-size: 24rpx;">{{ item.nickName }}</text>
									<text class=""
										style="margin-left: 10rpx; font-size: 24rpx;">{{ formatItem(item.createdTime, true) + '下单' }}</text>
								</view>
							</swiper-item>
						</swiper>
					</view>
				</view>
			</view>

			<!-- 下半部分 -->
			<div class="product_content_main">
				<view class="" style="padding: 30rpx 30rpx 0; background-color: #ffffff;">
					<view class="flex-s">
						<view class="flex-dir-row flex-y-c">
							<view class="c-red">
								<!-- parentMemberType 5 家长会员 -->
								<text class="f-24 color_red_css"
									v-if="identityType == 4 || parentMemberType == 5 || shopdetail.goodsType == 2">
									{{ shopdetail.goodsType == 2 ? '体验价' : '会员价' }}
								</text>
								<text class="f-48 lh-64 color_red_css ml-5 bold">
									<text class="f-24">￥</text>
									<!-- 体验价是会员价 -->
									<text v-if="shopdetail.goodsType == 2">{{ shopdetail.goodsVipPrice }}</text>
									<text v-else-if="isDyy">
										{{alreadyLogin ? (
                            shopdetail.goodsSpecPriceList[0].specLevelTwo * addPrices +
                            shopdetail.goodsSpecPriceList[1].specLevelTwo * (priceDetail[periodIndex].extArr.CENTER / 100)
                          ).toFixed(2)
                        : shopdetail.goodsOriginalPrice
                    }}
									</text>
									<text
										v-else>{{ identityType == 4 || parentMemberType == 5 ? shopdetail.goodsVipPrice : shopdetail.goodsOriginalPrice }}</text>
								</text>
							</view>
							<!-- 没开通显示的会员价 -->
							<view v-if="identityType != 4 && parentMemberType != 5 && shopdetail.goodsType != 2"
								class="f-24 c-ff member_back">
								<text>会员价</text>
								<text v-if="isDyy">
									￥{{
                    alreadyLogin
                      ? (
                          shopdetail.goodsSpecPriceList[0].specLevelTwo * addPrices +
                          shopdetail.goodsSpecPriceList[1].specLevelTwo * (priceDetail[periodIndex].extArr.CENTER / 100)
                        ).toFixed(2)
                      : shopdetail.goodsVipPrice
                  }}起
								</text>
								<text v-else>￥{{ shopdetail.goodsVipPrice }}起</text>
							</view>

							<!-- 是会员且是正式课 -->
							<view v-if="(identityType == 4 || parentMemberType == 5 )&& shopdetail.goodsType != 2"
								class="c-99 ml-15 f-26">
								￥
								<text
									class="line-t">{{ shopdetail == null ? '??' : shopdetail.goodsOriginalPrice }}</text>
							</view>
						</view>
						<!--<view v-if="shopdetail&&shopdetail.meetingCate==0">
				            <view v-if="userinfo.identityType!=0" class="benefits" @click="openBenefits">查看收益</view>
				        </view> -->
						<view class="">
							<view class="f-24" style="color: #7C7C7C;">{{ formatSalesCount(shopdetail.goodsSales) }}
							</view>
						</view>
					</view>

					<view class="flex-s f-28 c-33 mt-8">
						<view class="goods_name_css twolist bold w686 lh-42">
							<span class="tags" style="">{{shopdetail.goodsTagOne}}</span>
							{{ shopdetail == null ? '' : shopdetail.goodsName }}
						</view>
						<!-- <view class="f-26 c-99">{{shopdetail == null? "1000+":shopdetail.studyNumber}}+人付款</view> -->
					</view>
				</view>
				<!-- <view v-if="shopdetail.coupon.id" class="coupon_label_css w686 mt-24 fontWeight f-24">
          <view class="coupon_item radius-8">
            <text class="display_block coupon_header_css">券</text>
            <text v-if="shopdetail.coupon.couponHasCondition == 1" class="display_block plr1416">
              满{{ shopdetail.coupon.couponCondition }}减{{ shopdetail.coupon.couponDiscount }}
            </text>
            <text v-if="shopdetail.coupon.couponHasCondition == 0" class="display_block plr1416">无门槛减{{ shopdetail.coupon.couponDiscount }}</text>
          </view>
          <view v-if="shopdetail.goodsCommission" class="coupon_item radius-8 ml-15">
            <text class="display_block plr-15">预估返{{ shopdetail.goodsCommission }}元</text>
          </view>
        </view> -->

				<!-- 标签 -->
				<view class="flexbox" v-if="shopdetail.goodsTagOne"
					style="padding: 16rpx 30rpx; background-color: #ffffff;">
					<view class="fontWeight lh-36">
						<!-- <text v-if="shopdetail.goodsTagOne"
							class="label_css radius-8 mr-8 display_inline">{{ shopdetail.goodsTagOne }}</text> -->
						<text v-if="shopdetail.goodsTagTwo"
							class="label_css radius-8 mr-8 ">{{ shopdetail.goodsTagTwo }}</text>
						<text v-if="shopdetail.goodsTagThree"
							class="label_css radius-8 mr-8 ">{{ shopdetail.goodsTagThree }}</text>
					</view>
				</view>
				<!-- 开通会员 -->
				<!-- <view v-if="!identityType" class="member_content_css mt-24 f-24 fontWeight">
          <text class="display_inline ml-15 member_text">成为超级会员，尊享会员价，开通领超值权益~</text>
          <button @click="getBecomMember" class="display_block f-24 c-ff member_botton ml-50 plr-12 ptb-10 radius-8 lh-32" hover-class="none">立即开通</button>
        </view> -->

				<!-- 会员 -->
				<view v-if="!parentMemberType" class="member_content_css mt-16 f-24 fontWeight">
					<text class="display_inline  member_text">成为家长会员，尊享会员价，开通领超值权益~</text>
					<button @click="getBecomMember" class="display_block f-24 c-ff member_botton radius-8"
						hover-class="none">立即开通</button>
				</view>

				<!-- 拼团区域 -->
				<view style="padding: 0 30rpx;">
					<JoinGroup v-if="isGroupBuyGood && tempPropObj.isJoinGroup && groupInstanceId"
						:needUpdate="needUpdateGroupStatus" :groupInstanceId="tempPropObj.groupInstanceId"
						:isJoinGroup="isJoinGroup" :isInGroup="isInGroup" :goodsId="id"
						@getGroupUserList="handleHideFooter" @joinGroup="handleJoinGroup"
						@handleUpdate="handleUpdateGroupStatus"></JoinGroup>
				</view>
				<!-- 参与拼团入口 -->
				<view class="group-enter"
					v-if="!groupFromActivity && shopdetail && shopdetail.groupActivityId != 'null'"
					@click="getMoreGroups('1')">
					<image class="enter-pic"
						src="https://document.dxznjy.com/course/ab05d6abd1f54e83856e97416cd91cb3.png" mode="aspectFit">
					</image>
				</view>

				<!-- 参与拼团列表 -->
				<view class="moduleStyle"
					v-if="groupFromActivity && shopdetail && shopdetail.groupActivityId != 'null'">
					<view class="flex-s">
						<view class="f-28">
							<!-- <image class="wh24 display_block mr-8"
								src="https://document.dxznjy.com/course/0c9fa08112a44df49fa48023fb5b0aad.png"></image> -->
							<text class="fontWeight">已有{{ accessGroupToTal }}人开团</text>
						</view>
						<view class="flex-a-c" @click="getMoreGroups('2')">
							<text class="mr-10 f-26 c-66 primary_color">立即参与</text>
							<uni-icons type="right" size="16" color="#999"></uni-icons>
						</view>
					</view>
					<view class="mt-40" v-if="accessGroupList.length">
						<view v-if="accessGroupList.length <= 2" v-for="(item, index) in accessGroupList" :key="index">
							<view class="flex-s">
								<view class="flex-a-c">
									<view class="avatars">
										<image class="avatar" v-if="item.nickName1" :src="item.headPortrait1 || avaUrl"
											mode="aspectFit"></image>
										<image class="avatar" v-if="item.nickName2" :src="item.headPortrait2 || avaUrl"
											mode="aspectFit"></image>
										<image class="avatar" v-if="item.nickName3" :src="item.headPortrait3 || avaUrl"
											mode="aspectFit"></image>
									</view>
									<view class="mr-15 c-66 f-26 user_name">
										{{ item.nickName1 }}{{ item.nickName2 ? `、${item.nickName2}` : '' }}{{ item.nickName3 ? `、${item.nickName3}` : '' }}
									</view>
								</view>
								<view class="flex-a-c">
									<view class="countdown">
										<text class="c-99 f-24 count_down_css">拼团即将结束</text>
										<text
											class="c-99 f-24 count_down_css">{{ `${item.countdownObj.hours}:${item.countdownObj.minutes}:${item.countdownObj.seconds}` }}</text>
									</view>
									<view class="ml-20 c-66 f-24 join-btn" @click.stop="handlePopupJoinGroup(item)">立即参团
									</view>
								</view>
							</view>
						</view>
						<swiper v-if="accessGroupList.length > 2" display-multiple-items="2" circular vertical autoplay
							interval="3000" duration="1000" class="swiperBox" style="height: 240rpx">
							<swiper-item v-for="(item, index) in accessGroupList" :key="index" class="swiper-item">
								<view class="flex-s">
									<view class="flex-a-c">
										<view class="avatars">
											<image class="avatar" v-if="item.nickName1"
												:src="item.headPortrait1 || avaUrl" mode="aspectFit"></image>
											<image class="avatar" v-if="item.nickName2"
												:src="item.headPortrait2 || avaUrl" mode="aspectFit"></image>
											<image class="avatar" v-if="item.nickName3"
												:src="item.headPortrait3 || avaUrl" mode="aspectFit"></image>
										</view>
										<view class="mr-15 c-66 f-26 user_name">
											{{ item.nickName1 }}{{ item.nickName2 ? `、${item.nickName2}` : '' }}{{ item.nickName3 ? `、${item.nickName3}` : '' }}
										</view>
									</view>
									<view class="flex-a-c">
										<view class="countdown">
											<text class="c-99 f-24 count_down_css">拼团即将结束</text>
											<text
												class="c-99 f-24 count_down_css">{{ `${item.countdownObj.hours}:${item.countdownObj.minutes}:${item.countdownObj.seconds}` }}</text>
										</view>
										<view class="ml-20 c-66 f-24 join-btn" @click.stop="handlePopupJoinGroup(item)">
											立即参团</view>
									</view>
								</view>
							</swiper-item>
						</swiper>
					</view>
				</view>

				<!-- 购买记录轮播滚动 -->
				<!-- <view class="moduleStyle" v-else>
					<view class="flex-s">
						<view class="f-28">
							<text class="fontWeight">{{ shopdetail.goodsSales }}人已购买</text>
						</view>
						<view class="flex-a-c" @click="seeMore">
							<text class="mr-10 f-26 c-66">查看更多</text>
							<uni-icons type="right" size="16" color="#999"></uni-icons>
						</view>
					</view>
					<view class="mt-40 " v-if="buyDataList.length">
						<view v-if="buyDataList.length <= 2" v-for="(item, index) in buyDataList" :key="index">
							<view class="flex-s">
								<view class="flex-a-c">
									<image :src="item.headPortrait || avaUrl" class="head-img"></image>
									<view class="mr-15 c-66 f-26 user_name">{{ item.nickName }}</view>
								</view>
								<view class="flex-a-c">
									<text
										class="c-99 f-24 time_height_css">{{ formatItem(item.createdTime, true) + '下了一单' }}</text>
									<view class="ml-20 c-66 shopping" @click.stop="handleBuySubject()">去下单</view>
								</view>
							</view>
						</view>
						<swiper v-if="buyDataList.length > 2" display-multiple-items="2" circular vertical autoplay
							interval="3000" duration="1000" class="swiperBox" style="height: 240rpx">
							<swiper-item v-for="(item, index) in buyDataList" :key="index" class="swiper-item">
								<view class="flex-s">
									<view class="flex-a-c">
										<image :src="item.headPortrait || avaUrl" class="head-img"></image>
										<view class="mr-15 c-66 f-26 user_name">{{ item.nickName }}</view>
									</view>
									<view class="flex-a-c">
										<text
											class="c-99 f-24 time_height_css">{{ formatItem(item.createdTime, true) + '下了一单' }}</text>
										<view class="ml-20 c-66 shopping" @click.stop="handleBuySubject()">去下单</view>
									</view>
								</view>
							</swiper-item>
						</swiper>
					</view>
				</view> -->

				<!-- 商品评价 -->
				<view id="goods-evaluation" class="moduleStyle">
					<view class="flex-s ">
						<view class="f-28">
							<!-- <image class="wh24 display_block mr-8"
								src="https://document.dxznjy.com/course/8a4b7c1e25564b72a42f1b1ccc07ba42.png"></image> -->
							<text class="fontWeight">商品评价（{{ evaluatelist.totalCount || 0 }}）</text>
						</view>
						<view class="flex-a-c" @click="goUrl">
							<text class="mr-10 f-26 c-66">{{ evaluatelist.totalCount > 0 ? '查看更多' : '暂无评价' }}</text>
							<uni-icons type="right" size="16" color="#999"></uni-icons>
						</view>
					</view>
					<view class="mt-40 " v-if="evaluatelist.list.length > 0">
						<view class="flex-s">
							<view class="flex-a-c">
								<image :src="evaluatelist.list[0].headPortrait || avaUrl" class="head-img"></image>
								<view class="mr-20 c-55 f-28">{{ evaluatelist.list[0].userName }}</view>
								<view class="golden">
									{{ evaTypeList.find((i) => i.key == evaluatelist.list[0].identityType).value }}
								</view>
								<!-- <view class="golden" v-if="evaluatelist.list[0].identityType != 0 && evaluatelist.list[0].identityType != 4">
                  {{ evaluatelist.list[0].identityType != 4 ? (evaluatelist.list[0].identityType == 5 ? 'B2俱乐部' : 'B3俱乐部') : 'B1俱乐部' }}
                </view>
                <view class="golden" v-if="evaluatelist.list[0].identityType == 0 || evaluatelist.list[0].identityType == 4">
                  {{ evaluatelist.list[0].identityType == 0 ? '家长' : '超人' }}
                </view> -->
								<!-- <view class="golden">超人俱乐部B级</view> -->
							</view>
							<uni-rate disabled :value="evaluatelist.list[0].evaluateGrade" active-color="#FD9B2A"
								class="mt-20" size="20" />
						</view>
						<view class="mt-15 f-24 c-55 pl-8 evalue-content twolist">
							<span v-if="firstimgs(evaluatelist.list[0].photoUrl)">{{'[图片]'}}</span>
							{{ evaluatelist.list[0].evaluateContent }}
						</view>
					</view>
					<view class="mt-40" v-if="evaluatelist.list.length > 1">
						<view class="flex-s">
							<view class="flex-a-c">
								<image :src="evaluatelist.list[1].headPortrait || avaUrl" class="head-img"></image>
								<view class="mr-20 c-55 f-28">{{ evaluatelist.list[1].userName }}</view>
								<view class="golden">
									{{ evaTypeList.find((i) => i.key == evaluatelist.list[1].identityType).value }}
								</view>
								<!-- <view class="golden" v-if="evaluatelist.list[1].identityType != 0 && evaluatelist.list[1].identityType != 4">
                  {{ evaluatelist.list[1].identityType != 4 ? (evaluatelist.list[1].identityType == 5 ? 'B2俱乐部' : 'B3俱乐部') : 'B1俱乐部' }}
                </view>
                <view class="golden" v-if="evaluatelist.list[1].identityType == 0 || evaluatelist.list[1].identityType == 4">
                  {{ evaluatelist.list[1].identityType == 0 ? '家长' : '超人' }}
                </view> -->
							</view>
							<uni-rate disabled :value="evaluatelist.list[1].evaluateGrade" active-color="#FD9B2A"
								class="mt-20" size="20" />
						</view>
						<view class="mt-15 f-24 c-55 pl-8 evalue-content twolist">
							<span v-if="firstimgs(evaluatelist.list[1].photoUrl)">{{'[图片]'}}</span>
							{{ evaluatelist.list[1].evaluateContent }}
						</view>
					</view>
				</view>
				<!-- 交付课 录播课 学习资料 -->
				<view
					v-if="CurriculumCodeArr.includes(shopdetail.curriculumCode) || (shopdetail && shopdetail.goodsType && [2, 3, 4].includes(shopdetail.goodsType))"
					class="moduleStyle">
					<view class="flex-s ">
						<view class="f-28">
							<!-- <image class="wh24 display_block mr-8"
								src="https://document.dxznjy.com/course/20fbc71836df4b4aa5701450e0021fe6.png"></image> -->
							<text class="fontWeight">学习资料</text>
						</view>
						<view class="flex-a-c" @click="goProfile(shopdetail)">
							<text class="mr-10 f-26 c-66">查看</text>
							<uni-icons type="right" size="16" color="#999"></uni-icons>
						</view>
					</view>
				</view>

				<!-- 商品详情 -->
				<view v-if="shopdetail.goodsType !== 4" id="goods-detail" class="moduleStyles">
					<view class="moduleTitleStyles">
						<!-- <image class="wh24 ml-25 mr-8"
							src="https://document.dxznjy.com/course/ad3dff63abb6421a9eda1e7b411ea2ff.png"></image> -->
						<text class="f-28 c-22 bold" style="">商品详情</text>
					</view>
					<view class="bg-ff pb-30">
						<view class="rich-text-container">
							<rich-text  v-if="shopdetail && shopdetail.meetingCate == 0"
								:nodes="shopdetail != null ? getRichText(shopdetail.courseInfo) : ''"></rich-text>
							<rich-text v-else
								:nodes="shopdetail != null ? getRichText(shopdetail.goodsDesc) : ''"></rich-text>
						</view>
					</view>
				</view>

				<view v-else id="goods-detail" class="pb-150 moduleStyles">
					<view class="bg-ff w686 product_details" style="padding: 0 32rpx;">
						<u-tabs :list="userList" lineWidth="60" lineHeight="19"
							:activeStyle="{ color: '#333333', fontWeight: 'bold' }" :inactiveStyle="{
                color: '#5A5A5A ',
                transform: 'scale(1)',
                fontSize: '28rpx'
              }" itemStyle="padding-bottom:20rpx;width:300rpx;" :lineColor="`url(${lineBg}) 100% 110%`"
							@click="tabsClick"></u-tabs>
					</view>
					<view class="bg-ff  pb-30" v-if="tabName == '课程简介'">
						<view>
							<rich-text v-if="shopdetail && shopdetail.meetingCate == 0"
								:nodes="shopdetail != null ? getRichText(shopdetail.courseInfo) : ''"></rich-text>
							<rich-text v-else
								:nodes="shopdetail != null ? getRichText(shopdetail.goodsDesc) : ''"></rich-text>
						</view>
					</view>
					<view v-else class="bg-ff  pb-20">
						<courseCatalog v-for="(item, index) in shopdetail.goodsCatalogueList" :key="item.id"
							:type="crouseType" :index="index" :item="item" :courseId="goodsId" :userCode="userCode"
							@changeDown="changeDown"></courseCatalog>
					</view>
				</view>

				<!-- 常见问题 -->
				<view class="moduleStyle" v-if="introduce.fieldOne.name">
					<view class="moduleTitleStyle">
						<span
							style="font-size: 28rpx; font-weight: bold;color: #333333;">{{introduce.fieldOne.name}}</span>
						<!-- <image :src="imgHost + 'course/a8b539eb60924a5fbcee94ee4e9c2fe8.png'" mode=""
							style="width: 28rpx; height: 28rpx; margin-left: 8rpx;"></image> -->
					</view>
					<view class="" style="margin-top: 24rpx;">
						<!-- <text></text> -->
						<rich-text :nodes="introduce.fieldOne.content"></rich-text>
					</view>
				</view>
				<view class="moduleStyle" v-if="introduce.fieldTwo.name">
					<view class="moduleTitleStyle">
						<span
							style="font-size: 28rpx; font-weight: bold;color: #333333;">{{introduce.fieldTwo.name}}</span>
						<!-- <image :src="imgHost + 'course/a8b539eb60924a5fbcee94ee4e9c2fe8.png'" mode=""
							style="width: 28rpx; height: 28rpx; margin-left: 8rpx;"></image> -->
					</view>
					<view class="" style="margin-top: 24rpx;">
						<rich-text :nodes="introduce.fieldTwo.content"></rich-text>
					</view>
				</view>

				<!-- 购买须知 -->
				<view class="moduleStyle" v-if="introduce.fieldThree.name">
					<view class="moduleTitleStyle">
						<span
							style="font-size: 28rpx; font-weight: bold;color: #333333;">{{introduce.fieldThree.name}}</span>
						<!-- <image :src="imgHost + 'course/a8b539eb60924a5fbcee94ee4e9c2fe8.png'" mode=""
							style="width: 28rpx; height: 28rpx; margin-left: 8rpx;"></image> -->
					</view>
					<view class="" style="margin-top: 24rpx;">
						<rich-text :nodes="introduce.fieldThree.content"></rich-text>
					</view>
				</view>

				<!-- 相关推荐 -->
				<view id="related-recommend" class="moduleStyle" v-if="courseData.length > 0">
					<view class="moduleTitleStyle">
						<span style="font-size: 28rpx; font-weight: bold;color: #333333;">相关推荐</span>
						<!-- <image :src="imgHost + 'course/a8b539eb60924a5fbcee94ee4e9c2fe8.png'" mode=""
							style="width: 28rpx; height: 28rpx; margin-left: 8rpx;"></image> -->
					</view>
					<view class="course-container" style="margin-top: 24rpx;">
						<courseList v-for="(item, index) in courseData" :key="index" :item="item" :index="index"
							:width="'326rpx'"
							@click="skintap('Coursedetails/productDetils?id=' + item.goodsId, item.goodsId)">
						</courseList>
					</view>
					<!-- <view class="" style="height: 100rpx;">

					</view> -->
				</view>
			</div>
		</view>

		<!-- 领券按钮固定定位区域 -->
		<view class="fixed_coupon_area"
			v-if="availableList.length >= 1 && !isDyy && !isGroupBuyGood && !groupHideFooter">
			<!-- <button class="display_block get-coupons" @click="getCoupons" hover-class="none">领券</button> -->
			<view class="fixed_coupon" @click="getCoupons">
				<view class="">
					您有可领取的优惠券
				</view>
				<view class="" style="display: flex;">
					<span>去领取</span>
					<u-icon name="arrow-right" color="#FC6600"></u-icon>
				</view>
			</view>
		</view>

		<!-- 底部栏 -->
		<view class="fixed_b flex" v-if="!groupHideFooter" style="justify-content: space-around;">
			<!-- #ifdef MP-WEIXIN -->
			<!-- <button class="plr-20 padding_left" open-type="contact" hover-class="none">
        <image class="wh40" src="https://document.dxznjy.com/course/c40968f952914189ab801e1abd52a6d8.png" mode=""></image>
        <view class="f-24 c-55 ml-10">客服</view>
      </button> -->
			<view class="" style="display: flex;">
				<button class="mr-45" @tap="$noMultipleClicks(collectChange, shopdetail.whetherCollect ? 2 : 1)"
					hover-class="none">
					<!--  -->
					<image v-if="shopdetail.whetherCollect" class="wh40"
						src="https://document.dxznjy.com/course/b154d6ebc67d458b872ed6148a52774a.png" mode=""></image>
					<image v-else class="wh40"
						src="https://document.dxznjy.com/course/7fc5232bb51b406f97e91814e3bb3b45.png" mode=""></image>
					<view class="f-24 c-55">收藏</view>
				</button>
				<navigator @click="customerService" url="plugin://qiyukf/chat">
					<button class="mr-45 " hover-class="none">
						<image class="wh40"
							src="https://document.dxznjy.com/course/c40968f952914189ab801e1abd52a6d8.png" mode="">
						</image>
						<view class="f-24 c-55">客服</view>
					</button>
				</navigator>
				<button class="mr-45" hover-class="none" @click="shareFriend">
					<image class="wh40" src="https://document.dxznjy.com/course/a890e444730b42bab717a47a3037c54d.png"
						mode=""></image>
					<view class="f-24 c-55">分享</view>
				</button>
			</view>

			<!--  @click="sharePoster" -->
			<!--<button class="pr-20" hover-class="none" open-type="share">
			    <image  class="wh40 mb-8" :src="imgHost+'dxSelect/three/icon/tab_lj.png'"  mode=""></image>
				<view class="f-24 c-55 ml-10">链接分享</view>
			</button> -->
			<!-- #endif -->

			<!-- #ifdef APP-PLUS -->
			<button class="plr-20 flex-c" hover-class="none" @tap="contactApp">
				<!-- <image src="/static/user/icon_sp_kf.png" class="box-36"></image> -->
				<uni-icons custom-prefix="iconfont" type="icon-headset" size="18" color="#000"></uni-icons>
				<text class="f-28 c-99 ml-10">客服</text>
			</button>

			<button class="plr-20 flex-c" hover-class="none" @tap="shareApp">
				<uni-icons custom-prefix="iconfont" type="icon-share" size="16" color="#000"></uni-icons>
				<text class="f-28 c-99 ml-10">分享</text>
			</button>
			<view class="buy_s mlr-20 f-32" @tap="buySubject()">立即购买</view>
			<!-- #endif -->
			<!-- <view class="addcart ml-20 f-32" @tap="openCart()">加入购物车</view> -->

			<!-- #ifdef MP-WEIXIN -->
			<view class="fixed_button f-28">
				<template v-if="!isGroupBuyGood || !groupFromActivity">
					<!-- <view class="button_left f-28 c-ff" @click="shareFriend">分享好友</view> -->
					<!-- <button class="button_left  f-28 c-ff"  hover-class="none"  open-type="share">分享好友</button> -->
					<view class="button_rights" @tap="buySubject()">
						<span>￥
							<text v-if="shopdetail.goodsType == 2">{{ shopdetail.goodsVipPrice }}</text>
							<text v-else-if="isDyy">
								{{alreadyLogin ? (
						        shopdetail.goodsSpecPriceList[0].specLevelTwo * addPrices +
						        shopdetail.goodsSpecPriceList[1].specLevelTwo * (priceDetail[periodIndex].extArr.CENTER / 100)
						      ).toFixed(2)
						    : shopdetail.goodsOriginalPrice
						}}
							</text>
							<text
								v-else>{{ identityType == 4 || parentMemberType == 5 ? shopdetail.goodsVipPrice : shopdetail.goodsOriginalPrice }}</text>
							</text>
						</span>
						<span style="margin-left: 42rpx;">立即购买</span>
					</view>
				</template>
				<template v-else>
					<view class="button_left f-28 c-ff" @tap="buySubject(1)">单独购买</view>

					<view v-if="notStart" class="button_right button_disabled">未开始</view>
					<view v-else-if="hasEnd" class="button_right button_disabled">已结束</view>
					<view v-else class="button_right" @tap="buySubject(2)">
						{{ isGroupLeader && !isJoinGroup ? '发起拼团' : '马上参团' }}
					</view>
				</template>
			</view>
			<!-- #endif -->
		</view>
		<!-- 购物车 -->
		<!-- <navigator class="cartIcon bg-ff flex-col" hover-class="none" url="/pages/index/cart">
			<image src="/static/user/cart.png" class="box-50"></image>
			<text class="f-24 c-m">购物车</text>
		</navigator> -->
		<!-- 数量加减 -->
		<!-- <u-popup :show="show" mode="bottom" closeable='closeable' @close="show=false" @open="open">
            <view class="plr-30 pt-60 pb-40">
                <view class="flex-dir-row">
                    <view class="box-180 radius-10">
                        <image :src="shopdetail ? shopdetail.bannerImages[0] :''" class="wh100"></image>
                    </view>
                    <view class="flex-box ml-20">
                        <view class="f-32 bold">{{ shopdetail != null ?shopdetail.courseName:"" }}</view>
                        <view class="f-32 pt-15 color_tangerine">￥<text
                                class="bold f-38">{{ shopdetail != null ?shopdetail.memberPrice:"" }}</text></view>
                        <view class="flex pt-15">
                            <text class="c-66 f-32">数量</text>
                            <u-number-box v-model="value" :min="1" :max="10" @change="valChange"></u-number-box>
                        </view>
                    </view>
                </view>
               <view class="pt-75 flex">
                    <view class="addcart flex-box ml-20 f-32" @tap="addCart(shopdetail)">加入购物车</view>
                    <view class="buy_s flex-box f-32" @tap="buy()">确认购买</view>
                    <view class="add_cart flex-box f-32" @tap="addCart(shopdetail)">加入购物车</view>
                </view>
            </view>
        </u-popup> -->
		<!-- 加入购物车成功提示 -->
		<!-- <u-popup :show="notifyShow" @close="notifyShow=false" mode="top" :overlay="false" :round="20"
            bgColor="transparent" customStyle="border-radius: 16rpx">
            <view class="t-c plr-50">
                <view class="bg-ff ptb-25 radius-50 flex-c">
                    <uni-icons type="checkbox-filled" size="28" color="#95C14B"></uni-icons><text
                        class="ml-15 f-34">加入购物车成功</text>
                </view>
            </view>
        </u-popup> -->
		<!-- 查看收益（该功能暂未使用） -->
		<uni-popup ref="popup" type="center" @change="changePopup">
			<view class="dialogBG">
				<view class="reviewCard_box positionRelative">
					<view class="cartoom_image">
						<image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
					</view>
					<view class="review_close" @click="closeDialog">
						<uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
					</view>
					<view class="reviewCard">
						<view class="bold t-c f-34">本单预估收益</view>
						<view class="c-fea f-30 t-c mt-12" v-if="shopdetail.courseLabel == 2">注：以产品会员价计算收益</view>
						<view class="c-fea f-30 t-c mt-12" v-if="shopdetail.courseLabel == 1">将体验课分享给好友并成功下单可获得该收益
						</view>

						<view class="f-30 mt-40 t-c" v-if="shopdetail.courseLabel == 1">
							<view>推荐人分润：100%</view>
							<view class="mt-20">推荐人预估收益：{{ shopdetail.memberPrice }}元</view>
						</view>
						<view class="flex-s f-30" v-if="shopdetail.courseLabel == 2">
							<view class="content mt-40">
								<view v-if="userinfo.identityType == 1 || userinfo.identityType == 3">
									学习超人分润：{{ amount.memberRite }}%</view>
								<view class="mt-20" v-if="userinfo.identityType == 2 || userinfo.identityType == 3">
									{{ amount.level != 4 ? (amount.level == 5 ? 'B2' : 'B3') : 'B1' }}俱乐部分润：{{ amount.merchantRite }}%
								</view>
							</view>
							<view class="content mt-40">
								<view v-if="userinfo.identityType == 1 || userinfo.identityType == 3">
									预估收益：{{ amount.memberPrice }}元</view>
								<view class="mt-20" v-if="userinfo.identityType == 2 || userinfo.identityType == 3">
									预估收益：{{ amount.merchantPrice }}元</view>
							</view>
						</view>
						<view class="t-c mt-40 flex-c">
							<view class="review_btn" @click="closeDialog">确定</view>
						</view>
						<view class="flex-a-c flex-x-e mt-20">
							<view class="f-24 c-99">最终解释权归鼎校甄选所有</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		<u-toast ref="uToast"></u-toast>

		<!-- 交付课（体验课）和录播课购买 -->
		<uni-popup ref="experiencePopup" type="bottom" @change="changeExperienceDialog" :mask-click="true"
			@maskClick="experiencePopupClose">
			<view class="content-popup">
				<view class="content_top_popup">
					<view class="icon-clear" @click="closeExperienceDialog">
						<view class="order_title">订单支付</view>

						<!-- <uni-icons type="clear" size="30" color="#B1B1B1"></uni-icons> -->
					</view>
					<view class="product_content_css pt-35">
						<view class="product_info">
							<view class="radius-15">
								<image :src="shopdetail.goodsPicUrl" class="couser_image"></image>
							</view>
							<view class="product_right">
								<!-- <view class="f-32 bold mb-20">{{ shopdetail.courseName }}</view> -->
								<view>
									<view class="f-24 price_tangerine">
										<view class="">
											<text class="price_title">{{ isBuyGroup ? '拼团价' : '实付价' }}</text>
											<text class="price_icon">￥</text>
											<text class="price_css"
												v-if="materialInfo.actualPayment || materialInfo.actualPayment === 0">
												{{ !isBuyGroup ? materialInfo.actualPayment : calcGroupPrice }}
											</text>
										</view>
										<!-- <text class="bold f-30 c-fea">{{ shopdetail.originalPrice }}</text> -->
										<text class="original_price_css">
											<text class="original_title">原价￥</text>
											<text>{{ materialInfo.goodsOriginalPrice }}</text>
										</text>
									</view>
									<view class="number_plus_css">
										<uni-number-box class="no-input" :min="1" :max="1" v-model="value"
											@change="changeValue" />
									</view>
									<!-- <view v-if="userinfo.identityType==0"class="amount">实付款：</view>
									<view v-if="userinfo.identityType!=0"class="amount">实付款：{{}}</view> -->
								</view>
							</view>
						</view>

						<view v-if="couponList.length > 0 && !isGroupBuyGood" class="flexbox coupon_content">
							<view class="coupon_left_css">
								<text class="fontWeight f-28">优惠券</text>
								<text v-if="couponInfo && couponInfo.couponId" class="coupon_botton f-24">已选择优惠</text>
							</view>
							<view class="coupon_right_css" @tap="selectCoupon">
								<text v-if="couponInfo && couponInfo.couponId" class="coupon_title">
									<text
										v-if="couponInfo.couponHasCondition == 1">满{{ couponInfo.couponCondition }}减{{ couponInfo.couponDiscount }}</text>
									<text v-else>无门槛减{{ couponInfo.couponDiscount }}</text>
								</text>
								<text v-else class="coupon_title">请选择优惠</text>
								<u-icon style="display: inline-block" name="arrow-right" color="#555555"
									size="24"></u-icon>
							</view>
						</view>
					</view>
				</view>
				<view class="bg-ff">
					<view class="f-28 radius-15 plr-30">
						<!-- <view class="c-55 mt-35 fontWeight f-28">填写信息</view> -->
						<form id="#nform">
							<view class="information">
								<view style="width: 240rpx; margin-left: 16rpx;" class="c-00">
									<!-- <span class="redtext">*</span> -->
									{{ shopdetail && shopdetail.meetingCate == 0 ? '家长信息：' : '家长信息：' }}
								</view>
								<view class="phone-input">
									<input type="number" disabled placeholder-class="placeholder-style"
										v-model="parentMobile" @blur="changeParentEx()" name="number"
										:focus="parentFocuse"
										:placeholder="shopdetail && shopdetail.meetingCate == 0 ? '请输入家长联系方式' : '请输入家长联系方式'"
										class="input c-55" maxlength="11" />
								</view>
								<!-- <view v-if="parentMobile" @click="changeFouce('parentFocuse')">
									<image src="https://document.dxznjy.com/dxSelect/image/edit_icon.png"
										style="width: 30upx; height: 30upx"></image>
								</view> -->
							</view>
							<view class="informations">
								<view class="" style="display: flex;">
									<view style="width: 260rpx" class="c-00">
										<span class="redtext">*</span>
										{{ shopdetail && shopdetail.meetingCate == 0 ? '学员姓名：' : '学员姓名：' }}
									</view>
									<!-- 体验课录播课逻辑 -->
									<view class="">
										<!-- <picker v-if="shopdetail.goodsType != 2" class="btnchange" :disabled="showFalse"
											@change="bindPickerChange" @click="getStudentPopu" :value="studentIndex"
											:range="studentList" range-key="realName" name="grade"> -->
										<span class="c-55" v-if="shopdetail.goodsType != 2" @click="getStudentPopu">
											{{ studentIndex >= 0 ? studentList[studentIndex].realName : '请输入学员姓名' }}
										</span>
										<!-- </picker> -->
										<input v-else type="text" :maxlength="12" placeholder-class="placeholder-style"
											v-model="trialclassStudent" name="trialname" placeholder="请输入学员姓名"
											class="input c-55" />
									</view>
								</view>
								<!-- studentList -->
								<!-- 学员选择 -->
								<view class="" v-if="studentList.length > 0 && shopdetail.goodsType != 2">
									<view class="status-buttons-container">
										<view v-for="(item, index) in studentList" :key="index" class="status-button"
											:class="{ 'status-button-active': selectedStatusIndex === index }"
											@click="bindPickerChanges(index)">
											{{ item.realName }}
										</view>
										<view class="status-button" v-if="studentList.length < 3 "
											@click="getStudentPopus">
											<span>+</span>
										</view>
									</view>
								</view>
							</view>

							<!-- <view class="information">
								<view style="width: 260rpx" class="c-00">
									<span class="redtext">*</span>
									推荐人姓名：
								</view>
								<view class="phone-input">
									<input type="text" placeholder-class="placeholder-style"
										v-model="recommendInfo.name" name="trialname" :disabled="showRecommend"
										:focus="referrerFocuse" placeholder="请输入推荐人姓名" class="input c-55" />
								</view>
								<view v-if="!showRecommend" @click="changeFouce('referrerFocuse')">
									<image src="https://document.dxznjy.com/dxSelect/image/edit_icon.png"
										style="width: 30upx; height: 30upx"></image>
								</view>
							</view> -->
							<view class="informations">
								<view style="" class="c-00">
									<span class="redtext">*</span>
									推荐人联系方式：
								</view>
								<view class="" style="display: flex; justify-content: start; margin-top: 24rpx;">
									<view class="" style="width: 320rpx;  display: flex; align-items: center;">
										<u-icon name="phone" size="32"></u-icon>
										<view class="" style="margin-left: 20rpx;">
											<input type="text" placeholder-class="placeholder-style"
												v-model="recommendInfo.mobile" :disabled="showRecommend"
												name="trialname" @blur="referrerFocuses" placeholder="必填,请填写真实号码" />
										</view>
									</view>
									<view class="" style="margin-left: 100rpx; display: flex;   align-items: center;"
										v-if="referrerName !== '' || recommendInfo.name">
										<image
											src="https://document.dxznjy.com/course/4bc9a09c50db4dfcac177851879c50f4.png"
											mode="" style="width: 32rpx; height: 32rpx;"></image>
										<span
											style="margin-left: 24rpx;">{{referrerName ? referrerName :recommendInfo.name}}</span>

									</view>
								</view>
								<!-- 						<view v-if="!showRecommend" @click="changeFouce('referrerFocuse')">
									<image src="https://document.dxznjy.com/dxSelect/image/edit_icon.png"
										style="width: 30upx; height: 30upx"></image>
								</view> -->
							</view>
							<!-- recommendInfo.mobile -->
							<view class="font14 tipText pb-30">
								<!-- <image src="https://document.dxznjy.com/dxSelect/three/icon/prompt-icon.png"
									style="width: 30upx; height: 30upx"></image> -->
								<u-icon name="error-circle-fill" color="#60BA91"></u-icon>
								<text class="ml-20 c-66" style="color: #60BA91;">无推荐人时该项填写无</text>
							</view>
							<!-- <view class="information" v-if="!(couponInfo && couponInfo.couponId)"> -->
							<view class="information" v-if="!isGroupBuyGood">
								<view style="width: 260rpx" class="c-00">
									<span class="redtext"></span>
									兑换码：
								</view>
								<view class="phone-input">
									<input placeholder-class="placeholder-style" v-model="redeemCode" name="redeemCode"
										placeholder="请输入兑换码" class="c-55 redeemCode-input" auto-blur
										@blur="handleCodeBlur()" style="width: 380rpx" />
								</view>
								<view class="codelose" v-if="redeemCode && redeemCode.length > 0"
									@click="handleClearCode()">
									<uni-icons type="clear" size="24" color="#B1B1B1"></uni-icons>
								</view>
							</view>
							<!-- <view class="font14 tipText pb-30">
                <image src="https://document.dxznjy.com/dxSelect/three/icon/prompt-icon.png" style="width: 30upx; height: 30upx"></image>
                <text class="ml-20 c-66">兑换码和优惠券不可同时使用</text>
              </view> -->
							<view class="ptb-15 mt-30 f-28">订单备注</view>
							<view class="">
								<u--textarea autoHeight :maxlength="200" count="" placeholder="请输入"
									@input="inputtext"></u--textarea>
								<!-- <textarea placeholder="请输入" :maxlength="200" placeholder-class="placeholder-style"
									@input="inputtext" class="remark f-30"></textarea>
								<text class="orderRemark">{{ this.remark.length }}/200</text> -->
							</view>
							<view class="" style="height: 40rpx;">

							</view>
						</form>
					</view>
					<!-- <view class="plr-30 pb-30 bg-ff radius-15 mtb-30"></view> -->
					<view class="flex mt-50 pb-30" style="padding: 0 30rpx;">
						<!-- <view v-if="shopdetail&&shopdetail.meetingCate==0" class="paybtn f-32 c-ff"  @click="coursePay(2)">立即支付
					    <text class="ml-10">￥{{materialInfo.actualPayment}}</text></view> -->
						<view class="paybtn f-32 c-ff" @click="coursePay(2)">
							立即支付
							<text class="ml-10">￥{{ isBuyGroup ? calcGroupPrice : materialInfo.actualPayment }}</text>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>

		<!-- 实物 交付课正式课 通用商品 时间类型商品 -->
		<uni-popup ref="materialObjectPopup" type="bottom" @change="changeMaterialDialog" :mask-click="true"
			@maskClick="materialObjectPopupClose">
			<view class="content-popup">
				<view class="content_top_popup" :style="shopdetail.goodsType == 1 ? 'margin-bottom:8rpx' : ''">
					<view class="icon-clear" @click="closeMaterialDialog">
						<view class="order_title">订单支付</view>
						<!-- <uni-icons type="clear" size="30" color="#B1B1B1"></uni-icons> -->
					</view>
					<!-- addressInfo -->
					<view v-if="shopdetail.goodsType == 1">
						<view v-if="addressInfo && addressInfo.addressId" @click="getAddress">
							<view class="f-28 c-55 mtb-15">配送至：</view>
							<view class="f-28 c-55 lh-44">
								{{ addressInfo.cityName }}{{ addressInfo.provinceName }}{{ addressInfo.districtName }}{{ addressInfo.address }}
							</view>
							<view class="f-28 c-55 lh-42 mt-15">
								<text>{{ addressInfo.consigneeName }}</text>
								<text class="tet_css">{{ addressInfo.consigneePhone }}</text>
							</view>
						</view>
						<view v-else class="ptb-55" @click="getAddress">请选择收货地址</view>
					</view>
				</view>
				<view class="bg-ff material_content">
					<view class="product_content_css">
						<view class="product_info">
							<view class="radius-15">
								<image :src="materialInfo.goodsPicUrl" class="couser_image"></image>
							</view>
							<view class="product_right">
								<!-- <view class="f-32 bold mb-20">{{ shopdetail.courseName }}</view> -->
								<view>
									<view class="f-24 price_tangerine">
										<view class="">
											<text class="price_title">{{ isBuyGroup ? '拼团价' : '实付价' }}</text>
											<text class="price_icon">￥</text>
											<text class="price_css"
												v-if="(materialInfo.actualPayment && !isDyy) || (materialInfo.actualPayment === 0 && !isDyy)">
												{{ !isBuyGroup ? materialInfo.actualPayment : calcGroupPrice }}
											</text>
											<text class="price_css" v-else-if="isDyy">
												{{
											  (
											    shopdetail.goodsSpecPriceList[0].specLevelTwo * addPrices +
											    shopdetail.goodsSpecPriceList[1].specLevelTwo * (priceDetail[periodIndex].extArr.CENTER / 100)
											  ).toFixed(2)
											}}
											</text>
										</view>
										<!-- <text class="bold f-30 c-fea">{{ shopdetail.originalPrice }}</text> -->
										<text class="original_price_css">
											<text class="original_title">原价￥</text>
											<text v-if="isDyy">
												{{
                          (
                            shopdetail.goodsSpecPriceList[0].specLevelTwo * addPrices +
                            shopdetail.goodsSpecPriceList[1].specLevelTwo * (priceDetail[periodIndex].extArr.CENTER / 100)
                          ).toFixed(2)
                        }}
											</text>
											<text
												v-else>{{ (materialInfo.goodsOriginalPrice * ([1, 8, 9].includes(shopdetail.goodsType) ? buyNum : 1)).toFixed(2) }}</text>
										</text>
									</view>
									<view class="number_plus_css">
										<uni-number-box class="no-input" v-if="shopdetail.goodsType == 3" :min="1"
											:max="1" v-model="value" @change="changeValue" />
										<uni-number-box v-else :min="1" :max="500" v-model="value"
											@change="changeValue" />
									</view>
									<!-- <view v-if="userinfo.identityType==0"class="amount">实付款：</view>
									<view v-if="userinfo.identityType!=0"class="amount">实付款：{{}}</view> -->
								</view>
								<!-- <view></view> -->
							</view>
						</view>
						<!-- 通用商品可用优惠券 时间类型商品 -->
						<view class="flexbox coupon_content"
							v-if="couponList.length > 0 && (changeSpecLevelOneValue > 0 || this.shopdetail.goodsType == 8 || this.shopdetail.goodsType == 8) && !isGroupBuyGood">
							<view class="coupon_left_css">
								<text class="fontWeight f-28">优惠券</text>
								<text v-if="couponInfo && couponInfo.couponId" class="coupon_botton f-24">已选择优惠</text>
							</view>
							<view class="coupon_right_css" @tap="selectCoupon">
								<text v-if="couponInfo && couponInfo.couponId" class="coupon_title">
									<text
										v-if="couponInfo.couponHasCondition == 1">满{{ couponInfo.couponCondition }}减{{ couponInfo.couponDiscount }}</text>
									<text v-else>无门槛减{{ couponInfo.couponDiscount }}</text>
								</text>
								<text v-else class="coupon_title">请选择优惠</text>
								<u-icon style="display: inline-block" name="arrow-right" color="#555555"
									size="24"></u-icon>
							</view>
						</view>
						<!-- <view class="f-30 mt-30 flex-s bg-ff radius-15 p-30">
							<view class="">数量</view>
							<uni-number-box :min="1" :max="500" v-model="value" @change="changeValue"/>
						</view> -->
					</view>
					<view v-if="isDyy" style="margin-top: 40rpx;">
						<view class="f-24 c-33">课程（课时）</view>
						<view class="flex-a-c c-55 f-28 mt-16" v-if="haveHourSelfVisible">
							<view v-if="specLevelTwoFalse2" class="specLevelTwo_css f-22 c-ff radius-5">
								{{ shopdetail.goodsSpecPriceList[0].specLevelTwo }}
							</view>
							<uni-number-box v-else :min="0" :max="999" @change="(val) => changeDyySelfCourseValue(val)"
								v-model="shopdetail.goodsSpecPriceList[0].specLevelTwo" />
							<view v-if="!isGroupBuyGood || isBuyAlone" @click="selfCourseSaveAndModify" class="ml-20">
								{{ specLevelTwoFalse2 ? '修改' : '保存' }}
							</view>
							<view class="ml-20">价格：{{ addPrices }}</view>
						</view>
						<view class="flex-a-c c-55 f-28 mt-16" v-else>
							<view v-if="specLevelTwoFalse2" class="specLevelTwo_css f-22 c-ff radius-5">
								{{ shopdetail.goodsSpecPriceList[0].specLevelTwo }}
							</view>
							<uni-number-box v-else :min="minCount" :max="999"
								@change="(val) => changeDyySelfCourseValue2(val)"
								v-model="shopdetail.goodsSpecPriceList[0].specLevelTwo" />
							<view v-if="!isGroupBuyGood || isBuyAlone" @click="selfCourseSaveAndModify" class="ml-20">
								{{ specLevelTwoFalse2 ? '修改' : '保存' }}
							</view>
							<view class="ml-20">价格：{{ addPrices }}</view>
						</view>
						<view class="f-24 c-33">交付课程（课时）</view>
						<view class="flex-a-c c-55 f-28 mt-16" v-if="haveHourSelfVisible">
							<!-- <view class="specLevelTwo_css f-22 c-ff radius-5">
                {{ shopdetail.goodsSpecPriceList[1].specLevelTwo }}
              </view> -->
							<view v-if="specLevelTwoFalse" class="specLevelTwo_css f-22 c-ff radius-5">
								{{ shopdetail.goodsSpecPriceList[1].specLevelTwo }}
							</view>
							<uni-number-box v-else :min="1" :max="999" @change="(val) => changeDyyCourseValue(val)"
								v-model="shopdetail.goodsSpecPriceList[1].specLevelTwo" />
							<view v-if="!isGroupBuyGood || isBuyAlone" :disabled="maxSpecLevelTwoValue == 0"
								@click="deliverCourseSaveAndModify" class="ml-20">
								{{ specLevelTwoFalse ? '修改' : '保存' }}
							</view>
							<view class="ml-20">价格：{{ priceDetail[periodIndex].extArr.CENTER / 100 }}</view>
						</view>
						<view class="flex-a-c c-55 f-28 mt-16" v-else>
							<!-- <view class="specLevelTwo_css f-22 c-ff radius-5">
                {{ shopdetail.goodsSpecPriceList[1].specLevelTwo }}
              </view> -->
							<view v-if="specLevelTwoFalse" class="specLevelTwo_css f-22 c-ff radius-5">
								{{ shopdetail.goodsSpecPriceList[1].specLevelTwo }}
							</view>
							<uni-number-box v-else :min="1" :max="shopdetail.goodsSpecPriceList[0].specLevelTwo"
								@change="(val) => changeDyyCourseValue(val)"
								v-model="shopdetail.goodsSpecPriceList[1].specLevelTwo" />
							<view v-if="!isGroupBuyGood || isBuyAlone" :disabled="maxSpecLevelTwoValue == 0"
								@click="deliverCourseSaveAndModify" class="ml-20">
								{{ specLevelTwoFalse ? '修改' : '保存' }}
							</view>
							<view class="ml-20">价格：{{ priceDetail[periodIndex].extArr.CENTER / 100 }}</view>
						</view>
					</view>
					<view v-else-if="shopdetail.goodsType == 3 && !isDyy">
						<!-- goodsSpecPriceList -->
						<view v-for="spec in shopdetail.goodsSpecPriceList" :key="spec.id">
							<view v-if="spec.specLevelOne == '自有'" class="mt-24">
								<view class="f-24 c-33">课程（课时）</view>
								<view v-if="SecondBuy" class="flex-a-c c-55 f-28 mt-16">
									<!-- <view class="specLevelTwo_css gray_background f-22 c-ff radius-5">{{ specLevelOneValue }}</view>
                  <view class="ml-20">价格：0</view> -->
									<view v-if="specLevelTwoFalse1" class="specLevelTwo_css f-22 c-ff radius-5">
										{{ changeSpecLevelOneValue }}
									</view>
									<uni-number-box v-else :min="isUseSecondBuyNewRule ? 0 : minSpecLevelOneValue"
										:max="maxSpecLevelOneValue"
										:step="isUseSecondBuyNewRule ? 1 : maxSpecLevelOneValue"
										@change="(val) => changeSelfCourseValue(val, spec)"
										v-model="changeSpecLevelOneValue" />
									<!-- 单独购买  -->
									<view v-if="!isGroupBuyGood || isBuyAlone"
										:disabled="minSpecLevelOneValue == -1 || isUseSecondBuyNewRule"
										v-show="minSpecLevelOneValue != -1 || isUseSecondBuyNewRule"
										@click="saveAndModify()" class="ml-20">
										{{ specLevelTwoFalse1 ? '修改' : '保存' }}
									</view>
									<view class="ml-20" v-if="(couponInfo && couponInfo.couponId) || realRedeemCode">
										价格：{{ calculateInfo.finalPrice >= 0 ? calculateInfo.finalPrice : '' }}</view>
									<view class="ml-20" v-else>
										价格：{{
                      (identityType == 4 || parentMemberType == 5) && !isGroupBuyGood
                        ? ((spec.goodsVipPrice / spec.specLevelTwo) * changeSpecLevelOneValue).toFixed(2)
                        : ((spec.goodsOriginalPrice / spec.specLevelTwo) * changeSpecLevelOneValue).toFixed(2)
                    }}
									</view>
								</view>
								<view v-else class="flex-a-c c-55 f-28 mt-16">
									<view class="specLevelTwo_css f-22 c-ff radius-10">{{ spec.specLevelTwo }}</view>

									<view class="ml-20" v-if="(couponInfo && couponInfo.couponId) || realRedeemCode">
										价格：{{ calculateInfo.finalPrice >= 0 ? calculateInfo.finalPrice : '' }}</view>

									<view class="ml-20" v-else>
										价格：{{ (identityType == 4 || parentMemberType == 5) && !isGroupBuyGood ? spec.goodsVipPrice.toFixed(2) : spec.goodsOriginalPrice.toFixed(2) }}
									</view>
								</view>
							</view>
							<view v-if="spec.specLevelOne == '交付'" class="mt-24">
								<view class="f-24 c-33">交付课程（课时）</view>
								<view class="flex-a-c c-55 f-28 mt-16">
									<view v-if="specLevelTwoFalse" class="specLevelTwo_css f-22 c-ff radius-5">
										{{ specLevelTwoValue }}
									</view>
									<uni-number-box v-else :min="isUseSecondBuyNewRule ? 1 : minSpecLevelTwoValue"
										:max="showNum ? maxSecondSpecLevelTwoValue : maxSpecLevelTwoValue"
										@change="(val) => changeCourseValue(val, spec)" v-model="specLevelTwoValue" />
									<view v-if="!isGroupBuyGood || isBuyAlone" :disabled="maxSpecLevelTwoValue == 0"
										@click="deliverSaveAndModify" class="ml-20">
										{{ specLevelTwoFalse ? '修改' : '保存' }}
									</view>

									<view class="ml-20">
										价格：{{
                      (identityType == 4 || parentMemberType == 5) && !isGroupBuyGood
                        ? ((spec.goodsVipPrice / spec.specLevelTwo) * specLevelTwoValue).toFixed(2)
                        : ((spec.goodsOriginalPrice / spec.specLevelTwo) * specLevelTwoValue).toFixed(2)
                    }}
									</view>
								</view>
							</view>
						</view>
					</view>
					<!-- 课程数量 -->
					<view v-else class="mt-12 c-55 f-28">
						<view class="mt-35">
							<view>{{ materialInfo.specTypeOne }}</view>
							<view>
								<text v-for="(info, index) in materialInfo.specNameOne" :key="index"
									@click="getSelectName('One', info)"
									:class="['mt-25 mr-25 item_css', { active_css: info == selectSpecNameOne }]">
									{{ info }}
								</text>
							</view>
						</view>
						<view class="mt-35">
							<view>{{ materialInfo.specTypeTwo }}</view>
							<view>
								<text v-for="(info, index) in materialInfo.specNameTwo" :key="index"
									@click="getSelectName('Two', info)"
									:class="['mt-25 mr-25 item_css', { active_css: info == selectSpecNameTwo }]">
									{{ info }}
								</text>
							</view>
						</view>
					</view>
					<!-- 正式课 -->
					<view v-if="shopdetail.goodsType != 1" class="f-28 radius-15">
						<!-- <view class="c-55 mt-35 fontWeight f-28">填写信息</view> -->
						<form id="#nform">
							<view class="information">
								<view style="width: 240rpx; margin-left: 16rpx;" class="c-00">
									<!-- <span class="redtext">*</span> -->
									{{ shopdetail && shopdetail.meetingCate == 0 ? '家长信息：' : '家长信息：' }}
								</view>
								<view class="phone-input">
									<input type="number" placeholder-class="placeholder-style"
										:disabled="!isShowChangePhoneNumber" v-model="parentMobile" name="number"
										:focus="parentFocuse"
										:placeholder="shopdetail && shopdetail.meetingCate == 0 ? '请输入家长联系方式' : '请输入家长联系方式'"
										class="input c-55" maxlength="11" @input.native="changeParent()" />
								</view>
								<view v-if="parentMobile && isShowChangePhoneNumber"
									@click="changeFouce('parentFocuse')">
									<image src="https://document.dxznjy.com/dxSelect/image/edit_icon.png"
										style="width: 30upx; height: 30upx"></image>
								</view>
							</view>
							<view class="informations">
								<view class="" style=" display: flex;">
									<view style="width: 260rpx" class="c-00">
										<span class="redtext">*</span>
										{{ shopdetail && shopdetail.meetingCate == 0 ? '选择学员：' : '选择学员：' }}
									</view>

									<!-- 学员选择 -->
									<view class="">
										<!-- <picker class="btnchange" :disabled="showFalse" @click="getStudentPopu"
											@change="bindPickerChange" :value="studentIndex" :range="studentList"
											range-key="realName" name="grade">
											<view class="c-55">
												{{ studentIndex >= 0 ? studentList[studentIndex].realName : '未选择' }}
											</view>
										</picker> -->

										<span
											@click="getStudentPopu">{{ studentIndex >= 0 ? studentList[studentIndex].realName : '未选择' }}</span>
										<!-- <input type="text" placeholder-class="placeholder-style" v-model="trialclassStudent" name="trialname" :placeholder="(shopdetail&&shopdetail.meetingCate==0)?'请输入学员姓名':'请输入参会人姓名'" class="input c-55" /> -->
									</view>
								</view>
								<!-- 学员选择 -->
								<view class="" v-if="studentList.length > 0 ">
									<view class="status-buttons-container">
										<view v-for="(item, index) in studentList" :key="index" class="status-button"
											:class="{ 'status-button-active': selectedStatusIndex === index }"
											@click="bindPickerChanges(index)">
											{{ item.realName }}
										</view>
										<view class="status-button" v-if="studentList.length < 3 "
											@click="getStudentPopus">
											<span>+</span>
										</view>
									</view>
								</view>
								<!-- studentList -->
							</view>
							<view class="information" v-if="isDyy">
								<view style="width: 260rpx" class="c-00">
									<span class="redtext">*</span>
									{{ '学段' }}
								</view>
								<view class="phone-input">
									<picker class="btnchange" @change="periodPickerChange" :value="periodIndex"
										:range="periodList" range-key="name" name="value">
										<view class="c-55">
											{{ periodIndex >= 0 ? periodList[periodIndex].name : '请输入学段' }}
										</view>
									</picker>
								</view>
							</view>
							<!-- 实物商品 通用商品 -->
							<template
								v-if="(shopdetail.goodsType != 1 && shopdetail.goodsType != 8) || !isHideRecommend">
								<!-- <view class="information">
									<view style="width: 260rpx" class="c-00">
										<span class="redtext">*</span>
										推荐人姓名：
									</view>
									<view class="phone-input">
										<input type="text" placeholder-class="placeholder-style"
											v-model="recommendInfo.name" name="trialname" :disabled="showRecommend"
											:focus="referrerFocuse" placeholder="请输入推荐人姓名" class="input c-55" />
									</view>
									<view v-if="!recommendInfo.name && identityType != 4 && parentMemberType != 5"
										@click="changeFouce('referrerFocuse')">
										<image src="https://document.dxznjy.com/dxSelect/image/edit_icon.png"
											style="width: 30upx; height: 30upx"></image>
									</view>
								</view> -->
								<view class="informations">
									<view style="" class="c-00">
										<span class="redtext">*</span>
										推荐人联系方式：
									</view>
									<view class="" style="display: flex; justify-content: start; margin-top: 24rpx;">
										<view class="" style="width: 320rpx;  display: flex; align-items: center;">
											<u-icon name="phone" size="32"></u-icon>
											<view class="" style="margin-left: 20rpx;">
												<input type="text" placeholder-class="placeholder-style"
													v-model="recommendInfo.mobile" name="trialname"
													:disabled="showRecommend" @blur="referrerFocuses"
													placeholder="必填,请填写真实号码" />
											</view>
										</view>
										<view class=""
											style="margin-left: 100rpx; display: flex;   align-items: center;"
											v-if="referrerName !== '' || recommendInfo.name">
											<image
												src="https://document.dxznjy.com/course/4bc9a09c50db4dfcac177851879c50f4.png"
												mode="" style="width: 32rpx; height: 32rpx;"></image>
											<span
												style="margin-left: 24rpx;">{{referrerName ? referrerName : recommendInfo.name}}</span>
										</view>
									</view>

									<!-- <view v-if="!recommendInfo.mobile && identityType != 4 && parentMemberType != 5"
										@click="changeFouce('referrerFocuse')">
										<image src="https://document.dxznjy.com/dxSelect/image/edit_icon.png"
											style="width: 30upx; height: 30upx"></image>
									</view> -->
								</view>
								<view class="font14 tipText pb-30" v-if="!isDyy">
									<!-- <image src="https://document.dxznjy.com/dxSelect/three/icon/prompt-icon.png"
										style="width: 30upx; height: 30upx"></image> -->
									<u-icon name="error-circle-fill" color="#60BA91"></u-icon>
									<text class="ml-20 c-66" style="color: #60BA91;">无推荐人时该项填写无</text>
								</view>
							</template>
							<!-- <view class="information" v-if="!(couponInfo && couponInfo.couponId)"> -->
							<view class="information" v-if="!isGroupBuyGood && !isDyy">
								<view style="width: 260rpx" class="c-00">
									<span class="redtext"></span>
									兑换码：
								</view>
								<view class="phone-input">
									<input placeholder-class="placeholder-style" v-model="redeemCode" name="redeemCode"
										placeholder="请输入兑换码" class="c-55 redeemCode-input" auto-blur
										@blur="handleCodeBlur()" style="width: 380rpx" />
								</view>
								<view class="codelose" v-if="redeemCode && redeemCode.length > 0"
									@click="handleClearCode()">
									<uni-icons type="clear" size="24" color="#B1B1B1"></uni-icons>
								</view>
							</view>
							<!-- <view class="font14 tipText pb-30">
                <image src="https://document.dxznjy.com/dxSelect/three/icon/prompt-icon.png" style="width: 30upx; height: 30upx"></image>
                <text class="ml-20 c-66">兑换码和优惠券不可同时使用</text>
              </view> -->
							<view class="ptb-15 mt-30 f-28">订单备注：</view>
							<view class="">
								<!-- <input type="text" :maxlength="200" placeholder="请输入" @input="inputtext"/> -->
								<u--textarea autoHeight :maxlength="200" count="" placeholder="请输入"
									@input="inputtext"></u--textarea>
								<!-- <textarea placeholder="请输入" :maxlength="200" placeholder-class="placeholder-style"
									@input="inputtext" class="remarks f-30"></textarea> -->
								<!-- <text class="orderRemark">{{ this.remark.length }}/200</text> -->
							</view>
							<view class="" style="height: 40rpx;">

							</view>
						</form>
					</view>
					<!-- #ifdef APP-PLUS -->
					<view class="payment_css f-24">
						<text class="c-55">使用</text>
						<text class="ml-8 display_inline">微信支付</text>
						<text class="ml-8 display_inline">更多支付方式</text>
					</view>
					<!-- #endif -->
					<view class="flex mt-50">
						<!-- <view v-if="shopdetail&&shopdetail.meetingCate==0" class="paybtn f-32 c-ff"  @click="coursePay(2)">立即支付
					    <text class="ml-10">￥{{materialInfo.actualPayment}}</text></view> -->
						<!-- <view class="paybtn f-32 c-ff" @click="coursePayDyy" v-if="isDyy">
              立即支付
              <text class="ml-10">
                ￥{{
                  shopdetail.goodsSpecPriceList[0].specLevelTwo * (addPrices) +
                  shopdetail.goodsSpecPriceList[1].specLevelTwo * (priceDetail[periodIndex].extArr.CENTER / 100)
                }}
              </text>
            </view> -->
						<view class="paybtn f-32 c-ff" @click="coursePay(2)">
							立即支付
							<text class="ml-10" v-if="isDyy">
								￥{{
                  (
                    shopdetail.goodsSpecPriceList[0].specLevelTwo * addPrices +
                    shopdetail.goodsSpecPriceList[1].specLevelTwo * (priceDetail[periodIndex].extArr.CENTER / 100)
                  ).toFixed(2)
                }}
							</text>
							<text class="ml-10"
								v-else>￥{{ isBuyGroup ? calcGroupPrice : materialInfo.actualPayment }}</text>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		<!-- 定价和尾款 -->
		<uni-popup ref="depositPaymentRef" type="bottom" @change="changeFormalEditDialog">
			<view class="content-popup">
				<view class="content_top_popup" :style="shopdetail.goodsType == 1 ? 'margin-bottom:8rpx' : ''">
					<view class="icon-clear" @click="closeMaterialDialog">
						<view class="order_title">订单支付</view>
						<!-- <uni-icons type="clear" size="30" color="#B1B1B1"></uni-icons> -->
					</view>
					<!-- addressInfo -->
				</view>
				<view class="bg-ff material_content">
					<view class="product_content_css">
						<view class="product_info">
							<view class="radius-15">
								<image :src="materialInfo.goodsPicUrl" class="couser_image"></image>
							</view>
							<view class="product_right">
								<!-- <view class="f-32 bold mb-20">{{ shopdetail.courseName }}</view> -->
								<view>
									<view class="f-24 price_tangerine">
										<view class="">
											<text class="price_title">{{ isBuyGroup ? '拼团价' : '实付价' }}</text>
											<text class="price_icon">￥</text>
											<text class="price_css"
												v-if="(materialInfo.actualPayment && !isDyy) || (materialInfo.actualPayment === 0 && !isDyy)">
												{{ !isBuyGroup ? materialInfo.actualPayment : calcGroupPrice }}
											</text>
											<text class="price_css" v-else-if="isDyy">
												{{
                    	      (
                    	        shopdetail.goodsSpecPriceList[0].specLevelTwo * addPrices +
                    	        shopdetail.goodsSpecPriceList[1].specLevelTwo * (priceDetail[periodIndex].extArr.CENTER / 100)
                    	      ).toFixed(2)
                    	    }}
											</text>
										</view>
										<!-- <text class="bold f-30 c-fea">{{ shopdetail.originalPrice }}</text> -->
										<text class="original_price_css">
											<text class="original_title">原价</text>
											<text v-if="isDyy">
												{{
                            (
                              shopdetail.goodsSpecPriceList[0].specLevelTwo * addPrices +
                              shopdetail.goodsSpecPriceList[1].specLevelTwo * (priceDetail[periodIndex].extArr.CENTER / 100)
                            ).toFixed(2)
                          }}
											</text>
											<text
												v-else>{{ (materialInfo.goodsOriginalPrice * ([1, 8, 9].includes(shopdetail.goodsType) ? buyNum : 1)).toFixed(2) }}</text>
										</text>
									</view>
									<view class="number_plus_css">
										<uni-number-box :min="1" :max="1" disabled v-model="value"
											@change="changeValue" />
									</view>
								</view>
							</view>
						</view>
					</view>
					<view>
						<view class="c-55 mt-35 fontWeight f-28">{{shopdetail.goodsSpecList[0].specType }}</view>
						<view class="specLevelTwo_css number_css mt-30 f-22 c-ff radius-5">
							{{shopdetail.goodsSpecList[0].specName}}</view>
					</view>
					<view v-if="shopdetail.goodsType != 1" class="f-28 radius-15">
						<view class="c-55 mt-45 fontWeight f-28">填写信息</view>
						<form id="#nform">
							<view class="information">
								<view style="width: 260rpx" class="c-00">
									<span class="redtext">*</span>
									{{ shopdetail && shopdetail.meetingCate == 0 ? '家长联系方式：' : '家长联系方式：' }}
								</view>
								<view class="phone-input">
									<input type="number" placeholder-class="placeholder-style"
										:disabled="!isShowChangePhoneNumber" v-model="parentMobile" name="number"
										:focus="parentFocuse"
										:placeholder="shopdetail && shopdetail.meetingCate == 0 ? '请输入家长联系方式' : '请输入家长联系方式'"
										class="input c-55" style="font-size: 28rpx" maxlength="11"
										@input.native="changeParent()" />
									<view v-if="parentMobile && isShowChangePhoneNumber"
										@click="changeFouce('parentFocuse')">
										<image src="https://document.dxznjy.com/dxSelect/image/edit_icon.png"
											style="width: 30upx; height: 30upx"></image>
									</view>
								</view>
							</view>
							<view class="ptb-15 mt-30 f-28">订单备注</view>
							<view class="">
								<u--textarea autoHeight :maxlength="200" count="" placeholder="请输入"
									@input="inputtext"></u--textarea>
								<!-- <textarea placeholder="请输入" :maxlength="200" placeholder-class="placeholder-style" @input="inputtext" class="remark f-30"></textarea>
                <text class="orderRemark">{{ remark.length }}/200</text> -->
							</view>
						</form>
					</view>
					<view class="flex mt-50">
						<view class="paybtn f-32 c-ff" @click="coursePay(2)">
							立即支付
							<text class="ml-10" v-if="isDyy">
								￥{{
                    (
                      shopdetail.goodsSpecPriceList[0].specLevelTwo * addPrices +
                      shopdetail.goodsSpecPriceList[1].specLevelTwo * (priceDetail[periodIndex].extArr.CENTER / 100)
                    ).toFixed(2)
                  }}
							</text>
							<text class="ml-10"
								v-else>￥{{ isBuyGroup ? calcGroupPrice : materialInfo.actualPayment }}</text>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>

		<!-- 正式课 -->
		<uni-popup ref="formalCourse" type="bottom" @change="changeFormalEditDialog">
			<view class="content-popup">
				<view class="icon-clear" @click="closeDialog">
					<view class="order_title">订单支付</view>
					{{ couponList.length }}
					<!-- <uni-icons type="clear" size="30" color="#B1B1B1"></uni-icons> -->
				</view>
				<view class="product_content_css">
					<view class="product_info">
						<view class="radius-15">
							<image :src="shopdetail.bannerImages[0]" class="couser_image"></image>
						</view>
						<view class="product_right">
							<!-- <view class="f-32 bold mb-20">{{ shopdetail.courseName }}</view> -->
							<view>
								<view class="f-24 price_tangerine">
									<text class="price_title">{{ isBuyGroup ? '拼团价' : '实付价' }}</text>
									<text class="price_icon">￥</text>
									<text class="price_css">
										<template v-if="isBuyGroup">{{ calcGroupPrice }}</template>
										<template v-else>
											{{ userinfo.identityType == 0 ? (cartTotalPrice ? cartTotalPrice : shopdetail.originalPrice) : cartTotalPrice ? cartTotalPrice : shopdetail.memberPrice }}
										</template>
									</text>
									<!-- <text class="bold f-30 c-fea">{{ shopdetail.originalPrice }}</text> -->
									<text class="original_price_css">
										<text class="original_title">原价￥</text>
										<text>{{ shopdetail.originalPrice }}</text>
									</text>
								</view>
								<view class="number_plus_css">
									<uni-number-box :min="1" :max="500" v-model="value" @change="changeValue" />
								</view>
								<!-- <view v-if="userinfo.identityType==0"class="amount">实付款：</view>
								<view v-if="userinfo.identityType!=0"class="amount">实付款：{{}}</view> -->
							</view>
							<view></view>
						</view>
					</view>
					<view class="flexbox coupon_content" v-if="couponList.length > 0 && !isGroupBuyGood">
						<view class="coupon_left_css">
							<text class="fontWeight f-28">优惠券</text>
							<text v-if="couponInfo && couponInfo.couponId" class="coupon_botton f-24">已选择优惠</text>
						</view>
						<view class="coupon_right_css" @tap="selectCoupon">
							<text v-if="couponInfo && couponInfo.couponId" class="coupon_title">
								<text
									v-if="couponInfo.couponHasCondition == 1">满{{ couponInfo.couponCondition }}减{{ couponInfo.couponDiscount }}</text>
								<text v-else>无门槛减{{ couponInfo.couponDiscount }}</text>
							</text>
							<text v-else class="coupon_title">请选择优惠</text>
							<u-icon style="display: inline-block" name="arrow-right" color="#555555" size="24"></u-icon>
						</view>
					</view>
					<!-- <view class="f-30 mt-30 flex-s bg-ff radius-15 p-30">
						<view class="">数量</view>
						<uni-number-box :min="1" :max="500" v-model="value" @change="changeValue"/>
					</view> -->
				</view>
				<view class="">
					<view class="plr-30 pb-30 bg-ff radius-15 mtb-30">
						<view class="ptb-30 f-32 ">订单备注</view>
						<view class="">
							<u--textarea autoHeight :maxlength="200" count="" placeholder="请输入"
								@input="inputtext"></u--textarea>
							<!-- <textarea auto-height placeholder="请输入" :maxlength="200"
								placeholder-class="placeholder-style" @input="inputtext" class="remark f-30"></textarea>
							<text class="orderRemark">{{ this.remark.length }}/200</text> -->
						</view>
						<view class="" style="height: 40rpx;">

						</view>
					</view>
					<view class="flex">
						<view v-if="userinfo.identityType == 0" class="paybtn f-32 c-ff" @click="coursePay">
							立即支付
							<text
								class="ml-10">￥{{ isBuyGroup ? calcGroupPrice : cartTotalPrice ? cartTotalPrice : shopdetail.originalPrice }}</text>
						</view>
						<view v-if="userinfo.identityType != 0" class="paybtn f-32 c-ff" @click="coursePay">
							立即支付
							<text
								class="ml-10">￥{{ isBuyGroup ? calcGroupPrice : cartTotalPrice ? cartTotalPrice : shopdetail.memberPrice }}</text>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		<!-- 购买记录 -->
		<uni-popup ref="morePopup" type="bottom" @change="changePopup">
			<view class="browse-popup positionRelative">
				<view class="dialog-icon" @click="closeBrowse">
					<uni-icons type="clear" size="30" color="#B1B1B1"></uni-icons>
				</view>
				<view class="t-c f-32 c-00 p-30">购买记录</view>
				<view class="flex-s browse-title">
					<view>买家</view>
					<view class="flex-s" style="width: 380rpx">
						<view>份数</view>
						<view>购买时间</view>
					</view>
				</view>
				<scroll-view :scroll-top="0" scroll-y="true" style="height: 870rpx">
					<view class="pb-30" v-for="(item, index) in buyDataList" :key="index">
						<view class="flex-s plr-30 mt-30">
							<view class="flex-a-c">
								<image :src="item.headPortrait || avaUrl" class="head-img"></image>
								<text class="f-28">{{ item.nickName }}</text>
							</view>
							<view class="flex-s" style="width: 380rpx">
								<view class="c-66 pl-5 f-28">+{{ item.purchaseQuantity }}</view>
								<view class="c-66 f-28">{{ formatItem(item.createdTime) }}</view>
							</view>
						</view>
					</view>
					<!-- <view class="pb-30" v-for="(item,index) in noticeList" :key="index">
						<view class="flex-s plr-30 mt-30">
							<view class="flex-a-c">
								<image :src="item.avaUrl" class="head-img"></image>
								<text class="f-28">{{item.name}}</text>
							</view>
							<view class="flex-s" style="width: 380rpx;">
								<view class="c-66 pl-5 f-28">+1</view>
								<view class="c-66 f-28">{{item.time}}</view>
							</view>
						</view>
					 </view>	 -->
					<view v-if="buyDataList.length > 0" class="ptb-30">
						<u-divider text="到底了"></u-divider>
					</view>
				</scroll-view>
			</view>
		</uni-popup>

		<!-- 优惠券 -->
		<uni-popup ref="couponPopup" type="bottom" @change="changeCouponPopup">
			<view class="couponPopup_content">
				<view class="order_title pt-25 fontWeight f-32" style="height: 35rpx">优惠券</view>
				<view class="coupon_price">
					<view class="flex_state pt-24 f-28">
						<view class="w96 mr-75">折后价</view>
						<view class="w65 mr-60">原价</view>
						<view class="w124 mr-55" v-if="calculateInfo.vipDiscount || calculateInfo.vipDiscount === 0">
							会员折扣</view>
						<view class="w96">优惠券</view>
					</view>
					<view class="flex_state f-28 pt-12">
						<!-- 正式课折后价（自有课-优惠券）+交付课-会员价 -->
						<view class="w96" v-if="shopdetail.goodsType == 3">{{ materialInfo.actualPayment }}</view>
						<view class="w96" v-else>{{ calculateInfo.finalPrice }}</view>
						<view class="w75">=</view>
						<view class="w96" v-if="shopdetail.goodsType == 3">{{ shopdetail.goodsOriginalPrice }}</view>
						<view class="w65" v-else>{{ calculateInfo.originalPrice }}</view>
						<view class="w60">-</view>
						<view class="w124" v-if="calculateInfo.vipDiscount || calculateInfo.vipDiscount === 0">
							{{ calculateInfo.vipDiscount }}
						</view>
						<view class="w55" v-if="calculateInfo.vipDiscount || calculateInfo.vipDiscount === 0">-</view>
						<view class="w96">{{ calculateInfo.couponDiscount }}</view>
					</view>
				</view>
				<view class="fontWeight f-32 p-30" style="height: 94rpx; line-height: 94rpx">可选优惠券</view>
				<view class="plr-32 height_scroll">
					<view v-for="item in couponList" @tap="() => changeCoupon(item)" :key="item.id"
						class="coupon_item mb-25 f-28">
						<view class="c-ff coupon_left">
							<view v-if="item.couponHasCondition == 1">
								满{{ item.couponCondition }}减{{ item.couponDiscount }}</view>
							<view v-if="item.couponHasCondition == 0">
								<view>无门槛</view>
								<view>减{{ item.couponDiscount }}</view>
							</view>
						</view>
						<view class="coupon_center">
							<view class="coupon_name_css">{{ item.couponName }}</view>
							<view class="coupon_time_css">有效期至{{ item.couponEndTime }}</view>
						</view>
						<view>
							<image v-if="item.check" class="wh26"
								src="https://document.dxznjy.com/course/c1a9486f704a453a8b57e20c3cd9eab8.png"></image>
							<image v-else class="wh26"
								src="https://document.dxznjy.com/course/82d6cfb8c6294809adb277a95138634d.png"></image>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		<!-- 分享弹窗 -->
		<sharePopup ref="sharePopups"></sharePopup>
		<uni-popup ref="paymentPopup" type="center" style="padding: 0" @change="changePopup">
			<view class="content_css">
				<view class="content_title_css">
					<view class="f-30 bold lh-36">购买成功</view>
					<view class="f-26 c-77 lh-44 mt-55">
						<view>您购买的课程已自动发放至--我的课程</view>
						<view>点击查看课程后，无法再发起退款申请</view>
					</view>
					<view @tap="paymentPopupClose" class="active_css button_css f-26 mt-70">我已知晓</view>
					<view class="close_css" @tap="$refs.paymentPopup.close()"></view>
				</view>
				<image class="curriculum_image" mode="widthFix"
					src="https://document.dxznjy.com/course/68d3c5370be54a81808e0b1efe64c1cf.png"></image>
			</view>
		</uni-popup>
		<uni-popup ref="studentPopup" type="center" style="padding: 0" @change="changePopup">
			<view class="content_css">
				<view class="content_title_css">
					<view class="f-30 c-33 lh-44 content_center_css">
						<span
							v-if="showStudentType == 1">您暂未绑定学员，点击{{ showStudentType == 1 ? '' : '此学生鼎学能课时已经全部购买' }}</span>
						<view v-if="showStudentType == 2">此学生鼎学能课时已经全部购买，点击</view>
						<span @tap="studentPopupClose" class="color_student f-26">添加学员</span>
					</view>
					<view class="close_css" @tap="$refs.studentPopup.close()"></view>
				</view>
				<image class="curriculum_image" mode="widthFix"
					src="https://document.dxznjy.com/course/68d3c5370be54a81808e0b1efe64c1cf.png"></image>
			</view>
		</uni-popup>

		<!-- 领券弹框 -->
		<uni-popup ref="getCoupon" type="bottom" border-radius="24px 24px 24px 24px"
			style="padding: 32rpx 14rpx 34rpx 18rpx" @change="changePopup">
			<view class="coupon-popup">
				<view class="coupon-title">优惠明细</view>
				<view class="content">
					<view class="require">满足条件后可享</view>
					<view v-for="item in availableList" class="item">
						<view style="display: flex">
							<view class="activity-type">
								<view class="activity-name">
									{{ item.couponType === 1 ? `减${item.couponDiscount}元` : '' }}
								</view>
								<view class="activity-limit">
									{{ item.couponHasCondition === 1 ? `满${item.couponCondition}元` : '无门槛' }}
								</view>
							</view>
							<view class="activity-content">
								<view class="title">{{ item.couponName }}</view>
								<view class="time">{{ item.couponStartTime }} - {{ item.couponEndTime }}</view>
							</view>
							<view style="display: flex; align-items: center">
								<view class="activity-use" @click="receivedCoupon(item)">
									{{ !item.couponAvailableStatus ? '领取' : '已领取' }}
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		<!-- 区分新老订单 -->
		<uni-popup ref="newAndOld" type="center">
			<view class="newAndOld-center">
				<view class="newAndOld-title">温馨提示</view>
				<view class="">因系统判断，您的账户仍有{{ leftHaveCourseHours }}节自有课时未消耗完，现购买交付课时需补齐差价购买， 是否确认下单购买。</view>
				<view class="newAndOld-footer">
					<view class="newAndOld-btn left" @click="closePay()">取消</view>
					<view class="newAndOld-btn right" @click="immediatePayment()">立即支付</view>
				</view>
			</view>
		</uni-popup>
		<!-- 弹幕 -->
		<barrage v-if="shopdetail && shopdetail.meetingCate == 0" ref="Barrage" :barrageData="barrageList"
			style="z-index: 1"></barrage>
		<invite-popup ref="inviteGroupBuyingRef"></invite-popup>
		<!-- 拼团大厅弹窗 -->
		<group-hall-popup ref="groupHallRef" :groupActivityId="groupActivityId" :goodsId="id"
			@preventScrollPenetrate="changePopup" @joinGroup="handlePopupJoinGroup"></group-hall-popup>

		<uni-popup ref="loadingPopup" type="center" border-radius="10px 10px 0 0">底部弹出 Popup 自定义圆角</uni-popup>
	</view>
</template>

<script>
	const {
		$http,
		$showSuccess,
		$showMsg,
		$navigationTo
	} = require('@/util/methods.js');
	import Config from '@/util/config.js';
	import Util from '@/util/util.js';
	import Sumperman from '@/common/superman.js';
	import dayjs from 'dayjs';
	import {
		countdownUtil
	} from './utilFun/timer.js';
	const MD5 = require('../util/md5.js');
	let secretkey = 'Jkk4ml1Of8';
	let vid = '';
	let ts = new Date().getTime(); //获取时间
	let sign = '';
	const {
		httpUser
	} = require('@/util/luch-request/indexUser.js');
	// import lffBarrage from '@/components/lff-barrage/lff-barrage.vue'
	import courseCatalog from './components/courseCatalog.vue'; //视频列表组件
	import courseList from '@/components/course-list/course-list.vue'; //商品列表组件
	import InvitePopup from './components/InvitePopup.vue';
	import Barrage from './components/barrage/barrage.vue'; // 弹幕插件
	import sensors from 'sa-sdk-miniprogram';
	import sharePopup from '@/components/sharePopup.vue';
	import JoinGroup from './components/JoinGroup.vue';
	import AppGuidePopup from './components/AppGuidePopup.vue';
	import GroupHallPopup from './components/groupHallPopup.vue';
	export default {
		components: {
			Barrage,
			courseCatalog,
			sharePopup,
			JoinGroup,
			InvitePopup,
			AppGuidePopup,
			GroupHallPopup,
			courseList
		},
		data() {
			return {
				isLoading: true, // 页面加载状态
				minCount: 1,
				alreadyLogin: false,
				referenceArr: [],
				referenceArrSave: [],
				haveHourSelf: 0, // 剩余自有时长（节）
				haveHourDeliver: 0, // 剩余交付时长（节）
				maxPurchaseDeliverSum: 0, // 可购买最大交付时长（节）
				haveHourSelfVisible: false, // 自有课时可见
				noClick: true, //防抖
				//保利威视频
				playerIdcont: 'polyvPlayercont',
				startTime: 0,
				ts: ts,
				sign: sign,
				width: '100%',
				payInfo: {},
				orderNo: '',
				flag1: false,
				value: 1,
				show: false,
				id: '',
				currentIndex: 1,
				currentSwiperIndex: 0, // 当前轮播图索引
				identityType: uni.getStorageSync('identityType'), //  0 普通用户 1 合伙人 2 俱乐部 3 合伙人&俱乐部 4-会员
				parentMemberType: 0, // 0：非会员，5：会员
				// swiperheight: 750,
				// productTop: 358,
				tabindex: 1,
				shopdetail: null,
				showSpecLevelOne: false,
				sceneId: '',
				notifyShow: false, // 消息提示
				closeable: true,
				imgHost: getApp().globalData.imgsomeHost,
				invitationInfo: {}, //邀请信息
				calculateInfo: {}, //优惠券信息
				crouseType: 0,
				userList: [{
						name: '课程简介'
					},
					{
						name: '课程目录'
					}
				],
				periodList: [{
						name: '小学',
						value: 'xiaoxue'
					},
					{
						name: '初中',
						value: 'chuzhong'
					},
					{
						name: '高中',
						value: 'gaozhong'
					}
				], // 学段list
				// 状态按钮相关数据
				selectedStatusIndex: null, // 当前选中的状态索引
				courseInfo: {},
				tabName: '课程简介',
				lineBg: 'https://document.dxznjy.com/course/01c96465d9ea46f69aa97465b1201aef.png',
				// 课程
				goods_list: [],
				order_total_price: 0,
				disabled: false,
				addressInfo: {
					address: '',
					buyerName: '',
					buyerPhone: '',
					addressId: ''
				},
				params: {
					type: 'default',
					message: '支付成功',
					duration: 3000,
					url: '/splitContent/order/order'
				},
				materialInfo: {},
				selectSpecNameTwo: '',
				selectSpecNameOne: '',
				cartTotalPrice: 0,
				useCoin: false,
				userinfo: {},
				remark: '',
				code1: '',
				height: 0,
				shareId: '', //分享id
				invitationInfo: {}, // 邀请信息
				shardInfo: {}, //邀请人信息
				recommendInfo: {
					//推荐人信息
					mobile: '',
					name: '',
					merchantCode: ''
				},
				/** 评价type */
				evaTypeList: [{
						key: '0',
						value: '家长'
					},
					{
						key: '1',
						value: '超人'
					},
					{
						key: '4',
						value: '超级会员'
					},
					{
						key: '5',
						value: '家长会员'
					},
					{
						key: '6',
						value: '超级合伙人'
					},
					{
						key: '7',
						value: '超级俱乐部'
					},
					{
						key: '8',
						value: '超级品牌'
					}
				],
				trialclassStudent: '', //试课学员姓名
				orderId: '',
				parentMobile: '', //家长联系方式
				specPriceId: undefined, //实物价格id
				specPriceTwoId: '',
				parentFocuse: false,
				referrerFocuse: false,
				showTrialClass: false, //是否展示试课单
				buyNum: 1, // 课程购买数量
				amount: {}, // 分润收益
				rollShow: false,
				token: '', // 判断用户是否登录
				avaUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
				rateValue: 1, // 评价评分
				couponList: [],
				couponInfo: {},
				studentInfo: {},
				studentList: [], //学员信息
				studentIndex: undefined,
				periodIndex: 0, //默认小学
				periodInfo: {
					level: 'xiaoxue'
				},
				noticeList: [
					//公告栏播报
					{
						avaUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
						name: '王**开',
						time: '刚刚购买了一件'
					},
					{
						avaUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
						name: '王**开',
						time: '9分钟前购买了一件'
					},
					{
						avaUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
						name: '王**开',
						time: '20分钟前购买了一件'
					},
					{
						avaUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
						name: '王**开',
						time: '30分钟前购买了一件'
					}
				],
				page: 1,
				showStudentType: 1,
				showFalse: true,
				isTrueAdress: false,
				showRecommend: false,
				no_more: false,
				specLevelTwoValue: 0, //交付课时数量
				specLevelTwoInfo: {},
				maxSpecLevelTwoValue: 0, //交付课最大课时
				minSpecLevelTwoValue: 1, //交付课最小课时
				maxSpecLevelOneValue: 0, //交付课自有课时
				minSpecLevelOneValue: 0, //交付课自有课时
				maxSpecLevelTwoInfoCopy: {},
				specLevelOneValue: 0, //自由课时数量
				specLevelTwoFalse: true,
				specLevelTwoFalse1: true,
				specLevelTwoFalse2: true,
				changeSpecLevelOneValue: 0, //选择自有课时数
				secondBuyHaveCouresgoodsVipPrice: 0, //第二次购买自由课时vip价格
				secondBuyHaveCouresgoodsOriginalPrice: 0, //第二次购买自由课时真实价格
				oldSpecLevelOneValue: 0,
				evaluatelist: {
					list: []
				}, // 评论列表
				buyDataList: [], // 购买记录
				barrageList: {}, // 弹幕
				flag: false,
				availableList: [], //可领取列表
				newPrice: 0, //自有交付课
				userCode: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '',
				goodsId: '',
				SecondBuy: false,
				maxSecondSpecLevelTwoValue: 0, //最大二次购买交付课时
				courseOnePrice: 0,
				courseTowPrice: 0,
				showNum: false,
				getStudentInfo: true,
				redeemCode: '', //兑换码
				realRedeemCode: '',
				activityInfo: {},
				bannerId: '',
				isGroupBuyGood: false, // 是否是拼团商品
				isGroupLeader: false, // 是否是拼团团长
				groupActivityId: '', // 拼团活动id
				groupInstanceId: '', // 拼团实例id
				isBuyAlone: false, // 是否单独购买
				isBuyGroup: false, // 是否拼团购买
				isJoinGroup: false, // 是否参团
				groupActivityInfo: {}, // 拼团活动信息
				groupHideFooter: false, // 拼团活动是否隐藏底部栏
				isInGroup: false, // 是否在拼团中
				notStart: false, // 拼团活动是否未开始
				hasEnd: false, //
				CurriculumCodeArr: Config.CurriculumCodeArr,
				MATHCurriculumCodeArr: Config.MATHCurriculumCodeArr,
				XKTAndXSMCurriculumCodeArr: Config.XKTAndXSMCurriculumCodeArr,
				XKTNCurriculumCodeArr: Config.XKTNCurriculumCodeArr,
				storagePhone: '', // 缓存学员手机号,
				isNeedUser: '', // 是否需要用户身份，
				newData: {},
				leftHaveCourseHours: '',
				isNewAndOldOrder: false,
				isDyy: false,
				orderIdDyy: '',
				addPrices: 0,
				priceDetail: {}, // 价格详情
				showTips: false,
				isGroup: '',
				// 新二次购买规则
				newBuyRule: false,
				accessGroupToTal: '', // 拼团总人数
				accessGroupList: [], // 正在拼团列表
				groupFromActivity: false, // 是否从拼团活动入口参与拼团
				isNoRecommend: false, // 初始状态下是否没有推荐人
				tempPropObj: {
					// 保存页面初始状态时拼团数据
					groupActivityId: '',
					groupInstanceId: '',
					isGroupBuyGood: false,
					isJoinGroup: false,
					groupActivityInfo: {},
					showRecommend: false
				},
				needUpdateGroupStatus: false, // 是否需要更新当前拼团区域状态
				shareSource: '', // 分享进入详情页的来源
				popupStartTime: 0, //记录弹窗打开的时间
				rightPadding: 0, // 右侧内边距，用于适配胶囊位置
				paddingTop: 0,
				introduce: {}, //详情介绍
				courseData: [], //相关推荐,
				scrollTop: 0,
				iconStyle: {
					width: '70rpx',
					height: '70rpx'
				},
				// 吸顶导航相关
				showStickyNav: false, // 是否显示吸顶导航
				activeAnchor: '', // 当前激活的锚点
				sectionOffsets: {}, // 各个区域的偏移量
				isScrollingToAnchor: false, // 是否正在滚动到锚点（防止滚动期间自动更新锚点）
				lastClickedAnchor: '', // 用户最后点击的锚点
				lastClickTime: 0, // 最后点击时间
				referrerName: '',
				swiperIndex: ''
			};
		},
		computed: {
			// 动态锚点列表
			anchorList() {
				const baseList = [{
						id: 'goods',
						name: '商品'
					}, {
						id: 'goods-evaluation',
						name: '评价'
					},
					{
						id: 'goods-detail',
						name: '详情'
					}
				];

				// 如果有相关推荐数据，则添加相关推荐锚点
				if (this.courseData && this.courseData.length > 0) {
					baseList.push({
						id: 'related-recommend',
						name: '推荐'
					});
				}

				return baseList;
			},
			// 正式课 可以修改家长信息（合伙人以上身份支持修改，会员身份及普通用户账号不支持修改，仅可帮自己购买）
			isShowChangePhoneNumber() {
				return this.shopdetail && this.shopdetail.goodsType == 3 && this.userinfo && (this.userinfo.merchantCode ||
					this.userinfo.julebuCode || this.userinfo.brandCode);
			},
			// 是否显示推荐人输入
			isHideRecommend() {
				return this.shopdetail && this.shopdetail.goodsType == 8 && this.shopdetail.enableCommander == 0;
			},
			// 拼团价
			calcGroupPrice() {
				return this.isBuyGroup ? (this.groupActivityInfo.groupPrice * ([1, 8, 9].includes(this.shopdetail
					.goodsType) ? this.buyNum : 1)).toFixed(2) : 0;
			},
			isUseSecondBuyNewRule() {
				return this.newBuyRule;
			}
		},
		watch: {
			realRedeemCode(newVal, oldVal) {
				uni.showLoading({
					title: '计算中...',
					mask: true
				});
				if (newVal && newVal != oldVal) {
					this.getCalculate('', newVal);
					this.couponInfo = {};
				} else {
					this.handleCodeBlur();
					this.getCalculate();
				}
			}
		},
		onLoad(e) {
			this.calculateRightPadding();
			// this.$refs.couponPopup.open()
			// 判断推荐人信息
			if (e.id == undefined) {
				this.id = 'c499556f9da64dce19b723296c884bc4';
				this.sceneId = e.scene;
			} else {
				if (e.id == '1116761476733865984') {
					e.id = '1283596029749366784';
				}
				this.id = e.id;
				this.sceneId = uni.getStorageSync('user_id');
				let data = {
					userId: e.scene,
					invitationCode: '',
					secCode: ''
				};
				uni.setStorageSync('invitationInfo', data);
			}

			if (e.appSource == 'app') {
				this.showTips = true;
			}

			if (e.isNeedUser) this.isNeedUser = e.isNeedUser;

			// 是否是拼团活动
			if (e.groupActivityId) {
				this.groupActivityId = e.groupActivityId;
				this.tempPropObj.groupActivityId = e.groupActivityId;
				// 是否是拼团商品
				if (e.isGroupBuyGood == 1) {
					this.isGroupBuyGood = true;
					this.tempPropObj.isGroupBuyGood = true;
					this.groupFromActivity = true;
					// 获取正在拼团数据
					this.getAccessGroupList();
				}
				// 是否是发起拼团
				if (e.isGroupLeader == 1) {
					this.isGroupLeader = true;

					if (e.notStart == 1) {
						this.notStart = true;
					}
					if (e.hasEnd == 1) {
						this.hasEnd = true;
					}
				}

				// 是否是参团
				if (e.isJoinGroup == 1) {
					this.isJoinGroup = true;
					this.tempPropObj.isJoinGroup = true;
					this.groupHideFooter = true;
				}
				if (e.groupInstanceId) {
					this.groupInstanceId = e.groupInstanceId;
					this.tempPropObj.groupInstanceId = e.groupInstanceId;
				}

				this.getGroupActivityInfo();
			}

			if (e.bannerId) {
				this.bannerId = e.bannerId;
			}
			if (e.shareSource) {
				this.shareSource = e.shareSource;
			}
			const pages = getCurrentPages(); // 获取当前页面栈
			const prevPage = pages[pages.length - 2]; // 上一个页面
			//判断是海报分析还是链接分享
			let shardInfo = uni.getStorageSync('shardInfo');
			let shardType = '';
			if (e.activityId && !e.shareUserCode) {
				//埋点-商品详情页
				getApp().sensors.track('$MPViewScreen', {
					pageName: '商品详情页',
					goodsId: this.id,
					activityId: e.activityId,
					refereePath: prevPage?.route || ''
				});
			} else if (e.bannerId && !e.shareUserCode) {
				//埋点-商品详情页
				getApp().sensors.track('$MPViewScreen', {
					pageName: '商品详情页',
					goodsId: this.id,
					bannerId: this.bannerId,
					refereePath: prevPage?.route || ''
				});
			} else if (shardInfo.enterType && shardInfo.enterType === 'qrcode') {
				//海报分享进入
				shardType = 'qrcode';
				getApp().sensors.track('$MPViewScreen', {
					pageName: '商品详情页',
					goodsId: this.id,
					name: shardInfo.mobile,
					refereePath: shardType
				});
			} else if (shardInfo.enterType && shardInfo.enterType === 'link') {
				//链接分享进入
				shardType = 'link';
				getApp().sensors.track('$MPViewScreen', {
					pageName: '商品详情页',
					goodsId: this.id,
					name: shardInfo.mobile,
					refereePath: shardType
				});
			} else {
				//非活动非banner非海报非链接进入
				getApp().sensors.track('$MPViewScreen', {
					pageName: '商品详情页',
					goodsId: this.id,
					activityId: e.activityId,
					refereePath: prevPage?.route || ''
				});
			}

			// 活动邀请
			if (e.activityId && e.shareUserCode) {
				this.activityInfo = {
					activityId: e.activityId,
					shareUserCode: e.shareUserCode
				};
			}

			this.getInvitation(e);
			if (this.shareId != undefined && this.shareId != '') {
				this.getMobile();
			}
			this.params.url = '/splitContent/order/paymentSuccess';
			this.getBarrageList();
			//埋点
			let enterType = 'jump';
			if (e.sampshare && e.scene) {
				enterType = 'link';
			} else if (e.scene && e.type == 1) {
				enterType = 'qrcode';
			}
			sensors.track('courseDetailEvt', {
				$enterType: enterType,
				$refereeId: this.shareId
			});
			// 默认为false 不可在本地面销毁时清除缓存的isDyy值，否则会导致下一次进入详情页时无法再次进入详情页进行兑换/购买操作
			uni.setStorageSync('isDyy', false);
			this.detail();
			let token = uni.getStorageSync('token');
			if (token) {
				this.getPrice();
				this.alreadyLogin = true;
			} else {
				this.alreadyLogin = false;
			}
			// 立即购买唤起弹窗
			uni.$on('purchasePop-up', () => {
				// 在这里执行页面刷新的操作
				this.buySubject(); // 假设 getData 是一个获取数据的方法
			});
		},
		onHide() {
			//判断用户是否登录，未登录进入后台的话不触发埋点
			if (!uni.getStorageSync('token')) return
			//在小程序进入后台时触发，强制关闭弹窗，浏览结束并计算浏览时间
			this.$refs.experiencePopup.close();
			this.closeMaterialObjectPopup();
			const duration = (Date.now() - this.popupStartTime) / 1000;
			this.reportStayTime(duration);
			//触发埋点事件后清空
			this.popupStartTime = 0;
			// uni.removeStorageSync('isDyy');
		},
		onUnload() {
			//如果记录的时间为0，表示弹窗是关闭的，则不触发埋点
			if (this.popupStartTime === 0) return
			//在当前页面返回或跳转其它页面时触发，浏览结束，计算浏览时间
			const duration = (Date.now() - this.popupStartTime) / 1000;
			this.reportStayTime(duration);
			//触发埋点事件后清空
			this.popupStartTime = 0;
			// uni.removeStorageSync('isDyy');
			// 在页面卸载时清除缓存的 studentPhone
			uni.removeStorageSync('studentPhone');
			this.storagePhone = '';

			// 清除推荐人绑定冲突提示
			let hadConflictToast = uni.getStorageSync('hasConflictToast');
			if (hadConflictToast) {
				uni.removeStorageSync('scanInfo');
				uni.removeStorageSync('hasConflictToast');
			}
			// getApp().sensors.track('$MPPageLeave', {
			//   pageName: '商品详情页',
			//   goodId: this.id
			// });
		},
		onShow() {
			console.log('详情页-----------')
			// uni.showLoading({
			// 	title: '加载中'
			// });
			// 从缓存中获取 studentPhone
			this.storagePhone = uni.getStorageSync('studentPhone') || '';
			if (this.flag1) {
				uni.$tlpayResult(this.sucees, this.fail, this.payInfo.orderId);
			}
			let token = uni.getStorageSync('token');
			this.getEvaluateList();
			this.browseList();
			if (this.groupActivityId) {
				this.getGroupActivityInfo();
			}

			if (token) {
				this.getAvailableCoupons();
				this.homeData();
				this.getIncome();
				if (!this.storagePhone) {
					this.getStudent();
				} else {
					this.changeParent();
				}
			} else {
				// uni.navigateTo({
				//   url: '/Personalcenter/login/login'
				// });
			}
			if (uni.getStorageSync('address')) {
				this.addressInfo = uni.getStorageSync('address');
			} else {
				if (token) this.getAddressList();
			}

			// 初始化锚点
			this.$nextTick(() => {
				this.initializeAnchors();
			});
		},
		onShareAppMessage(res) {
			// 拼团邀请分享
			if (this.isGroupBuyGood && res.from == 'button' && res.target.dataset && res.target.dataset.invite) {
				getApp().sensors.track('clickToGo', {
					name: '邀请好友',
					goodId: this.groupInstanceId
				});
				return {
					title: this.groupActivityInfo.piGroupActivityVo.groupShareText,
					imageUrl: this.groupActivityInfo.piGroupActivityVo.groupShareImage, //分享封面
					//如果有参数的情况可以写path
					path: `/Coursedetails/productDetils?id=${this.id}&groupActivityId=${this.groupActivityId}&groupInstanceId=${this.groupInstanceId}&isGroupBuyGood=1&isJoinGroup=1&isGroupShare=1`
				};
			}

			// 正常商品分享
			return {
				title: this.shopdetail.goodsShareTextList[0].shareText,
				imageUrl: this.shopdetail.goodsSharePoster, //分享封面
				//如果有参数的情况可以写path
				path: '/pages/beingShared/index?id=' + this.id + '&type=6&scene=' + this.sceneId + '&bannerId=' + this
					.bannerId + '&shareSource=miniApp'
			};
		},
		onPageScroll(e) {
			console.log(e, '滚动-----------------')
			this.scrollTop = e.scrollTop;
			this.handleStickyNav(e.scrollTop);
		},
		methods: {
			swiperChangeindex(event) {
				console.log(event, '轮播弹幕1111---------')
				this.swiperIndex = event.detail.current;
			},
			// 计算每个项目的透明度
			getOpacity(index) {
				const len = this.buyDataList.length;
				if (len === 0) return 1;

				// 计算相对当前激活项的位置（处理循环）
				const diff = (index - this.swiperIndex + len) % len;

				// 根据位置设置不同透明度
				if (diff === 0) {
					return 0.2; // 当前项最清晰
				} else if (diff === 1 || diff === len - 1) {
					return 0.4; // 相邻项中等透明度
				} else {
					return 0.6; // 其他项最暗
				}
			},
			firstimgs(photoUrl) {
				if (photoUrl == '') {
					return false
				} else {
					let isArry = JSON.parse(photoUrl)
					// console.log(isArry,'数组判断------')
					if (isArry.length == 0) {
						return false
					} else {
						return true
					}
				}
			},
			skintap(url, goodsId) {
				//相关推荐商品点击时的埋点
				getApp().sensors.track('recommendedGoodsClick', {
					goods_id: goodsId
				});
				$navigationTo(url);
			},
			// 处理吸顶导航显示和锚点激活
			handleStickyNav(scrollTop) {
				// 显示吸顶导航的临界值（可以根据实际需要调整）
				const showThreshold = 200;
				this.showStickyNav = scrollTop > showThreshold;

				// 获取各个锚点的位置并判断当前激活的锚点
				this.updateActiveAnchor(scrollTop);
			},
			async referrerFocuses() {
				console.log('失焦------------------')
				const res = await $http({
					url: 'zx/user/getUserNameByMobile',
					data: {
						mobile: this.recommendInfo.mobile ? this.recommendInfo.mobile : null
					}
				})
				console.log(res, '查询--------- ')
				if (res.code == 20000) {
					this.referrerName = res.data
				}
			},
			searchFocus() {
				getApp().sensors.track('indexSearchHandleClick', {
					name: '甄选搜索'
				});
				uni.navigateTo({
					url: '/interestModule/searchPage'
				});
			},

			// 更新当前激活的锚点
			async updateActiveAnchor(scrollTop) {
				// 如果正在主动滚动到某个锚点，则不进行自动更新
				if (this.isScrollingToAnchor) {
					return;
				}

				// 如果用户在3秒内主动点击过锚点，且当前锚点是用户点击的，则优先保持
				const timeSinceClick = Date.now() - this.lastClickTime;
				if (timeSinceClick < 3000 && this.activeAnchor === this.lastClickedAnchor) {
					return;
				}

				try {
					// 获取各个区域的位置信息
					const evaluationInfo = await this.getElementInfo('#goods-evaluation');
					const detailInfo = await this.getElementInfo('#goods-detail');
					const recommendInfo = await this.getElementInfo('#related-recommend');

					// 动态计算偏移量，考虑内容高度
					const offset = 150; // 基础偏移量
					const viewportHeight = uni.getSystemInfoSync().windowHeight;

					// 计算各区域的中心点位置
					let activeSection = '';

					if (recommendInfo && scrollTop + viewportHeight / 2 >= recommendInfo.top) {
						// 如果页面中心点已经到达推荐区域，则激活推荐
						activeSection = 'related-recommend';
					} else if (detailInfo && scrollTop + viewportHeight / 2 >= detailInfo.top) {
						// 如果页面中心点到达详情区域，则激活详情
						activeSection = 'goods-detail';
					} else if (evaluationInfo && scrollTop + viewportHeight / 2 >= evaluationInfo.top) {
						// 如果页面中心点到达评价区域，则激活评价
						activeSection = 'goods-evaluation';
					} else {
						// 默认激活商品区域
						activeSection = 'goods';
					}

					// 只有当激活的区域发生变化时才更新
					if (this.activeAnchor !== activeSection) {
						this.activeAnchor = activeSection;
					}
				} catch (error) {
					console.log('获取元素位置失败:', error);
				}
			},

			// 获取元素位置信息
			getElementInfo(selector) {
				return new Promise((resolve) => {
					const query = uni.createSelectorQuery().in(this);
					query.select(selector).boundingClientRect((data) => {
						resolve(data);
					}).exec();
				});
			},

			// 滚动到指定锚点
			async scrollToAnchor(anchorId, anchorName) {
				getApp().sensors.track('productNavigationClick', {
					name: anchorName
				});
				try {
					// 记录用户的主动点击
					this.lastClickedAnchor = anchorId;
					this.lastClickTime = Date.now();

					// 添加标志位，防止滚动期间自动更新锚点
					this.isScrollingToAnchor = true;

					const elementInfo = await this.getElementInfo(`#${anchorId}`);
					if (elementInfo) {
						const scrollTop = elementInfo.top + this.scrollTop - 120; // 减去导航栏高度的偏移

						// 立即设置激活的锚点
						this.activeAnchor = anchorId;

						uni.pageScrollTo({
							scrollTop: Math.max(scrollTop, 0),
							duration: 300,
							success: () => {
								// 滚动完成后，延迟更长时间再允许自动更新锚点
								setTimeout(() => {
									this.isScrollingToAnchor = false;
								}, 500); // 增加延迟时间到500ms
							},
							fail: () => {
								this.isScrollingToAnchor = false;
							}
						});
					}
				} catch (error) {
					console.log('滚动到锚点失败:', error);
					this.isScrollingToAnchor = false;
				}
			},

			// 初始化锚点
			initializeAnchors() {
				// 延迟一点时间确保DOM元素已经渲染
				setTimeout(() => {
					this.updateActiveAnchor(this.scrollTop);
				}, 500);
			},
			leftClicks() {
				uni.navigateBack()
			},
			formatSalesCount(count) {
				const num = parseInt(count);
				if (num > 10000) {
					const wan = Math.floor(num / 10000);
					return `${wan}万+人付款`;
				} else {
					return `${num}人付款`;
				}
			},
			// 轮播图切换事件
			swiperChange(e) {
				this.currentSwiperIndex = e.detail.current;
			},
			// 获取轮播图总数量（包括视频）
			getTotalSwiperCount() {
				if (!this.shopdetail || !this.shopdetail.goodsCarouselList) return 0;
				let count = this.shopdetail.goodsCarouselList.length;
				// 如果有视频，总数加1
				if (this.shopdetail.goodsVideoUrl) {
					count += 1;
				}
				return count;
			},
			// 获取图片数量（不包括视频）
			getImageCount() {
				if (!this.shopdetail || !this.shopdetail.goodsCarouselList) return 0;
				return this.shopdetail.goodsCarouselList.length;
			},
			// 判断当前是否显示图片（不是视频）
			isShowingImage() {
				if (!this.shopdetail) return false;
				// 如果没有视频，都是图片
				if (!this.shopdetail.goodsVideoUrl) return true;
				// 如果有视频，判断当前索引是否大于0（第一个是视频）
				return this.currentSwiperIndex > 0;
			},
			// 获取当前图片的索引（从1开始）
			getImageIndex() {
				if (!this.shopdetail) return 1;
				// 如果没有视频，直接返回当前索引+1
				if (!this.shopdetail.goodsVideoUrl) {
					return this.currentSwiperIndex + 1;
				}
				// 如果有视频，当前索引减1（因为第一个是视频）
				return this.currentSwiperIndex;
			},
			// 计算右侧图标需要的内边距
			calculateRightPadding() {
				// 获取胶囊按钮的位置信息
				const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
				console.log(menuButtonInfo, '获取胶囊按钮的位置信息')
				// 获取屏幕宽度
				// const {
				// 	windowWidth
				// } = uni.getSystemInfoSync();
				// console.log(menuButtonInfo,windowWidth,'按钮距离0-----------')

				// 计算胶囊右侧到屏幕右边的距离 = 屏幕宽度 - 胶囊右边界坐标
				this.rightPadding = menuButtonInfo.width;
				this.paddingTop = menuButtonInfo.top + menuButtonInfo.height

				// 增加额外的安全距离（可选，根据设计需求调整）
				// this.rightPadding += 8;
			},
			handleOpenDownload() {
				this.$refs.appGuidePopupRef.open();
			},
			handleHideFooter(userList, isClose) {
				// isClose 拼团成功或者拼团失败
				const userId = uni.getStorageSync('user_id');
				if (userId) {
					const item = (userList || []).find((item) => item.userId === userId);
					if (item) {
						this.groupHideFooter = true;
						this.isInGroup = true;
					} else if (isClose) {
						this.groupHideFooter = true;
					} else {
						this.groupHideFooter = false;
					}
				}
			},
			handleJoinGroup() {
				this.buySubject(2);
			},
			openInvitePopup() {
				console.log('拼团邀请弹窗', this.groupInstanceId, this.isJoinGroup);
				this.$refs.inviteGroupBuyingRef.open({
					groupInstanceId: this.groupInstanceId,
					// groupFlag 发起拼团团长 2、参与拼团用户
					groupFlag: this.isJoinGroup ? 2 : 1
				});
			},
			getRichText(text) {
				// return text.replaceAll('style="max-width:100%;"', 'style="vertical-align:bottom;max-width:100%;"');
				return text.replaceAll('<img ', '<img class="vertical-img"');
			},

			//判断自有课时是否能修改
			saveAndModify() {
				setTimeout(() => {
					let that = this;
					if (this.minSpecLevelOneValue == -1 && !this.isUseSecondBuyNewRule) return;
					that.specLevelTwoFalse1 = !that.specLevelTwoFalse1;
				}, 100);
			},
			deliverSaveAndModify() {
				setTimeout(() => {
					this.specLevelTwoFalse = !this.specLevelTwoFalse
				}, 100);
			},
			// 课程保存和修改
			selfCourseSaveAndModify() {
				setTimeout(() => {
					this.specLevelTwoFalse2 = !this.specLevelTwoFalse2;
				}, 100);
			},
			deliverCourseSaveAndModify() {
				setTimeout(() => {
					this.specLevelTwoFalse = !this.specLevelTwoFalse;
				}, 100);
			},
			// 修改二次购买自由课程数量
			async changeSelfCourseValue(e, item) {
				let that = this;
				let num = e;
				let list = this.shopdetail.goodsSpecPriceList.filter((spec) => spec.specLevelOne == '自有');
				if (e == 0) {
					if (!this.isUseSecondBuyNewRule) {
						that.specLevelTwoValue = 1;
					}
					that.showNum = false;
				} else {
					that.showNum = true;
					that.changeSpecLevelOneValue = e;
					// that.specLevelTwoValue = e + that.maxSpecLevelTwoValue;
					if (!this.isUseSecondBuyNewRule) {
						that.specLevelTwoValue = e + that.maxSpecLevelTwoValue;
						that.maxSecondSpecLevelTwoValue = that.specLevelTwoValue;
					} else {
						that.maxSecondSpecLevelTwoValue = that.maxSpecLevelTwoValue;
					}
				}

				// 获取自由课时,总价格
				that.secondBuyHaveCouresgoodsVipPrice = ((item.goodsVipPrice / item.specLevelTwo) * num).toFixed(2);
				// 获取自由课时,原始总价格
				that.secondBuyHaveCouresgoodsOriginalPrice = ((item.goodsOriginalPrice / item.specLevelTwo) * num)
					.toFixed(2);
				//自有课时价格
				that.courseOnePrice = Number(
					(this.identityType == 4 || this.parentMemberType == 5) && !this.isGroupBuyGood ? that
					.secondBuyHaveCouresgoodsVipPrice : that.secondBuyHaveCouresgoodsOriginalPrice
				);
				this.courseTowPrice = Number(
					(this.identityType == 4 || this.parentMemberType == 5) && !this.isGroupBuyGood ?
					((that.specLevelTwoInfo.goodsVipPrice / that.specLevelTwoInfo.specLevelTwo) * that
						.specLevelTwoValue).toFixed(2) :
					((that.specLevelTwoInfo.goodsOriginalPrice / that.specLevelTwoInfo.specLevelTwo) * that
						.specLevelTwoValue).toFixed(2)
				);
				// 支付价格
				this.materialInfo.actualPayment = (that.courseTowPrice + that.courseOnePrice).toFixed(2);
			},

			sucees() {
				this.flag1 = false;
				this.flag = false;
				this.handleSavePayRecord();
				if (this.isGroupBuyGood) {
					this.getAccessGroupList();
				}
				if (this.shopdetail.goodsType == 4) {
					this.$refs.paymentPopup.open();
				} else {
					this.redirectToOrderIndex();
				}
			},
			fail(type) {
				this.flag1 = false;
				this.flag = false;
				// 取消支付跳到支付取消页面
				if (type === 'cancel') {
					uni.navigateTo({
						url: `/splitContent/order/payCancel?orderId=${this.payInfo.sourceOrderId}&cancelType=class&type=create`
					});
				}
			},
			fails() {
				uni.showToast({
					title: '支付失败',
					icon: 'none',
					duration: 2000
				});
				setTimeout(function() {
					uni.redirectTo({
						url: '/splitContent/order/order'
					});
				}, 1500);
				this.flag1 = false;
				this.flag = false;
			},
			// 弹幕
			colrdo(data) {
				//插入一条弹幕
				this.$refs.lffBarrage.add({
					item: data
				});
			},
			// 禁止滚动穿透
			changePopup(e) {
				this.rollShow = e.show;
			},
			// 优惠券弹框状态变化（特殊处理，避免影响支付弹框的滚动防护）
			changeCouponPopup(e) {
				// 优惠券弹框关闭时不重置 rollShow，保持支付弹框的滚动防护
				if (e.show) {
					// 优惠券弹框打开时设置滚动防护
					this.rollShow = true;
				}
				// 关闭时不处理，让支付弹框继续保持滚动防护
			},
			// 获取学员信息
			async getStudent() {
				let result = await this.$httpUser.get('znyy/review/query/my/student');
				if (result) {
					this.studentList = result.data.data;
					if (this.studentIndex >= 0 && this.shopdetail.goodsType == 3) {
						// this.getSummary( this.studentList[this.studentIndex])
						this.bindPickerChange({
								target: {
									value: this.studentIndex
								}
							},
							true
						);
					}
					if (this.studentList.length <= 0) {
						this.showFalse = true;
					} else {
						this.showFalse = false;
					}
					//
				}
			},
			getStudentPopu() {
				const phoneRegex = /^1[3-9]\d{9}$/;
				const PHONE_ERROR_MSG = '请输入有效的 11 位手机号码';
				if (!phoneRegex.test(this.parentMobile)) {
					$showMsg(PHONE_ERROR_MSG);
					return;
				}
				if (this.showFalse) {
					this.showStudentType = 1;
					this.$refs.studentPopup.open();
				}
			},
			getStudentPopus() {
				console.log('新加学员------')
				this.studentPopupClose()
				// const phoneRegex = /^1[3-9]\d{9}$/;
				// const PHONE_ERROR_MSG = '请输入有效的 11 位手机号码';
				// if (!phoneRegex.test(this.parentMobile)) {
				// 	$showMsg(PHONE_ERROR_MSG);
				// 	return;
				// }
			},
			// 弹幕
			async getBarrageList() {
				let _this = this;
				const res = await $http({
					url: 'zx/order/evaluate/bulletCommentList',
					data: {
						courseId: _this.id
					}
				});
				if (res) {
					// 给个数组即可
					this.barrageList = res.data;
					// this.$refs.Barrage.startBarrage(res.data);
				}
			},
			//收货地址
			async getAddressList() {
				let _this = this;
				const resdata = await $http({
					url: 'zx/order/userAddressList'
				});
				if (resdata) {
					_this.addressInfo = resdata.data[0] || {};
				}
			},
			//获取收货地址
			getAddress() {
				uni.navigateTo({
					url: '/splitContent/address/list/list?from=goods'
				});
			},
			async getEvaluateList() {
				let _this = this;
				const res = await $http({
					url: 'zx/order/evaluate/orderEvaluatePage',
					data: {
						courseId: _this.id,
						page: _this.page
					}
				});
				if (res) {
					_this.evaluatelist = res.data;
				}
			},
			async browseList() {
				const res = await $http({
					url: 'zx/order/top-hundred-orders',
					data: {
						goodsId: this.id,
						pageNum: 1,
						pageSize: 100
					}
				});
				if (res) {
					this.buyDataList = res.data;
				}
			},
			// 加入购物车
			async addCart(shopdetail) {
				let _this = this;
				const res = await $http({
					url: 'zx/course/addShoppingCar',
					method: 'POST',
					data: {
						buyNum: _this.value,
						courseId: _this.id
					}
				});
				if (res) {
					// $showSuccess('加入成功')
					this.notifyShow = true;
					setTimeout(function() {
						_this.notifyShow = false;
						_this.show = false;
					}, 2000);
				}
			},
			// 获取首页信息
			async homeData() {
				let _this = this;
				const res = await $http({
					url: 'zx/user/userInfoNew'
				});
				if (res) {
					uni.setStorageSync('log_userCode', res.data.userCode);
					_this.parentMemberType = res.data.parentMemberType;
					_this.showRecommend = false;
					// _this.pageShow = false;
					_this.userinfo = res.data;
					_this.parentMobile = res.data.mobile;
					// 甄选第十四次迭代 不区分合伙人的类型
					_this.recommendInfo.name = res.data.shareUserName || _this.recommendInfo.name || '';
					_this.recommendInfo.mobile = res.data.shareUserPhone || _this.recommendInfo.mobile || '';
					// if (_this.storagePhone) {
					//   _this.parentMobile = _this.storagePhone;
					// } else {
					//   _this.parentMobile = res.data.mobile;
					// }
					// if (res.data.memberStartTime && res.data.parentMemberStartTime) {
					//   let memberTime = new Date(res.data.memberStartTime);
					//   let parentMemberTime = new Date(res.data.parentMemberStartTime);
					//   let timeDiff = parentMemberTime.getTime() - memberTime.getTime();
					//   // console.log('ttt', timeDiff, memberTime.getTime(), parentMemberTime.getTime());
					//   _this.recommendInfo.name = timeDiff > 0 ? res.data.shareUserName : res.data.parentShareUserName;
					//   _this.recommendInfo.mobile = timeDiff > 0 ? res.data.shareUserPhone : res.data.parentShareUserPhone;
					//   // if (timeDiff > 0) {
					//   //   if (res.data.shareUserPhone) {
					//   //     _this.showRecommend = true;
					//   //   }
					//   // } else {
					//   //   if (res.data.parentShareUserPhone) {
					//   //     _this.showRecommend = true;
					//   //   }
					//   // }
					// } else {
					//   if (res.data.identityType == 4) {
					//     _this.recommendInfo.name = res.data.shareUserName;
					//     _this.recommendInfo.mobile = res.data.shareUserPhone;
					//     // if (res.data.shareUserPhone) {
					//     //   _this.showRecommend = true;
					//     // }
					//   }
					//   if (res.data.parentMemberType == 5) {
					//     _this.recommendInfo.name = res.data.parentShareUserName;
					//     _this.recommendInfo.mobile = res.data.parentShareUserPhone;
					//     // if (res.data.parentShareUserPhone) {
					//     //   _this.showRecommend = true;
					//     // }
					//   }
					// }
					if (_this.recommendInfo.mobile) {
						_this.showRecommend = true;
						_this.tempPropObj.showRecommend = true;
					}
				}
			},
			//改版家长手机号(正式课)
			async changeParent() {
				const phoneRegex = /^1[3-9]\d{9}$/;
				if (!phoneRegex.test(this.parentMobile)) {
					// $showMsg('请输入有效的手机号码');
					this.studentList = [];
					this.studentIndex = -1; // 重置学生列表索引
					this.isTrueAdress = true;
					return;
				}
				this.isTrueAdress = false;
				const resdata = await $http({
					url: 'zx/student/studentList/byPhone',
					data: {
						phone: this.parentMobile
					}
				});
				if (resdata && resdata.data && resdata.data.length > 0) {
					this.studentList = resdata.data.map((item) => {
						const newItem = {
							...item
						};
						if (newItem.realname !== undefined) {
							// 将 realname 改为 realName
							newItem.realName = newItem.realname;
							delete newItem.realname;
						}
						if (newItem.studentcode !== undefined) {
							newItem.studentCode = newItem.studentcode;
							delete newItem.studentcode;
						}
						return newItem;
					});
					if (this.studentIndex >= 0 && this.shopdetail.goodsType == 3) {
						this.bindPickerChange({
								target: {
									value: this.studentIndex
								}
							},
							true
						);
					}
					this.showFalse = false;
				} else {
					this.showFalse = true;
					this.studentList = [];
				}
				if (this.shopdetail.goodsType == 2 && (this.identityType == 4 || this.parentMemberType == 5)) {
					if (this.userinfo.mobile != this.parentMobile) {
						this.showRecommend = false;
					} else {
						if (this.recommendInfo.name && this.recommendInfo.mobile) {
							this.showRecommend = true;
						}
					}
				}
			},
			// 防止触摸穿透的方法
			preventTouchMove(e) {
				// 阻止事件冒泡和默认行为
				e.stopPropagation();
				e.preventDefault();
				return false;
			},
			//改版家长手机号
			changeParentEx() {
				if (this.shopdetail.goodsType == 2 && (this.identityType == 4 || this.parentMemberType == 5)) {
					if (this.userinfo.mobile != this.parentMobile) {
						this.showRecommend = false;
					} else {
						if (this.recommendInfo.name && this.recommendInfo.mobile) {
							this.showRecommend = true;
						}
					}
				}
			},
			async getPrice() {
				let _this = this;
				const res = await $http({
					url: 'znyy/bSysConfig/list/course/level/price/zx',
					methods: 'get'
				});

				// 根据课程数量确定档位
				let tierIndex = 0; // 默认档位
				if (_this.isDyy && _this.shopdetail && _this.shopdetail.goodsSpecPriceList && _this.shopdetail
					.goodsSpecPriceList[0]) {
					const courseCount = _this.shopdetail.goodsSpecPriceList[0].specLevelTwo;
					if (courseCount >= 20 && courseCount <= 49) {
						tierIndex = 0; // 20-49档位
					} else if (courseCount >= 50 && courseCount <= 99) {
						tierIndex = 1; // 50-99档位
					} else if (courseCount >= 100) {
						tierIndex = 2; // 100-无穷大档位
					}
				}

				// 根据档位选择对应的selectResultList
				_this.priceDetail = res.data?.[0]?.selectResultList ?? [];
				_this.priceDetail.forEach((item) => {
					item.extArr = JSON.parse(item.ext);
					item.price = item.value.split('#')[1];
				});

				const res2 = await $http({
					url: 'znyy/bSysConfig/course/profit/price/zx',
					method: 'get'
				});
				const adjustPrice = Number(res2.data?.adjustCoursePrice || 0) / 100;
				const periodPrice = Number(_this.priceDetail[_this.periodIndex]?.price || 0);
				_this.addPrices = (adjustPrice + periodPrice).toFixed(2);
			},
			// 获取商品详情
			async detail() {
				let _this = this;
				_this.goodsId = _this.id;
				_this.isLoading = true; // 设置加载状态为true，阻止页面滑动
				try {
					const res = await $http({
						url: 'zx/wap/goods/single/detail',
						data: {
							goodsId: _this.id,
							userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '',
							fromSource: _this.shareSource
						}
					});
					if (res) {
						console.log(res.data, '商品详情信息---------');
						if (res.data.extraFields) {
							console.log(JSON.parse(res.data.extraFields), '常见问题查看--------')
							this.introduce = JSON.parse(res.data.extraFields)
						}

						this.courseData = res.data.goodsList
						_this.shopdetail = res.data;
						_this.minCount = _this.shopdetail.goodsSpecPriceList[0] ? _this.shopdetail.goodsSpecPriceList[
							0].specLevelTwo : 1;
						// isDyy 鼎英语正课
						_this.isDyy = _this.shopdetail.isDyy && _this.shopdetail.goodsType == 3 ? true : false;
						uni.setStorageSync('isDyy', _this.isDyy);
						if (_this.isDyy) {
							const res2 = await $http({
								url: 'zx/order/dyy-partner-get',
								method: 'GET'
							});
							if (res2 && res2.data && !_this.recommendInfo.mobile) {
								// 无手机号则赋值，若无则提供编辑
								_this.recommendInfo.name = res2.data.partnerName || '';
								_this.recommendInfo.mobile = res2.data.partnerPhone || '';
								_this.showRecommend = true;
							}
							// _this.recommendInfo.mobile优先以甄选为主，甄选没有则取渠道
							if (_this.recommendInfo.mobile) {
								let joioData = {};
								joioData.referrerPhone = _this.recommendInfo.mobile;
								const res3 = await $http({
									url: 'zx/wap/order/getReferenceCode',
									method: 'get',
									data: joioData
								});
								if (res3.success) {
									_this.referenceArrSave = res3.data;
								} else {
									$showMsg(res3.message);
								}
							} else {
								// 鼎英语2.0
								// 视为没有添加过推荐人，没有购买过鼎英语，第一次购买
								// 根据zx/order/dyy-partner-get接口返回的数据判断，不符合则视为首次购买课程
								// 使用原有的课程数量初始值，确保交付课程数量不超过课程数量
								if (_this.shopdetail.goodsSpecPriceList && _this.shopdetail.goodsSpecPriceList[0] &&
									_this.shopdetail.goodsSpecPriceList[1]) {
									// 确保交付课程数量不超过课程数量
									if (_this.shopdetail.goodsSpecPriceList[1].specLevelTwo > _this.shopdetail
										.goodsSpecPriceList[0].specLevelTwo) {
										_this.shopdetail.goodsSpecPriceList[1].specLevelTwo = _this.shopdetail
											.goodsSpecPriceList[0].specLevelTwo;
									}
								}
							}
						}
						if (_this.shopdetail.goodsType === 3) {
							// const newPrice =
							_this.shopdetail.goodsSpecPriceList.forEach((item) => {
								// console.log('123', item.goodsVipPrice, _this.materialInfo.actualPayment);
								// if (_this.identityType == 4) {
								// 	_this.materialInfo.actualPayment = item.goodsVipPrice = item.goodsVipPrice + _this.materialInfo.actualPayment
								// } else {
								// 	item.goodsVipPrice = item.goodsVipPrice
								// }
							});
						}
						_this.shopdetail.goodsSpecPriceList.forEach((item) => {
							if (item.specLevelOne == '交付') {
								this.specLevelTwoValue = item.specLevelTwo;
								this.maxSpecLevelTwoValue = item.specLevelTwo;
								this.maxSpecLevelTwoInfoCopy = {
									...item
								};
								this.specLevelTwoInfo = item;
							} else if (item.specLevelOne == '自有') {
								this.specLevelOneValue = item.specLevelTwo;
								this.oldSpecLevelOneValue = item.specLevelTwo;
							}
						});
						_this.shopdetail.goodsCatalogueList.forEach((item) => {
							item.down = true;
						});

						//标题
						let titleName = '商品详情';
						if (_this.shopdetail.meetingCate == 0) {
							titleName = '商品详情';
						}
						// 动态设置标题
						// uni.setNavigationBarTitle({
						// 	title: titleName
						// });
					}
					this.getHeight();
					// uni.hideLoading()

					// 所有接口请求和处理完成后，设置加载状态为false，允许页面滑动
					this.isLoading = false;
				} catch (error) {
					console.error('获取商品详情失败:', error);
					// 即使出错也要设置isLoading为false，避免页面一直被锁定
					this.isLoading = false;
					uni.showToast({
						title: '加载失败，请稍后再试',
						icon: 'none'
					});
				}
			},
			bindloadedmetadata() {
				let polyvPlayerContext = this.selectComponent('#polyv-player-id');
				polyvPlayerContext.switchQuality(1);
			},
			getHeight() {
				let _this = this;
				_this.height = '100vh';
				setTimeout(() => {
					const query = uni.createSelectorQuery().in(_this);
					query
						.select('.product_content_main')
						.boundingClientRect((data) => {
							_this.height = Number(data.bottom) + 'px';
						})
						.exec();
				}, 1000);
			},
			setCurrent(e) {
				this.currentIndex = e.detail.current + 1;
			},
			// 打开弹窗
			openCart() {
				if (this.id == 'c499556f9da64dce19b723296c884bc4' || this.id == 'f2cf0e76538473a7179c993674e09bdf') {
					uni.showToast({
						icon: 'none',
						title: '不能加入购物车哦'
					});
				} else {
					this.show = true;
				}
			},
			// 查看更多评价
			goUrl() {
				getApp().sensors.track('moreCommentsClick', {
					goodsId: this.id
				});
				if (this.evaluatelist.list.length > 0) {
					uni.navigateTo({
						url: '/splitContent/order/evaluateList?id=' + this.id
					});
				} else {
					this.$util.alter('暂无更多评论');
				}
			},
			// 查看学习资料
			goProfile(shopdetail) {
				getApp().sensors.track('learningMaterialsClick', {
					goodsId: this.id
				});
				// uni.navigateTo({
				//   url: '/Coursedetails/study/downloadProfile?id=' + this.id
				// });
				// console.log('mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm');
				//  根据不同的课程大类id跳转不同的学习资料
				// console.log('跳转学习资料', shopdetail, this.studentList);
				// 1. 数学课程进专属页面
				if (this.MATHCurriculumCodeArr.includes(shopdetail.curriculumCode)) {
					uni.navigateTo({
						url: `/Coursedetails/study/learningMaterials?goodsId=${shopdetail.goodsId}&curriculumId=${shopdetail.curriculumId}&curriculumCode=${shopdetail.curriculumCode}&goodsType=${shopdetail.goodsType}&id=${this.id}`
					});
				} else {
					//
					uni.navigateTo({
						url: `/Coursedetails/study/courseMaterials?id=${this.id}&goodsId=${shopdetail.goodsId}&curriculumId=${shopdetail.curriculumId}&curriculumCode=${shopdetail.curriculumCode}&goodsType=${shopdetail.goodsType}`
					});
				}
			},
			// 时间处理
			formatItem(createtime, show) {
				return Util.timeago(createtime, show); // 总收益
			},
			closeBrowse() {
				this.$refs.morePopup.close();
			},
			// 浏览记录查看更多
			seeMore() {
				getApp().sensors.track('buyMoreClick', {
					goodsId: this.id
				});
				this.$refs.morePopup.open();
			},
			getTimeText(specName) {
				let text = '';
				if (!isNaN(specName)) {
					// 时间类型商品
					switch (Number(specName)) {
						case 7:
							text = '周卡7天';
							break;
						case 30:
							text = '月卡30天';
							break;
						case 90:
							text = '季卡90天';
							break;
						case 180:
							text = '半年卡180天';
							break;
						default:
							text = `天卡${specName}天`;
							break;
					}
				} else {
					text = specName;
				}

				// console.log('text', text);
				return text;
			},
			// 去下单
			handleBuySubject() {
				getApp().sensors.track('placeOrderClick', {
					goodsId: this.id
				});
				this.buySubject();
			},
			// 购买    goodsType  1:实物商品，2:交付课-体验课，3:交付课-正式课，4:录播课，5:会议，6:鼎币商城 9时间类型商品
			async buySubject(type, groupHall) {
				this.isBuyAlone = false;
				this.isBuyGroup = false;
				this.flag = false;
				this.maxSecondSpecLevelTwoValue = this.maxSpecLevelTwoValue;
				// console.log(this.maxSecondSpecLevelTwoValue);

				this.couponInfo = {};
				if (!uni.getStorageSync('token')) {
					uni.navigateTo({
						url: '/Personalcenter/login/login'
					});
				} else {
					if (type == 1) {
						this.isBuyAlone = true;
						this.isGroup = '拼团';
						getApp().sensors.track('purchaseSeparately', {
							event_name: '单独购买',
							goodsId: this.id
						});
					}
					if (type == 2) {
						this.isBuyGroup = true;
						this.isGroup = '拼团';
						//发起拼团
						// console.log('发起拼团');

						getApp().sensors.track('initiateGroupBuying', {
							event_name: '发起拼团',
							goodsId: this.id,
							groupId: this.groupInstanceId
						});
					}
					console.log('this.isJoinGroup', this.isJoinGroup);
					// 若在正常购买页面，用户放弃参团，则恢复拼团默认数据
					if (!this.groupFromActivity && !this.isBuyGroup) {
						this.groupActivityId = this.tempPropObj.groupActivityId;
						this.isGroupBuyGood = this.tempPropObj.isGroupBuyGood;
						// 参团，非发起拼团
						this.isJoinGroup = this.tempPropObj.isJoinGroup;
						this.groupInstanceId = this.tempPropObj.groupInstanceId;
						this.groupActivityInfo = this.tempPropObj.groupActivityInfo;
					}
					// 若在拼团页面，用户放弃参团，恢复初始参团参数
					if (this.groupFromActivity && this.isJoinGroup && !groupHall) {
						this.isJoinGroup = this.tempPropObj.isJoinGroup;
						this.groupInstanceId = this.tempPropObj.groupInstanceId;
					}
					if (this.isNoRecommend && !this.isJoinGroup) {
						this.showRecommend = this.tempPropObj.showRecommend;
						this.recommendInfo.name = '';
						this.recommendInfo.mobile = '';
					}
					console.log('isJoinGroup', this.isJoinGroup);
					let _this = this;
					// _this.$refs.materialObjectPopup.open()
					//3:交付课-正式课
					// console.log(_this.shopdetail.goodsType, this.studentInfo.studentCode);
					if (_this.shopdetail.goodsType == 3 && this.studentInfo.studentCode) {
						// console.log(this.shopdetail.curriculumName);
						if (
							this.shopdetail.curriculumName == '鼎数学' ||
							this.CurriculumCodeArr.includes(this.shopdetail.curriculumCode) ||
							this.XKTNCurriculumCodeArr.includes(this.shopdetail.curriculumCode) ||
							this.shopdetail.curriculumName == '鼎学能' ||
							this.shopdetail.curriculumName == '珠心算' ||
							this.shopdetail.curriculumName == '拼音法' ||
							this.shopdetail.curriculumName == '拼音法（高年级）' ||
							this.shopdetail.curriculumName.includes('1对')
						) {
							//获取课程信息
							// this.getBuyInfo();
							//获取课程
							this.getSummary(this.studentInfo);
						}
					}
					//4:录播课  2:交付课-体验课
					if (_this.shopdetail.goodsType == 4 || _this.shopdetail.goodsType == 2) {
						_this.$refs.experiencePopup.open();
						//打开弹窗，记录打开时间
						this.popupStartTime = Date.now();
						//埋点-立即购买-打开订单支付弹框
						getApp().sensors.track('openOrderPayPopup', {
							name: '打开订单支付弹框',
							type: '课程',
							goodsId: _this.id
						});
						this.materialInfo.goodsOriginalPrice = this.shopdetail.goodsOriginalPrice;
						this.materialInfo.goodsPicUrl = this.shopdetail.goodsPicUrl;
						if (this.specLevelTwoValue && this.specLevelTwoInfo) {
							this.changeCourseValue(this.specLevelTwoValue, this.specLevelTwoInfo);
						} else {
							if (((this.identityType && this.identityType == 4) || this.parentMemberType == 5) && !this
								.isGroupBuyGood) {
								this.materialInfo.actualPayment = this.shopdetail.goodsVipPrice;
							} else {
								this.materialInfo.actualPayment = this.shopdetail.goodsOriginalPrice;
							}
						}
					} else if (_this.shopdetail.goodsType == 10 || _this.shopdetail.goodsType == 11) {
						if (!(this.userinfo.merchantCode)) {
							uni.showModal({
								title: '温馨提示',
								content: '该商品只支持合伙人购买',
								showCancel: false
							});
							return
						}
						this.$refs.depositPaymentRef.open();
						//埋点-立即购买-打开订单支付弹框
						getApp().sensors.track('openOrderPayPopup', {
							name: `打开订单支付弹框${this.isGroup}`,
							type: '商品',
							goodsId: _this.id
						});
						this.materialInfo.goodsOriginalPrice = this.shopdetail.goodsOriginalPrice;
						this.materialInfo.goodsPicUrl = this.shopdetail.goodsPicUrl;
						if (this.specLevelTwoValue && this.specLevelTwoInfo) {
							this.changeCourseValue(this.specLevelTwoValue, this.specLevelTwoInfo);
						} else {
							if (((this.identityType && this.identityType == 4) || this.parentMemberType == 5) && !this
								.isGroupBuyGood) {
								this.materialInfo.actualPayment = this.shopdetail.goodsVipPrice;
							} else {
								this.materialInfo.actualPayment = this.shopdetail.goodsOriginalPrice;
							}
						}
					} else {
						_this.$refs.materialObjectPopup.open();
						//打开弹窗，记录打开时间
						this.popupStartTime = Date.now();
						//埋点-立即购买-打开订单支付弹框
						getApp().sensors.track('openOrderPayPopup', {
							name: `打开订单支付弹框${this.isGroup}`,
							type: '商品',
							goodsId: _this.id
						});
						_this.materialInfo = {
							specNameOne: [],
							specNameTwo: [],
							specTypeOne: '',
							specTypeTwo: ''
						};
						// whetherSelected
						_this.shopdetail.goodsSpecList.forEach((item) => {
							if (item.specLevel == 1) {
								if (this.shopdetail.goodsType != 9) {
									_this.materialInfo.specNameOne.push(item.specName);
								} else {
									let text = this.getTimeText(item.specName);
									_this.materialInfo.specNameOne.push(text);
								}
								_this.materialInfo.specTypeOne = item.specType;
							} else {
								_this.materialInfo.specTypeTwo = item.specType;
								_this.materialInfo.specNameTwo.push(item.specName);
							}
						});

						this.shopdetail.goodsSpecPriceList.forEach((item) => {
							if (item.whetherSelected) {
								this.selectSpecNameOne = item.specLevelOne;
								this.selectSpecNameTwo = item.specLevelTwo;
								this.materialInfo = {
									...this.materialInfo,
									...item
								};
								this.specPriceId = item.id;
								if (_this.shopdetail.goodsType == 3) {
									this.specLevelTwoValue = item.specLevelTwo;
								}
								if (((this.identityType && this.identityType == 4) || this.parentMemberType ==
										5) && !this.isGroupBuyGood) {
									this.materialInfo.actualPayment =
										this.couponInfo.couponId && (this.calculateInfo.finalPrice || this
											.calculateInfo.finalPrice == 0) ?
										this.calculateInfo.finalPrice :
										Number(this.buyNum * item.goodsVipPrice).toFixed(2);
								} else {
									this.materialInfo.actualPayment =
										this.couponInfo.couponId && (this.calculateInfo.finalPrice || this
											.calculateInfo.finalPrice == 0) ?
										this.calculateInfo.finalPrice :
										Number(this.buyNum * item.goodsOriginalPrice).toFixed(2);
								}
							}
							if (item.specLevelOne == '自有') {
								this.specPriceId = item.id;
							} else {
								this.specPriceTwoId = item.id;
							}
						});
						if (_this.shopdetail.goodsType == 3) {
							if (this.specLevelTwoValue && this.specLevelTwoInfo) {
								this.changeCourseValue(this.specLevelTwoValue, this.specLevelTwoInfo);
							} else {
								if (((this.identityType && this.identityType == 4) || this.parentMemberType == 5) && !
									this.isGroupBuyGood) {
									this.materialInfo.actualPayment = this.shopdetail.goodsVipPrice;
								} else {
									this.materialInfo.actualPayment = this.shopdetail.goodsOriginalPrice;
								}
							}
							this.materialInfo.goodsPicUrl = this.shopdetail.goodsPicUrl;
							this.materialInfo.goodsVipPrice = this.shopdetail.goodsVipPrice;
							this.materialInfo.goodsOriginalPrice = this.shopdetail.goodsOriginalPrice;
						}
						if (_this.materialInfo.specNameOne.findIndex((item) => item == this.selectSpecNameOne) == -1) {
							this.selectSpecNameOne = _this.materialInfo.specNameOne[0];
							this.getSelectName('One', this.selectSpecNameOne);
						}
					}
					// console.log(this.calculateInfo, 101010);
					// if (this.calculateInfo.finalPrice) {
					// 	this.materialInfo.actualPayment = this.calculateInfo.finalPrice;
					// }
					// 自有课程数为0
					if (this.showSpecLevelOne) {
						this.couponList = [];
						this.couponInfo = {};
					} else {
						_this.getCouponList();
					}
					// console.log(this.materialInfo);
					// console.log('----------------------------------------------------');
				}
			},
			//获取优惠券 /
			async getCouponList(key) {
				let _this = this,
					arr = [];
				// const res = await $http({
				// 	url: "zx/operation/goods/coupon/list",
				// 	data: {
				// 		goodsId: this.shopdetail.goodsId,
				// 		payPrice: this.materialInfo.goodsOriginalPrice * this.value,
				// 	},
				// });
				const res = await $http({
					url: 'zx/wap/coupon/user/usable/list',
					data: {
						goodsId: _this.shopdetail.goodsId,
						userId: uni.getStorageSync('user_id') || '',
						shareable: 1
					}
				});
				if (res) {
					if (res.data) {
						_this.couponList = res.data;
						// 改变商品数量且有选择优惠券
						if (key) {
							// console.log(_this.couponInfo.couponUserReceiveId);
							let index = res.data.findIndex((item) => item.couponUserReceiveId == _this.couponInfo
								.couponUserReceiveId);
							if (index >= 0) {
								_this.getCalculate(_this.couponInfo.couponId);
							} else {
								_this.couponInfo = {};
								_this.materialInfo.actualPayment = (Number(_this.value) * Number(_this.materialInfo
									.goodsOriginalPrice)).toFixed(2);
							}
						}
						_this.couponList.forEach((item) => {
							if (item.couponUserReceiveId == _this.couponInfo.couponUserReceiveId) {
								item.check = true;
							} else {
								item.check = false;
							}
						});
					}
				}
			},
			selectCoupon() {
				this.$refs.couponPopup.open();
				this.redeemCode = '';
				if (!this.couponInfo.couponId) {
					this.couponInfo = this.couponList[0];
					this.couponList[0].check = true;
					this.getCalculate(this.couponInfo.couponId);
				}
			},
			//切换优惠券item
			changeCoupon(item) {
				// console.log('item1', item, this.couponInfo);
				if (item.check === true) {
					this.couponList.forEach((info) => {
						if (item.couponUserReceiveId == info.couponUserReceiveId) {
							info.check = false;
						}
					});
					this.couponInfo = {};
					this.getCalculate('');
				} else {
					this.couponInfo = item;
					this.getCalculate(item.couponId);
					this.couponList.forEach((info) => {
						if (item.couponUserReceiveId == info.couponUserReceiveId) {
							info.check = true;
						} else {
							info.check = false;
						}
					});
				}

				// console.log('item2', item, this.couponInfo);
			},
			// /zx/wab/order/calculate/price
			//获取优惠券
			async getCalculate(id, code) {
				let _this = this;
				const res = await $http({
					url: 'zx/wap/order/calculate/price',
					method: 'post',
					data: {
						couponId: id,
						goodsId: this.shopdetail.goodsId,
						quantity: this.value,
						specPriceId: this.specPriceId,
						redemptionCode: code // 兑换码
					}
				});
				if (res) {
					this.calculateInfo = res.data;
					this.calculateInfo.finalPrice = this.calculateInfo.finalPrice < 0 ? 0 : this.calculateInfo
						.finalPrice;
					// console.log('999999912', this.shopdetail, this.calculateInfo.finalPrice);
					// 判断是否是正式课
					if (this.shopdetail.goodsType == 3) {
						this.changeCourseValue(this.specLevelTwoValue, this.specLevelTwoInfo);
					} else {
						this.materialInfo.actualPayment = this.calculateInfo.finalPrice;
					}
				}
			},
			getSelectName(key, info) {
				this['selectSpecName' + key] = info;
				if (this.selectSpecNameOne) {
					this.shopdetail.goodsSpecPriceList.forEach((item) => {
						// 时间类型商品显示需要拼接天季年
						if ((this.shopdetail.goodsType == 9 ? this.getTimeText(item.specLevelOne) : item
								.specLevelOne) + item.specLevelTwo == this.selectSpecNameOne + this
							.selectSpecNameTwo) {
							this.materialInfo = {
								...this.materialInfo,
								...item
							};
							this.specPriceId = item.id;
							this.getCouponList();
						}
					});
					if (((this.identityType && this.identityType == 4) || this.parentMemberType == 5) && !this
						.isGroupBuyGood) {
						this.materialInfo.actualPayment = (Number(this.value) * Number(this.materialInfo.goodsVipPrice))
							.toFixed(2);
					} else {
						this.materialInfo.actualPayment = (Number(this.value) * Number(this.materialInfo
							.goodsOriginalPrice)).toFixed(2);
					}
					this.couponInfo = {};
				}
			},
			//点击好友分享
			shareFriend() {
				getApp().sensors.track('shareFriend', {
					name: '好友分享',
					goodsId: this.id
				});

				let shareContent = {
					type: '6',
					id: this.shopdetail.goodsId,
					bannerId: this.bannerId
				};
				shareContent.imgurl = this.shopdetail.goodsSharePoster;
				shareContent.title = this.shopdetail.goodsShareTextList[0] ? this.shopdetail.goodsShareTextList[0]
					.shareText : null;
				this.$refs.sharePopups.open(shareContent);
			},
			sharePoster() {
				this.$refs.sharePopup.close();
				uni.navigateTo({
					url: `/splitContent/poster/index?type=6&id=${this.id}`
				});
			},
			//立即开通
			getBecomMember() {
				getApp().sensors.track('getMembershipClick', {
					goodsId: this.id
				});
				//2024-11-6 紧急修改 购买超级会员修改成购买家长会员
				$navigationTo('Personalcenter/my/parentVipEquity?type=2');
				// $navigationTo('Personalcenter/my/nomyEquity?type=2');
			},
			//app分享
			shareApp() {
				uni.share({
					provider: 'weixin',
					scene: 'WXSceneSession',
					type: 5,
					title: this.shopdetail.courseName == null ? '有好东西分享给你' : this.shopdetail.courseName,
					imageUrl: this.shopdetail.goodsSharePoster, //分享封面
					miniProgram: {
						id: Config.miniOriginalId,
						path: '/Coursedetails/productDetils?id=' + this.id + '&scene=' + this.sceneId,
						type: 0,
						webUrl: Config.webUrl
					},
					success: (ret) => {
						uni.showToast({
							icon: 'none',
							title: '分享成功'
						});
					},
					fail: (ret) => {
						uni.showToast({
							icon: 'none',
							title: '分享失败'
						});
					}
				});
			},
			async collectChange(key) {
				if (key == 1) {
					let _this = this;
					//埋点-收藏
					getApp().sensors.track('collectClick', {
						name: '收藏',
						goodsId: _this.id
					});
					const res = await $http({
						url: 'zx/wap/goods/collect/save',
						method: 'post',
						data: {
							goodsId: this.shopdetail.goodsId,
							userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
						}
					});
					if (res) {
						uni.showToast({
							title: '收藏成功',
							icon: 'none'
						})
						this.shopdetail.whetherCollect = true;
					}
				} else {
					let _this = this;
					const res = await $http({
						url: 'zx/wap/goods/collect/cancel',
						method: 'post',
						data: {
							goodsIdList: [this.shopdetail.goodsId],
							userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
						}
					});
					if (res) {
						uni.showToast({
							title: '取消收藏',
							icon: 'none'
						})
						this.shopdetail.whetherCollect = false;
					}
				}
			},
			//小程序客服埋点
			customerService() {
				getApp().sensors.track('contactApp', {
					name: '客服',
					goodsId: this.id
				});
			},
			//app客服
			contactApp() {
				uni.share({
					provider: 'weixin',
					scene: 'WXSceneSession',
					openCustomerServiceChat: true,
					corpid: Config.contactId,
					customerUrl: Config.contactUrl,
					success: function(res) {
						// console.log(res);
					},
					fail: function(res) {
						// console.log(res);
					}
				});
			},
			tabsClick(item) {
				this.tabName = item.name;
				this.getHeight();
			},
			changeDown(index) {
				this.$set(this.videoList[Number(index)], 'down', !this.videoList[Number(index)].down);
			},
			openBenefits() {
				this.$refs.popup.open();
			},
			closeDialog() {
				this.$refs.popup.close();
				this.$refs.experiencePopup.close();
				this.$refs.formalCourse.close();
				this.value = 1;
				let result = [{
					buyNum: Number(this.value),
					courseId: this.id
				}];
				this.updateTotalPrice(result);
			},

			changeExperienceDialog(e) {
				this.rollShow = e.show;
				if (!e.show) {
					// 恢复页面进入时初始拼团数据
					if (!this.flag1) {
						this.isJoinGroup = this.tempPropObj.isJoinGroup;
						this.groupInstanceId = this.tempPropObj.groupInstanceId;
					}
					this.closeMDEventFunc('课程');
				}
			},
			changeMaterialDialog(e) {
				this.studentIndex = undefined;
				if (!e.show) {
					// 恢复页面进入时初始拼团数据
					if (!this.flag1) {
						this.isJoinGroup = this.tempPropObj.isJoinGroup;
						this.groupInstanceId = this.tempPropObj.groupInstanceId;
					}
					this.closeMDEventFunc('商品');
				}
				this.rollShow = e.show;
			},
			changeFormalEditDialog(e) {
				this.rollShow = e.show;
				if (!e.show && !this.flag1) {
					this.isJoinGroup = this.tempPropObj.isJoinGroup;
					this.groupInstanceId = this.tempPropObj.groupInstanceId;
				}
			},
			//记录弹窗存在时间的埋点事件
			reportStayTime(stayTime) {
				getApp().sensors.track('buyPageLeave', {
					event_duration: stayTime
				});
			},
			//手动点击关闭弹窗事件
			closeExperienceDialog() {
				this.closeMDEventFunc('课程');
				this.$refs.experiencePopup.close();
				const duration = (Date.now() - this.popupStartTime) / 1000;
				this.reportStayTime(duration);
				//触发埋点事件后清空
				this.popupStartTime = 0;
			},
			//点击遮罩层关闭弹窗事件
			experiencePopupClose() {
				const duration = (Date.now() - this.popupStartTime) / 1000;
				this.reportStayTime(duration);
				//触发埋点事件后清空
				this.popupStartTime = 0;
			},
			//手动点击关闭弹窗事件
			closeMaterialDialog() {
				this.closeMDEventFunc('商品');
				this.closeMaterialObjectPopup();
				const duration = (Date.now() - this.popupStartTime) / 1000;
				this.reportStayTime(duration);
				//触发埋点事件后清空
				this.popupStartTime = 0;
			},
			//点击遮罩层关闭弹窗事件
			materialObjectPopupClose() {
				const duration = (Date.now() - this.popupStartTime) / 1000;
				this.reportStayTime(duration);
				//触发埋点事件后清空
				this.popupStartTime = 0;
			},

			//埋点-立即购买-关闭订单支付弹框
			closeMDEventFunc(type) {
				getApp().sensors.track('closeOrderPayPopup', {
					name: `关闭订单支付弹框${this.isGroup}`,
					type: type,
					goodsId: this.id
				});
			},

			// 初始获取分享人信息
			async getInvitation(e) {
				let _this = this;
				_this.invitationInfo = uni.getStorageSync('invitationInfo');
				_this.shardInfo = uni.getStorageSync('shardInfo');
				if (_this.isNeedUser === '0') {
					_this.recommendInfo.name = '';
					_this.recommendInfo.mobile = '';
				} else if (this.identityType != 4 && this.parentMemberType != 5) {
					_this.recommendInfo.name = _this.shardInfo.nickName;
					_this.recommendInfo.mobile = _this.shardInfo.mobile;
				}
				if (_this.invitationInfo) {
					_this.shareId = _this.invitationInfo.userId;
					// 取过值之后立马清除本地缓存
					uni.removeStorageSync('invitationInfo');
					uni.removeStorageSync('shardInfo');
				}
			},
			changeDyyCourseValue(e) {
				// 当课程数量改变时，重新获取价格信息
				if (this.isDyy) {
					this.getPrice();
				}
			},
			// 鼎英语课程数量修改
			changeDyySelfCourseValue(e) {
				// 当课程数量改变时，重新获取价格信息
				if (this.isDyy) {
					this.getPrice();
				}
			},
			changeDyySelfCourseValue2(e) {
				this.shopdetail.goodsSpecPriceList[0].specLevelTwo = e;
				// 当课程数量改变时，重新获取价格信息
				if (this.isDyy) {
					this.getPrice();
				}
			},
			//交付课时修改
			changeCourseValue(e, item) {
				this.buyNum = e;
				let list = this.shopdetail.goodsSpecPriceList.filter((spec) => spec.specLevelOne == '自有');
				// one 自有 tow 交付
				this.courseTowPrice = Number(
					(this.identityType == 4 || this.parentMemberType == 5) && !this.isGroupBuyGood ?
					((item.goodsVipPrice / item.specLevelTwo) * this.buyNum).toFixed(2) :
					((item.goodsOriginalPrice / item.specLevelTwo) * this.buyNum).toFixed(2)
				);
				// console.log(this.courseTowPrice);
				//minSpecLevelOneValue =-1 无剩余课时
				// console.log(this.couponInfo.couponId);
				// 正式课 且有优惠券
				if (this.shopdetail.goodsType == 3 && this.couponInfo.couponId) {
					// console.log(this.minSpecLevelOneValue);
					if (this.minSpecLevelOneValue == -1 || this.minSpecLevelOneValue == -2) {
						this.courseOnePrice =
							this.couponInfo.couponId && (this.calculateInfo.finalPrice || this.calculateInfo.finalPrice ==
								0) ?
							this.calculateInfo.finalPrice :
							Number(this.identityType == 4 ? list[0].goodsVipPrice : list[0].goodsOriginalPrice);
					} else {
						this.courseOnePrice = this.calculateInfo.finalPrice;
					}
				}
				//  正式课 有效的兑换码
				else if (this.shopdetail.goodsType == 3 && this.realRedeemCode) {
					// console.log(this.realRedeemCode);
					if (this.minSpecLevelOneValue == -1 || this.minSpecLevelOneValue == -2) {
						this.courseOnePrice =
							this.realRedeemCode && (this.calculateInfo.finalPrice || this.calculateInfo.finalPrice == 0) ?
							this.calculateInfo.finalPrice :
							Number(this.identityType == 4 ? list[0].goodsVipPrice : list[0].goodsOriginalPrice);
					} else {
						this.courseOnePrice = this.calculateInfo.finalPrice;
					}
				} else {
					// 判断是否有选择学员
					if (this.getStudentInfo) {
						this.courseOnePrice =
							(this.couponInfo.couponId || this.realRedeemCode) && (this.calculateInfo.finalPrice || this
								.calculateInfo.finalPrice == 0) ?
							this.calculateInfo.finalPrice :
							Number(this.identityType == 4 ? list[0].goodsVipPrice : list[0].goodsOriginalPrice);
					} else {
						this.courseOnePrice = Number(
							(this.identityType == 4 || this.parentMemberType == 5) && !this.isGroupBuyGood ?
							(list[0].goodsVipPrice / this.oldSpecLevelOneValue) * this.changeSpecLevelOneValue :
							(list[0].goodsOriginalPrice / this.oldSpecLevelOneValue) * this.changeSpecLevelOneValue
						);
					}
				}
				this.materialInfo.actualPayment = (this.courseTowPrice + this.courseOnePrice).toFixed(2);
				// console.log(this.materialInfo.actualPayment);
			},

			async changeValue(e) {
				let _this = this;
				_this.buyNum = e;
				let result = [{
					buyNum: Number(_this.buyNum),
					courseId: this.id
				}];
				if (this.couponInfo.couponId) {
					this.getCouponList(1);
				} else {
					this.getCouponList();
					if (((this.identityType && this.identityType == 4) || this.parentMemberType == 5) && !this
						.isGroupBuyGood) {
						this.materialInfo.actualPayment = (Number(this.value) * Number(this.materialInfo
							.goodsVipPrice)).toFixed(2);
					} else {
						this.materialInfo.actualPayment = (Number(this.value) * Number(this.materialInfo
							.goodsOriginalPrice)).toFixed(2);
					}
				}
				// materialInfo.actualPayment
				this.updateTotalPrice(result);
			},

			// 更新价格
			async updateTotalPrice(result) {
				let _this = this,
					arr = [];
				const res = await $http({
					url: 'zx/course/calculatePrice',
					method: 'post',
					data: {
						courseAndNumDto: result,
						currencyNumber: 0
					}
				});
				if (res) {
					_this.cartTotalPrice = res.data.payPrice;
				}
			},

			handleSavePayRecord() {
				if (this.activityInfo.activityId && this.activityInfo.shareUserCode) {
					$http({
						url: 'zx/wap/activity/pay/record/save',
						method: 'post',
						data: {
							activityId: this.activityInfo.activityId,
							shareUserCode: this.activityInfo.shareUserCode,
							type: 1,
							orderId: this.orderId
						}
					});
				}
			},

			inputtext(e) {
				console.log(e, '订单备注-------')
				this.remark = e;
				// console.log(this.remark, '订单备注');
				// console.log(this.remark.length, '订单备注长度');
				if (this.remark.length >= 200) {
					$showMsg('订单备注最多200个字');
					this.$nextTick(() => {
						this.remark = e.substring(0, 200);
					});
					return false;
				}
			},
			async coursePayDyy() {
				uni.showLoading();
				let params = {
					...this.referenceArr
				};
				// params.studentName = this.studentInfo.studentName; // 学员姓名
				params.studentCode = this.studentInfo.studentCode; // 支付账户
				params.deliverMode = 'CENTER'; // 交付方式
				params.level = this.periodInfo.level; // 学段
				params.courseLength = this.shopdetail.goodsSpecPriceList[0].specLevelTwo; // 自有课时数量
				params.deliverCourseLength = this.shopdetail.goodsSpecPriceList[1].specLevelTwo; // 交付课时数量
				params.coursePrice = this.addPrices; // 自有课时单价
				params.deliverCoursePrice = this.priceDetail[this.periodIndex].extArr.CENTER / 100; // 交付课时单价
				params.totalPrice = params.deliverCoursePrice * params.deliverCourseLength + params.coursePrice *
					params.courseLength; // 合计金额
				params.remark = this.remark; // 充值说明
				params.orderSource = 1; // 订单来源
				params.zxOrderId = this.orderIdDyy; // 订单ID
				uni.showLoading();
				let res = await $http({
					url: 'znyy/areas/student/charge/course/save/zx',
					method: 'post',
					data: params
				});
				res.data.lineCollectInfo.collectUserCodes = JSON.parse(res.data.lineCollectInfo.userCollectDetail);
				this.payBtn(res.data.lineCollectInfo);
			},
			// 课程支付
			async coursePay(key) {
				if (this.shopdetail.goodsType == 3) {
					if (!this.specLevelTwoFalse) {
						$showMsg('请保存购买交付课时数量');
						return;
					}
					if (!this.specLevelTwoFalse1) {
						$showMsg('请保存购买自有课时数量');
						return;
					}
				}
				if (this.isDyy) {
					if (!this.specLevelTwoFalse2) {
						$showMsg('请保存购买课程数量');
						return;
					}
					// 验证课程数量：必须为0或大于等于初始默认数
					const courseCount = this.shopdetail.goodsSpecPriceList[0].specLevelTwo;
					if (courseCount > 0 && courseCount < this.minCount) {
						$showMsg(`课程数量必须为0或大于等于${this.minCount}课时`);
						return;
					}

					// 统一校验：验证交付课程数量与自有课程数量的关系
					const totalCount = Number(this.shopdetail.goodsSpecPriceList[1].specLevelTwo) - Number(this
						.shopdetail.goodsSpecPriceList[0].specLevelTwo);
					const currentDeliverHours = Number(this.maxPurchaseDeliverSum);
					const tipHours = Number(this.shopdetail.goodsSpecPriceList[0].specLevelTwo) + currentDeliverHours;
					if (totalCount > currentDeliverHours) {
						uni.showModal({
							title: '提示',
							content: `交付课时数大于自有课时数，建议：可购买最大交付课时${tipHours}课时`,
							showCancel: false,
							confirmText: '我知道了'
						});
						return;
					}

					// 当课程数量大于0时，交付课程数量至少为1节
					if (this.shopdetail.goodsSpecPriceList[0].specLevelTwo > 0 && this.shopdetail.goodsSpecPriceList[1]
						.specLevelTwo < 1) {
						$showMsg('当课程数量大于0时，交付课程数量至少为1课时');
						return;
					}
				}
				let _this = this;
				if (_this.flag) {
					return;
				}

				_this.flag = true;
				uni.showLoading();
				let result = {
					buyNum: Number(_this.buyNum),
					courseId: this.id
				};
				// 只有39.9试课券才会走这里
				// specLevelTwoFalse

				if (_this.shopdetail.goodsType != 1 && _this.shopdetail.goodsType != 10 && _this.shopdetail
					.goodsType != 11) {
					if (_this.isDyy) {
						// 鼎英语
						if (!_this.trialclassStudent) {
							$showMsg('请输入姓名');
							_this.flag = false;
							return false;
						}
						if (!_this.periodInfo.level) {
							$showMsg('请输入学段');
							_this.flag = false;
							return false;
						}
						// 推荐人信息
						// if (!_this.recommendInfo.name) {
						//   $showMsg('请输入正确的推荐人姓名');
						//   _this.flag = false;
						//   return false;
						// }
						// 推荐人信息
						const phoneRegex = /^1[3-9]\d{9}$/;
						console.log(_this.recommendInfo.mobile, 'wuwuwuwuuwuwuw ');

						if (!_this.recommendInfo.mobile || !phoneRegex.test(_this.recommendInfo.mobile)) {
							$showMsg('请输入正确的推荐人手机号');
							_this.flag = false;
							return false;
						} else {
							let joioData = {};
							joioData.referrerPhone = _this.recommendInfo.mobile;
							let res = await $http({
								url: 'zx/wap/order/getReferenceCode',
								method: 'get',
								data: joioData
							});
							if (res.success) {
								_this.referenceArr = res.data;
							} else {
								$showMsg(res.message);
								_this.flag = false;
								return false;
							}
						}
					} else {
						console.log('表单校验--------')
						if (!_this.trialclassStudent) {
							$showMsg('请输入姓名');
							_this.flag = false;
							return false;
						}

						// if (!Util.isMobile(_this.parentMobile)) {
						//   $showMsg("请输入正确的联系方式");
						//   _this.flag = false;
						//   return false;
						// }

						// 不显示推荐人
						if (this.isHideRecommend) {
							// console.log('dukang🚀 ~ _this.is', _this.recommendInfo);
							_this.recommendInfo.name = '无';
							_this.recommendInfo.mobile = '无';
						}

						// 推荐人信息
						// if (!_this.recommendInfo.name) {
						// 	$showMsg('请输入正确的推荐人姓名,如无推荐人则填写‘无’');
						// 	_this.flag = false;
						// 	return false;
						// }
						// 推荐人信息
						if (!_this.recommendInfo.mobile) {
							$showMsg('请输入推荐人联系方式,如无推荐人则填写‘无’');
							_this.flag = false;
							return false;
						}
						if (_this.recommendInfo.mobile != '无') {
							if (!Util.isMobile(_this.recommendInfo.mobile)) {
								$showMsg('请输入正确的推荐人手机号');
								_this.flag = false;
								return false;
							}
						}
					}
				}

				if (this.shopdetail.goodsType == 1) {
					if (!_this.addressInfo.addressId) {
						$showMsg('请填写收货地址');
						_this.flag = false;
						return false;
					}
				}
				// 若扫码绑定的合伙人与当前账号历史绑定的合伙人不一致，则给出合伙人冲突提示
				let scanInfo = uni.getStorageSync('scanInfo');
				let parseScanInfo = scanInfo ? JSON.parse(scanInfo) : null;
				let hasReconmmedInfo = _this.recommendInfo.mobile && this.shopdetail.goodsType != 1;
				if (hasReconmmedInfo && scanInfo && parseScanInfo.merchantPhone && parseScanInfo.merchantPhone
					.trim() != _this.recommendInfo.mobile.trim()) {
					// uni.hideLoading();
					// $showMsg('绑定信息冲突，请返回首页后重新下单');
					uni.showModal({
						title: '温馨提示',
						content: '绑定信息冲突，请返回首页后重新下单',
						showCancel: false,
						success: function(res) {
							if (res.confirm) {
								uni.setStorageSync('hasConflictToast', true);
								uni.navigateBack();
							}
						}
					});
					_this.flag = false;
					return false;
				}
				//埋点-立即购买-去支付
				getApp().sensors.track('productDetilsBuyClick', {
					name: this.isGroupBuyGood ? '拼团活动' : '立即购买',
					goodsId: this.id
				});
				/////埋点
				// sensors.track("courseDetailPayEvt" ,{
				//     "$recommendName": _this.recommendInfo.name,
				//     "$parentMobile":_this.parentMobile,
				//     "$studentName":_this.trialclassStudent,
				//     "$remark":_this.remark
				// });
				////埋点
				let url = '',
					params = {};
				url = 'zx/wap/order/generate';
				// console.log(this.value);
				params.addressId = this.shopdetail.goodsType == 1 ? _this.addressInfo.addressId : undefined;
				// params.currencyNumber = _this.userinfo.currencyNumber;
				// params.discountsAmount = 0;
				params.remark = _this.remark;
				if (this.isBuyGroup) {
					// groupFlag 发起拼团团长 2、参与拼团用户
					params.groupFlag = this.isJoinGroup ? 2 : 1;
					// groupInstanceId 1、参与拼团的活动id 2、拼团的id
					params.groupActivityOrInstanceId = this.isJoinGroup ? this.groupInstanceId : this.groupActivityId;
				}
				if (this.isBuyAlone) {
					// 购买单独商品
					params.groupFlag = 0;
				}
				// params.courseAndNumDto = result;
				params.goods = {
					// 兑换码
					redemptionCode: this.realRedeemCode,
					goodsType: this.shopdetail.goodsType,
					couponId: this.couponInfo.couponId,
					goodsId: this.shopdetail.goodsId,
					payAmount: this.isBuyGroup ? this.calcGroupPrice : this.materialInfo.actualPayment,
					purchaseQuantity: this.value,
					specPriceId: this.shopdetail.goodsType != 4 ? this.specPriceId : undefined,
					couponUserReceiveId: this.couponInfo.couponUserReceiveId
				};
				// 鼎英语
				if (_this.isDyy) {
					params.level = this.periodInfo.level; // 学段
					params.goods.payAmount =
						_this.shopdetail.goodsSpecPriceList[0].specLevelTwo * _this.addPrices +
						_this.shopdetail.goodsSpecPriceList[1].specLevelTwo * (_this.priceDetail[_this.periodIndex]
							.extArr.CENTER / 100); // 拼团价格
				}
				if (this.shopdetail.goodsSpecPriceList && this.shopdetail.goodsSpecPriceList.length > 0) {
					this.shopdetail.goodsSpecPriceList.forEach((item) => {
						if (item.specLevelOne == '自有') {
							params.goods.couponSpecPriceId = item.id;
						}
					});
				}
				// console.log('goodsSpecPriceList', this.shopdetail.goodsSpecPriceList);
				if (this.shopdetail.goodsType == 3) {
					if (this.shopdetail.goodsSpecPriceList && this.shopdetail.goodsSpecPriceList.length > 0 && this
						.isBuyGroup) {
						this.shopdetail.goodsSpecPriceList.forEach((item) => {
							if (item.specLevelOne == '自有') {
								params.goods.specPriceQuantity = item.specLevelTwo;
							}
						});
					} else {
						params.goods.specPriceQuantity = this.changeSpecLevelOneValue;
					}
					// console.log('🚀 ~ coursePay ~ this.changeSpecLevelOneValue:', this.changeSpecLevelOneValue);
					params.goods.specPriceTwoId = this.specPriceTwoId;
					params.goods.specPriceTwoQuantity = this.specLevelTwoValue;
				} else if (this.shopdetail.goodsType == 10 || this.shopdetail.goodsType == 11) {
					params.goods.specPriceQuantity = this.shopdetail.goodsSpecPriceList[0].specLevelOne
				}
				// 课程数量问题处理
				// specPriceQuantity 自有 // specPriceTwoQuantity 交付
				if (this.isDyy) {
					if (this.shopdetail.goodsSpecPriceList && this.shopdetail.goodsSpecPriceList.length > 0) {
						this.shopdetail.goodsSpecPriceList.forEach((item) => {
							if (item.specLevelOne == '自有') {
								params.goods.specPriceQuantity = Number(item.specLevelTwo) || 0;
							}
							if (item.specLevelOne == '交付') {
								params.goods.specPriceTwoQuantity = Number(item.specLevelTwo) || 0;
							}
						});
					}
				}
				params.shareId = _this.shareId;
				if (_this.recommendInfo != null && this.shopdetail.goodsType != 1) {
					params.referrerName = _this.recommendInfo.name;
					params.referrerPhone = _this.recommendInfo.mobile;
				}
				if (this.shopdetail.goodsType == 2) {
					params.studentName = _this.trialclassStudent;
				}
				params.parentsMobile = _this.parentMobile;
				if (this.isShowChangePhoneNumber) {
					// 可代购 实际购买人
					params.actualBuyMobile = _this.parentMobile;
				}
				params = {
					...params,
					...this.studentInfo
				};
				uni.showLoading();
				const res = await $http({
					url: url,
					method: 'post',
					data: params
				});
				if (res) {
					if (res.data) {
						_this.isNewAndOldOrder = res.data.newAndOldOrder;
						_this.leftHaveCourseHours = res.data.leftHaveCourseHours;
						_this.orderId = res.data.orderId;
						_this.orderNo = res.data.sourceOrderId;
						_this.newData = res.data;
						uni.setStorageSync('orderId_zx', res.data.sourceOrderId);
						if (!this.groupInstanceId && this.isGroupBuyGood) {
							this.groupInstanceId = res.data.groupInstanceId;
						}
						// needPay 1需要支付  0不需要支付
						if (res.data.needPay == 1) {
							if (_this.isNewAndOldOrder) {
								this.$refs.newAndOld.open();
							} else if (_this.isDyy) {
								// 购买鼎英语
								_this.orderIdDyy = res.data.orderId;
								_this.coursePayDyy();
							} else {
								_this.payBtn(res.data);
							}
						} else {
							this.handleSavePayRecord();
							_this.successorder(res.data.orderId);
						}
					} else {
						uni.hideLoading();
						// uni.showToast({
						//   icon: "none",
						//   title: res.message,
						//   duration: 2500,
						// });
						uni.showModal({
							title: '温馨提示',
							content: res.message,
							showCancel: false
						});
					}
				} else {
					_this.flag = false;
				}
			},
			closePay() {
				this.$refs.newAndOld.close();
				this.isNewAndOldOrder = false;
			},
			immediatePayment() {
				this.payBtn(this.newData);
			},
			async payBtn(data) {
				uni.showLoading();
				let _this = this;
				let resdata = await httpUser.post('mps/line/collect/order/unified/multi/collect/check', data);
				let res = resdata.data.data;
				_this.disabled = false;
				uni.hideLoading();
				if (res) {
					if (res.openAllinPayMini) {
						this.flag1 = true;
						this.payInfo = res;
						uni.$payTlian(res);
					} else {
						uni.requestPayment({
							provider: 'wxpay',
							timeStamp: res.payInfo.timeStamp,
							nonceStr: res.payInfo.nonceStr,
							package: res.payInfo.packageX,
							signType: res.payInfo.signType,
							paySign: res.payInfo.paySign,
							success: function(ress) {
								this.flag = false;
								// _this.successpay(id);
								if (_this.shopdetail.goodsType == 4) {
									_this.$refs.paymentPopup.open();
								} else {
									_this.redirectToOrderIndex();
								}
							},
							fail: function(err) {
								uni.showToast({
									title: '支付失败'
								});
								this.flag = false;
								setTimeout(function() {
									uni.redirectTo({
										url: '/splitContent/order/order'
									});
								}, 1500);
							}
						});
					}
				}
				this.couponInfo = {};
				this.getCouponList();
				//支付完成，支付弹窗关闭
				this.$refs.experiencePopup.close();
				this.closeMaterialObjectPopup();
				//支付弹窗关闭，浏览结束，计算浏览时间
				const duration = (Date.now() - this.popupStartTime) / 1000;
				this.reportStayTime(duration);
				//触发埋点事件后清空
				this.popupStartTime = 0;
				this.$refs.formalCourse.close();
			},
			//关闭弹窗
			closeMaterialObjectPopup() {
				this.$refs.materialObjectPopup.close();
			},
			paymentPopupClose() {
				this.$refs.paymentPopup.close();
				this.redirectToOrderIndex();
			},
			studentPopupClose() {
				this.$refs.studentPopup.close();
				let url = '';
				if (this.isShowChangePhoneNumber) {
					url =
						`/Personalcenter/my/mystudentAdd?type=1&phone=${this.parentMobile}&memberId=${uni.getStorageSync('user_code')}`;
				} else {
					url = `/Personalcenter/my/mystudentAdd?type=1&memberId=${uni.getStorageSync('user_code')}`;
				}
				uni.navigateTo({
					url: url
				});
			},
			async successorder(orderId) {
				let _this = this;
				const resdata = await $http({
					url: 'zx/course/orderPayNoMoney/' + orderId,
					data: {}
				});
				if (resdata) {
					_this.redirectToOrderIndex();
				}
			},
			getNewTime() {
				var taday = new Date();
				var year = taday.getFullYear();
				var month = taday.getMonth() + 1;
				var day = taday.getDate();
				var hours = taday.getHours();
				var min = taday.getMinutes();
				var seconds = taday.getSeconds();
				return (
					year +
					'-' +
					(month < 10 ? '0' + month : month) +
					'-' +
					(day < 10 ? '0' + day : day) +
					' ' +
					(hours < 10 ? '0' + hours : hours) +
					':' +
					(min < 10 ? '0' + min : min) +
					':' +
					(seconds < 10 ? '0' + seconds : seconds)
				);
			},
			redirectToOrderIndex() {
				let _this = this;
				_this.flag = false;
				let info = {
					payAmount: this.materialInfo.actualPayment,
					payAmountMode: '微信支付',
					orderId: this.orderId,
					orderNo: this.orderNo,
					payAmountTime: this.getNewTime(),
					goodsType: this.shopdetail.goodsType
				};
				uni.setStorageSync('orderInfoPayAmount', JSON.stringify(info));
				// 拼团购买显示邀请
				if (!this.isBuyGroup) {
					let url = '/Coursedetails/tips/lessonTips';
					let referrerType = '';
					let nickName = uni.getStorageSync('nickName');
					if (nickName != this.parentMobile) {
						referrerType = 1;
					} else {
						referrerType = 0;
					}
					// console.log('referrerType', referrerType, this.shopdetail.goodsType);

					uni.redirectTo({
						// url: _this.params.url //原来跳转商品列表页面
						url: `${url}?goodsType=${encodeURIComponent(this.shopdetail.goodsType)}&referrerType=${encodeURIComponent(referrerType)}`
					});
				} else {
					if (this.groupInstanceId == this.tempPropObj.groupInstanceId) {
						this.needUpdateGroupStatus = true;
					}
					this.openInvitePopup();
				}

				// _this.$refs.uToast.show({
				// 	..._this.params,
				// 	complete() {
				// 		_this.params.url && uni.redirectTo({
				// 			url: _this.params.url
				// 		})
				// 	}
				// })
			},

			// 根据分享id获取推荐人信息
			async getMobile() {
				let that = this;
				const res = await $http({
					url: 'zx/exp/getReferrerMobile',
					data: {
						shareId: that.shareId
					}
				});
				if (res) {
					that.recommendInfo = res.data;
					if (that.recommendInfo == null) {
						that.recommendInfo = {
							mobile: '无',
							name: ''
						};
					} else {
						if (!that.recommendInfo.name) {
							that.recommendInfo.name = '无';
						}
						if (!that.recommendInfo.mobile) {
							that.recommendInfo.mobile = '无';
						}
					}
				}
			},
			periodPickerChange(e) {
				this.periodIndex = Number(e.target.value);
				this.periodInfo.level = this.periodList[this.periodIndex].value;
				this.getPrice();
			},
			async bindPickerChange(e) {
				this.haveHourSelfVisible = false;
				this.studentIndex = Number(e.target.value);
				// 如果是鼎英语课程，重置默认展示初始值
				if (this.isDyy && this.shopdetail && this.shopdetail.goodsSpecPriceList) {
					// 重置课程数量为初始值
					if (this.shopdetail.goodsSpecPriceList[0]) {
						this.shopdetail.goodsSpecPriceList[0].specLevelTwo = this.maxSpecLevelTwoInfoCopy
							.specLevelTwo || this.shopdetail.goodsSpecPriceList[0].specLevelTwo;
					}
					if (this.shopdetail.goodsSpecPriceList[1]) {
						this.shopdetail.goodsSpecPriceList[1].specLevelTwo = this.maxSpecLevelTwoInfoCopy
							.specLevelTwo || this.shopdetail.goodsSpecPriceList[1].specLevelTwo;
					}
				}
				if (e.target.value >= 0) {
					if (
						(this.shopdetail.curriculumName == '鼎数学' ||
							this.CurriculumCodeArr.includes(this.shopdetail.curriculumCode) ||
							this.XKTNCurriculumCodeArr.includes(this.shopdetail.curriculumCode) ||
							this.shopdetail.curriculumName == '鼎学能' ||
							this.shopdetail.curriculumName == '珠心算' ||
							this.shopdetail.curriculumName == '拼音法' ||
							this.shopdetail.curriculumName == '拼音法（高年级）' ||
							this.shopdetail.curriculumName.includes('1对')) &&
						this.shopdetail.goodsType == 3
					) {
						this.getSummary(this.studentList[this.studentIndex], !this.studentInfo.studentCode);
					}
					this.trialclassStudent = this.studentList[this.studentIndex].realName;
					this.studentInfo.studentCode = this.studentList[this.studentIndex].studentCode;
					this.studentInfo.studentName = this.studentList[this.studentIndex].realName;
				}

				if (this.studentInfo.studentCode && this.referenceArrSave.merchantCode && this.referenceArrSave
					.schoolType) {
					let _this = this;
					const res = await $http({
						url: 'zx/student/getStudentCourseInfoForDyy',
						data: {
							studentCode: _this.studentInfo.studentCode,
							merchantCode: _this.referenceArrSave.merchantCode,
							schoolType: _this.referenceArrSave.schoolType
						}
					});
					if (res.success) {
						_this.haveHourSelf = Number(res.data.haveCourseHours); // 自有课时
						_this.haveHourDeliver = Number(res.data.haveDeliverHours); // 交付课时
						_this.maxPurchaseDeliverSum = Number(res.data.maxPurchaseDeliverSum); // 可购买最大交付时长（节）
						if (_this.haveHourSelf === 0 && _this.haveHourDeliver === 0) {
							// 交付课时 = 0 且 自有课时 = 0 时，视为第一次购买
							// 使用原有的课程数量初始值，确保交付课程数量不超过课程数量
							if (_this.shopdetail.goodsSpecPriceList && _this.shopdetail.goodsSpecPriceList[0] && _this
								.shopdetail.goodsSpecPriceList[1]) {
								// 确保交付课程数量不超过课程数量
								if (_this.shopdetail.goodsSpecPriceList[1].specLevelTwo > _this.shopdetail
									.goodsSpecPriceList[0].specLevelTwo) {
									_this.shopdetail.goodsSpecPriceList[1].specLevelTwo = _this.shopdetail
										.goodsSpecPriceList[0].specLevelTwo;
								}
							}
						} else {
							// 非首次购买，可购买0节自有，单独购买交付
							_this.haveHourSelfVisible = true; // 自有课时可见
						}
					}
				}
			},
			async bindPickerChanges(e) {
				// console.log(e,'下拉框选项被选中-------')
				console.log('设置selectedStatusIndex为:', e); // 调试信息

				// 如果点击的是已选中的项目，则取消选中
				if (this.selectedStatusIndex === e) {
					this.selectedStatusIndex = null; // 取消选中状态
					this.studentIndex = -1;
					this.trialclassStudent = '';
					this.studentInfo.studentCode = '';
					this.studentInfo.studentName = '';
					console.log('取消选中，清空数据'); // 调试信息
					return;
				}

				this.selectedStatusIndex = e; // 设置选中的状态索引
				this.studentIndex = Number(e);
				console.log('当前selectedStatusIndex:', this.selectedStatusIndex); // 调试信息
				if (e >= 0) {
					if (
						(this.shopdetail.curriculumName == '鼎数学' ||
							this.CurriculumCodeArr.includes(this.shopdetail.curriculumCode) ||
							this.shopdetail.curriculumName == '鼎学能' ||
							this.shopdetail.curriculumName == '珠心算' ||
							this.shopdetail.curriculumName == '拼音法' ||
							this.shopdetail.curriculumName == '拼音法（高年级）' ||
							this.shopdetail.curriculumName.includes('1对')) &&
						this.shopdetail.goodsType == 3
					) {
						this.getSummary(this.studentList[this.studentIndex], !this.studentInfo.studentCode);
					}
					this.trialclassStudent = this.studentList[this.studentIndex].realName;
					this.studentInfo.studentCode = this.studentList[this.studentIndex].studentCode;
					this.studentInfo.studentName = this.studentList[this.studentIndex].realName;
				}
			},
			// 我的课程列表
			async getSummary(info, show) {
				// console.log(info);
				const res = await $http({
					// url: 'zx/wap/course/student/course/summary',
					url: 'zx/wap/course/student/course/buyInfo',
					data: {
						studentCode: info.studentCode,
						// needZnyyCourseHours: false,
						curriculumId: this.shopdetail.curriculumId,
						goodsId: this.shopdetail.goodsId
					}
				});
				if (res) {
					// console.log(res.data);
					this.showSpecLevelOne = false;
					this.getStudentInfo = false;
					if (res.data) {
						//haveDeliverHours:交付专用课时
						//销课交付课时 :relievedDeliverHours
						// maxSpecLevelTwoValue: 0, //交付课最大课时
						//minSpecLevelTwoValue: 0, //交付课最小课时
						//maxSpecLevelOneValue: 0, //交付课自有课时
						//         minSpecLevelOneValue: 0, //交付课自有课时
						// Number(res.data.maxBuyHaveDeliverHours) +
						// 拼团 正式课-自有交付课数量不可修改
						if (!this.isBuyGroup) {
							this.maxSpecLevelTwoValue = Number(res.data.haveCourseHours) > 0 ? Number(res.data
								.haveCourseHours) : Number(res.data.maxBuyHaveDeliverHours); //可购买最大交付课时
							this.maxSpecLevelOneValue = Number(res.data.maxBuyHaveCourseHours); //可购买最大自有课时
							// console.log(this.maxSpecLevelTwoValue);
							let num = Number(res.data.totalCourseHours);

							if (num > 0) {
								this.newBuyRule = res.data.newBuyRule; //新二次购买规则
								this.minSpecLevelTwoValue = Number(res.data.minBuyHaveDeliverHours) == -1 ? 1 : Number(
									res.data.minBuyHaveDeliverHours); //可购买最小交付课时
								this.minSpecLevelOneValue = Number(res.data.minBuyHaveCourseHours) == 0 ? Number(res
									.data.minBuyHaveCourseHours) : -1; //可购买最小自有课时
								this.SecondBuy = true;
								this.specLevelTwoValue = Number(res.data.minBuyHaveDeliverHours);
								if (this.newBuyRule) {
									// this.specLevelTwoValue = this.shopdetail.goodsSpecPriceList[1].specLevelTwo;
									this.maxSpecLevelTwoValue = Number(res.data.maxBuyHaveDeliverHours) + Number(res
										.data.haveCourseHours);
									this.changeSpecLevelOneValue = res.data.defaultCourseHoursShow;
									this.specLevelTwoValue = res.data.defaultDeliverHoursShow;
								} else {
									this.changeSpecLevelOneValue = Number(res.data.minBuyHaveCourseHours) == -1 ?
										Number(res.data
											.maxBuyHaveCourseHours) : Number(res.data.minBuyHaveCourseHours);
								}
								this.courseOnePrice = 0;
								this.courseTowPrice = 0;
							} else {
								this.SecondBuy = false;
								this.specLevelTwoValue = Number(res.data.maxBuyHaveDeliverHours);
								this.changeSpecLevelOneValue = Number(res.data.maxBuyHaveCourseHours);
							}
							if ((this.specLevelTwoValue >= 0 && res.data[0]) || (this.specLevelTwoValue < 0 && res
									.data[0])) {
								this.showSpecLevelOne = true;
								this.specLevelOneValue = 0;
								if (this.specLevelTwoValue <= 0) {
									this.specLevelTwoValue = 0;
									if (this.studentList.length < 3) {
										this.showStudentType = 2;
										this.$refs.studentPopup.open();
									} else {
										this.showSpecLevelOne = false;
										this.maxSpecLevelTwoValue = this.maxSpecLevelTwoInfoCopy.specLevelTwo;
									}
								}
							} else {
								// this.shopdetail.goodsSpecPriceList.forEach((item) => {
								//   if (item.specLevelOne == '交付') {
								//     if (item.specLevelTwo < this.specLevelTwoValue || !show) {
								//       this.specLevelTwoValue = item.specLevelTwo;
								//     }
								//     this.maxSpecLevelTwoValue = item.specLevelTwo;
								//     this.specLevelTwoInfo = item;
								//   } else if (item.specLevelOne == '自有') {
								//     this.specLevelOneValue = item.specLevelTwo;
								//   }
								// });
							}
						} else {
							this.SecondBuy = false;
							this.specLevelTwoFalse1 = true;
							this.specLevelTwoFalse = true;
							this.specLevelTwoFalse2 = true;
							this.specLevelTwoValue = this.specLevelTwoInfo.specLevelTwo;
							this.changeSpecLevelOneValue = Number(res.data.maxBuyHaveCourseHours);
						}

						this.changeCourseValue(this.specLevelTwoValue, this.specLevelTwoInfo);
					}
				}
			},
			getGroupActivityInfo() {
				$http({
					url: 'zx/wap/group/activity/good/page',
					method: 'get',
					data: {
						groupActivityId: this.groupActivityId,
						goodsId: this.id,
						pageSize: 1
					}
				}).then((res) => {
					// console.log('🚀 ~ getGroupActivityInfo ~ res:', res);
					// 活动下架
					if (!res) {
						this.isGroupBuyGood = false;
					}
					if (res.data) {
						this.groupActivityInfo = res.data.data && res.data.data[0] ? res.data.data[0] : {};
						this.tempPropObj.groupActivityInfo = res.data.data && res.data.data[0] ? res.data.data[0] :
						{};
					}
				});
			},
			// 获取可参团列表
			async getAccessGroupList() {
				this.accessGroupList.forEach((el) => {
					if (el.currentTimer) {
						// 清除定时器
						el.currentTimer.clear();
						el.currentTimer = null;
					}
				});
				console.log('拼团定时器清除成功');
				this.accessGroupList = [];

				// 获取参团人数
				let _this = this;
				$http({
					url: 'zx/wap/group/underWayCount',
					data: {
						groupActivityId: _this.groupActivityId,
						goodsId: _this.id
					}
				}).then((el) => {
					_this.accessGroupToTal = el.data;
				});
				const res = await $http({
					url: 'zx/wap/group/canJoinPage',
					data: {
						pageNum: 1,
						pageSize: 100,
						groupActivityId: _this.groupActivityId,
						goodsId: this.id
					}
				});
				if (res.data) {
					_this.accessGroupList = res.data.data.map((el, index) => {
						el.countdownObj = {
							hours: '00',
							minutes: '00',
							seconds: '00'
						};
						el.currentTimer = null;
						el.handleFunc = (type, countdown) => {
							// 1 倒计时结束 2 更新倒计时时间 3 开启倒计时
							if (type == '1') {
								el.countdownObj.hours = '00';
								el.countdownObj.minutes = '00';
								el.countdownObj.seconds = '00';
								el.currentTimer = null;
								_this.accessGroupList.splice(index, 1);
								// 重新加载拼团列表数据
								_this.getAccessGroupList();
							} else if (type == '2') {
								el.countdownObj.hours = countdown.hours;
								el.countdownObj.minutes = countdown.minutes;
								el.countdownObj.seconds = countdown.seconds;
							}
						};
						el.currentTimer = countdownUtil.start(el.endTime, el.currentTimer, el.countdownObj, el
							.handleFunc);
						return el;
					});
				}
			},
			// 点击立即参团按钮
			async handlePopupJoinGroup(value) {
				// 第一次点击立即参团按钮，保存页面初始拼团数据
				if (!this.isNoRecommend) {
					this.tempPropObj.groupActivityId = this.groupActivityId;
					this.tempPropObj.groupInstanceId = this.groupInstanceId;
					this.tempPropObj.isGroupBuyGood = this.isGroupBuyGood;
					this.tempPropObj.isJoinGroup = this.isJoinGroup;
					this.tempPropObj.groupActivityInfo = this.groupActivityInfo;
					this.tempPropObj.showRecommend = this.showRecommend;
				}
				this.isJoinGroup = true;
				this.groupInstanceId = value.groupInstancesId.toString();
				// 正常商品详情内参与拼团活动// 参团，非发起拼团
				if (this.shopdetail.groupActivityId && !this.isGroupBuyGood && !this.groupFromActivity) {
					this.groupActivityId = this.shopdetail.groupActivityId;
					this.isGroupBuyGood = true;
					// 获取拼团活动信息
					const groupInfo = await $http({
						url: 'zx/wap/group/activity/good/page',
						data: {
							groupActivityId: this.groupActivityId,
							goodsId: this.id,
							pageSize: 1
						}
					});
					if (!groupInfo) {
						this.isGroupBuyGood = false;
					}
					if (groupInfo.data) {
						this.groupActivityInfo = groupInfo.data.data && groupInfo.data.data[0] ? groupInfo.data.data[
							0] : {};
					}
				}
				// 推荐人信息
				if ((!this.recommendInfo.name && !this.recommendInfo.mobile) || !this.showRecommend || this
					.isNoRecommend) {
					this.isNoRecommend = true;
					this.showRecommend = true;
					this.recommendInfo.name = value.leadUserName;
					this.recommendInfo.mobile = value.leadMobile;
				}
				this.buySubject(2, true);
			},
			// 点击拼团列表--立即参与
			getMoreGroups(enterType) {
				// 1正常商品参与拼团入口  2拼团商品参与拼团入口
				if (enterType == '1') {
					this.$refs.groupHallRef.openPopup(this.shopdetail.groupActivityId, this.id);
				} else if (enterType == '2') {
					this.$refs.groupHallRef.openPopup();
				}
			},

			// 点击修改按钮
			changeFouce(ele) {
				this[ele] = true;
			},
			// 兑换码校验格式
			validateCouponCode(code) {
				const couponCodeRegex = /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;
				return couponCodeRegex.test(code);
			},

			// 兑换码输入框失焦
			async handleCodeBlur() {
				this.realRedeemCode = '';
				if (!this.redeemCode) {
					return;
				}
				if (!this.validateCouponCode(this.redeemCode)) {
					$showMsg('请输入正确的兑换码');
					return;
				}

				uni.showLoading({
					title: '校验中...',
					mask: true
				});
				const res = await $http({
					url: `zx/wap/coupon/redemption/code/info?redemptionCode=${this.redeemCode}`
				});

				if (res.data) {
					this.realRedeemCode = this.redeemCode;
				}
			},
			handleClearCode() {
				this.redeemCode = '';
				this.realRedeemCode = '';
			},

			async getIncome() {
				let _this = this;
				const resdata = await $http({
					url: 'zx/course/getCourseShareProfitVo',
					data: {
						courseId: _this.id
					}
				});
				if (resdata) {
					_this.amount = resdata.data;
				}
			},
			// 获取可领取优惠券
			async getAvailableCoupons() {
				// /zx/wap/coupon/user/available/list
				const res = await $http({
					url: 'zx/wap/coupon/user/available/list',
					data: {
						goodsId: this.id,
						userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '',
						shareable: 1,
						// 可显示在小程序端
						showApp: 1
					}
				});
				if (res.code === 20000) {
					this.availableList = res.data;
				}
				// console.log('user', res, this.availableList);
			},
			// 领券列表
			getCoupons() {
				this.$refs.getCoupon.open();
				this.availableList = [];
				this.getAvailableCoupons();
			},
			async receivedCoupon(item) {
				uni.showLoading({
					title: '领取中...',
					mask: true
				});
				const res = await $http({
					url: 'zx/wap/coupon/user/received',
					method: 'POST',
					data: {
						couponId: item.couponId,
						userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
					}
				});

				if (res.code == 20000) {
					uni.showToast({
						title: '领取成功',
						icon: 'success'
					});
					this.getAvailableCoupons();
				}
			},
			handleUpdateGroupStatus() {
				this.needUpdateGroupStatus = false;
			}
		}
	};
</script>
<style>
	.placeholder-style {
		color: #555555 !important;
		font-size: 28rpx;
	}

	.vertical-img {
		vertical-align: bottom;
	}
</style>
<style lang="scss" scoped>
	.no-input ::v-deep input {
		pointer-events: none;
		/* 禁止点击输入框 */
		/* 或使用 readonly 属性效果 */
		/* readonly: true; */
	}

	/* 搜索框容器 */
	.search-container {
		padding: 10rpx 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		// background: linear-gradient(to right, #f4fafc, #f0fcf8);
		/* 添加内部底部边距，不会影响与下方盒子的贴合 */
	}

	.search-wrapper {
		margin: 0 auto;
	}

	/* 搜索框 */
	.search-css {
		height: 60rpx;
		display: flex;
		align-items: center;
		border-radius: 40rpx;
		width: 578rpx;
		background-color: #f4f4f6;

		.search-input {
			color: #bebebe;
			height: 100%;
			font-size: 24rpx;
			line-height: 60rpx;
			margin-left: 16rpx;
		}
	}

	// 吸顶导航样式
	.sticky-nav {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
		background-color: #ffffff;
		border-bottom: 1px solid #f0f0f0;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		opacity: 0;
		transform: translateY(-100%);
		transition: all 0.3s ease-in-out;

		&.sticky-nav-show {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.sticky-nav-container {
		display: flex;
		height: 74rpx;
		align-items: center;
		justify-content: space-around;
		padding: 0 30rpx;
		background-color: #ffffff;
	}

	.sticky-nav-item {
		flex: 1;
		text-align: center;
		font-size: 28rpx;
		color: #666666;
		padding: 20rpx 10rpx;
		position: relative;
		transition: all 0.3s ease;
		border-radius: 8rpx;

		&.active {
			color: #009b55;
			font-weight: bold;

			&::after {
				content: '';
				position: absolute;
				bottom: 8rpx;
				left: 50%;
				transform: translateX(-50%);
				width: 60rpx;
				height: 4rpx;
				// background: linear-gradient(135deg, #ff6b35, #f7931e);
				border-radius: 2rpx;
			}
		}

		&:active {
			background-color: #f8f8f8;
			transform: scale(0.95);
		}
	}

	.tags {
		background-color: #ffa816;
		color: #ffffff;
		font-size: 24rpx;
		padding: 5rpx 20rpx;
		border-radius: 10rpx;
		align-items: center;
		margin-right: 8rpx;
	}

	/deep/ .u-back-top {
		background-color: transparent !important;
	}

	.moduleStyle {
		margin-top: 16rpx;
		// border-radius: 24rpx;
		padding: 30rpx;
		background-color: #ffffff;
	}

	.moduleStyles {
		margin-top: 16rpx;
		// border-radius: 24rpx;
		padding: 30rpx 0;
		background-color: #ffffff;
	}

	.moduleTitleStyle {
		display: flex;
		align-items: center;
		// background-color: aqua;
		padding-bottom: 28rpx;
		border-bottom: 1rpx solid #e6e6e6;
	}

	.moduleTitleStyles {
		display: flex;
		align-items: center;
		// background-color: aqua;
		padding: 0 30rpx 28rpx;
		border-bottom: 1rpx solid #e6e6e6;
	}

	.course-container {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		align-items: center;
	}

	/* 轮播图页码指示器样式 */
	.swiper-indicator {
		position: absolute;
		bottom: 20rpx;
		right: 20rpx;
		background-color: rgba(0, 0, 0, 0.4);
		color: #ffffff;
		padding: 10rpx 20rpx;
		border-radius: 30rpx;
		font-size: 24rpx;
		z-index: 10;
	}

	.swiper-barrage {
		position: absolute;
		bottom: 20rpx;
		left: 20rpx;

		color: #ffffff;
		// padding: 10rpx 20rpx;

		font-size: 20rpx;
		// z-index: 10;
	}

	/* 左侧按钮样式 */
	.custom-left-btn {
		width: 48rpx;
		height: 48rpx;
		border-radius: 50%;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		align-items: center;
		justify-content: center;
		// transition: background-color 0.2s;
		margin-left: 10px;
		/* 左侧安全距离 */
	}

	/* 右侧图标样式 */
	.custom-right-icon {
		width: 48rpx;
		height: 48rpx;
		border-radius: 50%;
		background: rgba(0, 0, 0, 0.5);
		/* 垂直居中 */
		display: flex;
		align-items: center;
		justify-content: center;
		/* 优化点击区域 */
		// padding: 8px 0 8px 8px;
		// height: 100%;
		// box-sizing: border-box;
	}

	.swiperImage {
		width: 750rpx;
		height: 840rpx;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.cartIcon {
		width: 120rpx;
		height: 120rpx;
		position: fixed;
		bottom: 20vh;
		right: 30rpx;
		border-radius: 70rpx;
		box-shadow: 0 0 15rpx rgba(0, 0, 0, 0.1);
	}

	.addcart {
		border: 1rpx solid #ea6031;
		height: 70upx;
		width: 200rpx;
		border-radius: 40upx;
		line-height: 70upx;
		text-align: center;
		color: #ea6031;
		// padding: 0 20rpx;
	}

	.sheets {
		position: absolute;
		bottom: 30rpx;
		right: 30rpx;
		padding: 10rpx 30rpx;
		border-radius: 40rpx;
		background-color: rgba(0, 0, 0, 0.3);
		z-index: 1;
	}

	.fixed_coupon_area {
		width: 750rpx;
		height: 58rpx;
		position: fixed;
		padding: 0 28rpx;

		// right: 30rpx;
		bottom: 130rpx;
		z-index: 999;
		background-color: #ffebde;
	}

	.fixed_coupon {
		height: 100%;
		width: 694rpx;
		display: flex;
		color: #fc6600;
		font-size: 24rpx;
		justify-content: space-between;
		align-items: center;
	}

	.fixed_b {
		position: fixed;
		left: 0;
		bottom: 0;
		width: 100%;
		padding: 14upx 0 40rpx 0;
		background-color: #fff;
		box-shadow: 0 0 15upx 0 rgba(0, 0, 0, 0.2);

		.padding_left {
			padding-left: 35rpx;
		}

		.fixed_button {
			border-radius: 16rpx;
			overflow: hidden;
			display: flex;
			heigth: 74rpx;
			line-height: 74rpx;
			text-align: center;
			// width: 426rpx;
			color: #fff;
			// margin-right: 32rpx;

			.button_left {
				width: 194rpx;
				background-color: #56c7a5;
			}

			.button_rights {
				width: 332rpx;
				font-size: 28rpx;
				background-color: #009b55;
			}

			.button_right {
				width: 232rpx;
				background-color: #fd9b2a;
			}

			.button_disabled {
				background-color: #d3d3d3;
			}
		}
	}

	.add_cart {
		background-image: linear-gradient(to bottom, #88cfba, #1d755c);
		height: 90upx;
		border-radius: 50upx;
		line-height: 90upx;
		text-align: center;
		color: #fff;
		// padding: 0 20rpx;
	}

	.buy_s {
		background-image: linear-gradient(to bottom, #88cfba, #1d755c);
		height: 70upx;
		width: 200rpx;
		border-radius: 40upx;
		line-height: 70upx;
		text-align: center;
		color: #fff;
		// padding: 0 20rpx;
	}

	/deep/.u-safe-area-inset-bottom {
		padding-bottom: 20rpx !important;
	}

	.benefits {
		width: 150rpx;
		height: 60rpx;
		color: #0e5c4e;
		font-size: 28rpx;
		line-height: 60rpx;
		text-align: center;
		border-radius: 45rpx;
		border: 1px solid #0e5c4e;
	}

	/* 21天结束复习弹窗样式 */
	.reviewCard_box {
		width: 670rpx;
		/* height: 560rpx; */
		position: relative;
	}

	.reviewCard_box image {
		width: 100%;
		height: 100%;
	}

	.reviewCard {
		position: relative;
		width: 100%;
		height: 100%;
		background: #ffffff;
		color: #000;
		border-radius: 24upx;
		padding: 45upx 30upx;
		box-sizing: border-box;
	}

	.cartoom_image {
		width: 420rpx;
		position: absolute;
		top: -250rpx;
		left: 145rpx;
		z-index: -1;
	}

	.review_close {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		z-index: 1;
	}

	.top-tips {
		position: fixed;
		background: rgba(255, 255, 255, 0.9);
		top: 0;
		z-index: 1;
		width: 100%;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 10rpx;
		font-size: 24rpx;
		box-sizing: border-box;

		image {
			width: 60rpx;
			height: 60rpx;
		}

		.text-con {
			width: 480rpx;
			text-align: left;
		}

		.btn_download {
			width: 130rpx;
			height: 50rpx;
			font-size: 24rpx;
			text-align: center;
			line-height: 50rpx;
			border-radius: 8rpx;
			background: #339378;
			color: #fff;
		}
	}

	.content {
		display: flex;
		flex-direction: column;
		height: 100vh;
		overflow-y: scroll;

		.section_item {
			overflow: hidden;
		}

		.section_items {
			overflow: hidden;
			background-color: #000;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.video_css_content {
			height: 375rpx;
			width: 750rpx;
		}

		.product_content_main {
			position: absolute;
			// width: 686rpx;
			// border-top-left-radius: 24rpx;
			// border-top-right-radius: 24rpx;
			padding-bottom: 200rpx;
			// overflow-y: scroll;
			// height: calc(100vh - 472rpx);
			// background: linear-gradient(180deg, #ffffff 0%, #f0f5fa 15%, #f9fcff 50%, #f0f5fa 100%);
			background-color: #f2f6f7;

			.w686 {
				width: 686rpx;
			}

			.color_red_css {
				color: #fc6600;
				display: inline-block;
			}

			.member_back {
				width: 210rpx;
				height: 44rpx;
				line-height: 44rpx;
				text-align: center;
				background: url('https://document.dxznjy.com/course/8442f0de8da146798babc4aa04065bc9.png') no-repeat;
				background-size: 100%;
				margin-left: 5rpx;
			}

			.goods_name_css {
				// overflow: hidden;
				// text-overflow: ellipsis;  /* 超出部分省略号 */
				// word-break: break-all;  /* break-all(允许在单词内换行。) */
				// display: -webkit-box; /* 对象作为伸缩盒子模型显示 */
				// -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
				// -webkit-line-clamp: 2; /* 显示的行数 */
			}
		}
	}

	.review_btn {
		width: 250upx;
		height: 80upx;
		background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
		border-radius: 45upx;
		font-size: 30upx;
		color: #ffffff;
		line-height: 80upx;
		justify-content: center;
		text-align: center;
	}

	/deep/.uni-popup__wrapper {
		justify-content: center !important;
	}

	// 解决订单支付弹窗被吸顶导航遮挡的问题
	/deep/.uni-popup {
		z-index: 1000 !important;
	}

	// 步进器
	/deep/.uni-numbox__minus,
	/deep/.uni-numbox__plus {
		width: 48rpx;
		height: 48rpx;
		padding: 0 !important;
		// border-radius: 50% !important;
		background-color: #f7f7f7 !important;
	}

	/deep/.uni-numbox__value {
		margin: 0 !important;
		height: 60rpx !important;
		background-color: #fff !important;
		font-size: 40rpx;
		font-weight: bold;
		color: #333;
	}

	// /deep/.uni-numbox__plus{
	// 	width: 60rpx;
	// 	height: 60rpx;
	// 	padding: 0 !important;
	// 	border-radius: 50% !important;
	// 	background-color: #E0E0E0 !important;
	// }

	/deep/ .uni-numbox {
		height: 48rpx;
		// background-color: #F7F7F7 !important;
		border-radius: 48rpx !important;
	}

	/deep/ .uni-numbox__value {
		width: 69rpx !important;
		height: 48rpx !important;
		line-height: 40rpx;
	}

	// 正式课
	.content-popup {
		background-color: #f1f1f1;
		border-top-left-radius: 45rpx;
		border-top-right-radius: 45rpx;
		max-height: 85vh;
		overflow-y: scroll;

		.content_top_popup {
			background-color: #fff;
			padding: 24rpx 32rpx;

			.tet_css {
				display: inline-block;
				margin-left: 16rpx;
			}
		}

		.material_content {
			padding: 46rpx 32rpx;
		}
	}

	.remark {
		height: 150rpx;
		width: 100%;
		overflow: scroll;
	}

	.remarks {
		// height: 150rpx;
		width: 100%;
		overflow: scroll;
	}

	.icon-clear {
		display: flex;
		justify-content: flex-end;
	}

	.flex_s {
		display: flex;
		align-items: center;
	}

	.paybtn {
		position: fixed;
		z-index: 999;
		bottom: 10rpx;
		width: 686rpx;
		height: 74rpx;
		line-height: 74rpx;
		text-align: center;
		border-radius: 38rpx;
		background-color: #009b55;
		margin: 0 auto;
	}

	.amount {
		background-color: #ea6031;
		border-radius: 40rpx;
		padding: 8rpx 15rpx;
		margin-left: 20rpx;
		font-size: 28rpx;
		color: #fff;
	}

	// 体验课
	.information {
		height: 108upx;
		display: flex;
		align-items: center;
		border-bottom: 1px solid #efefef;
	}

	.informations {
		padding: 32rpx 0;
		// height: 168upx;
		// display: flex;
		// align-items: center;
		border-bottom: 1px solid #efefef;
	}

	.phone-input {
		color: #999;
		border: none;
		display: flex;
		height: 70rpx;
		font-size: 28rpx;
		align-items: center;
	}

	.redeemCode-input {
		font-family: AlibabaPuHuiTiBold;
		font-weight: normal;
		font-size: 28rpx;
		color: #555555;
		line-height: 40rpx;
	}

	.redtext {
		color: red;
		margin-right: 4rpx;
	}

	.tipText {
		padding-top: 30upx;
		display: flex;
		align-items: center;
	}

	/deep/ .input {
		width: 87% !important;
	}

	// 商品评价
	.head-img {
		width: 64rpx;
		height: 64rpx;
		border-radius: 50%;
		margin-right: 20rpx;
	}

	.avatars {
		position: relative;
		width: 128rpx;
		height: 64rpx;
		margin-right: 20rpx;

		.avatar {
			position: absolute;
			width: 64rpx;
			height: 64rpx;
			border-radius: 50%;
		}

		.avatar:first-child {
			top: 0;
			left: 0;
			z-index: 3;
		}

		.avatar:nth-child(2) {
			top: 0;
			left: 32rpx;
			z-index: 2;
		}

		.avatar:nth-child(3) {
			top: 0;
			left: 64rpx;
			z-index: 1;
		}
	}

	.golden {
		color: #886a34;
		height: 38rpx;
		font-size: 26rpx;
		padding: 0 8rpx;
		line-height: 38rpx;
		text-align: center;
		border-radius: 6rpx;
		background: linear-gradient(to right, #f5ebd6, #dec288);
	}

	.evalue-content {
		word-wrap: break-word;
		word-break: break-all;
		// display: -webkit-box;
		// -webkit-box-orient: vertical;
		// overflow: hidden;
		// word-break: break-all;
		// text-overflow: ellipsis;
		// -webkit-line-clamp: 2; /* 控制显示的行数 */
	}

	.content_css {
		width: 686rpx;
		margin: 0 auto;
		position: relative;

		.curriculum_image {
			width: 686rpx;
			height: 718rpx;
		}

		.content_title_css {
			position: absolute;
			bottom: 0;
			left: 0;
			height: 410rpx;
			width: 100%;
			text-align: center;

			.content_center_css {
				width: 100%;
				text-align: center;
				margin-top: 120rpx;
			}

			.color_student {
				color: #56c7a5;
			}
		}
	}

	// 购买记录
	.shopping {
		color: #fff;
		width: 120rpx;
		height: 50rpx;
		font-size: 28rpx;
		text-align: center;
		line-height: 50rpx;
		border-radius: 30rpx;
		background-color: #2f8c70;
	}

	.swiper-item {
		display: block;
		height: 120rpx !important;
		line-height: 120rpx;
		text-align: center;
	}

	.user_name {
		max-width: 130rpx;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
	}

	.browse-popup {
		background-color: #fff;
		border-top-left-radius: 45rpx;
		border-top-right-radius: 45rpx;
	}

	.dialog-icon {
		position: absolute;
		top: 25rpx;
		right: 25rpx;
	}

	.browse-title {
		padding: 30rpx;
		font-size: 30rpx;
		background-color: #f4f4f4;
	}

	/deep/ .browse-popup {
		height: 1100rpx !important;
	}

	/deep/ .u-divider {
		margin: 0 auto !important;
	}

	.order_title {
		width: 100%;
		text-align: center;
		font-size: 32rpx;
		color: #333;
		font-weight: bold;
	}

	.specLevelTwo_css {
		height: 50rpx;
		width: 80rpx;
		line-height: 50rpx;
		text-align: center;
		background-color: #009b55;
	}

	.gray_background {
		background-color: darkgray;
	}

	.product_content_css {
		padding-bottom: 40rpx;
		border-bottom: 2rpx solid #f0f0f0;

		.product_info {
			display: flex;
			justify-content: flex-start;

			.couser_image {
				width: 152rpx;
				height: 152rpx;
				display: block;
			}

			.product_right {
				margin-left: 24rpx;

				.price_tangerine {
					color: #fc6600;
					font-weight: bold;

					.price_icon {
						display: inline-block;
						margin-left: 7rpx;
						margin-right: 5rpx;
					}

					.price_title {
						font-size: 28rpx;
					}

					.price_css {
						font-size: 40rpx;
					}

					.original_price_css {
						margin-top: 10rpx;
						text-decoration: line-through;
						color: #959697;

						// text-decoration: line-through;
						.original_title {
							display: inline-block;
							text-decoration: line-through;
							// margin-left: 16rpx;
							// margin-right: 10rpx;
						}
					}
				}

				.number_plus_css {
					margin-top: 20rpx;
				}
			}
		}

		.coupon_content {
			margin-top: 48rpx;

			.coupon_left_css {
				.coupon_botton {
					display: inline-block;
					width: 154rpx;
					height: 42rpx;
					line-height: 41rpx;
					border: 1rpx solid #ff9100;
					color: #ff9100;
					text-align: center;
					border-radius: 8rpx;
					margin-left: 16rpx;
				}
			}

			.coupon_right_css {
				font-size: 28rpx;
				color: #ff9100;

				.coupon_title {
					display: inline-block;
					width: 220rpx;
					text-overflow: ellipsis;
					overflow: hidden;
					word-break: break-all;
					white-space: nowrap;
					vertical-align: middle;
				}

				/deep/.u-icon {
					display: inline-block !important;
					vertical-align: middle;
				}
			}
		}
	}

	.item_css {
		background-color: #f2f2f2;
		display: inline-block;
		border-radius: 8rpx;
		padding: 10rpx 24rpx;
	}

	.close_css {
		position: absolute;
		right: 29rpx;
		top: -11rpx;
		width: 40rpx;
		height: 40rpx;
	}

	.button_css {
		width: 230rpx;
		padding: 10rpx 0;
		margin: 0 auto;
		border-radius: 30rpx;
		margin-top: 90rpx;
	}

	.active_css {
		background-color: #56c7a5;
		color: #fff;
	}

	.payment_css {
		text-align: center;
		margin-top: 48rpx;
	}

	.display_inline {
		display: inline-block;
		color: #339378;
	}

	.couponPopup_content {
		height: 85vh;
		background-color: #fff;
		border-radius: 24rpx 24rpx 0rpx 0rpx;

		.coupon_price {
			width: 686rpx;
			height: 146rpx;
			margin: 0 auto;
			margin-top: 34rpx;
			background: url('https://document.dxznjy.com/course/5eb5e0ab050646bb80e1f69b1a2ccad9.png') no-repeat;
			background-size: 100%;
			color: #fff;

			.flex_state {
				display: flex;
				justify-content: flex-start;
				align-items: center;
				padding-left: 32rpx;
				text-align: center;
				line-height: 42rpx;

				.w96 {
					width: 96rpx;
				}

				.w65 {
					width: 56rpx;
				}

				.w124 {
					width: 124rpx;
				}

				.w96 {
					width: 96rpx;
				}

				.w75 {
					width: 75rpx;
				}

				.w60 {
					width: 60rpx;
				}

				.w55 {
					width: 55rpx;
				}
			}
		}

		.coupon_item {
			width: 686rpx;
			height: 146rpx;
			background: url('https://document.dxznjy.com/course/2613084130c84f9e80a6c91da112cdc5.png') no-repeat;
			background-size: 100%;
			display: flex;
			justify-content: flex-start;
			align-items: center;

			.coupon_left {
				width: 204rpx;
				text-align: center;
			}

			.coupon_center {
				width: 400rpx;
				margin-left: 32rpx;

				.coupon_name_css {
					color: #a25700;
				}

				.coupon_time_css {
					color: #fd9b2a;
					margin-top: 20rpx;
				}
			}

			.wh26 {
				width: 26rpx;
				height: 26rpx;
			}
		}
	}

	.display_block {
		display: inline-block;
	}

	.coupon_label_css {
		display: flex;
		justify-content: flex-start;
		align-items: center;
		color: #fd9b2a;

		.coupon_header_css {
			padding-left: 16rpx;
			padding-right: 14rpx;
			background-color: #fff2e3;
			position: relative;
		}

		.plr1416 {
			padding-left: 14rpx;
			padding-right: 16rpx;
		}

		.coupon_header_css::after {
			content: ' ';
			border-right: 2rpx solid #fd9b2a;
			position: absolute;
			height: 26rpx;
			right: 0;
			top: 7rpx;
		}

		.coupon_item {
			border: 1rpx solid #fd9b2a;
			line-height: 34rpx;
			padding: 4rpx 0;
		}
	}

	.label_css {
		font-size: 24rpx;
		padding: 8rpx 16rpx;
		color: #5f6162;
		background-color: #f0f4f5;
	}

	.member_content_css {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
		// width: 686rpx;
		height: 64rpx;
		background: url('https://document.dxznjy.com/course/7eefc948b350461c8c1e56f894ff54d6.png') no-repeat;
		background-size: 100%;
		// background-color: green;

		.member_botton {
			text-align: center;
			width: 120rpx;
			height: 52rpx;
			line-height: 50rpx;
			background-color: #009b55;
		}

		.member_botton,
		.member_text {
			vertical-align: middle;
		}
	}

	.purchase_records {
		border-radius: 24rpx;

		.time_height_css {
			height: 90rpx;
			line-height: 75rpx;
			padding-top: 25rpx;
			overflow: hidden;
		}
	}

	.wh24 {
		width: 24rpx;
		height: 24rpx;
	}

	.wh40 {
		width: 40rpx;
		height: 40rpx;
	}

	.product_details {
		border-radius: 24rpx 24rpx 0 0;

		/deep/.u-tabs__wrapper__scroll-view {
			margin-top: 24rpx;
			padding-bottom: 24rpx;
		}

		/deep/.u-tabs__wrapper__nav__line {
			margin-left: 26rpx;
		}
	}

	.get-coupons {
		width: 120rpx;
		height: 52rpx;
		text-align: center;
		line-height: 52rpx;
		background: #009b55;
		border-radius: 8rpx;
		margin-left: 26rpx;
		font-family: PingFang-SC, PingFang-SC;
		font-weight: bold;
		font-size: 24rpx;
		color: #ffffff;
		text-align: center;
	}

	.coupon-popup {
		background-color: #fff;
		padding: 32rpx 14rpx 34rpx 18rpx;
		border-radius: 24rpx 24rpx 0 0;

		// overflow-y: scroll;
		.coupon-title {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 42rpx;
			font-family: AlibabaPuHuiTi_2_85_Bold;
			font-size: 32rpx;
			color: #333333;
			line-height: 42rpx;
			text-align: left;
			font-style: normal;
			font-weight: bold;
			margin-bottom: 24rpx;
		}

		.content {
			max-height: 760rpx;
			background: #fbfbfb;
			border-radius: 8rpx;
			padding: 20rpx 16rpx 0 16rpx;

			.require {
				height: 42rpx;
				font-family: AlibabaPuHuiTi_2_85_Bold;
				font-size: 28rpx;
				color: #339378;
				line-height: 42rpx;
				text-align: left;
				font-style: normal;
				font-weight: bold;
				margin-left: 2rpx;
				margin-bottom: 24rpx;
			}

			.item {
				width: 686rpx;
				background: #f0fef9;
				border-radius: 16rpx;
				margin-bottom: 24rpx;
				padding: 50rpx 0 56rpx 0;

				.activity-type {
					width: 158rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;

					.activity-name {
						font-family: AlibabaPuHuiTi_2_85_Bold;
						font-size: 40rpx;
						color: #489981;
						line-height: 56rpx;
						text-align: left;
						font-style: normal;
						font-weight: bold;
						margin-bottom: 4rpx;
					}

					.activity-limit {
						font-family: AlibabaPuHuiTi_2_55_Regular;
						font-size: 24rpx;
						color: #489981;
						line-height: 34rpx;
						text-align: right;
						font-style: normal;
					}
				}

				.activity-content {
					width: 358rpx;

					.title {
						margin-top: 6rpx;
						font-family: AlibabaPuHuiTi_2_85_Bold;
						font-size: 28rpx;
						color: #555555;
						line-height: 40rpx;
						text-align: left;
						font-weight: bold;
						margin-bottom: 8rpx;
					}

					.time {
						font-family: AlibabaPuHuiTi_2_55_Regular;
						font-size: 28rpx;
						color: #c5c4c4;
						line-height: 40rpx;
						text-align: left;
					}
				}

				.activity-use {
					width: 120rpx;
					height: 48rpx;
					background: #339378;
					border-radius: 24rpx;
					margin-left: 20rpx;
					font-weight: bold;
					font-size: 24rpx;
					color: #ffffff;
					line-height: 48rpx;
					text-align: center;
				}
			}
		}
	}

	.height_scroll {
		height: calc(85vh - 380rpx);
		overflow-y: scroll;
	}

	.orderRemark {
		display: flex;
		justify-content: flex-end;
	}

	.newAndOld-center {
		width: 600rpx;
		height: 300rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
		flex-direction: column;
		padding: 20rpx 10rpx 20rpx 10rpx;
		background-color: #ffffff;
		border-radius: 10rpx;
	}

	.newAndOld-title {
		font-size: 40rpx;
		font-weight: 400;
	}

	.newAndOld-footer {
		display: flex;
		justify-content: space-around;
		align-items: center;
		width: 100%;
	}

	.newAndOld-btn {
		height: 60rpx;
		padding-left: 40rpx;
		padding-right: 40rpx;
		text-align: center;
		line-height: 60rpx;
		border-radius: 27rpx;
	}

	.right {
		color: #ffffff;
		background-color: #339378;
	}

	.left {
		background-color: #e8e6e6;
	}

	// 加载中样式
	.loading-container {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100vh;
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 9999;
		background-color: rgba(255, 255, 255, 0.9);
	}

	.loading-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.loading-image {
		width: 200rpx;
		margin-bottom: 20rpx;
	}

	.loading-text {
		font-size: 28rpx;
		color: #339378;
	}

	// 参与拼团入口
	.group-enter {
		width: 100%;
		height: 198rpx;
		margin-top: 14rpx;

		.enter-pic {
			width: 100%;
			height: 100%;
		}
	}

	// 参与拼团列表
	.countdown {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 115rpx;
		// line-height: 75rpx;
		overflow: hidden;
	}

	.count_down_css {
		color: #2f8c70;
		line-height: 40rpx;
	}

	.join-btn {
		color: #fff;
		width: 120rpx;
		height: 50rpx;
		text-align: center;
		line-height: 50rpx;
		border-radius: 30rpx;
		background-color: #2f8c70;
	}

	.primary_color {
		color: #2f8c70;
	}

	// 状态按钮样式
	.status-buttons-container {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
		padding: 20rpx 0;
	}

	.status-button {
		padding: 10rpx 15rpx;
		// flex: 1;
		// min-width: 150rpx;
		// height: 70rpx;
		// line-height: 70rpx;
		// text-align: center;
		border: 2rpx solid #e5e5e5;
		border-radius: 16rpx;
		background-color: #ffffff;
		color: #666;
		font-size: 28rpx;
		transition: all 0.3s ease;
	}

	.status-button-active {
		background-color: #029d56;
		border-color: #029d56;
		color: #ffffff;
		// font-weight: bold !important;
	}

	// u-number-box 按钮图标大小设置
	/deep/.u-number-box__plus {
		.u-icon__icon {
			font-size: 20rpx !important; // 调整加号图标大小
		}
	}

	/deep/.u-number-box__minus {
		.u-icon__icon {
			font-size: 20rpx !important; // 调整减号图标大小
		}
	}

	/* 弹幕透明度效果 - 使用三个独立的蒙版盒子 */
	.barrage-container {
		position: relative;
		// height: 150rpx;
		width: 420rpx;
	}

	/* 加载中样式 */
	.loading-container {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100vh;
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 9999;
		background-color: rgba(255, 255, 255, 0.9);
		touch-action: none;
		/* 阻止所有触摸操作 */
		pointer-events: auto;
		/* 确保元素可以接收事件 */
		user-select: none;
		/* 禁止选中文本 */
	}

	.loading-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		pointer-events: auto;
		/* 确保子元素可以接收事件 */
	}

	.loading-image {
		width: 160rpx;
		margin-bottom: 20rpx;
	}

	.loading-text {
		font-size: 28rpx;
		color: #339378;
		font-weight: bold;
	}

	.rich-text-container {
    // touch-action: pan-y;
		// min-height: 150rpx;
    // height: 200rpx;
		/* 确保至少有这个高度 */
		/* 其他样式 */
		// box-sizing: border-box;
	}
</style>