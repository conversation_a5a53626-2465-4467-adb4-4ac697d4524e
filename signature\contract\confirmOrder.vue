<template>
  <view class="ctxt plr-32 bg-ff">
    <view class="radius-20 pt-30">
      <view class="bold c-33 title_css f-28">选择套餐：</view>
      <!-- <view> -->
      <scroll-view scroll-x="true" @scrolltolower="scrolltolower">
        <view class="hbox">
          <view class="innerBox" :class="{ chooseBox: selectedIndex === index }" v-for="(item, index) in textGroup" @click="chooseImg(index)">
            <view class="txt_head">
              {{ item.packageName }}
            </view>
            <view class="txt_org">{{ item.packagePrice }}元</view>
            <view class="inner_text">
              <text class="txt_gray">{{ item.packageUnitPrice }}元/份 共计{{ item.contractAmount }}份合同</text>
              <text class="txt_green">有效期{{ item.packageTimeUnit }}{{ item.packageTimeUnitName }}</text>
            </view>
          </view>
        </view>
      </scroll-view>
      <!-- </view> -->
      <view>
        <view class="bold pt-35 c-33 title_css">确认订单信息：</view>
        <view class="mbox lh-40" v-if="hasValues(orderInfo)">
          <view>应用订单号 ：{{ orderInfo.orderNumber }}</view>
          <view>套餐名称 ：{{ orderInfo.packageName }}</view>
          <view>套餐内合同数（份）：{{ orderInfo.contractAmount }}</view>
          <view>
            有效期：{{ orderInfo.packageTimeUnit }}{{ orderInfo.packageTimeUnitName }}
            <span class="f-24 txt_gray">合同订单付款成功后开始计算</span>
          </view>
          <view>套餐总金额（元）：{{ orderInfo.packagePrice }}</view>
          <view>应付金额（元）：{{ orderInfo.packagePrice }}</view>
        </view>
      </view>
      <view class="m-45">
        <button class="nextstep f-28" form-type="submit" @click="submit('baseForm')" :disabled="disabled">创建订单并支付</button>
      </view>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  export default {
    data() {
      return {
        selectedIndex: 0,
        orderInfo: {},
        textGroup: [],
        packageName: '',
        infoLists: {},
        flag1: false,
        page: 1,
        payInfo: {}
      };
    },
    onLoad() {
      this.getPackageList();
    },
    onShow() {
      if (this.flag1) {
        uni.$tlpayResult(this.sucees, this.fail, this.payInfo.orderId);
      }
    },
    methods: {
      sucees() {
        this.flag1 = false;
        this.successorder();
      },
      fail() {
        this.flag1 = false;
      },
      fails() {
        uni.showToast({
          title: '支付失败',
          icon: 'none',
          duration: 2000
        });
        this.flag1 = false;
      },
      scrolltolower() {
        if (this.page * 20 >= this.infoLists.totalItems) {
          return false;
        }
        this.getPackageList(true, ++this.page);
      },
      chooseImg(index) {
        this.selectedIndex = index;
        this.orderInfo = this.textGroup[this.selectedIndex];
      },
      // /
      async getPackageList(isPage, page) {
        uni.showLoading({
          title: '加载中...'
        });
        page = page || 1;
        let _this = this;
        const res = await $http({
          url: 'zx/wap/pay/package/record/getPackageList',
          data: {
            packageName: this.packageName,
            pageNum: page,
            pageSize: 20
          }
        });
        if (res) {
          _this.infoLists = res.data;
          if (isPage) {
            _this.textGroup = [..._this.textGroup, ...res.data.data];
          } else {
            _this.textGroup = res.data.data;
          }
          _this.chooseImg(_this.selectedIndex);
          uni.hideLoading();
          console.log('---------------------------------------------------');
        }
      },
      async submit() {
        let _this = this;
        uni.showLoading();
        const res = await $http({
          url: 'zx/wap/pay/package/record/geneOrder',
          method: 'post',
          data: {
            actualPayPrice: this.orderInfo.packagePrice,
            orderNumber: this.orderInfo.orderNumber,
            packageConfigId: this.orderInfo.id
          }
        });
        if (res) {
          console.log(res);
          console.log('-----------------------------------------');
          if (res.data) {
            _this.orderId = res.data.orderId;
            // needPay 1需要支付  0不需要支付
            if (res.data.needPay == 1) {
              _this.payBtn(res.data.applyPayDto);
            } else {
              _this.successorder();
            }
          } else {
            uni.hideLoading();
            uni.showToast({
              icon: 'none',
              title: res.message,
              duration: 2500
            });
          }
        }
        console.log('创建订单，并拉起微信小程序支付，支付信息的订单备注：电子合同套餐购买；');
      },
      successorder() {
        uni.redirectTo({
          url: '/signature/contract/orderRecord'
        });
      },
      async payBtn(data) {
        let _this = this;
        let resdata = await httpUser.post('mps/line/collect/order/unified/collect', data);
        let res = resdata.data.data;
        _this.disabled = false;
        // uni.hideLoading();
        if (res) {
          if (res.openAllinPayMini) {
            this.flag1 = true;
            this.payInfo = res;
            uni.$payTlian(res);
          } else {
            uni.requestPayment({
              provider: 'wxpay',
              timeStamp: res.payInfo.timeStamp,
              nonceStr: res.payInfo.nonceStr,
              package: res.payInfo.packageX,
              signType: res.payInfo.signType,
              paySign: res.payInfo.paySign,
              success: function (ress) {
                _this.successorder();
              },
              fail: function (err) {
                uni.showToast({
                  title: '支付失败'
                });
              }
            });
          }
        }
      },
      // 判断对象是否有值
      hasValues(obj) {
        return Object.keys(obj).some((key) => obj[key] !== null && obj[key] !== undefined && obj[key] !== '');
      }
    }
  };
</script>

<style scoped lang="scss">
  .ctxt {
    height: 100vh;
    font-size: 28rpx;
  }
  .title_css {
    margin-top: 2rpx;
    padding-bottom: 16rpx;
  }

  .hbox {
    // width: 688rpx;
    display: flex;
    justify-content: flex-start;
    .innerBox {
      width: 332rpx;
      margin-right: 20rpx;
      padding-left: 45rpx;
      padding-right: 45rpx;
    }
  }
  .mbox {
    font-size: 28rpx;
    color: #555555;
    margin-top: 32rpx;
    view {
      margin-bottom: 24rpx;
    }
  }
  .innerBox {
    width: 332rpx;
    height: 220rpx;
    background-image: url(https://document.dxznjy.com/course/da0badbf248843a78547748157ae4c53.png);
    background-repeat: no-repeat;
    background-size: contain; /* 等比例缩放 且不超过其中最短一方的长度 */
    padding: 24rpx;
    box-sizing: border-box;
    line-height: 46rpx;
  }
  .chooseBox {
    background-image: url(https://document.dxznjy.com/course/79e99ae397094d5da35f74730cd6ab25.png);
    background-repeat: no-repeat;
    padding: 24rpx;
    line-height: 46rpx;
    box-sizing: border-box;
  }
  .txt_head {
    font-weight: 900;
    width: 226rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .txt_org {
    color: #fd9e31;
  }
  .txt_gray {
    color: #bababa;
  }
  .txt_green {
    color: #76b3a0;
  }
</style>
