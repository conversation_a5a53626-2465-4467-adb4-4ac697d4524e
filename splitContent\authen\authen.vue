<template>
    <view class="plr-30">
        <view class="ptb-20 bg-ff radius-15" v-if="!signContractStatus">
            <!-- <u-steps v-if="signContractStatus" current="4" activeColor="#ec7532" inactiveColor="#D8D8D8" :dot="true">
				<u-steps-item title="实名认证"></u-steps-item>
				<u-steps-item title="绑定银行卡"></u-steps-item>
				<u-steps-item title="绑定手机号"></u-steps-item>
				<u-steps-item title="电子签约"></u-steps-item>
			</u-steps> -->
            <u-steps :current="active" activeColor="#ec7532" inactiveColor="#D8D8D8" :dot="true">
                <u-steps-item title="实名认证"></u-steps-item>
                <u-steps-item title="绑定银行卡"></u-steps-item>
                <u-steps-item title="绑定手机号"></u-steps-item>
                <u-steps-item title="电子签约"></u-steps-item>
            </u-steps>
            <!-- <van-steps :steps="steps" :active="active" active-color="#006658" inactive-color="#ccc" /> -->
        </view>
        <view class="mt-20 bg-ff" v-if="!signContractStatus" :style="{height: useHeight+'rpx'}"
            style="position: relative;">
            <!-- 第一步实名认证 -->
            <view v-if="active==0">
                <form @submit="submit_s">
                    <view class="plr-30 bg-ff radius-20">
                        <view class="ptb-30 b-db flex">
                            <view class="titleleft f-30">登录用户</view>
                            <view></view>
                            <input placeholder-style="color:#999" class="flex-box" :value="info != null?info.mobile:''"
                                placeholder="手机号" />
                        </view>
                        <view class="ptb-30 b-db flex">
                            <view class="titleleft f-30">姓名</view>
                            <input placeholder-style="color:#999" class="flex-box f-30" placeholder="请输入您的姓名"
                                name="realName" />
                        </view>
                        <view class="ptb-30 b-db flex">
                            <view class="titleleft f-30">身份证</view>
                            <input placeholder-style="color:#999" class="flex-box f-30" type="idcard"
                                placeholder="请输入您的身份证号" name="identityNo" />
                        </view>
                    </view>
                    <view class="ptb-30 plr-20 f-26 c-99 flex-a-c">
					    <label class="radio" style="display: inline-block;transform: scale(0.6);">
					        <radio value="r1" :checked="isChecked" color="#1D755C" @click="changeischecked" />
					    </label>
					    我已阅读并同意
                        <navigator class="bg" hover-class="none" url="../authen/useragree">《用户服务协议》</navigator>
                        <text>、</text>
                        <navigator class="bg" hover-class="none" url="../authen/Privacyagree">《隐私协议》</navigator>
                    </view>
                    <view class="m-45">
                        <button class="nextstep" form-type="submit" :disabled="disabled">下一步</button>
                    </view>
                </form>
            </view>
            <!-- 绑定银行卡 -->
            <view v-if="active==1">
                <!-- <view > -->
                <form @submit="submit_bank">
                    <view class="plr-30 bg-ff radius-20">
                        <view class="ptb-30 b-db flex">
                            <view class="titleleft f-30">银行卡号</view>
                            <view></view>
                            <input class="flex-box f-30" :value="bankCard.cardNo" placeholder="请输入银行卡号" name="bankCard"
                                @input="step2card" />
                        </view>
                        <view class="ptb-30 b-db flex">
                            <view class="titleleft f-30">预留手机</view>
                            <input class="flex-box f-30" :value="bankCard.cardPhone" type="number"
                                placeholder="请输入银行卡预留手机号" @input="step2phone" name="mobile" />
                        </view>
                        <view class="ptb-30 b-db flex">
                            <view class="titleleft f-30">验证码</view>
                            <input class="flex-box f-30" placeholder="请输入您的验证码" type="number" name="verificationCode"
                                @input="stepCode" />
                            <view class="getcode" v-if="!yanStatus" @tap="getcodes()">获取验证码</view>
                            <view class="getcode" v-else>{{ minute }}s后获取</view>
                        </view>
                    </view>
                    <view class="m-45">
                        <button class="nextstep" form-type="submit" :disabled="disabled">下一步</button>
                    </view>
                </form>
            </view>
            <!-- 绑定支付手机号 -->
            <view v-if="active==2">
                <form @submit="bindpayphone">
                    <view class="plr-30 bg-ff radius-20">
                        <view class="ptb-30 b-db flex">
                            <view class="titleleft f-30">手机号</view>
                            <input class="flex-box f-30" placeholder="手机号" :value="telbangding.phone"
                                @input="userPhoneinput" name="phone" />
                        </view>
                        <view class="ptb-30 b-db flex">
                            <view class="titleleft f-30">验证码</view>
                            <input @input="codeInput" class="flex-box f-30" placeholder="请输入验证码" type="number"
                                name="verificationCode" />
                            <view class="getcode" v-if="!payyanStatus" @tap="getpaycodes()">获取验证码</view>
                            <view class="getcode" v-else>{{ minute }}s后获取</view>
                        </view>
                    </view>
                    <view class="m-45">
                        <button class="nextstep" form-type="submit" :disabled="disabled">完成</button>
                    </view>
                </form>
            </view>
            <!-- 签约 -->
            <view class="determine" v-if="active == 3">
                <view class="f-30 t-c prompt">请前往通联小程序完成电子签约，开通提现功能</view>
                <view>
                    <view class="determine-btn" @tap="tosign">确定</view>
                </view>
            </view>

        </view>
        <view class="p-45 bg-ff mt-20 radius-15 flex-s" v-if="signContractStatus" :style="{height: useHeight+'rpx'}">
            <image :src="imgHost+'dxSelect/image/wancheng-3.png'" class="complete_img" mode="widthFix"></image>
            <view class="f-32 t-c mt-30">实名认证已完成</view>
        </view>
    </view>
</template>

<script>
    const {
        $showMsg,
        $showSuccess,
        $showError,
        $http
    } = require("@/util/methods.js")
    import Util from '@/util/util.js'
    const {
        httpUser
    } = require('@/util/luch-request/indexUser.js');

    let count
    export default {
        data() {
            return {
                signContractStatus: false, // 实名认证是否完成
                // 实名认证
                realName: {
                    bizUserId: "",
                    identityNo: "",
                    name: "",
                    providerType: "ALLIN_PAY"
                },

                // 银行卡
                bankCard: {
                    cardNo: "", // 银行卡
                    cardPhone: "", // 预留手机号
                    verificationCode: "", // 验证码
                    bizUserId: "",
                    providerType: "ALLIN_PAY",
                    tranceNum: "",
                },

                // 银行卡验证码
                minuteList: {
                    cardNo: "",
                    cardPhone: "",
                    providerType: "ALLIN_PAY",
                },

                // // 手机号验证码
                // telApiList: {
                // 	bizUserId: "",
                // 	phone:"",
                // 	verificationCodeType:""
                // },

                payStatus: false, // 是否绑定支付标识

                // 绑定手机号
                telbangding: {
                    verificationCode: "",
                    phone: "",
                    bizUserId: "",
					tranceNum:"",
                },

                // 签约跳转
                contractList: {
                    backStyle: "DUBBO",
                    source: 1,
                    bizUserId: ""
                },
                contract: null,
                urls: 'http://localhost:9529/#/layout/personMe/realName',

                pageShow: true,
                info: null,
                disabled: false,
                yanStatus: false,
                payyanStatus: false,
                minute: 60,
                // bankCard: '', //第二步银行卡号
                // mobile1: '', //第二步手机号
                userCode: '',
                tranceNum: '',  // 第三步验证手机号返的
                // userPhone: '',
                // tranceNum2: '', //流水号
                signurl: '', // 签约跳转地址

                active: 0,
                steps: [{
                        text: '实名认证',
                    },
                    {
                        text: '绑定银行卡',
                    },
                    {
                        text: '绑定手机号',
                    },
                    {
                        text: '电子签约',
                    },
                ],

                useHeight: 0, //除头部之外高度
				imgHost: getApp().globalData.imgsomeHost,
				isChecked: false,
				type:1,
            }
        },
        onReady() {
            uni.getSystemInfo({
                success: (res) => {
                    // 可使用窗口高度，将px转换rpx
                    let h = (res.windowHeight * (750 / res.windowWidth));
                    this.useHeight = h - 180;
                }
            })
        },
        onLoad(e) {
		    this.type = e.type;
			this.userCode=e.userCode
		},
        onShow() {
            this.homeData()
            this.getRealName()
        },
        methods: {
            async getRealName() {
                // 先获取usrcode
                let code = await httpUser.get("mps/account/list/login/code");
                let data = code.data.data
                let findCode = data.find((item) => {
                    if (item.userType == "Member") {
                        return item;
                    }
                });

                // 判断用户是否实名认证
                let res = await httpUser.get('mps/user/info/user/code', {
                    userCode: this.userCode
                });
				
                this.contract = res.data.data
                this.realName.bizUserId = res.data.data.bizUserId;
                if (res.data.success) {
                    if (res.data.data.signContractStatus == 1) {
                        this.signContractStatus = true; // 实名认证完成
                        this.useHeight = this.useHeight + 40;
                    }
                }

                // 判断跳转  
                if (res.data.success && res.data.data != null) {
                    if (res.data.data.certStatus == "1" && res.data.data.signContractStatus != 1) {
                        if (res.data.data.bindCardStatus != 1) {
                            //已经通过实名认证 未绑定银行卡
                            this.active = 1;
                            uni.showToast({
                                icon: "none",
                                title: "您已完成实名认证,请绑定银行卡信息",
                                duration: 2000
                            });
                        } else if (res.data.data.bindPhoneStatus == 0 && res.data.data.signContractStatus != 1) {
                            //未绑定手机
                            this.active = 2;
                            uni.showToast({
                                icon: "none",
                                title: "您已完成实名认证,请绑定手机号",
                                duration: 2000
                            });
                        } else if (res.data.data.bindPhoneStatus == 1 && res.data.data.signContractStatus != 1) {
                            this.active = 3;
                            uni.showToast({
                                icon: "none",
                                title: "您已完成实名认证,请完成电子签约",
                                duration: 2000
                            });
                        }
                    }
                }
            },

            // 第四步去签约
            async tosign() {
                let _this = this
                _this.contractList.bizUserId = _this.realName.bizUserId;
                let res = await httpUser.get('mps/auth/sign/contract', _this.contractList);
                _this.signurl = res.data.data
                console.log(res);
                uni.navigateToMiniProgram({
                    appId: 'wxc46c6d2eed27ca0a',
                    path: '/pages/merchantAddress/merchantAddress',
                    extraData: {
                        targetUrl: _this.signurl
                    },
                    envVersion: 'release',
                    success(res) {
                        uni.navigateBack()
                        // 打开成功
                    }
                })
            },

            // 绑定手机号input框
            userPhoneinput(e) {
                this.telbangding.phone = e.detail.value
            },

            // 绑定手机号input框
            codeInput(e) {
                this.telbangding.verificationCode = e.detail.value
            },

            // 绑定手机号验证码
            async getpaycodes() {
                let _this = this
                if (!Util.isMobile(_this.telbangding.phone)) {
                    return $showError('请输入手机号')
                }
                _this.paymentId()
            },

            // 判断用户是否绑定支付标识
            async paymentId() {
                let _this = this
                let request = await $http({
                    url:'mps/auth/bind/apply/acct?bizUserId='+this.realName.bizUserId,
                }) 
                // 绑定支付标识
                let telApiList = {
                    bizUserId: _this.realName.bizUserId,
                    phone: _this.telbangding.phone
                }
                uni.showLoading({
                    title: '获取中，请稍后'
                })
                let res = await httpUser.get('mps/auth/bind/phone/sms/use/bank', telApiList)
                console.log(res);
                uni.hideLoading()
                if (res.data.success) {
                    _this.payyanStatus = true;
                    _this.countdown()
                    uni.showToast({
                        title: '验证码发送成功，请注意接收！'
                    })
                	this.tranceNum = res.data.data.tranceNum;
                    _this.payStatus = true
                }
     //            if (request.data.data) {
     //            } else {
     //                let telApiList = {
     //                    bizUserId: _this.realName.bizUserId,
     //                    phone: _this.telbangding.phone,
     //                    verificationCodeType: 9
     //                }
     //                uni.showLoading({
     //                    title: '获取中，请稍后'
     //                })
     //                let res = await httpUser.put('mps/auth/send/sms', telApiList)
     //                console.log(res);
     //                uni.hideLoading()
     //                if (res.data.success) {
     //                    _this.payyanStatus = true
     //                    _this.countdown()
     //                    uni.showToast({
     //                        title: '验证码发送成功，请注意接收！'
     //                    })
     //                    _this.payStatus = false
     //                }else{
					// 	_this.$util.alter(res.data.message);
					// }
     //            }
            },

            // 第三步绑定手机号
            async bindpayphone(e) {
                let _this = this,
                    values = e.detail.value
                if (!Util.isMobile(_this.telbangding.phone)) {
                    return $showError('请输入手机号')
                }
                if (!values.verificationCode) {
                    return $showError('请输入验证码')
                }
                if (_this.disabled) {
                    return
                }
                this.telbangding.bizUserId = this.realName.bizUserId;
				_this.telbangding.tranceNum = _this.tranceNum;
                uni.showLoading({
                    title: '提交中'
                })
                _this.disabled = true
                // if (_this.payStatus) {
                    let res = await httpUser.put('mps/auth/bind/phone/use/bank', _this.telbangding)
                    console.log(res);
                    _this.disabled = false
                    uni.hideLoading()
                    if (res.data.success) {
                        $showSuccess(res.data.message)
                        setTimeout(function() {
                            clearInterval(count);
                            _this.minute = 60
                            _this.payyanStatus = false
                            _this.yanStatus = false
                            _this.homeData()
                        }, 2000)
                        _this.active = 3
                    }
                // } else {
     //                let res = await httpUser.put('mps/auth/bind/phone', _this.telbangding)
     //                console.log(res);
     //                _this.disabled = false
     //                uni.hideLoading()
     //                if (res.data.success) {
     //                    $showSuccess(res.data.message)
     //                    setTimeout(function() {
     //                        clearInterval(count);
     //                        _this.minute = 60
     //                        _this.payyanStatus = false
     //                        _this.yanStatus = false
     //                        _this.homeData()
     //                    }, 2000)
     //                    _this.active = 3
     //                }else{
					// 	_this.$util.alter(res.data.message);
					// }
     //            }
            },

            // 第二步绑定银行卡
            async submit_bank(e) {
                let _this = this,
                    values = e.detail.value
                if (!Util.checkCard(_this.bankCard.cardNo)) {
                    return $showError('请输入银行卡号')
                }
                if (!_this.bankCard.cardPhone) {
                    return $showError('请输入预留手机号')
                }
                if (!values.verificationCode) {
                    return $showError('请输入验证码')
                }
                if (_this.disabled) {
                    return
                }
                _this.bankCard.bizUserId = _this.realName.bizUserId;
                uni.showLoading({
                    title: '提交中'
                })
                _this.disabled = true
                let res = await httpUser.put('mps/auth/bind/card/confirm', _this.bankCard);
                _this.disabled = false;
                if (res.data.success) {
					_this.active = 2;
                    $showSuccess(res.data.message);
                    setTimeout(function() {
                        clearInterval(count);
                        _this.minute = 60
                        _this.yanStatus = false
                        _this.homeData()
                    }, 2000)
                }else{
					_this.active = 1;
					_this.$util.alter(res.data.message);
				}
				_this.disabled = false;
				uni.hideLoading()
            },

            step2card(e) {
                this.bankCard.cardNo = e.detail.value
            },
            step2phone(e) {
                this.bankCard.cardPhone = e.detail.value
            },
            stepCode(e) {
                this.bankCard.verificationCode = e.detail.value
            },

            // 银行卡获取验证码
            async getcodes() {
                let _this = this
                if (!Util.checkCard(_this.bankCard.cardNo)) {
                    return $showError('请输入银行卡号')
                }
                if (!_this.bankCard.cardPhone) {
                    return $showError('请输入预留手机号')
                }
                if (_this.yanStatus) {
                    return
                }
                uni.showLoading({
                    title: '获取中，请稍后'
                })
                _this.minuteList.bizUserId = _this.realName.bizUserId;
                _this.minuteList.cardNo = _this.bankCard.cardNo;
                _this.minuteList.cardPhone = _this.bankCard.cardPhone;
                let res = await httpUser.put('mps/auth/bind/card', _this.minuteList);
                console.log(res);
                uni.hideLoading()
                if (res.data.success) {
                    _this.yanStatus = true
                    _this.countdown()
                    _this.bankCard.tranceNum = res.data.data
                    console.log(_this.bankCard.tranceNum);
                    uni.showToast({
                        title: '验证码发送成功，请注意接收！'
                    })
                }else{
					_this.$util.alter(res.data.message);
				}
            },
            // 倒计时
            countdown() {
                let _this = this
                count = setInterval(function() { //倒计时
                    var minute = _this.minute;
                    minute--;

                    _this.minute = minute
                    _this.yanStatus = true

                    if (minute == 0) {
                        clearInterval(count);
                        _this.minute = 60
                        _this.payyanStatus = false
                        _this.yanStatus = false
                    }
                }, 1000)
            },

            // 第一步实名认证
            async submit_s(e) {
                let _this = this, values = e.detail.value;
                if (!Util.isName(values.realName)) {
                    return (_this.active = 0);
                    return $showError('姓名不符合要求')
                }
                if (!Util.isCardID(values.identityNo)) {
                    return (_this.active = 0);
                    return $showError('身份证号不符合要求')
                }
                if (_this.disabled) {
                    return
                }
				if (!this.isChecked) {
				    this.$util.alter('请阅读并勾选下方协议')
				    return false;
				}
                uni.showLoading({
                    title: '提交中'
                })
                _this.disabled = true;
                _this.realName.name = values.realName;
                _this.realName.identityNo = values.identityNo;
                let res = await httpUser.put('mps/auth/verified', this.realName);
                console.log(res);
                _this.disabled = false;
                uni.hideLoading();
                if (res.data.success) {
                    _this.active = 1;
                    setTimeout(function() {
                        _this.homeData()
                    }, 2000);
					$showSuccess(res.data.message);
                }else{
					_this.$util.alter(res.data.message);
				}
            },
			
			// 同意已阅读用户服务协议
			changeischecked() {
			    this.isChecked = !this.isChecked;
			    // if (this.mobile.length == 11 && this.smsCode.length == 6 && this.isChecked) {
			    //     this.lastactive = true
			    // } else {
			    //     this.lastactive = false
			    // }
			},

            // 获取首页信息
            async homeData() {
                let _this = this
                const res = await $http({
                    url: 'zx/user/userInfoNew',
                })
                if (res) {
                    _this.pageShow = false
                    _this.info = res.data
                    // 最后一步获取签约地址	
                    // if (res.data.isBindPayPhone == 1 && res.data.signContractStatus == 0) {
                    // 	_this.active = 2
                    // 	uni.setNavigationBarTitle({
                    // 		title: '电子签约'
                    // 	})
                    // 	_this.signData()
                    // }
                    // if (res.data.signContractStatus == 1) {
                    // 	_this.active = 3
                    // }
                }
            },
            // async signData() {
            // 	let _this = this
            // 	const res = await httpUser.get('mps/auth/sign/contract',_this.contractList)
            // 	console.log(res);
            // },
        },
    }
</script>

<style lang="scss" scoped>
    .determine {
        position: absolute;
        bottom: 40rpx;
        width: 100%;
    }

    .prompt {
        margin-bottom: 560rpx;
        padding: 0 120rpx;
    }

    .nextstep {
        width: 586rpx;
        height: 80rpx;
        background-image: linear-gradient(to bottom, #88CFBA, #1D755C);
        margin: 30rpx auto;
        text-align: center;
        line-height: 80rpx;
        color: #fff;
        border-radius: 45rpx;
    }

    .determine-btn {
        width: 586rpx;
        height: 80rpx;
        background-image: linear-gradient(to bottom, #88CFBA, #1D755C);
        margin: 0 auto;
        text-align: center;
        line-height: 80rpx;
        color: #fff;
        border-radius: 45rpx;
    }

    .titleleft {
        width: 150upx;
    }

    .getcode {
        background-color: #2E896F;
        color: #fff;
        padding: 8upx 12upx;
        font-size: 28upx;
        border-radius: 25upx;
    }

    .circle {
        width: 160upx;
        height: 160upx;
        background-color: #fff;
        border-radius: 100upx;
        border: 8upx solid #eee;
        color: #006658;
        line-height: 160upx;
        text-align: center;
        margin: 30upx auto;
    }

    /deep/.u-text__value--main {
        color: #000 !important;
		font-size: 26rpx !important;
    }

    /deep/.u-text__value--content {
        color: #999 !important;
		font-size: 26rpx !important;
    }

    .bg {
        color: #eb673a;
    }

    .complete_img {
        width: 100rpx;
		height: 100rpx;
    }

    .flex-s {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }
	
	/deep/.u-steps-item__wrapper__dot{
		width: 30rpx !important;
		height: 30rpx !important;
	}
	
	/deep/.u-steps-item__content{
		margin-top: 20rpx !important;
	}
</style>