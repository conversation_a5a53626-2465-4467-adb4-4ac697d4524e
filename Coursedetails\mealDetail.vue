<template>
  <view class="content">
    <view class="pb-200">
      <view class="course-detail relative" v-if="shopdetail != null">
        <swiper
          :indicator-dots="false"
          autoplay="true"
          circular="true"
          duration="500"
          indicator-active-color="#ffffff"
          class="swiper"
          :style="{ height: swiperheight + 'rpx' }"
          @change="setCurrent"
        >
          <block v-for="(item, index) in shopdetail.imageList" :key="index">
            <swiper-item class="section_item">
              <image :src="item" class="wh100" mode="scaleToFill"></image>
            </swiper-item>
          </block>
        </swiper>
        <view class="sheets c-ff f-24">{{ currentIndex }}/{{ shopdetail != null && shopdetail.imageList != undefined ? shopdetail.imageList.length : 1 }}</view>
      </view>
      <view class="bg-ff p-30 mb-20">
        <view class="flex">
          <view class="flex-box">{{ shopdetail != null ? shopdetail.mealName : '' }}</view>
          <view class="c-red bold">
            <text class="f-26">￥</text>
            <text class="f-34">{{ shopdetail != null ? shopdetail.mealPrice : '' }}</text>
          </view>
        </view>
      </view>
      <view class="bg-ff p-30 flex-dir-row flex-y-c">
        <view class="storeline"></view>
        <text class="f-32 c-22 bold">套餐详情</text>
      </view>
      <view class="bg-ff plr-30 pb-30">
        <rich-text :nodes="shopdetail != null ? shopdetail.mealInfo : ''"></rich-text>
      </view>
    </view>
    <!-- 底部栏 -->
    <view class="fixed_b flex">
      <button class="plr-20 flex-c" hover-class="none" @click="sharePoster">
        <image :src="imgHost + 'dxSelect/tab_img.png'" class="shareIcon" mode=""></image>
        <text class="f-24 c-99 ml-10">海报分享</text>
      </button>
      <!-- #ifdef MP-WEIXIN -->
      <button class="pr-20 flex-c" hover-class="none" open-type="share">
        <image :src="imgHost + 'dxSelect/tab_lj.png'" class="shareIcon" mode=""></image>
        <text class="f-24 c-99 ml-10">链接分享</text>
      </button>
      <!-- #endif -->

      <!-- #ifdef APP-PLUS -->
      <button class="pr-20 flex-c" hover-class="none" @click="linkShare">
        <image :src="imgHost + 'dxSelect/tab_lj.png'" class="shareIcon" mode=""></image>
        <text class="f-24 c-99 ml-10">链接分享</text>
      </button>
      <!-- #endif -->

      <button class="buy_s flex-box mlr-20 f-32" v-if="userinfo.mobile" :disabled="disabled" @tap="buy">立即购买</button>
      <button class="buy_s flex-box mlr-20 f-32" v-else open-type="getPhoneNumber" @getphonenumber="onGetPhoneNumber">立即购买</button>
    </view>
    <u-toast ref="uToast"></u-toast>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  export default {
    data() {
      return {
        payInfo: {},
        flag1: false,
        id: '',
        currentIndex: 1,
        swiperheight: 416,
        tabindex: 1,
        shopdetail: null,
        disabled: false,
        params: {
          type: 'default',
          message: '支付成功',
          duration: 3000,
          url: '/Personalcenter/my/partnerUnion?type=2'
        },
        userinfo: {},
        payInfo: {},
        flag1: false,
        code1: '',
        imgHost: getApp().globalData.imgsomeHost,
        invitationCode: '' //超人分享邀请码
      };
    },
    onLoad(e) {
      this.id = e.id;
      if (e.invitationCode != undefined) {
        this.invitationCode = e.invitationCode;
      }
    },
    onShow() {
      if (this.flag1) {
        uni.$tlpayResult(this.sucees, this.fail, this.payInfo.orderId);
      }
      let _this = this;
      _this.disabled = false;
      _this.detail();
      _this.homeData();
      uni.login({
        success(res) {
          _this.code1 = res.code;
        }
      });
    },
    onShareAppMessage(res) {
      return {
        title: '叮，你的好友敲了你一下，赶紧过来看看',
        imageUrl: this.shopdetail.mealImage,
        path: `/Coursedetails/mealDetail?scene=${uni.getStorageSync('user_id')}&type=vip&id=${this.id}`
      };
    },
    methods: {
      sucees() {
        this.flag1 = false;
        this.redirectToOrderIndex();
      },
      fail() {
        this.flag1 = false;
      },
      fails() {
        uni.showToast({
          title: '支付失败',
          icon: 'none',
          duration: 2000
        });
        this.flag1 = false;
      },
      sharePoster() {
        uni.navigateTo({
          url: `/splitContent/poster/index?type=vip&id=${this.id}`
        });
      },

      // 手机号授权
      async onGetPhoneNumber(e) {
        let _this = this;
        uni.showLoading({
          title: '请等待',
          mask: false
        });
        // 检查登录态是否过期
        wx.checkSession({
          success: async function (res) {
            const encryptedData = e.detail.encryptedData;
            const iv = e.detail.iv;
            if (e.detail.errMsg == 'getPhoneNumber:ok') {
              const resdata = await $http({
                url: 'zx/common/decodeWechatPhone',
                method: 'POST',
                data: {
                  code: _this.code1,
                  encryptedData: encryptedData,
                  iv: iv
                }
              });
              if (resdata) {
                _this.show = false;
                uni.showToast({
                  title: '手机号码授权成功',
                  icon: 'none'
                });
                setTimeout(function () {
                  _this.homeData();
                  _this.buy();
                }, 1500);
              }
            } else {
              uni.showToast({
                title: '请重新获取',
                icon: 'none'
              });
            }
          },
          fail(err) {
            uni.login({
              success: (res) => {
                _this.code1 = res.code;
              }
            });
          }
        });
      },
      // 获取首页信息
      async homeData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.pageShow = false;
          _this.userinfo = res.data;
        }
      },
      buy() {
        // if(this.userinfo.identityType==1){
        // 	uni.showToast({
        // 	    title: '您已是学习超人，无需再次购买',
        // 	    icon: 'none'
        // 	});
        // 	return;
        // }
        let result = [
          {
            buyNum: 1,
            courseId: this.id
          }
        ];
        uni.navigateTo({
          url: './confirm?cart_ids=' + JSON.stringify(result) + '&pagetype=2&invitationCode=' + this.invitationCode
        });
      },
      // async buy() {
      //     let _this = this
      //     if (_this.disabled) {
      //         return false
      //     }
      //     _this.disabled = true
      //     uni.showLoading({
      //         title: '提交中'
      //     })
      //     const res = await $http({
      //         url: 'zx/meal/mealPlaceOrder',
      //         data: {
      //             mealId: _this.id
      //         }
      //     })
      //     _this.disabled = false
      //     if (res) {
      //         _this.payBtn(res.data)
      //     }
      // },

      async payBtn(data) {
        let _this = this;
        let resdata = await httpUser.post('mps/line/collect/order/unified/collect', data);
        let res = resdata.data.data;
        _this.disabled = false;

        if (res) {
          if (res.openAllinPayMini) {
            this.flag1 = true;
            this.payInfo = res;
            uni.$payTlian(res);
          } else {
            uni.requestPayment({
              provider: 'wxpay',
              timeStamp: res.payInfo.timeStamp,
              nonceStr: res.payInfo.nonceStr,
              package: res.payInfo.packageX,
              signType: res.payInfo.signType,
              paySign: res.payInfo.paySign,
              success: function (ress) {
                console.log('支付成功');
                // _this.successpay(id);
                _this.redirectToOrderIndex();
              },
              fail: function (err) {
                uni.showToast({
                  title: '支付失败',
                  icon: 'none',
                  duration: 2000
                });
              }
            });
          }
        }
      },

      redirectToOrderIndex() {
        let _this = this;
        _this.$refs.uToast.show({
          ..._this.params,
          complete() {
            console.log(_this.params);
            _this.params.url &&
              uni.redirectTo({
                url: _this.params.url
              });
          }
        });
      },
      // 获取商品详情
      async detail() {
        let _this = this;
        const res = await $http({
          url: 'zx/meal/mealDetail',
          data: {
            mealId: _this.id
          }
        });
        if (res) {
          _this.shopdetail = res.data;
        }
      },
      setCurrent(e) {
        this.currentIndex = e.detail.current + 1;
      },

      ///app链接分享
      linkShare() {
        uni.share({
          provider: 'weixin',
          scene: 'WXSceneSession',
          type: 5,
          title: '叮，你的好友敲了你一下，赶紧过来看看',
          imageUrl: this.shareContent.mealImage, //分享封面
          miniProgram: {
            id: Config.miniOriginalId,
            path: `/Coursedetails/mealDetail?scene=${uni.getStorageSync('user_id')}&type=vip&id=${this.id}`,
            type: 0,
            webUrl: Config.webUrl
          },
          success: (ret) => {
            console.log(JSON.stringify(ret));
            uni.showToast({
              icon: 'none',
              title: '分享成功'
            });
          },
          fail: (ret) => {
            console.log(JSON.stringify(ret));
            uni.showToast({
              icon: 'none',
              title: '分享失败'
            });
          }
        });
      }
    }
  };
</script>

<style>
  .sheets {
    position: absolute;
    bottom: 30rpx;
    right: 30rpx;
    padding: 10rpx 30rpx;
    border-radius: 40rpx;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 1;
  }

  .storeline {
    width: 10upx;
    height: 32upx;
    background-color: #006658;
    margin-right: 20upx;
    border-radius: 4rpx;
  }

  .fixed_b {
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    padding: 20upx 0 40rpx 0;
    background-color: #fff;
    box-shadow: 0 0 15upx 0 rgba(0, 0, 0, 0.2);
  }

  .buy_s {
    background-image: linear-gradient(to bottom, #f09234, #ea6031);
    height: 80upx;
    border-radius: 40upx;
    line-height: 80upx;
    text-align: center;
    color: #fff;
  }
  .shareIcon {
    width: 42upx;
    height: 42upx;
  }
</style>
