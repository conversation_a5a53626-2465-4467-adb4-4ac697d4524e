<template>
	<view class="bg-ff radius-15">
		<view class="canvas-container">
			<canvas canvas-id="poster" class="poster_canvas"></canvas>
		</view>
		<view class="flex-dir-row flex-x-c">
			<button @tap="save" class="saveButton f-30 c-ff t-c">保存图片</button>
		</view>
	</view>
</template>

<script>
	const {
		$http,
		$navigationTo,
	} = require("@/util/methods.js")
	const {
		httpUser
	} = require('@/util/luch-request/indexUser.js');
	var ctx = null
	var _this
	export default {
		data() {
			return {
				type: 1, //1课程  2学习超人（会员） 3俱乐部   9会议
				courseId: '', //课程id
				circular: true,
				// vipStatus: false,
				// authStatus: false,
				bbimg: [],
				previousMargin: '30px', //前边距，可用于露出前一项的一小部分，接受 px 和 rpx 值
				nextMargin: '30px', //后边距，可用于露出后一项的一小部分，接受 px 和 rpx 值
				currentSwiperIndex: 0,
				banner: [],
				code: null,
				giftListNum: 0,
				posterbg: null,
				shopname: null, //昵称
				headimg: '', //头像
				pageShow: false,
				canvasimg: null,
				slogan_text: '',
				userS: {},
				signContractStatus: '',
				userId: '',
				imgHost: getApp().globalData.imgsomeHost,
				mealLists: [],
				isClose: false, //是否关闭当前页
				isMeeting: false,
				activityId: '',
				imgUrl: '',
				qrcode: ''
			}
		},
		onLoad(options) {
			this.activityId = options.activityId;
		},
		created() {},
		onReady() {
			ctx = uni.createCanvasContext('poster', this);
			//app端只初始加载一次
			// #ifdef APP-PLUS
			if (uni.getStorageSync('token')) {
				this.homeData()
				// this.meal()
			} else {
				uni.navigateTo({
					url: '/Personalcenter/login/login'
				})
			}
			// #endif
		},
		onShow() {
			// #ifdef MP-WEIXIN
			if (uni.getStorageSync('token')) {
				this.homeData()
				// this.meal()
			} else {
				uni.navigateTo({
					url: '/Personalcenter/login/login'
				})
			}
			// #endif
		},
		onUnload() {
			uni.hideLoading()
		},
		onHide() {
			uni.hideLoading()
		},
		onShareAppMessage() {
			return {
				title: this.slogan_text,
				path: `/pages/index/index?scene=` + uni.getStorageSync('user_id')
			}
		},
		methods: {
			async homeData() {
				let _this = this;
				_this.giftListNum = 0;
				_this.bbimg = [];
				_this.currentSwiperIndex = 0;
				const res = await $http({
					url: `zx/user/userInfoNew`,
				});
				if (res) {
					_this.pageShow = true;
					_this.userS = res.data;
					uni.setStorageSync('user_id', res.data.userId)
					uni.setStorageSync('identityType', res.data.identityType);
					_this.list();
				}
			},
			async list() {
				let _this = this
				// const res = await this.$httpUser.get('zx/user/getTestActivityCode?activityId=' + this.activityId)

				const res = await this.$httpUser.get('zx/user/getActivityCode?activityId=' + this.activityId)
				// if (res) {
				_this.banner = res.data.data.bgImageUrl[0]
				_this.code = res.data.data.qrCode;
				_this.headimg = uni.getStorageSync('avaUrl') ? uni.getStorageSync('avaUrl') :
					'https://document.dxznjy.com/dxSelect/home_avaUrl.png'
				_this.shopname = uni.getStorageSync('nickName') ? uni.getStorageSync('nickName') : '我是昵称'
				if (_this.shopname != '我是昵称') {
					_this.shopname = _this.shopname.length > 8 ? _this.shopname.substring(0, 8) + '...' : _this
						.shopname
				}
				_this.canvasall();
				// }
			},
			canvasall() {
				let _this = this
				_this.drawCanvas(_this.banner)
				// _this.drawCanvas(_this.banner[0])
			},
			drawCanvas(bb_imgs) {
				let rpxWidth = uni.getSystemInfoSync().windowWidth;
				let rpxHeight = uni.getSystemInfoSync().windowHeight;
				let _this = this;
				if (this.isClose) return;
				uni.showLoading({
					title: '生成中'
				})
				uni.getImageInfo({
					src: bb_imgs,
					success(res) {
						_this.posterbg = res.path
						uni.getImageInfo({
							src: _this.headimg || '',
							success(res) {
								_this.head_s = res.path
								uni.getImageInfo({
									src: _this.code,
									success(res) {
										_this.code_s = res.path;
										ctx.beginPath();
										ctx.setFillStyle('#fff');
										ctx.fillRect(0, 0, rpxWidth, rpxHeight);
										ctx.restore();
										ctx.beginPath();
										ctx.drawImage(_this.posterbg, 0, 0, rpxWidth, rpxHeight);
										ctx.save();
										ctx.arc(55,35, 25, 0, 2 * Math.PI);
										ctx.stroke();
										ctx.clip();
										ctx.drawImage(_this.head_s, 30, 10, 50, 50);
										ctx.restore();
										const text_size = 40
										ctx.setFontSize(uni.upx2px(text_size))
										ctx.setFillStyle('#ffffff');
										ctx.fillText(_this.shopname, 100, 40);
										ctx.restore();
										const text_size2 = 25
										ctx.setFontSize(uni.upx2px(text_size2))
										ctx.fillText('扫码加入鼎校大家庭', rpxWidth - (rpxWidth * 0.65),
											rpxHeight - (rpxHeight * 0.1));
										ctx.fillText('长按识别二维码进入', rpxWidth - (rpxWidth * 0.65),
											rpxHeight - (rpxHeight * 0.15));

										ctx.drawImage(_this.code_s, rpxWidth - (rpxWidth * 0.3),
											rpxHeight - (rpxHeight * 0.2), 100,
											100);
										ctx.restore()
										// debugger
										ctx.fillStyle = "#10131c";
										ctx.draw(true, () => {
											// canvas画布转成图片并返回图片地址
											// setTimeout(() => {
											uni.canvasToTempFilePath({
												canvasId: 'poster',
												width: rpxWidth,
												height: rpxHeight,
												success: (result) => {
													_this.canvasimg = result
														.tempFilePath;

													uni.hideLoading();
													return
													// setTimeout(() => {
													//     _this.giftListNum++;
													//     _this.getDetail();
													// }, 1000)
												},
												fail: (err) => {
													uni.hideLoading();
												}
											}, this)
											// }, 800)
										});
									},
									fail: function(err) {
										//  console.log(err);
									}
								})
							},
							fail: function(err) {
								//  console.log(err, 'getImageInfo')
							}
						})
					},
					fail: function(err) {
						uni.hideLoading();
					}
				})

			},

			// 保存海报
			save() {
				let _this = this;
				let img = _this.canvasimg;
				uni.getImageInfo({
					src: img,
					success: function(res1) {
						uni.saveImageToPhotosAlbum({
							filePath: res1.path,
							success: function(res) {
								uni.showToast({
									title: '保存成功',
									icon: 'success',
								})
							},
							fail: function(res) {
								if (res.errMsg === "saveImageToPhotosAlbum:fail:auth denied" || res
									.errMsg === "saveImageToPhotosAlbum:fail auth deny" || res
									.errMsg ===
									"saveImageToPhotosAlbum:fail authorize no response"
								) {
									// 这边微信做过调整，必须要在按钮中触发，因此需要在弹框回调中进行调用
									uni.showModal({
										title: '提示',
										content: '需要您授权保存相册',
										showCancel: false,
										success: modalSuccess => {
											uni.openSetting({
												success(settingdata) {
													if (settingdata
														.authSetting[
															'scope.writePhotosAlbum'
														]) {
														uni.showModal({
															title: '提示',
															content: '获取权限成功,再次点击图片即可保存',
															showCancel: false,
														})
													} else {
														uni.showModal({
															title: '提示',
															content: '获取权限失败，将无法保存到相册哦~',
															showCancel: false,
														})
													}
												},
												fail(failData) {

												},
												complete(finishData) {

												}
											})
										}
									})
								}
							}
						})
					}
				})
			},
			//分享好友
			share() {
				if (this.bbimg.length == 0) {
					uni.showToast({
						icon: 'none',
						title: '海报生成中'
					})
					return
				}
				uni.share({
					provider: "weixin",
					scene: "WXSceneSession",
					type: 2,
					imageUrl: this.bbimg[this.currentSwiperIndex],
					success: function(res) {
						//  console.log("success:" + JSON.stringify(res));
						uni.showToast({
							icon: 'none',
							title: '分享成功'
						})
					},
					fail: function(err) {
						//  console.log("fail:" + JSON.stringify(err));
						uni.showToast({
							icon: 'none',
							title: '分享失败'
						})
					}
				});
			},
			//分享朋友圈
			shareBig() {
				if (this.bbimg.length == 0) {
					uni.showToast({
						icon: 'none',
						title: '海报生成中'
					})
					return
				}
				uni.share({
					provider: "weixin",
					scene: "WXSceneTimeline",
					type: 2,
					imageUrl: this.bbimg[this.currentSwiperIndex],
					success: function(res) {
						//  console.log("success:" + JSON.stringify(res));
						uni.showToast({
							icon: 'none',
							title: '分享成功'
						})
					},
					fail: function(err) {
						//  console.log("fail:" + JSON.stringify(err));
						uni.showToast({
							icon: 'none',
							title: '分享失败'
						})
					}
				});
			},
		},
		onUnload() {
			this.isClose = true;
			uni.hideLoading()
		},



	}
</script>

<style lang="scss" scoped>
	.Poster {
		font-size: 26rpx;
	}

	.PosterBot {
		background-color: #fff;
		height: 5vh;
	}

	.saveButton {
		background-image: linear-gradient(to bottom, #88cfba, #1d755c);
		width: 90%;
		height: 5vh;
		line-height: 5vh;
		border-radius: 8vh;
		position: fixed;
		bottom: 10rpx;
		margin: 0 auto;
		color: #fff;
	}

	.poster_canvas {
		width: 100vw;
		height: calc(100vh - 6vh);
		position: fixed;
		top: 0;
	}
</style>