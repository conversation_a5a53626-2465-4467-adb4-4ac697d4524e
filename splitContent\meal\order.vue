<template>
  <view>
    <u-sticky>
      <view class="plr-20 bg-f3 mt-30">
        <u-tabs
          :list="listCate"
          :current="current"
          keyName="name"
          lineWidth="30"
          lineHeight="5"
          lineColor="#EA6031"
          :activeStyle="{
            color: '#000',
            fontWeight: 'bold',
            paddingBottom: '20rpx',
            transform: 'scale(1.04)'
          }"
          :inactiveStyle="{
            color: '#666',
            paddingBottom: '20rpx',
            transform: 'scale(1)'
          }"
          itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;"
          @click="click"
        ></u-tabs>
      </view>
    </u-sticky>
    <view class="mlr-30">
      <view class="plr-30 bg-ff radius-10 mt-30" @tap.stop="toDetail(item.orderMealId)" v-for="(item, index) in listS.list" :key="index">
        <view class="flex ptb-25 order_s">
          <view class="f-30 c-33">编号：{{ item.orderMealId }}</view>
        </view>
        <view class="ptb-30 flex-dir-row">
          <view class="box-180 radius-15">
            <image :src="item.mealInfo.mealImage" class="wh100"></image>
          </view>
          <view class="flex-box ml-20">
            <view class="f-32 bold mb-20">{{ item.mealInfo.mealName }}</view>
            <view class="f-34 mb-20">
              <text class="bold color_tangerine">￥{{ item.mealInfo.mealPrice }}</text>
            </view>
            <view class="flex_x">
              <text class="f-32 c-00">x1</text>
              <text v-if="item.payStatus == 0" class="f-26 c-ff t-c tobe_paid">{{ item.payStatusName }}</text>
              <text v-if="item.payStatus == 1" class="f-26 c-ff t-c paid">{{ item.payStatusName }}</text>
              <text v-if="item.payStatus == 2" class="f-26 c-ff t-c close_pay">{{ item.payStatusName }}</text>
              <text v-if="item.payStatus == 3" class="f-26 c-ff t-c refunded">{{ item.payStatusName }}</text>
              <text v-if="item.payStatus == 4" class="f-26 c-ff t-c completed">{{ item.payStatusName }}</text>
              <text v-if="item.payStatus == 5" class="f-26 c-ff t-c drawback">{{ item.payStatusName }}</text>
            </view>
          </view>
        </view>
        <!-- <view class="flex flex-x-e" v-if="item.payStatus==0||item.payStatus==1||item.payStatus==5">
					<view class="flex-box flex-dir-row flex-x-b flex-wrap f-32 pt-30 pb-20 border_top" v-if="item.payStatus==0">
						<view class="btnone c-99" @tap.stop="cancel(item.orderMealId)">取消订单</view>
						<view class="btntwo c-ff t-c" @tap.stop="payorder(item.orderMealId)">立即付款</view>
					</view>
					<view class="flex-box flex-dir-row flex-x-s flex-wrap pt-30 pb-20 border_top" v-if="item.payStatus==1">
						<view class="btnone c-99" @tap.stop="reply(item.orderMealId,0)">申请退款</view>
					</view>
				</view> -->

        <view class="flex flex-x-e" v-if="item.payStatus == 0 || item.payStatus == 1 || item.payStatus == 5">
          <view class="flex-box flex-dir-row flex-x-b flex-wrap f-32 pt-30 pb-20 border_top" v-if="item.payStatus == 0">
            <view class="btnone c-99" @tap.stop="cancel(item.orderMealId)">取消订单</view>
            <view class="btntwo c-ff t-c" @tap.stop="payorder(item.orderMealId)">立即付款</view>
          </view>
          <view class="flex-dir-row flex-x-s flex-wrap pt-30 pb-20 border_top" v-if="item.payStatus == 1">
            <view v-if="item.isExpress == 0" class="btntwo c-ff t-c" @tap.stop="reply(item, 0)">查看物流</view>
          </view>
          <!-- <view class="flex-box flex-dir-row flex-x-e flex-wrap" v-if="item.payStatus==5">
						<view class="btntwo f-28 c-ff t-c" @tap.stop="reply(item.orderMealId,1)">取消申请退款</view>
					</view> -->
        </view>
      </view>
    </view>
    <view v-if="listS.list.length == 0" class="t-c flex-col" :style="{ height: useHeight + 'rpx' }">
      <!-- <image src="/static/cart/no_data.png" mode="widthFix" class="mb-20 img_s"></image> -->
      <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
      <view style="color: #bdbdbd">暂无数据</view>
    </view>
    <view v-if="no_more && listS.list.length > 0">
      <u-divider text="到底了"></u-divider>
    </view>
  </view>
</template>

<script>
  import { $tlpayResult } from '@/util/methods/common.js';
  const { $navigationTo, $showError, $showSuccess, $http } = require('@/util/methods.js');
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  export default {
    data() {
      return {
        payInfo: {},
        flag1: false,
        current: 0,
        // lineBg: 'http://mshnimg.vtui365.com/20220908104716f2e325872.png',
        tabindex: 0,
        imgHost: getApp().globalData.imgsomeHost,
        listCate: [
          {
            name: '全部',
            value: -1
          },
          {
            name: '待支付',
            value: 0
          },
          {
            name: '已支付',
            value: 1
          },
          {
            name: '退款中',
            value: 5
          },
          {
            name: '已退款',
            value: 3
          },
          {
            name: '已完成',
            value: 4
          },
          {
            name: '已取消',
            value: 2
          }
        ],
        payStatus: -1,
        page: 1,
        listS: {},
        useHeight: 0, //除头部之外高度
        orderList: {}
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 95;
        }
      });
    },
    onLoad(e) {
      this.payStatus = e.type || -1;
      this.current = e.type ? Number(e.type) + 1 : 0;
    },
    onShow() {
      if (this.flag1) {
        uni.$tlpayResult(this.sucees, this.fail, this.payInfo.orderId);
      }
      this.list();
      if (this.flag1) {
        $tlpayResult(this.sucees, this.fail, this.fails, this.lookpay);
      }
    },
    onReachBottom() {
      if (this.page >= this.listS.totalPage) {
        this.no_more = true;
        return false;
      }
      this.list(true, ++this.page);
    },
    methods: {
      sucees() {
        this.flag1 = false;
        // this.paysuccess(this.payInfo.bizOrderNo)
      },
      fail() {
        this.flag1 = false;
      },
      fails() {
        uni.showToast({
          title: '支付失败',
          icon: 'none',
          duration: 2000
        });
        this.flag1 = false;
      },
      // 取消订单
      async cancel(id) {
        let _this = this;
        uni.showModal({
          title: '提示',
          content: '确认取消该订单吗？',
          success: async function (res) {
            if (res.confirm) {
              const resdata = await $http({
                url: 'zx/order/mealOrderCancel',
                data: {
                  orderId: id
                }
              });
              if (resdata) {
                _this.list();
              }
            } else if (res.cancel) {
              console.log('用户点击取消');
            }
          }
        });
      },
      // 立即支付
      async payorder(id) {
        let _this = this;
        if (_this.disabled) {
          return false;
        }
        wx.showLoading();
        _this.disabled = true;
        const resdata = await $http({
          url: 'zx/meal/orderPayAnew/' + id
        });
        uni.hideLoading();
        _this.disabled = false;
        if (resdata) {
          _this.payBtn(resdata.data);
        }
      },

      async payBtn(data) {
        let _this = this;
        let resdata = await httpUser.post('mps/line/collect/order/unified/collect', data);
        let res = resdata.data.data;
        _this.disabled = false;

        if (res) {
          if (res.openAllinPayMini) {
            this.flag1 = true;
            this.payInfo = res;
            uni.$payTlian(res);
          } else {
            uni.requestPayment({
              provider: 'wxpay',
              timeStamp: res.payInfo.timeStamp,
              nonceStr: res.payInfo.nonceStr,
              package: res.payInfo.packageX,
              signType: res.payInfo.signType,
              paySign: res.payInfo.paySign,
              success: function (ress) {
                console.log('支付成功');
                // _this.successpay(id);
              },
              fail: function (err) {
                uni.showToast({
                  title: '支付失败',
                  icon: 'none',
                  duration: 2000
                });
              }
            });
          }
        }
      },

      // async successpay(orderid) {
      // 	let _this = this
      // 	const resdata = await $http({
      // 		url: 'zx/common/mealOrderNotifyHandle',
      // 		data: {
      // 			orderId: orderid,
      // 		}
      // 	})
      // 	if (resdata) {
      // 		_this.list();
      // 	}
      // },
      // 申请退款
      async reply(item, type) {
        let _this = this;
        debugger;
        if (item.isExpress == 1) {
          uni.showModal({
            title: '提示',
            content: type == 0 ? '确认申请退款吗？' : '确认取消申请退款吗？',
            success: async function (res) {
              if (res.confirm) {
                const resdata = await $http({
                  url: 'zx/order/applyRefundMeal',
                  method: 'post',
                  data: {
                    orderId: item.orderMealId,
                    refundType: type
                  }
                });
                if (resdata) {
                  $showSuccess(resdata.message);
                  _this.list();
                }
              } else if (res.cancel) {
                console.log('用户点击取消');
              }
            }
          });
        } else {
          uni.navigateTo({
            url: `splitContent/meal/logistics?orderId=${item.orderMealId}`
          });
        }
      },
      toDetail(id) {
        uni.navigateTo({
          url: './orderdetail?id=' + id
        });
      },
      async list(isPage, page) {
        let _this = this;
        uni.showLoading();
        const res = await $http({
          url: 'zx/order/userMealOrderList',
          data: {
            payStatus: _this.payStatus,
            page: page || 1
          }
        });
        uni.hideLoading();
        if (res) {
          if (isPage) {
            let old = _this.listS.list;
            _this.listS.list = [...old, ...res.data.list];
          } else {
            _this.listS = res.data;
          }
        }
      },
      click(e) {
        this.payStatus = e.value;
        console.log(this.payStatus);
        this.page = 1;
        this.no_more = false;
        this.list();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .order_s {
    border-bottom: 1px dashed #eee;
    // background-size: 60rpx 1px;
  }
  .border_top {
    border-top: 1px dashed #eee;
  }
  .flex_x {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .tobe_paid {
    background-color: #f04545;
    width: 90rpx;
    height: 36rpx;
    padding: 2rpx;
    border-radius: 4rpx;
    line-height: 36rpx;
  }

  .paid {
    background-color: #35a8f7;
    width: 90rpx;
    height: 36rpx;
    padding: 2rpx;
    border-radius: 4rpx;
    line-height: 36rpx;
  }

  .completed {
    background-color: #2dc032;
    width: 90rpx;
    height: 36rpx;
    padding: 2rpx;
    border-radius: 4rpx;
    line-height: 36rpx;
  }
  .drawback {
    background-color: #bf61de;
    width: 90rpx;
    height: 36rpx;
    padding: 2rpx;
    border-radius: 4rpx;
    line-height: 36rpx;
  }
  .close_pay {
    background-color: #c6c6c6;
    width: 90rpx;
    height: 36rpx;
    padding: 2rpx;
    border-radius: 4rpx;
    line-height: 36rpx;
  }
  .refunded {
    background-color: #389791;
    width: 90rpx;
    height: 36rpx;
    padding: 2rpx;
    border-radius: 4rpx;
    line-height: 36rpx;
  }
  .btntwo {
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    min-width: 160rpx;
    height: 60rpx;
    border-radius: 30rpx;
    line-height: 60rpx;
  }

  .u-tabs__wrapper {
    padding-top: 10rpx !important;
  }

  .btnone {
    height: 60rpx;
    line-height: 60rpx;
  }

  .img_s {
    width: 160rpx;
    // margin-top: 310rpx;
  }

  .tabs {
    padding: 30rpx 0;
    width: 100%;
  }

  .tabs text {
    font-size: 30upx;
    color: #666;
  }

  .tabs .active {
    color: #333;
    font-weight: bold;
  }

  /deep/ .u-tabs__wrapper__nav__line {
    left: 15rpx;
  }
</style>
