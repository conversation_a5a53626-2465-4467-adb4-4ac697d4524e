<!-- 资料学习记录结束页 -->
<template>
  <div class="container">
    <!-- 自定义导航栏 -->
    <view class="custom-nav">
      <view class="custom-nav-item">
        <view>
          <!-- #ifdef MP-WEIXIN -->
          <u-icon name="arrow-left" size="46" @click="goBack"></u-icon>
          <!-- #endif -->

          <!-- #ifdef APP-PLUS -->
          <u-icon name="arrow-left" size="23" @click="goBack"></u-icon>
          <!-- #endif -->
        </view>

        <view class="nav-title"></view>
        <view></view>
      </view>
    </view>
    <!-- 结束图 -->
    <div class="trailer"></div>
  </div>
</template>

<script>
  export default {
    methods: {
      goBack() {
        uni.navigateBack();
      }
    }
  };
</script>
<style>
  page {
    width: 100%;
    height: 100vh;
    overflow: hidden;
  }
</style>
<style lang="scss" scoped>
  .container {
    width: 100%;
    height: 100%;
    background: url('https://document.dxznjy.com/course/d5c47fb2d1fd4b96a5a299f6d5015320.png') 100% 100% no-repeat;
    background-position: center;
    background-size: 100% auto;
  }
  .trailer {
    position: absolute;
    top: 13%;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('https://document.dxznjy.com/course/1ca5542b953e4bd893dd830e158c594e.png') 100% 100% no-repeat;
    background-position: center top;
    background-size: 100% auto;
  }
  // 自定义导航栏
  .custom-nav {
    width: 100%;
    height: 169rpx;
    // background-color: #ffffff;

    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    // box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    padding-top: 105rpx;
    box-sizing: border-box;
  }

  .custom-nav-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 10rpx;
  }

  .custom-nav-item view {
    width: 33%;
  }

  .nav-title {
    font-size: 32rpx;
    // margin-top: 82rpx;
    font-weight: bold;
    color: #333333;
    text-align: center;
  }
</style>
