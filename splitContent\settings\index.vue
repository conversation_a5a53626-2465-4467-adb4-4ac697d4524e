<template>
  <view>
    <view class="setting-list pt-12">
      <view  @click="skintap('signature/contract/cManagement')"  class="setting-item setting-item-border">
        合同管理
      </view>
      <view  @click="showCodePoup" class="setting-item">
        营销小组
      </view>
    </view>
    <view class="setting-list">
      <view class="setting-item setting-item-border">
        <button class="customer" open-type="contact" hover-class="none">
          联系客服
        </button>
      </view>
      <view  @tap="skintap('Personalcenter/suggest/index')"  class="setting-item setting-item-border">
        意见反馈
      </view>
      <view  @click="handleCallPhone" class="setting-item">
        投诉电话
      </view>
    </view>
    <view class="setting-list">
<!--      <view class="setting-item setting-item-border">
        软件版本
      </view> -->
      <view  @click="skintap('signature/protocol/ProtocolList')" class="setting-item">
        协议与政策
      </view>
      <!-- <view class="setting-item"  @click="Logout">
        注销账号
      </view> -->
    </view>
    <view class="exit_btn"  @click="Logout">
      退出登录
    </view>
    <uni-popup ref="codePoup" type="center">
      <view class="codeCard">
        <view class="review_close" @click="closeCodePoup">
          <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
        </view>
        <view class="reviewTitle bold pb-50">输入营销码</view>
        <u-code-input v-model="codeValue" size="70" fontSize="32"></u-code-input>
        <view class="code_dxn_btn" :class="codeValue.length >= 6 ? 'code-active' : ''" @click="confirmCode">确定</view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  const { $navigationTo } = require('@/util/methods.js');
  export default {
    data(){
      return{
        codeValue:'',
      }
    },
    methods:{
      skintap(url) {
        $navigationTo(url)
      },
      showCodePoup() {
        getApp().sensors.track('myTools&ServiceItemClick', {
          name: '营销小组'
        });
        if (!uni.getStorageSync('token'))
          return uni.showToast({
            title: '请先登录',
            icon: 'error',
            duration: 2000
          });
        this.codeValue = '';
        this.$refs.codePoup.open();
      },
      
      // 退出登录
      async Logout() {
        await uni.removeStorage({
          key: 'log_userCode'
        });
        uni.removeStorage({
          key: 'token',
          success: (res) => {
            console.log('删除token');
            uni.reLaunch({
              url: '/pages/home/<USER>/index'
            });
          }
        });
        uni.setStorageSync('identityType', 0);
        uni.removeStorageSync('club');
        uni.removeStorageSync('Partner');
        uni.removeStorageSync('brand');
        uni.removeStorageSync('user_id');
        uni.removeStorageSync('currentStatus');
        uni.removeStorageSync('showContractInfo');
        uni.removeStorageSync('showMaskStatus');
      },
      // 400电话
      handleCallPhone() {
        getApp().sensors.track('myTools&ServiceItemClick', {
          name: '投诉电话'
        });
        uni.makePhoneCall({
          phoneNumber: '************',
          success: (res) => {
            console.log(res);
          },
          fail: (err) => {
            console.log(err);
          }
        });
      },
      // 关闭弹框
      closeCodePoup() {
        this.$refs.codePoup.close();
        this.codeValue = '';
      },
      // 确认校验
      confirmCode() {
        if (this.codeValue !== '184887')
          return uni.showToast({
            title: '请输入正确的营销码',
            icon: 'error',
            duration: 2000
          });
        this.$refs.codePoup.close();
        uni.navigateTo({
          url: '/middlePage/callPhone'
        });
      }
    }
  }
</script>

<style lang="scss">
  .setting-list{
    margin-bottom: 24rpx;
    .setting-item{
      padding:36rpx 0;
      padding-left:32rpx;
      background-color: #fff;
    }
    .setting-item-border{
     border-bottom: 1px solid #f0f0f0;
    }
  }
  .customer{
    text-align: left;
    font-size: 32rpx;
  }
  /* 营销码 */
  .codeCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 45upx 65upx;
    box-sizing: border-box;
  }
  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }
  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }
  .code_dxn_btn {
    // width: 250upx;
    height: 80upx;
    background: #e5e5e5;
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 80upx;
    margin: 40rpx auto 0 auto;
    justify-content: center;
    text-align: center;
  }
  .code-active {
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
  }
.exit_btn {
  width: 694rpx;
  height: 90rpx;
  color: #666;
  line-height: 90rpx;
  border-radius: 18rpx;
  background-color: #fff;
  text-align: center;
  font-size: 30rpx;
  margin: 60rpx auto;
}
</style>