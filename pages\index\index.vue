<template>

  <!-- <page-meta :page-style="'overflow:'+(rollShow?'hidden':'visible')"></page-meta> -->
  <view>
    <scroll-view class="scroll-box" @scrolltolower="scrolltolower" :upper-threshold="topBoxHeight" refresher-enabled :show-scrollbar="false" bounces :throttle="false" scroll-with-animation :scroll-top="productsScrollTop" @scroll="scrollPos" @scrolltoupper="bindscrolltoupper" :refresher-triggered="refreshing" @refresherpulling="refreshing = true" @refresherrefresh="onRefresh" scroll-anchoring scroll-y enhanced refresher-default-style="none">
      <view v-if="refreshing" class="pull-content">
        <uni-load-more status="loading" :contentText="{ contentrefresh: '下拉刷新内容～' }" />
      </view>
      <view id="scrool_height">
        <view>
          <view class="indextop">
            <view class="search-css pl-30 index-top-search" :style="'top:'+(refreshing?10:statusBarHeight)+'px;height:'+titleBarHeight+'px'">
              <u-icon name="search" class="box-50 search-image" color="#ffffff" size="38"></u-icon>
              <view class="search-input f-28 c-ff" type="text" @click="searchFocus" :readonly="true">
                鼎拼读</view>
            </view>
            <view :style="'top:'+(refreshing?560:644)+'rpx'" class="more-btn f-28" @tap="openspecial(specialList[bannerIndex], 1)">查看更多</view>
            <!--  :style="'background-color:'+homeInfo.specialList[bannerIndex].backgroundColor+';'" -->
            <view class="index-swiper radius-image-css">
              <swiper :autoplay="false" :current="bannerIndex" style="height:832rpx;" :interval="3000" @animationfinish="changeBannerCurrent" :duration="1000" circular>
                <block class="radius-image-css" v-for="(item, index) in specialList" :key="index">
                  <swiper-item class="items-center radius-image-css">
                    <view class="top-image-border radius-image-css">
                      <image class="cover-css" src="https://document.dxznjy.com/course/387f9edbc80e427687ed6d0e73d9b766.png" mode=""></image>
                      <view class="background-line radius-image-css" :style="'background:linear-gradient(to bottom, '+specialList[bannerIndex].backgroundColor+', #FFFFFF,'+specialList[bannerIndex].backgroundColor+')'">
                        <image class="background-image" :src="item.picUrl" mode=""></image>

                      </view>
                      <!-- <image mode="widthFix" style="width:750rpx" class="radius-image-css" lazy-load="true" :src="homeInfo.specialList[bannerIndex].picUrl"> -->
                      <view class="background-css">
                        <view class="search-height" :style="'height:'+(refreshing?0:80)+'rpx'"></view>
                        <view class="plr-32 pt-20">
                          <image :src="item.picUrl" @tap="openspecial(item, 0)" mode="aspectFill" class="banner-image radius-12" lazy-load="true">
                          </image>
                        </view>
                        <view class="flex-a-c flex-x-s items-center pl-30 mt-30">
                          <view v-for="goodsItem in item.goodsList" @click="productShowcase('Coursedetails/productDetils?id=' + goodsItem.goodsId, goodsItem)" class="goods-item mr-18" :key="goodsItem.goodsId">
                            <image :src="goodsItem.goodsSpecialPicUrl" mode="aspectFill" class="goods-image absolute radius-12" />
                            <view class="goods-price-content">
                              <view class="goods-price">
                                <span class='f-24 price-span' v-if='goodsItem.goodsType==2'>体验价</span>
                                <span class='f-24 price-span' v-if='goodsItem.goodsType==3'>会员价</span>
                                <span class='f-24 price-span' v-if='goodsItem.goodsType==4'>会员价</span>
                                <span class='h2'>¥{{goodsItem.goodsVipPrice}}</span>
                              </view>
                            </view>
                          </view>

                        </view>
                      </view>
                    </view>
                  </swiper-item>
                </block>
              </swiper>
              <!-- <view>
                <view class="banner_indicator_style">
                  <view v-for="(item, index) in bannerList" :key="index" :class="['indicator_style_css ', bannerIndex == index ? 'active_current' : '']">
                  </view>
                </view>
              </view> -->
            </view>
          </view>
        </view>
        <view class="flex-a-c flex-x-b items-center product_content">
          <view class="flex-col flex-box" @tap="skintap('pages/selectCourse/selectCourse')">
            <image src="https://document.dxznjy.com/course/9d852962b220495e928e7a591d2065b7.png" class="boxh-48" mode="heightFix"></image>
            <text class="f-28 c-44 mt-15 lh-36 fontWeight">甄选好课</text>
          </view>
          <view class="flex-col flex-box" @tap="skintap('memberCenter/index')">
            <image src="https://document.dxznjy.com/course/d1d6eb9372a944fc9fd901590f6773dc.png" class="boxh-48" mode="heightFix"></image>
            <text class="f-28 c-44 mt-15 lh-36 fontWeight">文化中心</text>
          </view>
          <view class="flex-col flex-box" @tap="skintap('shoppingMall/index')">
            <image src="https://document.dxznjy.com/course/8640875ede2f4c0ab0862b92738011e4.png" class="boxh-48" mode="heightFix"></image>
            <text class="f-28 c-44 mt-15 lh-36 fontWeight">鼎币商城</text>
          </view>
          <view class="flex-col flex-box" @click="openParentVip()">
            <image src="https://document.dxznjy.com/course/3d2fdd2f3968424a873942d753ea78b6.png" class="boxh-48" mode="heightFix"></image>
            <text class="f-28 fontWeight lh-36 c-44 mt-15">家长会员</text>
          </view>
        </view>
        <!-- <view class="product_content mt-50">
          <swiper :autoplay="false" class="uni-swiper-wrapper" @change="changeCurrent" style="width: 100%; height: 96rpx" :current="currentIndex">

            <swiper-item class="wh100 flex-c radius-16">
              <view class="flex product wh100">
                <view class="flex-col flex-box" @tap="skintap('shoppingMall/index')">
                  <image src="https://document.dxznjy.com/course/7be1d559fd4d469bba1cdaefc59d4481.png" class="boxh-48" mode="heightFix"></image>
                  <text class="f-24 c-44 mt-15 lh-36 fontWeight">鼎币商城</text>
                </view>
                <view class="flex-col flex-box" @tap="skintap('meeting/meetIndex')">
                  <image src="https://document.dxznjy.com/course/3f708b88091644a2bdd25184f5a21033.png" class="boxh-48" mode="heightFix"></image>
                  <text class="f-24 c-44 mt-15 lh-36 fontWeight">会议中心</text>
                </view>
                <view class="flex-col flex-box" @tap="skintap('memberCenter/index')">
                  <image src="https://document.dxznjy.com/course/77fba4f1f0e043ab98c8de95fb3a5acd.png" class="boxh-48" mode="heightFix"></image>
                  <text class="f-24 c-44 mt-15 lh-36 fontWeight">文化中心</text>
                </view>
                <view class="flex-col flex-box" @click="openParentVip()">
                  <image src="https://document.dxznjy.com/course/f33f3ab9e1034fc2871ac6eecf55ca31.png" class="boxh-48" mode="heightFix"></image>
                  <text class="f-24 fontWeight lh-36 c-44 mt-15">家长会员</text>
                </view>
                <view class="flex-col flex-box" @click="openspecial()">
                  <image src="https://document.dxznjy.com/course/f33f3ab9e1034fc2871ac6eecf55ca31.png" class="boxh-48" mode="heightFix"></image>
                  <text class="f-24 fontWeight lh-36 c-44 mt-15">专题</text>
                </view>
                <view class="flex-col flex-box" @click="openspecials()">
                  <image src="https://document.dxznjy.com/course/f33f3ab9e1034fc2871ac6eecf55ca31.png" class="boxh-48" mode="heightFix"></image>
                  <text class="f-24 fontWeight lh-36 c-44 mt-15">了解</text>
                </view>
              </view>
            </swiper-item>
          </swiper>
 
        </view> -->
        <!-- <view class="order_code">
          <view class="order_code1">
            <input class="input1" type="number" placeholder="请输入添加教练验证码" v-model.trim="smsCode" />
            <view class="confirmbutton" @click="finishCode">确定</view>
          </view> -->
        <!-- <u-code-input v-model="smsCode" :maxlength="6" fontSize="40" bold size="88rpx" borderColor="red;" space="30rpx" @finish="finishCode"></u-code-input> -->
        <!-- </view> -->
        <!-- <view class="pt-20 plr-32" v-if="isHideZXStudy">
          <mycurriculum ref="mycurriculum" class="my_class_css"></mycurriculum>
        </view> -->

        <!-- 拼团 -->
        <!-- <view class="group-buy pt-20 plr-32" v-if="groupList.length > 0">
          <view class="index-swiper">
            <swiper style="height: 204rpx" :autoplay="true" :current="groupIndex" :interval="3000" @animationfinish="changeGrouPCurrent" :duration="1000" circular>
              <block class="" v-for="(item, index) in groupList" :key="index">
                <swiper-item class="flex-c radius-16">
                  <image :src="item.bannerPicUrl" mode="aspectFill" class="wh100" lazy-load @tap="handlerGroupBuyClick(item)" @load="onImageLoad"></image>
                </swiper-item>
              </block>
            </swiper>
            <view v-if="groupList.length > 0">
              <view class="banner_indicator_style">
                <view v-for="(item, index) in groupList" :key="index" :class="['indicator_style_css ', groupIndex == index ? 'active_current' : '']">
                </view>
              </view>
            </view>
          </view>
        </view> -->
        <view class="plr-32">
          <view class="flex-a-c flex-x-b items-center ">
            <view class="radius-16" style="height: 335rpx;width:320rpx;">
              <swiper style="height: 335rpx;width:320rpx;" :autoplay="true" :current="groupIndex" :interval="3000" @animationfinish="changeGrouPCurrent" :duration="1000" circular>
                <swiper-item v-for="(item, index) in bannerList" :key="index">
                  <image :src="item.bannerPicUrl" @tap="bannerTab(item)" mode="scaleToFill" style="height: 335rpx;width:320rpx;" />
                </swiper-item>
              </swiper>
            </view>
            <view class="radius-16" style="height: 335rpx;width:320rpx;">
              <image :src="porcelainInfo.bannerPicUrl" class="radius-16" @tap="bannerTab(porcelainInfo)" mode="scaleToFill" style="height: 335rpx;width:320rpx;" />
            </view>
          </view>
        </view>
      </view>
      <view class="my-class mt-12">
        <u-sticky bgColor="transparent" :z-index="99">
          <view v-if="showScroll" class="bg-ff pb-25 plr-32" :style="'padding-top:'+statusBarHeight+'px'">
            <view class="pb-20">
              <view class="search-css search-css-sticky pl-30" :style="'height:'+titleBarHeight+'px'">
                <u-icon name="search" class="box-50 search-image" color="#BDBDBD" size="38"></u-icon>
                <view class="search-input search-input-sticky f-28" type="text" @click="searchFocus" :readonly="true">
                  鼎拼读
                </view>
              </view>
            </view>
            <u-tabs :list="categoryGoodsList" :current="tabsCurrent" keyName="moduleName" lineWidth="0" :activeStyle="{ color: '#009B55', fontWeight: 'bold', fontSize: '28rpx' }" :inactiveStyle="{
                color: '#555555 ',
                transform: 'scale(1)',
                fontSize: '28rpx'
              }" itemStyle="padding-left:5px; padding-right: 25px; height: 34px;" @click="tabsClick"></u-tabs>
          </view>
        </u-sticky>
        <view class="courseList mt-12 pt-20">
          <view v-if="showScroll" id='showScrollTab' class="h240">

          </view>
          <view v-for="(item, index) in categoryGoodsList" :key="index">
            <view class="pb-35 flex-s  plr-32" style='width:688rpx'>
              <view class="c-33 f-32 title-css-module" :id="`section-${index}`">
                {{ item.moduleName }}
              </view>
              <view v-if="!item.allGoodsDisplayed" class="f-28 more-btn-css flex-e" @click="openspecials(item)">
                更多
                <u-icon name="arrow-right" color="#888896" size="28"></u-icon>
              </view>
            </view>
            <!-- 纵向 -->
            <view class="flex-wrap flex-x-b plr-32" v-if="item.horzVert == 1">
              <courseList v-for="(goodsItem, indexs) in item.goodsList" :key="indexs" :item="goodsItem" :index="indexs" :width="'326rpx'" @click="skintap('Coursedetails/productDetils?id=' + goodsItem.goodsId, goodsItem)">
              </courseList>
            </view>
            <!-- 横向大于1个 -->
            <view v-if="item.horzVert == 0 && item.goodsList.length > 1" class="plr-32">
              <scroll-view scroll-x="true" class="scroll-content">
                <view v-for="(goodsItem, indexs) in item.goodsList" :key="indexs" class="goods-item-wrapper" :style="{ paddingRight: (indexs === item.goodsList.length - 1) ? '0' : '34rpx' }">
                  <courseList :item="goodsItem" :index="indexs" :width="'326rpx'" @click="skintap('Coursedetails/productDetils?id=' + goodsItem.goodsId, goodsItem)">
                  </courseList>
                </view>
              </scroll-view>
            </view>
            <!-- 横向只有1个 -->
            <view v-if="item.horzVert == 0 && item.goodsList.length == 1" class="course-center-css plr-32 pt-28 pb-25 mb-30">
              <!-- https://document.dxznjy.com/course/1bb7096adb1d446fa8f37e20bf5b0668.png -->
              <view class="bg-ff course-one-css radius-12 pb-35" @click="singleSkinTap('Coursedetails/productDetils?id=' + item.goodsList[0].goodsId)">
                <image class="css-image radius-12" :src="item.goodsList[0].goodsPicUrl" mode=""></image>
                <view class="plr-15 pt-30">
                  <view class="f-28 c-55 lh-40 bold">
                    <span v-if='item.goodsList[0].goodsTagOne' class="goodsTagOne-css f-28 lh-40 radius-5 mr-5">
                      {{item.goodsList[0].goodsTagOne}}
                    </span>
                    {{item.goodsList[0].goodsName}}
                  </view>
                  <view class="mt-10">
                    <span class="display_inline mr-10" v-if="item.goodsList[0].goodsTagTwo">{{item.goodsList[0].goodsTagTwo}}</span>
                    <span class="display_inline" v-if="item.goodsList[0].goodsTagThree">{{item.goodsList[0].goodsTagThree}}</span>
                  </view>
                  <view class="flex-s">
                    <view class="mt-20" style="color:#FA370E;">
                      <span class='f-24 price-span' v-if='item.goodsList[0].goodsType==2'>体验价</span>
                      <span class='f-24 price-span' v-if='item.goodsList[0].goodsType==3'>会员价</span>
                      <span class='f-24 price-span' v-if='item.goodsList[0].goodsType==4'>会员价</span>
                      <span class='f-32'>¥{{item.goodsList[0].goodsVipPrice}}</span>
                    </view>
                    <view class="f-24 c-9">
                      {{item.goodsList[0].goodsSales}}人付款
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <view class="learn-more-css plr-32" @tap="aboutClick('InvitationGifts/aboutPage')">
          <image class="more-image" src="https://document.dxznjy.com/course/1196f44ea9c748ba973b20e52bc80107.png" mode="widthFix"></image>
          <view class="flex-a-c learn-more-content" @tap="aboutClick('InvitationGifts/aboutPage')">
            <view v-for="(item,index) in learnMore" :key='index' class="learn-more-item c-ff">
              <view class="f-16">
                {{item.english}}
              </view>
              <view class="f-32 mt-20">
                {{item.subject}}
              </view>
              <view class="f-32 mt-12">
                {{item.title}}
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    <!-- 鼎英语体验劵弹窗 -->
    <uni-popup ref="popopSecurities" type="center" class="positionRelative" @change="change">
      <image :src="imgHost + 'alading/correcting/index_dialog.png'" class="dialog_img" style="width: 760rpx" mode="widthFix" @tap="skintap('Coursedetails/productDetils?id=' + 'c499556f9da64dce19b723296c884bc4')">
      </image>
      <view class="dialog-icon">
        <uni-icons type="clear" size="28" color="#d6d5d4" @click="close"></uni-icons>
      </view>
    </uni-popup>
    <uni-popup ref="popopChooseSchool" type="center" @change="change">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold pb-20">选择校区</view>
            <scroll-view :scroll-top="scrollTop" scroll-y="true" class="scroll-Y" @scrolltoupper="upper" @scrolltolower="lower" @scroll="scroll">
              <view class="dialogContent" @click="chooseSchoollist(item, index)" v-for="(item, index) in arraySchool" :key="item.merchantCode" :class="isactive == index ? 'addclass' : 'not-selected'">
                {{ item.merchantName }}
              </view>
            </scroll-view>
            <view class="review_btn" @click="confirmSchool()">确定</view>
          </view>
        </view>
      </view>
    </uni-popup>
    <!-- 选择学员 -->
    <uni-popup ref="popopChooseStudent" type="center" @change="change">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold pb-20">选择学员</view>
            <view class="dialogContent" @click="chooseStudentlist(item, index)" v-for="(item, index) in arrayStudent" :key="index" :class="isactive == index ? 'addclass' : 'not-selected'">
              {{ item.realName }}
            </view>
            <view class="review_btn" @click="$noMultipleClicks(confirmStudent)">确定</view>
          </view>
        </view>
      </view>
    </uni-popup>
    <!-- 没有开通权限 -->
    <uni-popup ref="popopPower" type="center" @change="change">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold">温馨提示</view>
            <view class="c-f0 t-c mtb-50 ptb-50">您还没有开通使用权限</view>
            <view class="review_btn" @click="nowBuy()">立即购买</view>
          </view>
        </view>
      </view>
    </uni-popup>
    <!-- 分享弹窗 -->
    <!-- shareContent -->
    <sharePopup ref="sharePopupRefs"></sharePopup>

    <!-- 发音提示 -->
    <uni-popup ref="pronunciationPopup" type="center" :mask-click="false" @change="change">
      <view class="pronunciation_container">
        <image :src="imgHost + 'dxSelect/fourthEdition/icon_pronunciation.png'" class="pronunciation_img positionRelative"></image>
        <uni-icons type="clear" size="28" class="close_icon" color="#B1B1B1" @click="closePronunciation"></uni-icons>
        <view class="pronunciation_title c-ff bold f-28">鼎校AI语音2.0</view>
        <view class="pronunciation_content f-28">
          <view class="bold mtb-10">1.清晰度和准确性</view>
          <view class="f-24 c-99 lh-42 ml-25">AI2.0模型能够产生清晰而准确的语音输出确保听者能够准确理解说话内容，而不会因发音不清或歧义而引起困扰。</view>
          <view class="bold mtb-10">2.绝对自然语调和韵律</view>
          <view class="f-24 c-99 lh-42 ml-25">AI2.0模型能够捕捉到自然语音中的语调、韵律和语音节奏，使合成语音更接近人类的语音模式。</view>
          <view class="bold mtb-10">3.媲美人声流畅度</view>
          <view class="f-24 c-99 lh-42 ml-25">AI2.0模型能够生成流畅、连贯的语音输出，避免在单词之间或语音单元之间出现突兀的断裂感。</view>
          <view class="bold mtb-10">4.多音节词处理</view>
          <view class="f-24 c-99 lh-42 ml-25">AI2.0模型能够有效处理多音节词汇，确保发音正确且自然，提高整体语音合成的质量。</view>
          <view class="bold mtb-10">5.音高和音质</view>
          <view class="f-24 c-99 lh-42 ml-25">AI2.0模型能够产生逼真的音高和音质，使得合成语音更加悦耳、自然，接近真实人类声音</view>
          <view class="f-24 mt-8 c-f0b ml-25">默认发音：美式-女声！修改请前往家长中心-切换发音</view>

          <view class="flex-c">
            <view class="pronunciation_enable c-ff f-30 t-c mt-45" @click="audition">立即试听</view>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 试听弹窗 -->
    <uni-popup ref="auditionPopop" type="center" @change="change">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeAudition">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold">鼎校AI语音2.0</view>
            <view class="c-33 f-46 bold flex-c mt-80 positionRelative">
              {{ words }}
              <!-- <image :src="imgHost+'dxSelect/fourthEdition/import-icon.png'" class="import-icon"></image> -->
            </view>
            <view class="flex-c mt-40 mb-65" @click="randomWords">
              <image :src="imgHost + 'dxSelect/fourthEdition/refresh.png'" class="refresh_icon"></image>
              <text class="random_text f-28 ml-15">随机单词</text>
            </view>

            <view class="flex-x-a f-30 flex-c">
              <view class="playType c-99 t-c flex-c no_selected" @click="changePlay(2)">
                <text>v2</text>
                <image :src="imgHost + 'dxSelect/fourthEdition/icon_voice.png'" class="icon_voice ml-15"></image>
              </view>
              <view class="playType c-99 t-c flex-c no_selected" @click="changePlay(1)">
                <text>v1</text>
                <image :src="imgHost + 'dxSelect/fourthEdition/icon_voice.png'" class="icon_voice ml-15"></image>
              </view>
              <view class="playType c-99 t-c flex-c no_selected" @click="Competitors">
                <text>竞品</text>
                <image :src="imgHost + 'dxSelect/fourthEdition/icon_voice.png'" class="icon_voice ml-15"></image>
              </view>
            </view>
            <view class="flex-c">
              <view class="pronunciation_enable c-ff f-30 t-c mt-45" @click="savePronunciation">立即开启
              </view>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>

    <!-- 单词错误 -->
    <uni-popup ref="notifyPopup" type="top" mask-background-color="rgba(0,0,0,0)">
      <view class="plr-60">
        <view class="t-c bg-ff flex-c ptb-25 radius-50 mt-30 notify">
          <image :src="imgHost + 'dxSelect/err_tip.png'" class="err_tip"></image>
          <view class="f-34 ml-15">单词有误，请重新输入</view>
        </view>
      </view>
    </uni-popup>

    <!-- 二维码 -->
    <u-popup :show="codeShow" :round="24" mode="center" overlay class="pay-content" :safeAreaInsetBottom="false" @open="openDialog">
      <image class="cartoom_image" src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix">
      </image>
      <view class="orderinfo">
        <view class="closeBox">
          <u-icon name="close-circle-fill" class="close-icon" size="42" labelColor="#fff" color="#b0b0b0" @click="closeCode"></u-icon>
        </view>
        <view class="flex flex-col" style="padding-top: 70rpx">
          <image :src="codeForm.qrCode" style="width: 330rpx; height: 330rpx" class="mb-20 img" show-menu-by-longpress="true" mode=""></image>
          <view class="color">
            <text>请用</text>
            <text style="color: red">微信</text>
            <text>扫码添加教练</text>
          </view>
          <view class="color">
            系统才会为
            <text style="font-weight: 600">{{ codeForm.studentName || '**' }}</text>
            学员建立专属体验群
          </view>
          <view class="color">请务必添加哦~</view>
        </view>
        <view class="btns">
          <view class="btn btn2" @click="saveCode">保存二维码</view>
        </view>
      </view>
    </u-popup>

    <view v-if="isGroupGiftClosed" class="gifts">
      <!-- <view class="group-gift" @click.self="openGroupGift"></view> -->
      <image class="group-gift" :src="floatWindow.picUrl" @click.self="openGroupGift" mode="widthFix" />
      <uni-icons type="close" size="22" color="#D6D6D6" @click.self="closeGroupGift" class="close-button"></uni-icons>
    </view>
    <user-dialog ref="userDialogRef" />
  </view>
</template>
<script>
const {
  httpUser
} = require('@/util/luch-request/indexUser.js');
import Config from '@/util/config.js';
import uniCard from '@/components/uni-card/uni-card.vue';
import helangWaterfall from '@/components/helang-waterfall/helang-waterfall';
import mycurriculum from '@/components/mycurriculum.vue';
import sharePopup from '@/components/sharePopup.vue';
import uniLoadMore from '@/components/uni-load-more/uni-load-more.vue';
import dayjs from 'dayjs';
import CryptoJS from 'crypto-js';
import UserDialog from '@/components/user-dialog/index.vue';
import getNotReadNews from '@/util/messages.js';
var innerAudioContext;
const {
  $navigationTo,
  $http
} = require('@/util/methods.js');
import {
  bindMerchantByScan
} from '@/common/superman.js';
import courseList from '@/components/course-list/course-list.vue';
export default {
  components: {
    sharePopup,
    uniCard,
    mycurriculum,
    helangWaterfall,
    uniLoadMore,
    UserDialog,
    courseList
  },
  data() {
    return {
      noClick: true, //防抖
      imageStyles: {
        width: 100,
        height: 100
      },
      homeInfo: {},
      // uni.getStorageSync('token')
      token: uni.getStorageSync('token'),
      topBoxHeight: 0,
      scrollHeight: 0,
      showScroll: false,
      productsScrollTop: 0,
      leftHeight: 0,
      rightHeight: 0,
      leftList: [],
      rightList: [],
      ajax: {
        // 是否可以加载
        load: true,
        // 加载中提示文字
        loadTxt: '',
        // 每页的请求条件
        rows: 10,
        // 页码
        page: 1
      },
      /*  */
      city: {},
      multiIndex: [0, 0],
      show: false,
      newCategotyDataList: [
        [],
        []
      ],
      learnMore: [{
        english: 'Learning with a coach',
        title: '教练伴学',
        subject: '一对一'
      },
      {
        english: 'Intelligent education',
        title: '智能教育',
        subject: 'AI+'
      },
      {
        english: 'Score-improving training',
        title: '提分训练',
        subject: '专业'
      }
      ],
      showCurriculum: false,
      categoryArr: [],
      mealLists: [],
      infoLists: {},
      bannerList: [],
      porcelainInfo: {},
      categoryGoodsList: [],
      specialList: [],
      groupList: [],
      currentIndex: 0,
      bannerIndex: 0,
      groupIndex: 0,
      page: 1,
      no_more: false,
      userinfo: {},
      CourseTips: true, // 是否显示课程
      appears: false, // 登录前是否显示我的课程
      isShow: true, // 登录后是否显示上次课程
      isshow: true, // 登录后是否显示下次课程
      isNextback: true, // 下次上课是否反馈
      // 日期选择器
      value: '',
      isstatus: false, //日历
      navIndex: 0, // tabs栏索引
      imgHost: getApp().globalData.imgsomeHost,
      interestSelect: false,
      txtContent: '您还没有开通使用权限',
      arraySchool: [],
      arrayStudent: [],
      isLogin: false,
      mobile: '', // 会员手机号
      userCode: '',
      // lastCourse: [], // 上次上课时间
      // nextCourse: [], // 上次上课时间
      id: '', // 课程id
      type: '', // 课程类型
      member: false,
      exhibition: false,
      shows: false,
      orderShow: false,
      planId: '', // 排课id
      altitudeShow: false, // 判断屏幕高度
      rollShow: false, //禁止滚动穿透
      isactive: -1, // 选中索引
      scrollTop: 0,
      showScrollTabHeight: 0,
      old: {
        scrollTop: 0
      },

      // 分享内容
      shareContent: {},
      supermanShow: false, // 是否可以购买学习超人

      words: 'strawberry',
      // playSatus:'', // 试听播放方式
      timbre: 'W', // 音色默认女声 M  W
      pronunciationType: 0, // 1  0  默认美式
      voiceModel: '',
      useHeight: 0,
      playSatus: 2, // 版本 默认2.0
      isplay: false,
      identityType: '',
      otherWord: ['strawberry', 'keep pace with', 'kangaroo', 'foreign', 'by means of', 'as a result of',
        'word'
      ], // 随机单词
      linkUrl: '',
      imgShow: false,
      sg: '',
      tabsCurrent: 0,
      scrollTimer: null,
      categoryList: [],
      sectionPositions: [], //记录时间
      lineBg: 'https://document.dxznjy.com/course/01c96465d9ea46f69aa97465b1201aef.png',
      categoryId: '',
      isGroupGiftClosed: false, // 控制入群有礼入口显示与隐藏的状态
      codeShow: false,
      codeForm: {
        qrCode: '',
        studentName: ''
      },
      pageScrollTop: 0,
      showTabs: false,
      floatWindow: {},
      textContent: '#4CAF50',
      // smsCode: '',
      refreshing: false,
      // 是否展示首页学习课程内容
      isHideZXStudy: true,
      statusBarHeight: 0, // 状态栏高度
      navBarHeight: 0, // 整个导航栏高度（状态栏高度 + 标题栏高度）
      titleBarHeight: 0 // 标题栏高度（胶囊按钮所在区域）
    };
  },
  onLoad(options) {
    // 判断用户是否为微信扫码进入
    const {
      isScanIn,
      qrId,
      merchantCode,
      merchantPhone
    } = options;
    if (isScanIn) {
      this.handleScanBind(qrId, merchantCode, merchantPhone);
    }
    this.meal();
    this.calculateNavBarHeight();
  },
  onUnload() {
    this.productsScrollTop = 0
  },
  onTabItemTap(e) {
    getApp().sensors.track('tabBarClick', {
      pagePath: e.pagePath,
      title: e.text
    });
  },
  onPageScroll(e) {
    console.log('cccccccccccccccccccccccccccccccccccccccccccc')
    this.pageScrollTop = e.scrollTop;
    // 当滚动距离超过分类区域高度时显示tabs
    this.showTabs = e.scrollTop > 200; // 可根据实际需要调整这个阈值
  },
  async onShow() {
    const isClosed = uni.getStorageSync('isGroupGiftClosed');
    if (isClosed) {
      this.isGroupGiftClosed = false;
    }
    this.productsScrollTop = 0
    this.identityType = uni.getStorageSync('identityType');
    uni.removeStorageSync('payToken');
    uni.removeStorageSync('wxpay');
    uni.removeStorageSync('clearData');
    uni.removeStorageSync('logintokenReview');
    this.page = 1;
    this.getFloatingWindow();
    this.getBanner();
    this.getPorcelain();
    this.getCategoryGoods();
    this.getSpecialList();
    this.productsScrollTop = 0;
    // 由于scroll-view组件在滚动位置为0时，设置scroll-top可能无效，所以采用先设置一个非0值，再设置为0的方法
    this.$nextTick(() => {
      this.productsScrollTop = 1; // 先设置一个非0值
      setTimeout(() => {
        this.productsScrollTop = 0; // 再设置为0，确保滚动到顶部
      }, 50);
    });
    let status = uni.getStorageSync('pronunciationEnable');
    let token = uni.getStorageSync('token');
    if (token) {
      getNotReadNews.getNotReadNews();
      let _this = this;
      this.member = true;
      await this.homeData();
      if (!status) {
        this.$refs.pronunciationPopup.open();
        this.userCode = uni.getStorageSync('userCode');
        console.log(this.userCode);
        let data = this.userCode + 'L0anhf';
        this.sg = CryptoJS.SHA1(data).toString();
        console.log(this.sg, '加密结果');
      }
      let that = this;
      if (!innerAudioContext) {
        innerAudioContext = uni.createInnerAudioContext();
        innerAudioContext.onPlay(() => {
          console.log('开始播放');
        });
        innerAudioContext.onStop(function () {
          console.log('播放结束');
          that.isplay = false;
        });
        innerAudioContext.onPause(function () {
          console.log('播放暂停');
          that.isplay = false;
        });
        innerAudioContext.onError((res) => {
          console.log(res.errMsg);
          console.log(res.errCode);
          that.isplay = false;
        });
      }
    } else {
      this.CourseTips = false;
    }
  },
  onHide() {
    this.leftHeight = 0;
    this.rightHeight = 0;
  },
  onShareAppMessage(res) {
    // let userId = uni.getStorageSync('user_id');
    let url =
      `/pages/beingShared/index?scene=${uni.getStorageSync('user_id')}&type=${this.shareContent.type}&id=${this.shareContent.id}`;
    console.log(url);
    if (res.from == 'menu') {
      return {
        title: '叮，你的好友敲了你一下，赶紧过来看看',
        path: `/pages/index/index`
      };
    } else {
      setTimeout(() => {
        this.$refs.sharePopup.close();
      }, 2000);
      if (!uni.getStorageSync('token')) {
        return {
          title: this.shareContent.title || '叮，你的好友敲了你一下，赶紧过来看看',
          imageUrl: this.shareContent.imgurl,
          // imageUrl: "https://document.dxznjy.com/dxSelect/superman_share.jpg",
          path: `Coursedetails/productDetils?id=${this.shareContent.id}`
        };
      }
      return {
        title: this.shareContent.title || '叮，你的好友敲了你一下，赶紧过来看看',
        imageUrl: this.shareContent.imgurl,
        // imageUrl: "https://document.dxznjy.com/dxSelect/superman_share.jpg",
        path: `/pages/beingShared/index?scene=${uni.getStorageSync('user_id')}&type=${this.shareContent.type}&id=${this.shareContent.id}`
      };
    }
  },
  onReachBottom() {
    if (this.page >= Number(this.infoLists.totalPage)) {
      this.no_more = true;
      return false;
    }
    // this.getProduct(this.categoryId, true, ++this.page);
  },
  mounted() {
    // this.openPopup() // 打开体验劵弹窗
    const query = uni
      .createSelectorQuery()
      .select('#scrool_height')
      .boundingClientRect((data) => {
        this.scrollHeight = data.height;
      })
      .exec();
    // 监听窗口变化，重新计算位置
    // window.addEventListener('resize', this.calculatePositions);
  },
  // beforeDestroy() {
  //   window.removeEventListener('resize', this.calculatePositions);
  // },
  methods: {
    //关于鼎校跳转
    aboutClick(url) {
      getApp().sensors.track('indexLearnMoreClick', {
        name: '为什么选择鼎校'
      });
      $navigationTo(url);
    },
    //横向一个时的点击事件
    singleSkinTap(url) {
      let goodsId = url.split('=')[1];
      getApp().sensors.track('indexProductDetailClick', {
        goodsId: goodsId,
        name: '横向'
      });
      $navigationTo(url);
    },
    calculateNavBarHeight() {
      // 1. 获取状态栏高度
      const systemInfo = uni.getSystemInfoSync();
      this.statusBarHeight = systemInfo.statusBarHeight;

      // 2. 获取菜单按钮（胶囊）的布局位置信息
      const menuButtonInfo = wx.getMenuButtonBoundingClientRect();

      // 3. 计算标题栏高度：胶囊高度 + (胶囊top - 状态栏高度) * 2
      // 标题栏高度是胶囊按钮所在区域的高度
      this.titleBarHeight = menuButtonInfo.height + (menuButtonInfo.top - this.statusBarHeight) * 2;

      // 4. （可选）计算整个导航栏的总高度（状态栏高度 + 标题栏高度）
      this.navBarHeight = this.statusBarHeight + this.titleBarHeight;

      console.log("状态栏高度:", this.statusBarHeight, "px");
      console.log("标题栏高度:", this.titleBarHeight, "px");
      console.log("整个导航栏高度:", this.navBarHeight, "px");
    },
    //商品封面点击事件
    productShowcase(url, goodsItem) {
      //埋点
      getApp().sensors.track('productShowcaseClick', {
        goods_id: goodsItem.goodsId
      });
      $navigationTo(url);
    },
    calculatePositions() {
      let _this = this;
      _this.sectionPositions = [];
      // 获取scroll-view容器的位置
      uni.createSelectorQuery()
        .select('.scroll-box')
        .boundingClientRect((containerRect) => {
          if (!containerRect) return;
          let promises = [];
          for (let i = 0; i < _this.categoryGoodsList.length; i++) {
            promises.push(new Promise((resolve) => {
              uni.createSelectorQuery()
                .select(`#section-${i}`)
                .boundingClientRect((data) => {
                  if (data) {
                    // 计算相对于容器顶部的距离
                    var top = data.top - containerRect.top;
                    if (this.scrollTop > 0) {
                      top += this.scrollTop
                    }
                    _this.sectionPositions.push({
                      index: i,
                      top: top,
                      bottom: top + data.height
                    });
                  }
                  resolve();
                })
                .exec();
            }));
          }
          Promise.all(promises).then(() => {
            _this.sectionPositions.sort((a, b) => a.top - b.top);
          });
        })
        .exec();
    },
    // 处理扫码进入小程序--绑定合伙人
    async handleScanBind(qrId, merchantCode, merchantPhone) {
      let scanInfo = {
        qrId,
        merchantCode,
        merchantPhone
      };
      uni.setStorageSync('scanInfo', JSON.stringify(scanInfo));
      let token = uni.getStorageSync('token');
      if (token) {
        let params = {
          id: qrId,
          merchantCode: merchantCode
        };
        let bindRes = await bindMerchantByScan(params);
        if (bindRes == 'success') {
          uni.removeStorageSync('scanInfo');
        }
      }
    },
    scrolltolower() {
      if (this.page >= Number(this.infoLists.totalPage)) {
        this.no_more = true;
        return false;
      }
      // this.getProduct(this.categoryId, true, ++this.page);
    },
    startNow() {
      this.$refs.notifyPopup.open();
    },
    audition() {
      // uni.setStorageSync('pronunciationEnable', true);
      this.$refs.auditionPopop.open();
    },

    closePronunciation() {
      this.$refs.pronunciationPopup.close();
      uni.setStorageSync('pronunciationEnable', true);
    },
    changePlay(val) {
      this.playSatus = val;
      this.sayWord();
    },
    // 竞品
    Competitors() {
      let that = this;
      let linkUrl = 'https://document.dxznjy.com/new/audio/oth/' + encodeURIComponent(that.words) + '.mp3';
      innerAudioContext.obeyMuteSwitch = false;
      innerAudioContext.src = linkUrl;
      innerAudioContext.play();
    },
    // 随机单词
    randomWords() {
      let that = this;
      const randomIndex = Math.floor(Math.random() * that.otherWord.length);
      that.words = that.otherWord[randomIndex];
    },

    closeAudition() {
      this.$refs.auditionPopop.close();
      uni.setStorageSync('pronunciationEnable', true);
      this.words = 'strawberry';
    },

    // 获取上传状态
    select(e) {
      console.log('选择文件：', e);
    },

    // 试听
    sayWord() {
      var that = this;
      that.$httpUser
        .get('znyy/app/query/word/voice', {
          word: that.words,
          v: that.playSatus,
          rp: false,
          sex: 'W',
          sg: that.sg
        })
        .then((res) => {
          if (res.data.success) {
            // var w = encodeURIComponent(res.data.data);
            let voiceUrl;
            let url;
            if (that.playSatus == 1) {
              voiceUrl = 'https://document.dxznjy.com/' + encodeURIComponent(res.data.data);
              that.linkUrl = voiceUrl;
            } else {
              voiceUrl = res.data.data;
              // url = client.signatureUrl(voiceUrl, {
              // 	expires: Date.now() + 3600 * 1000, // URL的有效期，单位是秒
              // 	process: 'audio/auto',
              // });
              that.linkUrl = voiceUrl;
            }
            innerAudioContext.obeyMuteSwitch = false;
            innerAudioContext.src = that.linkUrl;
            innerAudioContext.play();
          } else {
            that.$util.alter(res.data.message);
          }
        });
    },

    savePronunciation() {
      this.$refs.auditionPopop.close();
      uni.setStorageSync('pronunciationEnable', true);
      this.words = 'strawberry';
    },

    // 会员专享分享
    shareVip(type, item) {
      // if (!uni.getStorageSync('token')) {
      //   uni.navigateTo({
      //     url: '/Personalcenter/login/login'
      //   });
      //   return;
      // }
      this.shareContent.type = type;
      let id = '',
        imgurl = '';
      //type 1课程  2学习超人（会员） 3超人俱乐部
      if (type == '1') {
        id = item.courseId;
        imgurl = item.courseImage;
      } else {
        id = item.mealId;
        type == '2' ? (imgurl = Config.supermanShareImage) : (imgurl = Config.supermanClubShareImage);
      }
      this.shareContent.id = item.goodsId;
      if (type != 6) {
        this.shareContent.imgurl = imgurl;
      } else {
        this.shareContent.imgurl = item.goodsSharePoster;
        this.shareContent.title = item.goodsShareTextList[0] ? item.goodsShareTextList[0].shareText : null;
      }
      console.log(this.shareContent, '分享内容组装------------')
      //
      this.$refs.sharePopupRefs.open(this.shareContent);
    },
    tabsClick(item) {
      console.log('hhhhhhhhhhhhhhhhhhhhhhrrrrrrrrrrrrrrrrrrrrrrrrr')
      //埋点
      getApp().sensors.track('indexTabsHandleClick', {
        name: item.moduleName,
        id: item.id
      });
      const index = item.index;
      if (this.sectionPositions.length <= index) {
        return;
      }
      const currentScrollTop = this.productsScrollTop;
      var targetTop = this.sectionPositions[index].top - this.showScrollTabHeight + 110;

      // 先设置为当前值，确保下次设置时会触发滚动
      this.productsScrollTop = currentScrollTop;
      this.$nextTick(() => {
        this.productsScrollTop = targetTop;
      });
    },

    searchFocus() {
      getApp().sensors.track('indexSearchHandleClick', {
        name: '首页搜索'
      });
      uni.navigateTo({
        url: '/interestModule/searchPage'
      });
    },
    // 鼎英语体验劵弹窗
    // openPopup() {
    // 	this.$refs.popopSecurities.open()
    // },
    close() {
      this.$refs.popopSecurities.close();
    },
    // 禁止滚动穿透
    change(e) {
      this.rollShow = e.show;
    },

    // scroll-view 滑动
    upper: function (e) {
      console.log(e);
    },
    lower: function (e) {
      console.log(e);
    },
    scroll: function (e) {
      console.log(e);
      this.old.scrollTop = e.detail.scrollTop;
    },
    is6Num(str) {
      let x = /^[0-9]{6,6}$/;
      if (x.test(str)) {
        return true;
      }
      return false;
    },
    closeCode() {
      this.codeForm = {
        qrCode: '',
        studentName: ''
      };
      // this.smsCode = '';
      this.codeShow = false;
      // if (this.codeBtn.waitingCode) {
      //   return;
      // } else {
      //   this.codeShow = false;
      // }
    },
    // 保存图片
    saveCode() {
      let that = this;
      uni.downloadFile({
        url: that.codeForm.qrCode, //仅为示例，并非真实的资源
        success: (res) => {
          if (res.statusCode === 200) {
            console.log('下载成功');
            uni.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: function () {
                uni.showToast({
                  title: '保存成功',
                  duration: 2000
                });
              },
              fail: function () {
                uni.showToast({
                  title: '保存失败',
                  duration: 2000,
                  icon: 'none'
                });
              }
            });
          }
        },
        fail: (err) => {
          console.log(err, '下载失败');
        }
      });
    },
    openDialog() {
      this.codeShow = true;
    },
    // 时间判断
    checkNextTime(time) {
      if (time != undefined) {
        let datetime = time;
        let nowTime = Date.now();
        let setTime = dayjs(datetime).unix() * 1000;
        if (this.isNextback == false) {
          return nowTime < setTime;
        }
      }
    },
    async homeData() {
      let _this = this;
      const res = await $http({
        url: 'zx/user/userInfoNew'
      });
      if (res) {
        _this.pageShow = false;
        _this.userinfo = res.data;
        if (_this.userinfo.expireTime != '') {
          //到期时间与当前时间作对比
          const time = dayjs().format('YYYY-MM-DD');
          if (_this.userinfo.expireTime < time) {
            _this.supermanShow = true;
          }
        } else if (_this.userinfo.identityType == 0 || _this.userinfo.identityType == 2) {
          _this.supermanShow = true;
        }
        // uni.setStorageSync("user_id", res.data.userId);
        uni.setStorageSync('identityType', res.data.identityType);
        uni.setStorageSync('phone', res.data.mobile);
        uni.setStorageSync('userCode', res.data.userCode);
        uni.setStorageSync('log_userCode', res.data.userCode);
      } else {
        _this.exhibition = false;
      }
    },
    async getPayment() {
      let res = await this.$httpUser.get('deliver/app/parent/getPlanCourseInfo');
      if (res.data.success) {
        if (res.data.data != null) {
          this.orderShow = true;
          this.planId = res.data.data.planId;
        } else {
          this.orderShow = false;
        }
      }
    },
    goTable() {
      uni.navigateTo({
        url: '/Personalcenter/index/index?planId=' + this.planId
      });
    },
    // 点击趣味复习列表
    gotoInterest() {
      let that = this;
      that.interestSelect = true;
      // 选择校区弹窗
      if (that.arrayStudent.length == 0) {
        uni.showToast({
          title: '您还没有学员或没有正式学员哦',
          icon: 'none',
          duration: 3000
        });
        return;
      }
      this.$refs.popopChooseStudent.open();
    },
    // 获取学员列表
    async getStudent() {
      let that = this;
      let loginMobile = uni.getStorageSync('LoginMobile');
      that.StudentCodeKey = 'review_' + loginMobile;
      let result = await httpUser.get('znyy/review/query/my/student');
      if (result != undefined && result != '') {
        if (result.data.data != null) {
          that.arrayStudent = result.data.data;
          console.log(that.arrayStudent);
        }
      }
    },

    //点击选择学员
    chooseStudentlist(item, index) {
      this.isactive = index;
      this.studentCode = item.studentCode;
    },
    // 选择学员确定
    async confirmStudent() {
      let that = this;
      if (!this.interestSelect) {
        that.getPadToken();
        this.closeDialog();
        return;
      }
      uni.showLoading();
      httpUser.get('v2/mall/getStudentMerchantList?studentCode=' + that.studentCode).then((result) => {
        uni.hideLoading();
        if (result.data.success) {
          that.$refs.popopChooseStudent.close();
          // 没有购买去购买
          if (result.data.code == 20004) {
            setTimeout(function () {
              that.$refs.popopPower.open();
            }, 500);
            return;
          }
          if (result.data.success) {
            that.$refs.popopChooseStudent.close();
            that.arraySchool = result.data.data;
            // uni.setStorageSync('merchantCode', that.arraySchool[0].merchantCode)
            if (result.data.data == null) {
              that.$util.alter('您还没有学习过课程');
            }
            if (result.data.data.length == 1 && result.data.data != null) {
              that.merchantCode = that.arraySchool[0].merchantCode;
              that.getPadToken();
            } else if (result.data.data.length > 1 && result.data.data != null) {
              that.$refs.popopChooseSchool.open();
            }
          }
        }
      });
    },
    //点击选择校区
    chooseSchoollist(item, index) {
      console.log('点击选择校区');
      this.merchantCode = item.merchantCode;
      this.isactive = index;
    },
    // 选择校区确定
    confirmSchool() {
      this.closeDialog();
      this.getPadToken();
    },

    //立即购买趣味复习
    nowBuy() {
      let code = this.studentCode;
      console.log('立即购买趣味复习');
      this.closeDialog();
      uni.navigateTo({
        url: `/Personalcenter/interest/orderDetail?studentCode=${code}`
      });
    },
    //关闭弹窗
    closeDialog() {
      this.isactive = -1;
      this.studentCode = '';
      this.merchantCode = '';
      this.$refs.popopChooseStudent.close();
      this.$refs.popopChooseSchool.close();
      this.$refs.popopPower.close();

      // // 分享
      // this.$refs.sharePopup.close();
      // this.shareContent = {};
    },
    // 获取pad token
    getPadToken(scheduleCode) {
      let that = this;
      var logintoken = uni.getStorageSync('token');
      console.log(logintoken);
      uni.showLoading();
      httpUser.get(
        `new/security/v2/login/student/member/token?memberToken=${logintoken}&studentCode=${that.studentCode}&merchantCode=${that.merchantCode}`
      ).then((res) => {
        uni.hideLoading();
        if (res.data.success) {
          uni.setStorageSync('logintokenReview', res.data.data.token);
          that.isactive = -1;
          if (!that.interestSelect) {
            uni.navigateTo({
              url: `/antiAmnesia/review/studyFunReview?studentCode=${that.studentCode}&merchantCod{that.merchantCode}`
            });
          } else {
            uni.navigateTo({
              url: `/antiAmnesia/review/funReview?studentCode=${that.studentCode}&merchantCode=${that.merchantCode}`
            });
          }
        } else {
          that.$util.alter(res.data.message);
        }
      });
    },
    // banner下列表点击
    openParentVip() {
      let token = uni.getStorageSync('token');
      //埋点
      getApp().sensors.track('superVIPClick', {
        name: '超级会员'
      });
      // $navigationTo('Personalcenter/my/parentVipEquity');
      if (this.userinfo && this.userinfo.parentMemberType == 5) {
        //家长会员查看权益
        $navigationTo('Coursedetails/my/parentEquity');
      } else {
        if (token) {
          $navigationTo('Personalcenter/my/parentVipEquity?expire=1');
        } else {
          $navigationTo('Personalcenter/my/parentVipEquity');
        }
      }
    },
    //首页轮播banner图/查看更多跳转：0表示点击banner图跳转；1表示点击查看更多跳转
    openspecial(item, type) {
      //埋点
      if (type === 0) {
        getApp().sensors.track('specialBannerClick', {
          name: item.specialName,
        });
      } else if (type === 1) {
        getApp().sensors.track('productMoreClick', {

        });
      }
      $navigationTo('specialTitle/special?specialId=' + item.specialId + '&specialName=' + item.specialName)
      // $navigationTo('learnMore/learnmore')
    },
    //首页下方商品分类查看更多
    openspecials(item) {
      // $navigationTo('specialTitle/special')
      //埋点
      getApp().sensors.track('indexLearnMoreClick', {
        name: item.moduleName,
      });
      $navigationTo('learnMore/learnmore?categoryId=' + item.id)
    },
    goUrl(type, tab) {
      let that = this;
      let token = uni.getStorageSync('token');
      // uni.navigateTo({
      // 	url:"/supermanClub/supermanSign/purchaseCodesList"
      // })
      if (token) {
        // that.skintap(`pages/superman/superman?type=${type}`);
        if (type == '2') {
          // 不是学习超人跳转学习超人介绍页
          if (that.userinfo.identityType != 1 && that.userinfo.identityType != 3) {
            that.skintap(`supermanClub/superman/superman?type=${type}`);
          } else {
            //是学习超人跳转个人中心学习超人tab)
            uni.setStorageSync('tab', 1);
            setTimeout(() => {
              uni.switchTab({
                url: '/pages/home/<USER>/index'
              });
            }, 500);
          }
        } else if (type == 5) {
          //埋点
          getApp().sensors.track('superVIPClick', {
            name: '超级会员'
          });
          // $navigationTo('Personalcenter/my/parentVipEquity');
          if (that.userinfo.parentMemberType == 5) {
            //家长会员查看权益
            $navigationTo('Coursedetails/my/parentEquity');
          } else {
            $navigationTo('Personalcenter/my/parentVipEquity?expire=1');
          }
          // if (that.userinfo.identityType != 4) {
          //   // 非会员跳转
          //   //2024-11-6 紧急修改 购买超级会员修改成购买家长会员
          //   // $navigationTo('Personalcenter/my/nomyEquity');
          //   $navigationTo('Personalcenter/my/parentVipEquity');
          // } else {
          //   // 会员跳转
          //   $navigationTo('Coursedetails/my/myEquity');
          // }
        } else {
          // 不是超人俱乐部
          if (that.userinfo.identityType != 2 && that.userinfo.identityType != 3) {
            that.skintap(`supermanClub/superman/superman?type=${type}`);
          } else {
            //是学习超人跳转个人中心学习超人tab
            uni.setStorageSync('tab', 2);
            setTimeout(() => {
              uni.switchTab({
                url: '/pages/home/<USER>/index'
              });
            }, 500);
          }
          //是超人俱乐部跳转个人中心超人俱乐部tab
        }
      } else {
        uni.navigateTo({
          url: `/Personalcenter/login/login`
        });
      }
    },
    cancel() {
      if (!uni.getStorageSync('city')) {
        uni.setStorageSync('city', this.newCategotyDataList[1][0]);
        this.city = this.newCategotyDataList[1][0];
        this.show = false;
      } else {
        this.show = false;
      }
    },
    async getCity() {
      let _this = this;
      const res = await $http({
        url: 'zx/setting/areaList',
        data: {}
      });
      if (res) {
        let list = res.data;
        _this.categoryArr = res.data;
        for (let i = 0; i < list.length; i++) {
          let obj = {
            name: list[i].provinceName,
            value: list[i].provinceCode
          };
          this.newCategotyDataList[0].push(obj);
        }
        for (let i = 0; i < list[0].cityVoList.length; i++) {
          let obj = {
            name: list[0].cityVoList[i].cityName,
            value: list[0].cityVoList[i].cityCode
          };
          this.newCategotyDataList[1].push(obj);
        }
      }
    },
    confirm(e) {
      console.log('confirm', e);
      this.city = e.value[1];
      this.show = false;
      uni.setStorageSync('city', this.city);
    },
    changeHandler(e) {
      const {
        columnIndex,
        value,
        values, // values为当前变化列的数组内容
        index,
        // 微信小程序无法将picker实例传出来，只能通过ref操作
        picker = this.$refs.uPicker
      } = e;
      if (columnIndex === 0) {
        this.newCategotyDataList[1] = this.categoryArr[index].cityVoList.map((item, index) => {
          return {
            name: item.cityName,
            value: item.cityCode
          };
        });
        picker.setColumnValues(1, this.newCategotyDataList[1]);
      }
    },
    async getBanner() {
      const res = await $http({
        url: 'zx/wap/homePage/banner/list'
      });
      if (res) {
        // console.log(res)
        // console.log('kkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk')
        this.bannerList = res.data;
      }
    },
    async getPorcelain() {

      const res = await $http({
        url: 'zx/wap/homePage/porcelain'
      });
      if (res) {
        // console.log(res)
        // console.log('kkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk')
        this.porcelainInfo = res.data;
      }
    },
    async getCategoryGoods() {
      uni.showLoading();
      this.categoryGoodsList = []
      const res = await $http({
        url: 'zx/wap/homePage/categoryGoods/list',
        data: {
          placement: 1
        }
      });
      if (res) {
        this.categoryGoodsList = res.data;
        this.$nextTick(() => {
          setTimeout(() => {
            this.calculatePositions();
          }, 500); // 适当延迟，确保DOM渲染完成
        });
      }
    },
    async getSpecialList() {
      const res = await $http({
        url: 'zx/wap/homePage/special/list',
        data: {
          placement: 1
        }
      });
      if (res) {
        // console.log(res)
        // console.log('kkkkkkkkkkkkskkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk')
        this.specialList = res.data;
      }
    },
    // /wap/homePage/special/list
    async getGroupList() {
      const res = await $http({
        url: 'zx/wap/layout/banner/list',
        data: {
          // 首页中段 拼团专属
          bannerPosition: 10
        }
      });
      if (res) {
        this.groupList = res.data;
      }
    },
    async getFloatingWindow() {
      this.isGroupGiftClosed = false
      const res = await $http({
        url: 'zx/floatWindow/list',
        data: {
          pageSize: 10,
          pageNum: 1,
          status: 1,
        }
      });
      if (res) {
        if (res.data.data.length > 0) {
          this.isGroupGiftClosed = true
          this.floatWindow = res.data.data[0];
        }
      }
    },
    async getPopup(id, userId) {
      let _this = this;
      let result = await $http({
        url: 'zx/wap/layout/popup/last/time/modify?popupId=' + id + '&userId=' + userId,
        // data: { popupId:id,userId:userId },
        method: 'POST'
      });
      if (result) {
        console.log(result);
      }
    },
    async meal() {
      let _this = this;
      const res = await $http({
        url: 'zx/meal/mealList',
        data: {
          indexShow: 0,
          cityCode: '',
          page: 1,
          pageSize: 20
        }
      });
      if (res) {
        _this.mealLists = res.data.list;
      }
    },

    // 监听高度变化
    onHeight(height, tag) {
      if (tag == 'left') {
        this.leftHeight += height;
      } else {
        this.rightHeight += height;
      }
    },
    skintap(url) {
      if (url.includes('pages/selectCourse/selectCourse')) {
        // $navigationTo(url);
        uni.switchTab({
          url: '/pages/selectCourse/selectCourse',
        })
        return
      }
      if (
        !uni.getStorageSync('token') &&
        !url.includes('Coursedetails/productDetils') &&
        !url.includes('shoppingMall/index') &&
        !url.includes('meeting/meetIndex') &&
        !url.includes('memberCenter/index')
      ) {
        uni.navigateTo({
          url: '/Personalcenter/login/login'
        });
      } else {
        if (url == 'partnerApplication/index') {
          getApp().sensors.track('partnerApplicationClick', {
            name: '超级合伙人'
          });
        } else if (url == 'supermanClub/clubManagement/clubUpgrade') {
          getApp().sensors.track('supermanClubClick', {
            name: '超级俱乐部'
          });
        } else if (url.includes('Coursedetails/productDetils')) {
          //课程/商品
          console.log(url);
          let goodsId = url.split('=')[1];
          getApp().sensors.track('indexProductDetailClick', {
            goodsId: goodsId
          });
        } else if (url.includes('shoppingMall/index')) {
          getApp().sensors.track('shoppingMallClick', {
            name: '鼎币商城'
          });
        } else if (url.includes('meeting/meetIndex')) {
          getApp().sensors.track('meetingCenterClick', {
            name: '会议中心'
          });
        } else if (url.includes('memberCenter/index')) {
          getApp().sensors.track('memberCultureCenterClick', {
            name: '文化中心'
          });
        } else if (url.includes('pages/selectCourse/selectCourse')) {
          getApp().sensors.track('memberCultureCenterClick', {
            name: '甄选好课'
          });
        }
        $navigationTo(url);
      }
    },

    bannerTab(item) {
      getApp().sensors.track('indexBannerClick', {
        bannerId: item.id,
        name: item.bannerName,
        bannerType: item.bannerType
      });
      let a = uni.getStorageSync('identityType');
      console.log(a);
      if (item.needLogin == 0 && !uni.getStorageSync('token')) {
        uni.navigateTo({
          url: '/Personalcenter/login/login'
        });
      } else {
        if (item.goodsId) {
          $navigationTo(`Coursedetails/productDetils?id=${item.goodsId}&bannerId=${item.id}`);
        } else {
          if (item.bannerLinkUrl == 'Personalcenter/my/nomyEquity' && a == 4) {
            $navigationTo('Coursedetails/my/myEquity');
          } else {
            // bannerId埋点上报
            if (item.bannerLinkUrl.includes('?')) {
              $navigationTo(item.bannerLinkUrl + '&bannerId=' + item.id);
            } else {
              $navigationTo(item.bannerLinkUrl + '?bannerId=' + item.id);
            }
            // $navigationTo(item.bannerLinkUrl);
          }
        }
      }
    },
    onImageLoad() {
      console.log('111111');
      getApp().sensors.track('eventExposure', {
        name: '活动曝光'
      });
    },
    handlerGroupBuyClick(item) {
      //埋点
      getApp().sensors.track('groupurchasing', {
        name: '拼团'
      });
      if (item.needLogin == 0 && !uni.getStorageSync('token')) {
        uni.navigateTo({
          url: '/Personalcenter/login/login'
        });
      } else {
        // activity/groupBuying/index
        if (item.bannerLinkUrl) {
          $navigationTo(item.bannerLinkUrl);
        }
      }
    },
    scrollPos({
      detail
    }) {
      // if (this.scrollTimer) {
      //   clearTimeout(this.scrollTimer);
      // }
      if (detail.scrollTop > this.scrollTop) {
        if (detail.scrollTop > this.scrollHeight) {
          if (!this.showScroll) {
            this.tabsCurrent = 0;
            this.showScroll = true;
          }
          if (!this.showScrollTabHeight) {
            uni.createSelectorQuery()
              .select('#showScrollTab')
              .boundingClientRect((data) => {
                if (data) {
                  this.showScrollTabHeight = data.height - 110;
                }
              })
              .exec();
          }
        }
      } else {
        if (this.showScroll) {
          if (this.scrollTop < this.scrollHeight + 250) {
            this.showScroll = false
          }
        }
      }
      this.scrollTop = detail.scrollTop;
      // 显示固定的Tab栏


      // 计算当前应该高亮的Tab
      let currentTab = 0;
      for (let i = 0; i < this.sectionPositions.length; i++) {
        // 这里可以考虑增加一个偏移量，比如提前100rpx切换Tab，改善用户体验
        if (this.scrollTop >= this.sectionPositions[i].top - this.showScrollTabHeight) {
          currentTab = i;
        } else {
          break;
        }
      }
      this.tabsCurrent = currentTab;
      // }, 5);
    },
    bindscrolltoupper() {
      this.showScroll = false;
    },
    onRefresh() {
      console.log('yyyyyyyyyyyyyyyyytttttttttttttttttttttttttttttttt')
      let token = uni.getStorageSync('token');
      this.meal();
      if (token) {
        // this.$refs.mycurriculum.getSubject();
      }
      setTimeout(() => {
        this.refreshing = false;
        uni.stopPullDownRefresh();
      }, 1000);
    },
    changeCurrent(val) {
      console.log(val);
      this.currentIndex = val.detail.current;
    },
    changeBannerCurrent(val) {
      // console.log(val);
      this.bannerIndex = val.detail.current;
    },
    changeGrouPCurrent(val) {
      this.groupIndex = val.detail.current;
    },
    // 社群有礼
    openGroupGift() {
      if (this.floatWindow.insideType == 2) {
        $navigationTo('InvitationGifts/invitation');
        // uni.setStorageSync('invitationContent', this.floatWindow.content);
        wx.setStorageSync('invitationContent', this.floatWindow.content);
      } else if (this.floatWindow.insideType == 1) {
        $navigationTo(`Coursedetails/productDetils?id=${this.floatWindow.goodsId}`);
      } else if (this.floatWindow.insideType == 3) {
        $navigationTo(`activity/index?activityId=${this.floatWindow.activityId}`);
      }
      getApp().sensors.track('indexSmallPopup', {
        activityId: this.floatWindow.id
      });
    },
    closeGroupGift() {
      this.isGroupGiftClosed = false;
      uni.setStorageSync('isGroupGiftClosed', true); // 保存关闭状态到本地存储
    }
  }
};
</script>
<style lang="scss" scoped>
.orderinfo {
  position: relative;
  width: 630rpx;
  height: 620rpx;
  font-size: 30rpx;
  border-radius: 24rpx;
  padding-bottom: 50rpx;
  background-color: #fff;
  font-size: 28rpx;

  .color {
    color: #666666;
  }

  .btns {
    display: flex;
    justify-content: center;
    margin-top: 40rpx;
  }

  .btn {
    width: 250rpx;
    height: 80rpx;
    border-radius: 45rpx;
    line-height: 80rpx;
    text-align: center;
    font-size: 30rpx;
  }

  .btn1 {
    color: #64a795;
    background-color: #ffffff;
    border: 1px solid #64a795;
    margin-right: 40rpx;
    // margin-left: 40rpx;
  }

  .btn2 {
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    color: #fff;
    // border: 1px solid transparent;
  }

  .closeBox {
    display: flex;
    position: absolute;
    top: 20rpx;
    right: 20rpx;
  }

  .seconds {
    margin-right: 20rpx;
  }
}

.flex-title {
  font-size: 28rpx !important;
}

.indextop {
  position: relative;
  // width: 690upx;
  margin: 0 auto;

  .index-top-search {
    position: absolute;
    left: 28rpx;
    z-index: 9999;
  }

  .logoimg {
    width: 128rpx;
    height: 54rpx;
  }

  .banner-image {
    width: 688rpx;
    height: 386rpx;
    margin-top: 40rpx;
  }

  .goods-image {
    width: 312rpx;
    height: 158rpx;
    overflow: hidden;
    z-index: 999;
    position: absolute;
    top: 0;
    left: 0;
  }

  .more-btn {
    position: absolute;
    right: 0;
    z-index: 999999;
    width: 44rpx;
    text-orientation: upright;
    padding: 0 10rpx;
    color: #fff;
    text-align: center;
    background-color: rgba(0, 0, 0, 0.4);
    border-radius: 8rpx 0rpx 0rpx 8rpx;
  }

  .goods-price-content {
    width: 312rpx;
    height: 98rpx;
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(0, 0, 0, 0.55) 100%);
    border-radius: 0rpx 0rpx 8rpx 8rpx;
    position: absolute;
    bottom: 0rpx;
    left: 0rpx;
    z-index: 9999999;
  }

  .goods-item {
    position: relative;
    width: 312rpx;
    height: 158rpx;

    .goods-price {
      color: #fff;
      z-index: 99999991;
      position: absolute;
      bottom: 12rpx;
      left: 14rpx;

      .price-span {
        display: inline-block;
        margin-right: 5rpx;
      }
    }
  }

  .index-swiper {
    width: 750rpx;
    position: relative;
  }
}

.top-image-border {
  position: relative;
  width: 750rpx;
}

.cover-css {
  width: 750rpx;
  height: 822rpx;
  position: absolute;
  top: 0rpx;
  left: 0;
  z-index: 999;
}

.background-line {
  width: 750rpx;
  height: 822rpx;

  .background-image {
    position: absolute;
    width: 690rpx;
    height: 400rpx;
    left: 30rpx;
    top: 210rpx;
    filter: blur(13px);
    /* 建议先尝试将rpx转换为px */
    -webkit-filter: blur(13px);
    /* 针对iOS WebKit的内核 */
    background-color: rgba(255, 255, 255, 0.5);
    /* 半透明背景 */
    -webkit-backdrop-filter: blur(10px);
    /* 为iOS WebKit添加前缀 */
    backdrop-filter: blur(10px);
    /* 标准属性 */
    /* 缩放变换 */
    transform: scale(1.05);
    -webkit-transform: scale(1.05);
    /* 同样为transform添加webkit前缀以确保兼容性 */
    z-index: 999;
    /* 通常还会降低亮度使前景内容更清晰 */
    /* filter: blur(20rpx) brightness(0.8); */
  }

  // background: linear-gradient(to bottom, #4CAF50, #FFFFFF, #4CAF50);
}

.radius-image-css {
  border-radius: 0rpx 0rpx 20rpx 20rpx;
}

.group-buy {
  .index-swiper {
    height: 204rpx;
    position: relative;
  }
}

.h-flex-x {
  width: 686rpx;
}

.scroll-box {
  height: 100vh;
}

.display_inline {
  font-size: 22rpx;
  font-weight: bold;
  display: inline-block;
  color: #9595ae;
}

.course_scroll_top {
  // height: calc(100vh - 70rpx);
}

#scrool_height {
  position: relative;
}

.background-css {
  position: absolute;
  top: 80rpx;
  right: 0;
  z-index: 9999;

  // .search-height {
  //   height: 80rpx;
  // }
}

/* 搜索框 */
.search-css {
  display: flex;
  border-radius: 40rpx;
  background-color: rgba(255, 255, 255, 0.47);
  width: 490rpx;

  .search-image {
    margin: 0 10rpx;
    vertical-align: middle;
  }

  .search-input {
    width: 600upx;
    display: inline-block;
    height: 80rpx;
    line-height: 80rpx;
    vertical-align: middle;
    margin-left: 16rpx;
  }
}

.search-css-sticky {
  background-color: #f9fafa;

  .search-input-sticky {
    color: #bdbdbd;
  }
}

.banner_indicator_style {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  position: absolute;
  bottom: 20rpx;
  right: 20rpx;

  .indicator_style_css {
    width: 32rpx;
    height: 8rpx;
    border-radius: 4rpx;
    background-color: #fff;
    margin-left: 8rpx;
  }

  .active_current {
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
  }
}

.order_notice {
  display: flex;
  justify-content: space-between;
  background-color: #fff;
  padding: 20rpx 30rpx;
  border: 1px solid #f48f6f;
  border-radius: 14rpx;
  margin-top: 20rpx;
}

.product_content {
  height: 130rpx;
  padding-bottom: 32rpx;

  .boxh-48 {
    height: 72rpx;
  }

  .product {
  }

  .indicator_style {
    width: 48rpx;
    height: 8rpx;
    border-radius: 4rpx;
    background-color: #fae1c5;
    margin: 40rpx auto;
    position: relative;

    .active_current {
      width: 24rpx;
      height: 8rpx;
      border-radius: 4rpx;
      background-color: #fd9b2a;
      display: inline-block;
      position: absolute;
      top: 0;
    }

    .active_current_left {
      left: 0;
    }

    .active_current_right {
      right: 0;
    }
  }
}

.box-50 {
  width: 50rpx;
  height: 50rpx;
}

/* 我的课程 */
.course {
  display: flex;
  justify-content: space-between;
  font-size: 30rpx;
  font-weight: 700;
  margin-top: 30rpx;
}

.class_list {
  font-size: 30rpx;
  color: #333;
}

.subject {
  width: 100%;
  margin: 20rpx auto;
  border-radius: 20rpx;

  .subcard {
    background-color: #fff;
    border-radius: 24rpx;
    padding: 30rpx 30rpx 40rpx;
  }

  .title {
    display: flex;
    justify-content: space-between;
    padding-bottom: 20rpx;
    border-bottom: 1px solid #eee;
  }

  .title-one {
    color: #000;
    font-size: 30rpx;
    font-weight: 700;
  }

  .title-two {
    color: #999;
    font-size: 30rpx;
  }

  .card-content {
    position: relative;
    display: flex;
    justify-content: space-between;
    padding-top: 33rpx;
  }

  .content-one {
    font-size: 30rpx;
    color: #000;
    // margin-bottom: 30rpx;
  }

  /deep/.card-btn {
    position: absolute !important;
    right: 0;
    top: 40rpx;
    width: 150rpx;
    height: 60rpx;
    line-height: 58rpx;
    margin: 0;
    border-radius: 30rpx;
    font-size: 28rpx;
    padding-left: 14rpx;
    padding-right: 14rpx;
    color: #f64a50;
    border: 1rpx solid #f5875d;
  }

  .m-b {
    margin-bottom: 20rpx;
  }
}

.tips {
  position: relative;
  margin-top: 20rpx;
  border-radius: 14px;
  background-color: #fff;
  font-size: 28rpx;
}

.vertical {
  position: absolute;
  left: 42%;
  top: 38%;
}

.no_more {
  width: 104rpx;
}

.no_data {
  position: relative;
  margin-top: 20rpx;
  height: 240rpx;
  border-radius: 14px;
  background-color: #fff;
  font-size: 28rpx;

  .vertical {
    position: absolute;
    left: 42%;
    top: 20%;
  }

  .no_more {
    width: 104rpx;
  }
}

.list .item {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.courseimg {
  width: 100%;
}

.vipImage {
  width: 260rpx;
}

.addVip {
  width: 156rpx;
  height: 60rpx;
  background-image: linear-gradient(to right, #ff8761, #f93d4d);
  text-align: center;
  line-height: 60rpx;
  border-radius: 30rpx;
}

.flex-s {
  display: flex;
}

.vip_banner_box {
  width: 100%;
  padding: 0 30upx;
  height: 148upx;
  box-sizing: border-box;
}

.vipName {
  width: 380upx;
  left: 240upx;
  top: 44upx;
  font-size: 30upx;
  font-weight: bold;
  color: #fff;
}

.vipPrice {
  width: 380upx;
  left: 240upx;
  bottom: 26rpx;
  color: #fcd73e;
  font-weight: bold;
}

.courseList {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  min-height: 800rpx;
}

.title-css-module {
  font-family: AlibabaPuHuiTi_3_85_Bold;
  font-style: normal;
  font-weight: bold;
}

.course-center-css {
  background: url('https://document.dxznjy.com/course/1bb7096adb1d446fa8f37e20bf5b0668.png') no-repeat;
  background-size: 100% 100%;
  width: 750rpx;
  padding-bottom: 32rpx;

  .course-one-css {
    width: 686rpx;

    .css-image {
      width: 100%;
    }

    .goodsTagOne-css {
      border: 1px solid #62c92d;
      background-color: #efffee;
      padding: 0 5rpx;
      color: #62c92d;
    }
  }
}

.courseItem {
  width: 330upx;
  border-radius: 20upx;
  background-color: #fff;
  margin-bottom: 30rpx;
}

.courseTip {
  width: 95upx;
  height: 40rpx;
  top: 20upx;
  right: 0;
}

.courseItem_tig {
  width: 115upx;
  top: 0;
  left: 0;
}

/deep/.u-popup__content {
  background-color: transparent !important;
}

/deep/.u-safe-area-inset-bottom {
  padding: 0 !important;
}

/deep/.dialog-icon .uni-icons {
  position: absolute;
  width: 44rpx;
  bottom: 50rpx;
  left: 47%;
}

/* 弹窗样式 */
.dialogBG {
  width: 100%;
  /* height: 100%; */
}

/* 21天结束复习弹窗样式 */
.reviewCard_box {
  width: 670rpx;
  /* height: 560rpx; */
  position: relative;
}

.reviewCard_box image {
  width: 100%;
  height: 100%;
}

.reviewCard {
  position: relative;
  width: 100%;
  height: 100%;
  background: #ffffff;
  color: #000;
  border-radius: 24upx;
  padding: 50upx 55upx;
  box-sizing: border-box;
}

.more-btn-css {
  color: #abaaaa;
}

.cartoom_image {
  width: 420rpx;
  position: absolute;
  top: -250rpx;
  left: 145rpx;
  z-index: -1;
}

.review_close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  z-index: 1;
}

.reviewTitle {
  width: 100%;
  text-align: center;
  font-size: 34upx;
  display: flex;
  justify-content: center;
}

.dialogContent {
  box-sizing: border-box;
  font-size: 32upx;
  line-height: 45upx;
  text-align: center;
  margin-top: 40rpx;
}

.review_btn {
  width: 240upx;
  height: 80upx;
  background: linear-gradient(180deg, #f08c34 0%, #ea6531 100%);
  border-radius: 45upx;
  font-size: 30upx;
  color: #ffffff;
  line-height: 80upx;
  margin: 60rpx auto 0 auto;
  justify-content: center;
  text-align: center;
}

.addclass {
  width: 100%;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 30rpx;
  color: #ea6531;
  border: 1px solid #ea6531;
  border-radius: 35rpx;
}

.not-selected {
  width: 100%;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 30rpx;
  color: #000;
  border: 1px solid #c8c8c8;
  border-radius: 35rpx;
}

.scroll-Y {
  height: 440rpx;
}

.productShare {
  width: 30upx;
  height: 30upx;
}

.ty_img {
  width: 95rpx;
  height: 50rpx;
}

.pronunciation_container {
  // padding: ;
}

.pronunciation_img {
  width: 710rpx;
  height: 1230rpx;
  margin-left: 20rpx;
}

.pronunciation_title {
  position: absolute;
  top: 230rpx;
  left: 39%;
}

.pronunciation_content {
  position: absolute;
  top: 290rpx;
  left: 60rpx;
  width: 600rpx;
  color: #2c1a09;
}

.pronunciation_enable {
  width: 250rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 45rpx;
  background: linear-gradient(to bottom, #88cfba, #2f8c70);
}

.close_icon {
  position: absolute;
  top: 180rpx;
  right: 60rpx;
}

.uni-input {
  color: #b7b7b7;
  font-size: 30rpx;
  height: 140rpx;
  width: 500rpx;
  border: 1px solid #e9e9e9;
  border-radius: 14rpx;
  padding: 30rpx;
}

.refresh_icon {
  width: 30rpx !important;
  height: 30rpx !important;
}

.import-icon {
  position: absolute;
  bottom: 30rpx;
  right: 30rpx;
  width: 30rpx !important;
  height: 30rpx !important;
}

.random_text {
  color: #14806c;
}

.playType {
  width: 140rpx;
  height: 70rpx;
  line-height: 70rpx;
  border-radius: 9rpx;
}

.selected_type {
  // color: #fff;
  // background-color: #14806C;
}

.no_selected {
  color: #14806c;
  border: 1px solid #14806c;
  box-sizing: border-box;
}

.notify {
  box-shadow: 0rpx 0rpx 20rpx #e0e0e0;
}

.err_tip {
  width: 38rpx;
  height: 38rpx;
}

.icon_voice {
  width: 30rpx !important;
  height: 30rpx !important;
}

.popup_content {
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.1);

  .popup_tip {
    width: 400rpx;
  }

  .close_css {
    position: absolute;
    right: -30rpx;
    top: -30rpx;
  }
}

/deep/.u-tabs__wrapper__nav__item__text {
  width: 160rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.my-class {
  .tabs_css_content {
    position: fixed;
    top: 0;
    left: 26rpx;
    height: 80rpx;
  }
}

.my_class_css {
  width: 710rpx;
  position: relative;
  left: -12rpx;
}

.swiper_css_item {
  width: 220rpx;
  text-align: left;
}

//社群有礼
.gifts {
  position: fixed;
  bottom: 200rpx;
  right: 10rpx;
}

.group-gift {
  width: 110rpx;
}

.close-button {
  position: absolute;
  bottom: -40rpx;
  right: 30rpx;
}

.order_code {
  background-color: #fff;
  padding: 20rpx 30rpx;
  margin-top: 20rpx;
}

.order_code1 {
  border: 1px solid #fe0000;
  display: flex;
  border-radius: 14rpx;
  justify-content: space-between;
  align-items: center;

  .confirmbutton {
    width: 200rpx !important;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    background-color: #fe0000;
    color: #fff;
  }

  .input1 {
    flex: 1;
    padding-left: 20rpx;
  }
}

.pull-content {
  /deep/.uni-load-more__img {
    display: none;
  }

  /deep/.uni-load-more__text {
    margin-bottom: 20upx;
  }
}

.scroll-content {
  white-space: nowrap;
  width: 686rpx;
}

.goods-item-wrapper {
  display: inline-block;
  padding-right: 34rpx;
  box-sizing: border-box;
}

.learn-more-css {
  width: 750rpx;
  position: relative;

  .more-image {
    width: 686rpx;
  }

  .learn-more-content {
    position: absolute;
    top: 215rpx;
    text-align: center;

    .learn-more-item {
      width: 217rpx;
      text-align: center;
      margin-right: 20rpx;
    }
  }
}
</style>