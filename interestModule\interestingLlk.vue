<template>
  <view class="funContent">
    <!-- 连线 -->
    <canvas class="line" canvas-id="lineCanvas"></canvas>
    <interesting-head :title="titleText" @backPage="backPage" :hasTitleBg="true" :closeWhite="true" :hasRight="false"></interesting-head>
    <!-- 答案列表 10个-->
    <!-- view class="answerListBox">
            <view class="itemView" :class="(index+1)%2==1?'answerListFrog':'answerListLeaf'" v-for="(item,index) in showList" :key="index" @click="chooseAnswer(item,index)"
             :style="item.isShow?'opacity: 1':'opacity: 0'">
            	<text :class="(index+1)%2==1?'answerTextFrog':'answerTextLeaf'" :style="item.text.length>6?'font-size:28rpx':(item.text.length>10?'font-size:18rpx':'')">
                {{item.text}}</text>
            	<image v-if="(index+1)%2==1"
                :class="answerChooseList[index]==null?'answerIconFrog':!answerChooseList[index]? 'answerIconWrongFrog':'answerIconRightFrog'" mode=""></image>
            	<image v-if="(index+1)%2!=1"
                :class="answerChooseList[index]==null?'answerIconLeaf':!answerChooseList[index]? 'answerIconWrongLeaf':'answerIconRightLeaf'" mode=""></image>
            </view>
		</view> -->
    <!-- 答案列表 12个 带干扰项-->
    <view class="answerListBox12">
      <!-- 青蛙 -->
      <view style="position: absolute; left: 0; top: 50rpx">
        <view v-for="(item, index) in showList" :key="index" @click="chooseAnswer(item, index)" :style="item.isShow ? 'opacity: 1' : 'opacity: 0'">
          <view v-if="item.en" class="answerListFrog itemView">
            <text
              class="answerTextFrog"
              :style="{ opacity: item.isShow ? '1' : '0', 'font-size': item.text && item.text.length > 6 ? '26rpx' : item.text && item.text.length > 10 ? '15rpx' : '' }"
            >
              {{ item.text }}
            </text>
            <image
              :style="item.isShow ? 'opacity: 1' : 'opacity: 0'"
              :class="answerChooseList[index] == null ? 'answerIconFrog' : !answerChooseList[index] ? 'answerIconWrongFrog' : 'answerIconRightFrog'"
              mode=""
            ></image>
          </view>
        </view>
      </view>
      <!-- 叶子 -->
      <view style="position: absolute; right: 0; top: 0">
        <view v-for="(item, index) in showList" :key="index" @click="chooseAnswer(item, index)" :style="item.isShow ? 'opacity: 1' : 'opacity: 0'">
          <view v-if="!item.en" class="answerListLeaf itemView">
            <text
              class="answerTextLeaf"
              :style="{ opacity: item.isShow ? '1' : '0', 'font-size': item.text && item.text.length > 6 ? '26rpx' : item.text && item.text.length > 10 ? '15rpx' : '' }"
            >
              {{ item.text }}
            </text>
            <image
              :style="item.isShow ? 'opacity: 1' : 'opacity: 0'"
              :class="answerChooseList[index] == null ? 'answerIconLeaf' : !answerChooseList[index] ? 'answerIconWrongLeaf' : 'answerIconRightLeaf'"
              mode=""
            ></image>
          </view>
        </view>
      </view>
    </view>

    <!--结束弹窗 -->
    <uni-popup ref="popopPower" type="center" :maskClick="true" :classBG="''">
      <interesting-dialog
        :gradScreenWheel="3"
        :pageUrl="'interestingLlk'"
        :showData="showData"
        :play="play"
        :scheduleCode="showData.scheduleCode"
        @closeDialog="closeDialog1"
      ></interesting-dialog>
    </uni-popup>

    <!-- 引导 -->
    <uni-popup ref="guideOne" type="bottom" :maskClick="true" :classBG="''">
      <view style="position: relative">
        <image :style="{ height: screenHeight + 'rpx', width: screenWidth + 'rpx' }" src="https://document.dxznjy.com/applet/newInteresting/interesting_guide_llk_one1.png"></image>
        <image class="guide_btn_next" @click="guideClose()" src="https://document.dxznjy.com/applet/newInteresting/interesting_guide_end.png"></image>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import interestingHead from './components/interesting-head/interestingHead.vue';
  import interestingDialog from './components/interesting-dialog/review.vue';
  import cmdProgress from './components/cmd-progress/cmd-progress.vue';

  export default {
    components: {
      interestingHead,
      interestingDialog,
      cmdProgress
    },
    data() {
      return {
        nowGroup: '',
        nowLevel: '',
        waitGroup: '',
        imgHost: getApp().globalData.imguseHost,
        titleText: '连连看',
        scheduleCode: '', //课程进度
        showListData: [], //当前课程玩法一页显示单词
        allShowList: [], //当前玩法所有数据
        showData: {}, //当前课程所有数据
        distrubList: [], //干扰项列表数据
        play: '3', //玩法
        showListNum: 5, //一页展示几条数据
        pageIndex: 0, //当前页数
        allPage: 0, //总页数
        nowChooseAnswer: {}, //当前选择的答案
        otherWord: false, //除当前玩法其它玩法是否还有单词
        successList: [], //正确列表数据存储
        errorList: [], //错误列表数据存储
        correctRate: 0, //正确率
        gradScreenWheel: 1, //1组 2关 3轮 ，没有正确率这些数据的时候给0
        showList: [], //展示的数据
        answerChooseList: [], //答案用来展示是否隐藏
        downTimeCount: getApp().globalData.interestDownTime,
        showDownTime: this.downTimeCount,
        wordInfo: {
          roundId: null,
          scheduleCode: null,
          courseCode: null,
          nowGroup: null,
          wordCount: null,
          studentWordId: null,
          play: '3'
        },
        isEnd: false,

        firstChoseIndex: -1, //第一个选择的坐标

        isGuide: 0, //0知道了-已完成引导 1已完成引导
        screenHeight: 0,
        screenWidth: 0,
        //连线
        selectedTile1: null, // 第一个选中
        selectedTile2: null, // 第二个选中
        isConnecting: false, // 是否正在连接中
        lineCanvasContext: null, // 连线Canvas的上下文
        //坐标
        positionArr: [],
        positionArr1: [],
        positionArr2: [],
        leafIndexList: [],
        intervalId: null,

        timbre: 'W', // 音色默认女声 M  W
        pronunciationType: 0, // 1英式  0美式  默认美式
        playType: 2, // 版本
        linkUrl: ''
      };
    },
    onUnload() {
      if (this.intervalId != null) {
        clearInterval(this.intervalId);
        this.intervalId = null;
      }
    },
    onReady() {
      // 获取连线Canvas的上下文
      this.lineCanvasContext = uni.createCanvasContext('lineCanvas', this);
      this.resetPos();
    },
    onLoad(options) {
      this.getHeight();
      this.showData = JSON.parse(decodeURIComponent(options.params));
      this.getWordversion();
      this.isGuide = uni.getStorageSync('llkGuide');
      if (!this.isGuide) {
        this.isGuide = 0;
      }
      if (this.isGuide == 0) {
        this.$refs.guideOne.open();
      }
      if (this.isGuide == 1) {
        this.getShowData();
      }
    },
    methods: {
      resetPos() {
        if (this.positionArr.length <= 0) {
          this.getAllViewPosition();
          this.intervalId = setInterval(() => {
            if (this.positionArr.length > 0 && this.intervalId != null) {
              clearInterval(this.intervalId);
              this.intervalId = null;
              return;
            }
            this.getAllViewPosition();
          }, 500);
        }
      },
      getHeight() {
        // 获取系统信息
        const systemInfo = uni.getSystemInfoSync();
        this.screenHeight = systemInfo.windowHeight * 2;
        this.screenWidth = systemInfo.windowWidth * 2;
        console.log(this.screenHeight);
        console.log(this.screenWidth);
      },
      // 初始获取数据
      getShowData() {
        let that = this;
        that.wordInfo.nowGroup = that.showData.nowGroup;
        that.wordInfo.wordCount = that.showData.wordCount;
        that.wordInfo.nowLevel = that.showData.nowLevel;
        that.wordInfo.roundId = that.showData.roundId;
        that.nowGroup = that.showData.levelGroup;
        that.nowLevel = that.showData.nowLevel;
        that.waitGroup = that.showData.levelTotalGroup - that.showData.levelGroup;

        that.getNoLevelData();
        // // 判断除当前组之外其它组是否还有单词
        // if (that.showData.cos.play1.length == 0 && that.showData.cos.play2.length == 0 && that.showData.cos.play4.length ==
        // 	0) {
        // 	that.otherWord = false;
        // } else {
        // 	that.otherWord = true;
        // 	that.gradScreenWheel = 4;
        // }
        // if (!that.otherWord) {
        // 	// 判断这组结束是组还是轮还是关
        // 	if (!that.showData.status) {
        // 		that.gradScreenWheel = 1;
        // 	}
        // 	if (that.showData.status && that.showData.nowLevel != that.showData.totalLevel) {
        // 		that.gradScreenWheel = 2;
        // 	} //当前关数==总关数 代表是最后一关，所以是一轮结束(其它组都为空的时候才可以开启下一轮)
        // 	if (that.showData.status && that.showData.nowLevel == that.showData.totalLevel) {
        // 		that.gradScreenWheel = 3;
        // 	}
        // }
        // if (that.showData.groupEnd) {
        // 	that.judgeNewGroup();
        // }
      },
      // 获取当前学员设置的语音版本
      getWordversion() {
        var that = this;
        that.$httpUser
          .get('znyy/course/info', {
            studentCode: that.showData.studentCode
          })
          .then((res) => {
            if (res.data.success) {
              console.log(res.data.data);
              let name = res.data.data.voiceModel.split('#');
              that.pronunciationType = name[1];
              that.timbre = name[2];
              that.playType = name[0];
            } else {
              that.$util.alter(res.data.message);
            }
          });
      },
      getNoLevelData() {
        let that = this;
        that.$httpUser.get('znyy/course/query/fun/words/byType?scheduleCode=' + that.showData.scheduleCode + '&type=3').then((res) => {
          if (res.data.success) {
            if (res.data.data && res.data.data.length != 0) {
              that.allShowList = that.allShowList.concat(res.data.data);
              that.showList = [];
              // 对答案进行操作
              that.initialFunction();
            } else {
              that.endDialog();
              return;
            }
          } else {
            uni.navigateBack({ delta: 1 });
            // uni.redirectTo({
            // 	url: '/antiAmnesia/review/funReview?scheduleCode=' + that.showData.scheduleCode
            // })
            return;
          }
        });
      },

      initialFunction() {
        let that = this;

        //获取页数如果是整数
        if (that.allShowList.length % that.showListNum == 0) {
          that.allPage = that.allShowList.length / that.showListNum;
        } else {
          that.allPage = Math.ceil(that.allShowList.length / that.showListNum);
        }
        that.answerChooseList = [];
        if (that.allShowList.length > that.showListNum) {
          that.showListData = that.allShowList.slice(that.showListNum * that.pageIndex, that.showListNum * (that.pageIndex + 1));
        } else {
          that.showListData = that.allShowList;
        }
        that.showDownTime = that.downTimeCount;
        let chineseIndex = [];
        let englishIndex = [];
        for (var i = 0; i < that.showListData.length; i++) {
          chineseIndex.push(i);
          englishIndex.push(i);
        }
        that.distrubList = that.$util.randomSort(that.$distrubChinese).slice(0, 5);
        let forLen = 2; //干绕项去重
        for (var i = 0; i < forLen; i++) {
          let hasRepeat = that.showListData.findIndex((item) => {
            return item.translation == that.distrubList[i];
          });
          if (hasRepeat != -1 || (that.distrubList[i] && that.distrubList[i].length > 6)) {
            //如果重复或者干扰项文字过长超过6个
            that.distrubList.splice(i, 1);
            forLen++;
          } else {
            var data = {
              text: that.distrubList[i],
              isShow: true,
              id: null,
              en: false
            };
            chineseIndex.push(data);
          }
        }
        that.$util.shuffleArray(chineseIndex);
        that.$util.shuffleArray(englishIndex);
        let insert_word_index = 0;
        let insert_translations_index = 0;
        for (let i = 0; i < englishIndex.length + chineseIndex.length; i++) {
          if ((i == 0 || i % 2 == 0) && insert_word_index < englishIndex.length) {
            let index = englishIndex[insert_word_index];
            var data = {
              text: that.showListData[index].word,
              isShow: true,
              id: that.showListData[index].id,
              en: true,
              ta: that.showListData[index].translation,
              word: that.showListData[index].word
            };
            that.showList.push(data);
            insert_word_index++;
          } else {
            let index = chineseIndex[insert_translations_index];
            if (index.isShow) {
              that.showList.push(index);
              insert_translations_index++;
              continue;
            }
            var data = {
              text: that.showListData[index].translation,
              isShow: true,
              id: that.showListData[index].id,
              en: false,
              ta: that.showListData[index].word,
              word: that.showListData[index].word
            };
            that.showList.push(data);
            insert_translations_index++;
            //获取坐标
            //展示的中文和英文乱序
          }
          that.answerChooseList.push(null);
        }
        that.getFrogLeafIndex();
      },
      getFrogLeafIndex() {
        this.leafIndexList = [];
        for (let i = 0; i < this.showList.length; i++) {
          if (!this.showList[i].en) {
            this.leafIndexList.push(i);
          }
        }
        console.log('this.leafIndexList');
        console.log(this.leafIndexList);
      },

      //选择答案
      chooseAnswer(item, index) {
        let that = this;
        if (this.isEnd) {
          this.$util.alter('当前玩法已结束');
          return;
        }
        if (!item.isShow) {
          return;
        } //如果隐藏了则不可点击
        if (this.isConnecting) return; // 正在连接中，不允许选择方块
        //选择第一个
        if (JSON.stringify(that.nowChooseAnswer) == '{}') {
          // 第一个永远显示对
          that.answerChooseList[index] = true;
          that.firstChoseIndex = index;
          that.selectedTile1 = that.getViewPosition12(item, index);
          that.wordExecute(item, index);
        } else {
          //选择第二个
          //第二次点击是英文
          if (item.en) {
            //第一次选择的是英文则之前的选项清除
            if (that.nowChooseAnswer.en) {
              that.answerChooseList[that.nowChooseAnswer.firstNum] = null;
              that.answerChooseList[index] = true;
              that.firstChoseIndex = index;
              that.selectedTile1 = that.getViewPosition12(item, index);
              that.nowChooseAnswer = {};
              that.wordExecute(item, index);
            } else {
              // 开始匹配
              this.selectedTile2 = that.getViewPosition12(item, index);
              this.isConnecting = true; // 开始连接
              that.questionIsTrue(item, index);
            }
          } else {
            //点击中文
            //第一次选择的是中文则之前的选项清除
            if (!that.nowChooseAnswer.en) {
              that.answerChooseList[that.nowChooseAnswer.firstNum] = null;
              that.answerChooseList[index] = true;
              that.firstChoseIndex = index;
              that.selectedTile1 = that.getViewPosition12(item, index);
              that.nowChooseAnswer = {};
              that.wordExecute(item, index);
            } else {
              this.isConnecting = true; // 开始连接
              this.selectedTile2 = that.getViewPosition12(item, index);
              // 开始匹配
              that.questionIsTrue(item, index);
            }
          }
        }
        that.$forceUpdate();
      },

      //题目执行
      wordExecute(item, index) {
        let that = this;
        that.nowChooseAnswer = item;
        that.$set(that.nowChooseAnswer, 'firstNum', index); //记录第一个选择的是否是英文，和下标
      },

      // 开始匹配
      questionIsTrue(item, index) {
        let that = this;
        let enShow = item.en ? item : that.nowChooseAnswer;
        let cnShowEn = item.en ? that.nowChooseAnswer : item;
        // 正确
        if (enShow.word == cnShowEn.word) {
          that.drawLine(true);
          that.$playVoice('task_success.mp3', true, that.timbre, that.pronunciationType, that.playType, that.showData.studentCode);
          that.doItRight(enShow, index);
        } else {
          that.$playVoice('ino.mp3', true, that.timbre, that.pronunciationType, that.playType, that.showData.studentCode);
          // 第二个选项显示叉叉
          that.drawLine(false);
          that.answerChooseList[index] = false;
          that.answerChooseList[that.firstChoseIndex] = false;
          // n秒后取消图标显示
          setTimeout(function () {
            that.answerChooseList[index] = null;
            that.answerChooseList[that.nowChooseAnswer.firstNum] = null;
            that.clernDrawLine();
            that.$forceUpdate();
            // 对错误集合进行操作
            var errHas = that.errorList.indexOf(enShow.word);
            if (errHas == -1) {
              that.markWrongWord(enShow);
              that.errorList.push(enShow.word);
            }
            that.nowChooseAnswer = {};
          }, 500);
        }
      },

      //答对一个单词
      doItRight(item, index) {
        let that = this;
        let firstNum = that.nowChooseAnswer.firstNum;
        // 第二个选项显示图标
        that.answerChooseList[index] = true;
        that.answerChooseList[that.firstChoseIndex] = true;
        var errHas = that.errorList.indexOf(item.word);
        if (errHas == -1) {
          that.successList.push(item.word);
        }
        that.nowChooseAnswer = {};
        that.newMarkRightWord(item);
        setTimeout(function () {
          that.showList[index].isShow = false;
          that.showList[firstNum].isShow = false;
          that.clernDrawLine();
          that.nextQuestion();
        }, 500);
      },

      // 标记正确单词
      markRightWord(item) {
        let that = this;
        that.wordInfo.studentWordId = item.id;
        that.wordInfo.scheduleCode = that.showData.scheduleCode;
        that.$httpUser.post(`znyy/course/fun/play/word`, that.wordInfo).then((res) => {
          if (!res.data.success) {
            that.$util.alter(res.data.message);
          }
        });
      },

      // 标记正确单词---new
      newMarkRightWord(item) {
        let that = this;

        that.wordInfo.studentWordId = item.id;
        that.wordInfo.scheduleCode = that.showData.scheduleCode;
        that.$httpUser.post(`znyy/course/noLevel/fun/play/word/finish`, that.wordInfo).then((res) => {
          if (!res.data.success) {
            that.$util.alter(res.data.message);
          }
        });
      },

      // 标记错误单词
      markWrongWord(item) {
        let that = this;
        that.wordInfo.studentWordId = item.id;
        // that.$httpUser.post("znyy/course/mark/wrong/words", that.wordInfo).then((res) => {
        // 	if (!res.data.success) {
        // 		that.$util.alter(res.data.message)
        // 	}
        // })
      },

      // 题目翻页
      nextQuestion() {
        let that = this;
        //根据隐藏的条数和展示条数相比
        let nowPagelength = 0;
        that.showList.forEach((item) => {
          item.isShow == false ? nowPagelength++ : '';
        });
        //一页结束
        // if (nowPagelength >= that.showList.length) {
        //混淆
        if (nowPagelength == that.showList.length - 2) {
          if (that.pageIndex < that.allPage - 1) {
            that.pageIndex++;
            that.showList = [];
            that.initialFunction();
          } else {
            that.pageIndex++;
            setTimeout(function () {
              that.getNoLevelData();
            }, 500);
          }
        }
      },

      // 结束弹窗
      endDialog() {
        let that = this;
        that.isEnd = true;
        if (that.gradScreenWheel < 3 || that.gradScreenWheel == 4) {
          if (that.successList.length + that.errorList.length == 0) {
            that.correctRate = 0;
          } else if (that.successList.length != 0 && that.errorList.length == 0) {
            that.correctRate = 100;
          } else {
            that.correctRate = parseInt((that.successList.length * 100) / (that.successList.length + that.errorList.length));
            if (isNaN(that.correctRate)) {
              that.correctRate = 0;
            }
          }
        }
        that.$refs.popopPower.open();
      },

      // 是否进入下一组
      judgeNewGroup() {
        this.endDialog();
      },

      refreshWord() {
        this.showListData = []; //当前课程玩法组所有的单词
        this.allShowList = []; //当前玩法所有数据
        this.distrubList = []; //干扰项列表数据
        this.play = '3'; //玩法
        this.showListNum = 5; //一页展示几条数据
        this.pageIndex = 0; //当前页数
        this.allPage = 0; //总页数
        this.nowChooseAnswer = {}; //当前选择的答案
        this.otherWord = false; //除当前玩法其它玩法是否还有单词
        this.successList = []; //正确列表数据存储
        this.errorList = []; //错误列表数据存储
        this.correctRate = 0; //正确率
        this.gradScreenWheel = 1; //1组 2关 3轮 ，没有正确率这些数据的时候给0
        this.showList = []; //展示的数据
        this.answerChooseList = []; //答案用来展示是否隐藏

        this.wordInfo = {
          roundId: null,
          scheduleCode: null, //学习进度
          courseCode: null,
          nowGroup: null,
          wordCount: null,
          studentWordId: null,
          play: '3' // 玩法
        };
        const sc = this.showData.scheduleCode;
        this.$httpUser.get(`znyy/course/query/fun/words?scheduleCode=${sc}`).then((res) => {
          if (res.data.success) {
            (this.showData = {}), //当前课程所有数据
              (this.showData = res.data.data);
            this.showData.scheduleCode = sc;
            this.getShowData();
            this.closeDialog();
          } else {
            this.$util.alter(res.data.message);
          }
        });
      },

      //关闭弹窗
      closeDialog() {
        // console.log("复习报告quxiao")
        this.$refs.popopPower.close();
      },

      //查看报告弹框关闭
      closeDialog1() {
        // #ifdef APP-PLUS
        this.backPage();
        // #endif
        this.$refs.popopPower.close();
      },
      // 返回上一页
      backPage() {
        //返回按钮
        uni.navigateBack({ delta: 1 });
        // uni.redirectTo({
        // 	url: '/antiAmnesia/review/funReview?scheduleCode=' + this.showData.scheduleCode + '&merchantCode='+this.showData.merchantCode
        // })
      },
      async guideClose() {
        this.isGuide = 1;
        await uni.setStorageSync('llkGuide', this.isGuide);
        this.$refs.guideOne.close();
        this.getShowData();
      },
      //获取view的坐标值 弃用
      getAllViewPosition() {
        let that = this;
        that.$forceUpdate();
        const query = uni.createSelectorQuery().in(this);
        query.selectAll('.itemView').boundingClientRect();
        query.exec((res) => {
          console.log(res);
          if (res && res[0]) {
            var positions = res[0];
            if (positions && positions.length > 0) {
              that.positionArr = [];
              that.positionArr1 = [];
              that.positionArr2 = [];
              /* 10个
                            for(let i=0;i<positions.length;i++){
                                var viewWidth = positions[i].width;
                                var viewHeight = positions[i].height;
                                var x = 0;
                                var y = 0;
                                switch (i){
                                    case 0:
                                    case 2:
                                    case 4:
                                    case 6:
                                    case 8:
                                        x = positions[i].width;
                                        y = positions[i].height-13;
                                        break;
                                    case 1:
                                    case 3:
                                    case 5:
                                    case 7:
                                    case 9:
                                        x = 0;
                                        y = viewHeight = positions[i].height/2;
                                        break;
                                }
                                that.positionArr.push({"x":positions[i].left+x,"y":positions[i].top+y})
                            } */
              //12 干扰项
              let width1 = positions[0].width;
              let width2 = positions[positions.length - 1].width;
              let frog = width1 > width2 ? width2 : width1;
              let frogCount = 0;
              for (let i = 0; i < positions.length; i++) {
                var viewWidth = positions[i].width;
                var viewHeight = positions[i].height;
                var x = 0;
                var y = 0;
                if (viewWidth == frog) {
                  frogCount++;
                  x = positions[i].width;
                  y = positions[i].height - 13;
                  that.positionArr1.push({ x: positions[i].left + x, y: positions[i].top + y });
                } else {
                  x = 0;
                  y = viewHeight = positions[i].height / 2;
                  that.positionArr2.push({ x: positions[i].left + x, y: positions[i].top + y });
                }
                that.positionArr.push({ x: positions[i].left + x, y: positions[i].top + y });
              }
              //方法不可用
              // const maxLength = that.positionArr.length;
              // let frogList = that.positionArr.splice(0, frogCount);
              // let newList = [];
              // for(let i=0;i<maxLength;i++){
              //     if (i < frogList.length) {
              //         newList.push(frogList[i]);
              //     }
              //     if (i < this.positionArr.length) {
              //         newList.push(this.positionArr[i]);
              //     }
              // }
              // that.positionArr = newList;
              // console.log(that.positionArr);
            }
          }
        });
      },
      //获取view的坐标值 10个
      getViewPosition(index) {
        return this.positionArr[index];
      },
      //12个 加干扰项
      getViewPosition12(item, index) {
        if (item.en) {
          var newIndex1 = index / 2;
          if (newIndex1 >= this.positionArr1.length) {
            return null;
          }
          return this.positionArr1[newIndex1];
        } else {
          var newIndex2 = this.leafIndexList.indexOf(index);
          console.log('index', index);
          console.log('newIndex2', newIndex2);
          if (newIndex2 == -1 || newIndex2 >= this.positionArr2.length) {
            return null;
          }
          return this.positionArr2[newIndex2];
        }
      },
      // 绘制连线
      drawLine(isRight) {
        var color = '#FFC6C6';
        if (isRight) {
          color = '#97FF6C';
        }
        const ctx = this.lineCanvasContext;
        // 清空Canvas
        ctx.clearRect(0, 0, this.screenWidth, this.screenHeight); // 根据Canvas的大小调整清空区域
        // 在ctx上绘制连线
        if (this.selectedTile1 && this.selectedTile2) {
          ctx.beginPath();
          ctx.moveTo(this.selectedTile1.x, this.selectedTile1.y);
          ctx.lineTo(this.selectedTile2.x, this.selectedTile2.y);
          ctx.strokeStyle = color; // 连线的颜色
          ctx.lineWidth = 2; // 连线的宽度
          ctx.stroke(); // 绘制路径
          ctx.closePath(); // 关闭路径
          ctx.draw(); // 绘制到canvas上
        }
      },

      clernDrawLine() {
        this.isConnecting = false;
        const ctx = this.lineCanvasContext;
        ctx.clearRect(0, 0, this.screenWidth, this.screenHeight); // 根据Canvas的大小调整清空区域
        ctx.beginPath();
        ctx.moveTo(0, 0);
        ctx.lineTo(0, 0);
        ctx.strokeStyle = '#ffffff'; // 连线的颜色
        ctx.lineWidth = 0; // 连线的宽度
        ctx.stroke();
        ctx.closePath();
        ctx.draw();
      }
    }
  };
</script>

<style>
  page {
    height: 100vh;
    padding: 0;
  }

  .funContent {
    width: 100%;
    padding: 0 30rpx;
    box-sizing: border-box;
    height: 100%;
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_llk_bg.png') 100% 100% no-repeat;
    background-size: 100% 100%;
    position: relative;
  }

  .answerListBox12 {
    position: relative;
    width: 690rpx;
    margin: 10rpx auto 0 auto;
  }

  .answerListBox {
    width: 690rpx;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    margin: 60rpx auto 0 auto;
  }

  .answerListFrog {
    width: 220rpx;
    height: 176rpx;
    margin-bottom: 78rpx; /* 71rpx */
    box-sizing: border-box;
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_llk_frog.png') 100% 100% no-repeat;
    background-size: 100% 100%;
    position: relative;
    display: grid;
    place-items: center; /* 居中 */
  }

  .answerListLeaf {
    width: 281rpx;
    height: 138rpx;
    margin-bottom: 50rpx;
    /*        margin-bottom: 90rpx;  10*/
    margin-top: 30rpx;
    box-sizing: border-box;
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_llk_leaf.png') 100% 100% no-repeat;
    background-size: 100% 100%;
    position: relative;
    display: grid;
    place-items: center; /* 居中 */
  }

  .answerTextFrog {
    font-weight: bold;
    color: #000000;
    position: absolute;
    top: 72%;
  }
  .answerTextLeaf {
    font-weight: bold;
    color: #000000;
    position: absolute;
    top: 33%;
  }

  .answerIconFrog {
    position: absolute;
    bottom: 14rpx;
    right: -13rpx;
    width: 30rpx;
    height: 30rpx;
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_llk_normal.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }
  .answerIconWrongFrog {
    position: absolute;
    bottom: 14rpx;
    right: -13rpx;
    width: 30rpx;
    height: 30rpx;
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_llk_wrong.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }
  .answerIconRightFrog {
    position: absolute;
    bottom: 14rpx;
    right: -13rpx;
    width: 30rpx;
    height: 30rpx;
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_llk_tight.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  .answerIconLeaf {
    position: absolute;
    bottom: 55rpx;
    left: 2rpx;
    width: 30rpx;
    height: 30rpx;
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_llk_normal.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }
  .answerIconWrongLeaf {
    position: absolute;
    bottom: 55rpx;
    left: 2rpx;
    width: 30rpx;
    height: 30rpx;
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_llk_wrong.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }
  .answerIconRightLeaf {
    position: absolute;
    bottom: 55rpx;
    left: 2rpx;
    width: 30rpx;
    height: 30rpx;
    background: url('https://document.dxznjy.com/applet/newInteresting/interesting_llk_tight.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  .guide_btn_next {
    position: absolute;
    bottom: 57rpx;
    right: 64rpx;
    width: 269rpx;
    height: 142rpx;
  }

  .guide_btn_close {
    position: absolute;
    bottom: 57rpx;
    right: 64rpx;
    width: 269rpx;
    height: 142rpx;
  }
  .line {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
</style>
