<template>
  <view>
    <page-meta :page-style="'overflow:' + (rollShow ? 'hidden' : 'visible')"></page-meta>
    <view class="down-page">
      <!-- <view class="list" v-if="profileList.length > 0">
        <view class="list-item" :class="activeId == item.id ? 'active' : ''" v-for="item in profileList" :key="item.id" @tap="handleTap(item)">
          <text>{{ item.fileName }}</text>
        </view>
      </view> -->

      <view v-if="resourceList.length > 0">
        <view v-for="item in resourceList" :key="item.id">
          <view @click="expandList(item.id)" v-if="item.resourceUrlList && item.resourceUrlList.length > 0" class="first_item_file">
            <view>{{ item.fileName }}</view>
            <image
              class="expand_icon"
              :src="
                isExpandId == item.id
                  ? 'https://document.dxznjy.com/dxSelect/4b6bf860-a957-4c78-a20a-4f52a8c64481.png'
                  : 'https://document.dxznjy.com/dxSelect/b8a12bab-d60d-4173-889c-c3e2cf31cd2e.png'
              "
              mode="scaleToFill"
            />
          </view>
          <view v-else class="second_item_file" :class="isActive === item.id ? 'click_file' : ''" @tap="handleTap(item)">
            <image :class="fileType === 'video' ? 'image_video' : 'image_file'" :src="fileTypeJudge(item.fileExt)"></image>
            <view>{{ item.fileName }}</view>
          </view>
          <view v-if="isExpandId == item.id">
            <view v-for="secondItem in item.resourceUrlList" :key="secondItem.id">
              <view class="second_item_file" :class="isActive === secondItem.id ? 'click_file' : ''" @tap="handleTap(secondItem)">
                <image :class="fileType === 'video' ? 'image_video' : 'image_file'" :src="fileTypeJudge(secondItem.fileExt)"></image>
                <view>{{ secondItem.fileName }}</view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view v-else :style="{ height: useHeight + 'rpx', marginTop: '40rpx' }" class="t-c flex-col radius-15">
        <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
        <view style="color: #bdbdbd">暂无数据</view>
      </view>
    </view>
  </view>
</template>

<script>
  const { $http, $showMsg } = require('@/util/methods.js');
  export default {
    data() {
      return {
        activeId: '',
        profileList: [],
        isBuy: false,
        // 文件是否打开
        openLoading: false,
        imgHost: getApp().globalData.imgsomeHost,
        resourcefileurlList: [],
        resourceVideoUrl: [],
        isActive: '',
        isExpandId: ''
      };
    },
    props: {
      onloadInfo: {
        type: Object,
        default: {}
      },
      fileType: {
        type: String,
        default: 'file'
      }
    },
    computed: {
      id() {
        return this.onloadInfo?.id || '';
      },
      resourceList() {
        return this.fileType === 'video' ? this.resourceVideoUrl : this.resourcefileurlList;
      }
    },
    // onLoad(options) {
    //   this.id = options.id;
    //   if (!uni.getStorageSync('token')) {
    //     uni.navigateTo({
    //       url: '/Personalcenter/login/login'
    //     });
    //     return;
    //   }
    // },
    onShow() {
      this.activeId = '';
      this.openLoading = false;
    },
    mounted() {
      this.getProfileList();
      this.getUserHasBuyGoods();
    },
    methods: {
      refreshData() {
        if (!uni.getStorageSync('token')) {
          // uni.navigateTo({
          //   url: '/Personalcenter/login/login'
          // });
          // return;
        }
        this.activeId = '';
        this.openLoading = false;
        console.log('this.resourceVideoUrl123', this.resourceList);
        console.log('this.resourceVideoUrl123', this.resourceList, this.fileType);
        let tempList = this.fileType == 'video' ? this.resourceVideoUrl : this.resourcefileurlList;
        tempList.forEach((item) => {
          if (item.resourceUrlList && item.resourceUrlList.length > 0) {
            console.log(this.fileType, 'fileType.resourceUrlList', item.id);
            // this.isExpandId = item.id;
            return;
          }
        });
      },
      expandList(id) {
        if (this.isExpandId === id) {
          this.isExpandId = '';
        } else {
          this.isExpandId = id;
        }
      },
      handleTap(item) {
        this.isActive = item.id;
        let _this = this;
        if (!this.isBuy) {
          return $showMsg('您需要购买后才可进行查看/下载');
        }

        if (this.fileType === 'video') {
          console.log('视频', item, this.id);
          let dataInfo = {
            videoUrl: item.url,
            videoName: item.fileName,
            goodsId: this.id,
            fileId: item.id
          };
          uni.navigateTo({
            url: `/Coursedetails/study/videoPlay?info=${JSON.stringify(dataInfo)}`
          });
          return;
        }

        if (this.openLoading) {
          return $showMsg('文件打开中...');
        }

        this.activeId = item.id;
        this.openLoading = true;
        console.log('图片', item);
        const imageExtensions = ['.jpg', '.png', '.jpeg'];
        if (imageExtensions.includes(item.fileExt)) {
          console.log('图片');
          uni.previewImage({
            urls: [item.url],
            success: function () {
              _this.openLoading = false;
            }
          });
        }
        let tempUrl = item.url.replace(/^http:\/\//, 'https://');
        console.log('tempUrl', tempUrl);
        uni.downloadFile({
          url: tempUrl,
          success: function (res) {
            var filePath = res.tempFilePath;
            _this.openLoading = false;
            uni.openDocument({
              filePath: filePath,
              fileType: item.fileExt ? item.fileExt.split('.').pop() : 'pdf',
              showMenu: true,
              success: function (res) {
                _this.openLoading = false;
                console.log('打开文档成功');
              }
            });
          },
          fail: (err) => {
            this.openLoading = false;
            console.log(err, '下载失败');
          }
        });
      },
      // 是否购买过录播课
      async getUserHasBuyGoods() {
        const res = await $http({
          url: 'zx/wap/goods/isBuyGoods',
          data: {
            goodsId: this.id,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        this.isBuy = res.data;
      },
      async getProfileList() {
        console.log('this.id', this.id);
        const res = await $http({
          url: 'zx/wap/goods/learn/materials',
          data: {
            goodsId: this.id
          }
        });

        if (res) {
          res.data.forEach((element) => {
            if (element.fileType == '1') {
              this.resourceVideoUrl = element.fileDtos;
            } else {
              this.resourcefileurlList = element.fileDtos;
            }
          });
          this.$nextTick(() => {
            console.log('this.resourceVideoUrl123', this.resourceList);
            this.resourceList.forEach((item) => {
              if (item.resourceUrlList && item.resourceUrlList.length > 0 && this.isExpandId === '') {
                console.log('item.resourceUrlList', item.id);
                this.isExpandId = item.id;
                return;
              }
            });
            console.log('this.isExpandId', this.isExpandId);
          });
        }
      },
      fileTypeJudge(fileType) {
        const fileIconMap = {
          '.doc': 'cc86c7e6-e197-4cc4-90c9-f5bf2cb0fe91',
          '.docx': 'cc86c7e6-e197-4cc4-90c9-f5bf2cb0fe91',
          '.ppt': 'b1c6a54b-fb0d-40c9-ab40-c86a856e01fc',
          '.pptx': 'b1c6a54b-fb0d-40c9-ab40-c86a856e01fc',
          '.xls': '6e78c060-9c84-49b2-9602-31a88c25554a',
          '.xlsx': '6e78c060-9c84-49b2-9602-31a88c25554a',
          '.pdf': '27e2c534-8eb8-4996-b4ee-75f4eeaeee0d',
          '.jpg': '0eb69725-160e-4c48-811d-ffc418fa8a25',
          '.jpeg': '0eb69725-160e-4c48-811d-ffc418fa8a25',
          '.png': '0eb69725-160e-4c48-811d-ffc418fa8a25'
        };

        const baseurl = 'https://document.dxznjy.com/dxSelect/';
        const iconId = fileIconMap[fileType];

        return iconId ? `${baseurl}${iconId}.png` : 'https://document.dxznjy.com/course/5547f897939d493d8a151137c3a56241.png';
      }
    }
  };
</script>

<style lang="scss" scoped>
  .down-page {
    min-height: calc(100vh - 106rpx);
    padding: 32rpx;
    margin: 20rpx 30rpx 30rpx 35rpx;
    background-color: #fff;

    .list {
      width: 100%;
      padding-bottom: 130rpx;
    }

    .list-item {
      width: 100%;
      padding: 28rpx 32rpx;
      font-size: 28rpx;
      color: #555555;
      line-height: 40rpx;
      border-radius: 16rpx;
      border: 2rpx solid #efeff0;
      box-sizing: border-box;
      margin-bottom: 32rpx;
      &:last-child {
        margin-bottom: 0;
      }

      &.active {
        color: #46d49e;
        background: rgba(49, 207, 147, 0.1);
        border: 2rpx solid #31cf93;
      }
    }

    .img_s {
      width: 160rpx;
    }
    .first_item_file {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // width: 651rpx;
      height: 118rpx;
      font-size: 30rpx;
      color: #555555;
      background: #f6f7f9;
      border: 1rpx solid #f6f7f9;
      margin-top: 2rpx;
      padding: 0 54rpx 0 37rpx;
      .expand_icon {
        width: 25rpx;
        height: 14rpx;
        flex-shrink: 0;
      }
    }
    .second_item_file {
      box-sizing: border-box;
      display: flex;
      align-items: center;
      min-height: 118rpx;
      line-height: 45rpx;
      border: 1rpx solid #f3f3f3;
      padding: 12rpx 12rpx 12rpx 0;
      .image_file {
        width: 40rpx;
        height: 48rpx;
        margin-left: 54rpx;
        margin-right: 20rpx;
        flex-shrink: 0;
      }
      .image_video {
        width: 17rpx;
        height: 20rpx;
        margin-left: 66rpx;
        margin-right: 14rpx;
        flex-shrink: 0;
      }
    }
    .click_file {
      color: #00bc8c;
      background: rgba(82, 198, 132, 0.04);
      border-radius: 10rpx;
      border: 1rpx solid #52c584;
    }
  }
</style>
