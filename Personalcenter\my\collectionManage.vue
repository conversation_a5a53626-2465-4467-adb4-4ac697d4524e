<template>
  <view>
    <view class="flex-self-s">
      <view class="check_left_css">
        <image v-if="checkShow == 1" @tap="checkAll(0)" class="image_check_css" src="https://document.dxznjy.com/course/7b55388b298b46eb8959d6163e596e88.png"></image>
        <image v-else @tap="checkAll(1)" class="image_check_css" src="https://document.dxznjy.com/course/a9dface2e2b248f38707b5e1ae7f9d10.png"></image>
      </view>
      <view class="check_right_css f-28 c-00 lh-40">全选</view>
    </view>
    <view class="scroll_tab_content">
      <view class="flex-self-s mt-24" v-for="(item, index) in collectList" :key="item.id">
        <view class="check_left_css">
          <image v-if="item.check" @tap="checkChange(index, item)" class="image_check_css" src="https://document.dxznjy.com/course/7b55388b298b46eb8959d6163e596e88.png"></image>
          <image v-else @tap="checkChange(index, item)" class="image_check_css" src="https://document.dxznjy.com/course/a9dface2e2b248f38707b5e1ae7f9d10.png"></image>
        </view>
        <view class="check_right_css bg-ff flex-nowrap">
          <view class="width_css ml-25">
            <image class="w100" mode="widthFix" :src="item.goodsPicUrl"></image>
          </view>
          <view class="ml-25 w467">
            <view class="f-28 c-33 bold">{{ item.goodsName }}</view>
            <view class="mt-24 c-FA380E">
              <text class="f-24">￥</text>
              <text class="f-32 bold">{{ item.goodsVipPrice }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="flexbox button_css c-ff f-28">
      <view @tap="closeCollection" class="button_item_css button_left">取消收藏</view>
      <view @tap="collectionSave" class="button_item_css button_right">保存</view>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  export default {
    data() {
      return {
        collectList: [],
        checkShow: false,
        changeCheck: false
      };
    },
    onLoad() {
      this.getCollectList();
    },
    methods: {
      async getCollectList() {
        // let userId = uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '';
        const res = await $http({
          url: 'zx/wap/goods/collect/list',
          data: {
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '',
            pageNum: 1,
            pageSize: 15
          }
        });
        if (res) {
          this.collectList = res.data.data;
          this.collectList.forEach((item) => {
            item.check = false;
          });
        }
      },
      checkAll(key) {
        this.checkShow = key;
        this.collectList.forEach((item) => {
          if (key == 1) {
            this.$set(item, 'check', true);
          } else {
            this.$set(item, 'check', false);
          }
        });
      },
      collectionSave() {
        //
        uni.navigateBack({
          delta: 1
        });
      },
      async closeCollection() {
        let goodsIdList = [];
        this.collectList.forEach((item) => {
          if (item.check) {
            goodsIdList.push(item.goodsId);
          }
        });
        let _this = this;
        const res = await $http({
          url: 'zx/wap/goods/collect/cancel',
          method: 'post',
          data: {
            goodsIdList: goodsIdList,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        if (res) {
          this.getCollectList();
        }
      },
      checkChange(index, item) {
        if (this.checkShow == 1) {
          this.checkShow = 0;
        }
        this.changeCheck = !this.changeCheck;
        this.$set(this.collectList[index], 'check', !this.collectList[index].check);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .image_check_css {
    width: 32rpx;
    height: 32rpx;
    margin: 0 auto;
    display: block;
    border-radius: 50%;
  }
  .scroll_tab_content {
    height: calc(100vh - 170rpx);
    overflow-y: scroll;
  }
  .check_left_css {
    width: 94rpx;
  }
  .flex-self-s {
    align-items: center;
  }
  .check_right_css {
    width: 624rpx;
    border-radius: 16rpx;
    .width_css {
      width: 144rpx;
      height: 160rpx;
    }
    .w467 {
      width: 400rpx;
    }
    .c-FA380E {
      color: #fa380e;
    }
  }
  .flex-nowrap {
    padding-top: 24rpx;
    width: 625rpx;
    position: relative;
    height: 260rpx;
  }
  .button_css {
    width: 686rpx;
    height: 74rpx;
    line-height: 74rpx;
    position: absolute;
    left: 32rpx;
    bottom: 40rpx;
    text-align: center;
    .button_item_css {
      width: 328rpx;
      height: 74rpx;
      border-radius: 38rpx;
    }
    .button_left {
      color: #339378;
      border: 2rpx solid #339378;
    }
    .button_right {
      background-color: #339378;
    }
  }
</style>
