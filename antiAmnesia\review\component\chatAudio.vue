<template>
  <view class="audioPlay" v-if="show">
    <view>
      <view class="mb-50" style="position: relative">
        <view style="position: absolute; left: 50%; transform: translateX(-50%); top: 30rpx">
          <text v-if="60 - times <= 10" class="c-66 f-28">{{ 60 - times }}'后停止录音</text>
          <!-- 语音音阶动画 -->
          <view v-else :class="['record-animate-box', { active: closeShow }]">
            <view class="voice-scale">
              <view :class="['item', { active: closeShow }]" v-for="(item, index) in 10" :key="index"></view>
            </view>
          </view>
        </view>
      </view>
      <view id="close" class="close">
        <image style="width: 110rpx; height: 110rpx" src="https://document.dxznjy.com/dxSelect/aiTraining/ai_icon_no_send.png" mode=""></image>
      </view>
      <view class="c-ff f-28 mb-20" style="text-align: center">
        <text>{{ closeShow ? '松开 取消' : '松开 发送' }}</text>
      </view>
      <view style="position: relative">
        <image
          class="audio-icon"
          :src="
            closeShow ? 'https://document.dxznjy.com/dxSelect/aiTraining/ai_icon_record_audio.png' : 'https://document.dxznjy.com/dxSelect/aiTraining/ai_icon_record_audio1.png'
          "
        ></image>
        <image
          :style="{ width: winSize.witdh + 'px' }"
          style="height: 240rpx"
          :src="closeShow ? 'https://document.dxznjy.com/dxSelect/aiTraining/ai_img_record_bg1.png' : 'https://document.dxznjy.com/dxSelect/aiTraining/ai_img_record_bg.png'"
        ></image>
      </view>
    </view>
  </view>
</template>
<!-- @submit：通知父组件，发送消息内容。
	audioXY：手指在屏幕中的位置信息。
	audioShow：是否弹出子组件
	audioTouchendShow：是否结束录制
	closeAudioShow：子组件通知父组件，结束录制，-->
<script>
  import Config from '@/util/config.js';
  // import sensors from 'sa-sdk-miniprogram';
  const recorderManager = uni.getRecorderManager();
  const innerAudioContext = uni.createInnerAudioContext();
  innerAudioContext.autoplay = true;
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  export default {
    name: 'chatAudio',
    props: {
      audioShow: {
        type: Boolean,
        default: true
      },
      audioXY: {
        type: Object
      },
      audioTouchendShow: {
        type: Boolean,
        default: false
      }
    },
    watch: {
      // audioShow：true录制界面显示，开始录音，false关闭。
      audioShow(e, oldVal) {
        if (e) {
          console.log('开始录音');
          // 开始录制
          this.status = 0;
          this.audioSrc = '';
          this.number = 1;
          this.show = true;
          recorderManager.start({ format: 'mp3', sampleRate: 8000 });
          recorderManager.onStart((res) => {
            console.log('start', res);
          });
          // uni.vibrateShort();
          // 计算录制时长，>=60 录制结束
          this.times = 1;
          this.timesInt = setInterval(() => {
            this.times++;
            if (this.times >= 60) {
              clearInterval(this.timesInt);
              this.closeShow = false;
              this.show = false;
              recorderManager.stop();
              console.log('结束录制');
            }
          }, 1000);
          // 获取关闭按钮.close在屏幕中的位置信息
          this.$nextTick(() => {
            let close = uni.createSelectorQuery().in(this).select('#close');
            close
              .boundingClientRect((data) => {
                this.dom = data;
              })
              .exec();
          });
        } else {
          this.show = false;
          recorderManager.stop();
        }
      },
      // 手指在屏幕中的位置，判断是否：录制中/删除
      audioXY(e) {
        let x = e.x;
        let y = e.y;
        let left = this.dom.left;
        let top = this.dom.top;
        if (x > left && x < left + this.dom.width && y > top && y < top + this.dom.height) {
          this.closeShow = true;
        } else {
          this.closeShow = false;
        }
      },
      // 手指结束触摸，通知父组件关闭弹窗，根据this.closeShow类型，判断是取消还是发送
      audioTouchendShow(e) {
        if (this.number == 1) {
          this.number++;
          if (this.closeShow) {
            console.log('取消录制');
            this.status = 0;
            clearInterval(this.timesInt);
            this.closeShow = false;
            recorderManager.stop();
          } else {
            console.log('发送音频', this.audioSrc);
            clearInterval(this.timesInt);
            this.closeShow = false;
            recorderManager.stop();
            this.status = 1;
          }
        }
        this.$emit('closeAudioShow', this.status);
      }
    },
    data() {
      return {
        show: false, // 弹窗
        closeShow: false, // 正常/删除
        winSize: {},
        dom: {}, // 删除按钮位置
        times: 1, // 计时器
        timesInt: null, // 计时器
        status: 0, //0录制中，1录制结束。
        number: 1
      };
    },
    mounted() {
      // 录音停止事件，会回调文件地址，如果status == 1，上传音频，并通知父组件发送音频信息
      let that = this;
      if (that.closeShow) {
        console.log(2131);
      } else {
        recorderManager.onStop((res) => {
          let fileEmit = '';
          const { tempFilePath } = res;
          // wx.getFileSystemManager().readFile({
          //   filePath: tempFilePath,
          //   encoding: 'base64',
          //   success: (res) => {
          //     base64Data = res.data;
          //     // 将Base64编码后的文件数据作为查询参数的一部分
          //   }
          // });
          if (that.status == 1) {
            // 可以在这里处理录音文件，例如播放或上传
            wx.getFileSystemManager().saveFile({
              tempFilePath: tempFilePath,
              success(res) {
                const savedFilePath = res.savedFilePath;
                fileEmit = savedFilePath;
                const token = uni.getStorageSync('token');
                console.log(savedFilePath, '111111111111111');
                // 发送POST请求上传文件
                uni.uploadFile({
                  url: `${Config.DXHost}zx/common/uploadFile`, // 例如：'https://example.com/upload'
                  filePath: savedFilePath, // 这里直接使用文件路径，uni.uploadFile会自动处理文件读取和上传
                  name: 'file', // 后端接收文件时对应的字段名
                  formData: {
                    type: 'audio/mp3' // 根据实际情况设置MIME类型
                  }, // 如果需要额外添加其他表单字段
                  header: {
                    // anonymous_id: sensors.getAnonymousID(),
                    // Token: token,
                    'x-www-iap-assertion': token,
                    'dx-source': 'ZHEN_XUAN##WX##MINIAPP',
                    'temp-dx-source': 'ZHEN_XUAN##WX##MINIAPP'
                    // 'content-type': 'multipart/form-data', // 设置上传文件的Content-Type
                    // 你可以在这里添加其他自定义的HTTP头
                  },
                  success: (uploadFileRes) => {
                    let result = JSON.parse(uploadFileRes.data);
                    console.log(result.data, result.data.fileUrl, 'result.data.filePathresult.data.filePathresult.data.filePath');
                    that.$emit('submit', result.data.fileUrl, fileEmit);
                    // that.$emit('submit', base64Data, result.data.fileUrl);
                    // 处理上传成功后的逻辑
                  },
                  fail: (err) => {
                    console.error('uploadFile fail: ', err);
                    // 处理上传失败后的逻辑
                    uni.showToast({
                      title: '上传失败',
                      icon: 'none'
                    });
                  }
                });
              }
            });
          } else {
            console.log(333333333333333333333);
            uni.showToast({
              title: '已取消',
              icon: 'none'
            });
          }
        });
      }
      uni.getSystemInfo({
        success: (res) => {
          this.winSize = {
            witdh: res.windowWidth,
            height: res.windowHeight
          };
        }
      });
    },
    methods: {
      // 上传音频，并发送TODO对接上传音频接口
      async upload(file, type) {
        let res = await httpUser.post('zx/common/uploadFile', {
          file
        });
        let voice = res.data.data.fileUrl;
        this.audioSrc = voice;
      }
    }
  };
</script>
<style lang="scss" scoped>
  .audioPlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 999;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: flex-end;
  }
  .content {
    position: absolute;
    left: 0;
    bottom: 100rpx;
  }
  .close {
    margin-left: 80rpx;
    width: 110rpx;
    height: 110rpx;
  }
  .red {
    background-color: #ff3435;
  }
  .black {
    background-color: #000;
  }
  .audio-icon {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: 40%;
    width: 28rpx;
    height: 35rpx;
  }
  /* 上方语音动画 */
  .record-animate-box {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 300rpx;
    height: 140rpx;
    background-color: #ffffff;
    border-radius: 28rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s;
    &.active {
      background-color: #f56c6c;
      color: #fff;
      width: 140rpx;
    }
    /* 语音音阶 */
    .voice-scale {
      width: 60%;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .item {
        display: block;
        background: #2db265;
        width: 4rpx;
        height: 10%;
        margin-right: 2.5px;
        float: left;
        &.active {
          background-color: #fff;
        }
        &:last-child {
          margin-right: 0px;
        }
        &:nth-child(1) {
          animation: load 1s 0.8s infinite linear;
        }

        &:nth-child(2) {
          animation: load 1s 0.6s infinite linear;
        }

        &:nth-child(3) {
          animation: load 1s 0.4s infinite linear;
        }

        &:nth-child(4) {
          animation: load 1s 0.2s infinite linear;
        }

        &:nth-child(5) {
          animation: load 1s 0s infinite linear;
        }

        &:nth-child(6) {
          animation: load 1s 0.2s infinite linear;
        }

        &:nth-child(7) {
          animation: load 1s 0.4s infinite linear;
        }

        &:nth-child(8) {
          animation: load 1s 0.6s infinite linear;
        }

        &:nth-child(9) {
          animation: load 1s 0.8s infinite linear;
        }

        &:nth-child(10) {
          animation: load 1s 1s infinite linear;
        }
      }
    }

    @keyframes load {
      0% {
        height: 10%;
      }

      50% {
        height: 100%;
      }

      100% {
        height: 10%;
      }
    }
  }
</style>
