<template>
  <view class="bg-00 paly-video-css c-ff" @click="getVideo">
    <polyv-player
      id="polyv-player-id"
      :defaultQuality="startTime"
      :startTime="startTime"
      @loadedmetadata="bindloadedmetadata"
      @ended="bindEnded"
      :autoplay="true"
      :playerId="playerIdcont"
      :vid="videoInfo.videoUrl"
      :width="width"
      :height="height"
      :ts="ts"
      :sign="sign"
    ></polyv-player>
    <growthPopup ref="growthPopupRefs"></growthPopup>
  </view>
</template>

<script>
  import growthPopup from '../components/growthPopup.vue';
  const { $http } = require('@/util/methods.js');
  const MD5 = require('../../util/md5.js');
  let secretkey = 'Jkk4ml1Of8';
  let vid = '';
  let ts = new Date().getTime();
  let sign = '';
  export default {
    components: { growthPopup },
    data() {
      return {
        videoInfo: {},
        studyTime: 0,
        playerIdcont: 'polyvPlayercont',
        startTime: 0,
        ts: ts,
        sign: sign,
        width: '100%',
        height: '95%',
        eventType: ''
      };
    },
    onLoad(e) {
      this.eventType = e.eventType;
      uni.setNavigationBarTitle({
        title: e.eventType ? '家庭文化' : '读书文化'
      });
    },
    onReady() {
      let info = uni.getStorageSync('videoInfo');
      this.videoInfo = info;
      this.studyTime = info.studyTime;
      this.startTime = info.studyTime;
      console.log(this.videoInfo);
      this.getVideo();
    },
    onUnload() {
      this.closeVideo();
    },
    methods: {
      getVideo() {
        let _this = this;
        let polyvPlayerContext = _this.selectComponent('#polyv-player-id');
        const ts = new Date().getTime();
        const sign = MD5.md5(`${secretkey}${vid}${ts}`);
        polyvPlayerContext.changeVid({
          vid: _this.videoInfo.videoUrl,
          ts,
          sign
        });
      },
      bindloadedmetadata() {
        let polyvPlayerContext = this.selectComponent('#polyv-player-id');
        // polyvPlayerContext.pause()
        polyvPlayerContext.seek(Number(this.studyTime));
        polyvPlayerContext.switchQuality(1);
      },
      bindEnded() {
        if (this.eventType) {
          this.getGrowthValue();
        }
      },
      async closeVideo() {
        let polyvPlayerContext = this.selectComponent('#polyv-player-id');
        let learningProgress = (polyvPlayerContext.rCurrentTime / polyvPlayerContext.rDuration).toFixed(2);
        let rCurrentTime = polyvPlayerContext.rCurrentTime.toFixed(2);
        let _this = this;
        let courseType = this.eventType == 'LEARN' ? 1 : 2;
        const res = await $http({
          url:
            'zx/wap/recorded/course/saveLeanProcess?courseType=' +
            courseType +
            '&learningProgress=' +
            learningProgress * 100 +
            '&courseId=' +
            this.videoInfo.id +
            '&studyTime=' +
            rCurrentTime,
          showLoading: true,
          method: 'POST'
        });
        if (res) {
        }
      },
      async getGrowthValue() {
        const res = await $http({
          url: 'zx/wap/CultureCircle/getGrowthValue?eventType=' + this.eventType + '&userId=' + uni.getStorageSync('user_id'),
          method: 'POST',
          data: {}
        });
        if (res) {
          if (Number(res.data)) {
            this.$refs.growthPopupRefs.open(res.data);
          }
        }
      }
    }
  };
</script>

<style>
  .paly-video-css {
    height: 100vh;
  }
</style>
