<template>
  <view class="m-lesson-container">
    <view class="m-top-tabs">
      <view class="u-subsection">
        <u-subsection :list="courseTypeList" :current="currentIndex" @change="onSubsectionChange" activeColor="#2E896F" :fontSize="'32rpx'" mode="button" />
      </view>
      <view class="u-tabs">
        <u-tabs
          ref="tabs"
          :list="classList"
          @click="onClickHandle"
          :current="questParams.courseStatus"
          lineWidth="52rpx"
          lineColor="#2E896F"
          lineHeight="6"
          itemStyle="height: 34px;"
          :activeStyle="{ color: '#000', fontWeight: 'bold', fontSize: '30rpx', color: '#2E896F' }"
          :inactiveStyle="{ color: '#666', fontSize: '28rpx' }"
        ></u-tabs>

        <view class="u-tools" v-if="currentIndex !== 1">
          <view v-if="questParams.courseStatus !== 1" :class="{ active: showDatePickerModal === true }" class="date flex-dir-row" @click="showDatePickerModal = true">
            <u-icon :color="showDatePickerModal ? '#2E896F' : ''" :name="showDatePickerModal ? 'arrow-up-fill' : 'arrow-down-fill'" class="box-50" size="32"></u-icon>
            日期
            <u-badge :isDot="!!questParams.startStudyTime" type="error" />
          </view>
          <view :class="{ active: showCourseTypeModal === true }" class="all flex-dir-row" @click="showCourseTypeFilter">
            <u-icon :color="showCourseTypeModal ? '#2E896F' : ''" :name="showCourseTypeModal ? 'arrow-up-fill' : 'arrow-down-fill'" class="box-50" size="32"></u-icon>
            全部
            <u-badge :isDot="!!questParams.curriculumTypeId" type="error" />
          </view>
        </view>
      </view>
    </view>
    <view class="m-lesson-content">
      <!--伴学课-->
      <tutoring-class
        v-if="currentIndex === 0"
        style="display: flex; flex: 1; height: 0"
        :key="`tutoring-${currentIndex}-${questParams.courseStatus}-${questParams.curriculumTypeId}`"
        ref="tuToRing"
        :questParams="questParams"
      />
      <!--复习课-->
      <review-class style="display: flex; flex: 1; height: 0" :questParams="questParams" ref="reviewClass" v-if="currentIndex === 1" :key="`review-${currentIndex}`" />
    </view>
    <!--日历-->
    <u-datetime-picker
      @cancel="onDateCancel"
      itemHeight="68"
      @confirm="onDateConfirm"
      confirmColor="#2E896F"
      v-if="showDatePickerModal"
      :show="showDatePickerModal"
      v-model="startStudyTime"
      mode="date"
    ></u-datetime-picker>
    <!--课程类型-->
    <u-picker
      title="请选择课程类型"
      v-if="showCourseTypeModal"
      @cancel="cancelCourseModal"
      @confirm="confirmCourseModal"
      confirmColor="#2E896F"
      :loading="courseModalLoading"
      itemHeight="68"
      :immediateChange="true"
      :defaultIndex="defaultCourseIndex"
      :show="showCourseTypeModal"
      :columns="courseTypeOptions"
      keyName="enName"
    ></u-picker>
  </view>
</template>

<script>
  import TutoringClass from './tutoringClass.vue';
  import ReviewClass from './reviewClass.vue';
  import { formatTimestamp } from '@/util/util.js';

  export default {
    components: { ReviewClass, TutoringClass },
    data() {
      return {
        // tab相关
        currentIndex: 0,
        courseTypeList: [
          { name: '伴学课', courseType: '0' },
          { name: '复习课', courseType: '2' }
        ],
        classList: [{ name: '未上课' }, { name: '已上课' }],
        // 参数
        questParams: {
          merchantCode: '',
          courseStatus: 0, // 未上课0 已上课1
          curriculumTypeId: '', // 课程id
          startStudyTime: '' //筛选时间
        },
        startStudyTime: Number(new Date()), // 绑定值
        // 弹框相关
        showDatePickerModal: false, // 日历弹框
        showCourseTypeModal: false, //课程类型弹框
        courseModalLoading: false, // 课程loading
        courseTypeOptions: [],
        defaultCourseIndex: [0], // 默认第 0 个
        selectCourseTypeIndex: 0,
        courseLoading: false
      };
    },
    onLoad({ merchantCode }) {
      this.questParams.merchantCode = merchantCode;
    },
    watch: {
      currentIndex: {
        handler(newVal, oldVal) {
          const componentMap = {
            0: 'tuToRing',
            1: 'reviewClass'
          };
          const refName = componentMap[newVal] || 'tuToRing';
          this.$nextTick(() => {
            this.questParams.merchantCode && this.$refs[refName].getDeliverCoursePage(true);
          });
        },
        deep: true
      }
    },
    methods: {
      // 切换伴学课/复习课
      onSubsectionChange(index) {
        this.currentIndex = index;
        const selected = this.courseTypeList[index];
        console.log(selected, 'selected');
        this.questParams = {
          ...this.questParams,
          courseStatus: 0
        };
      },
      // 未上课 上课点击事件
      onClickHandle(item) {
        this.questParams = {
          ...this.questParams,
          courseStatus: item.index
        };
      },
      // 点击全部
      showCourseTypeFilter() {
        if (this.courseTypeOptions.length > 0) {
          this.showCourseTypeModal = true;
        } else {
          this.getCourseType();
        }
      },
      // 获取课程类型
      async getCourseType() {
        this.courseModalLoading = true;
        try {
          const res = await this.$httpUser.get('zx/student/course/getCourseType');
          const columns = res.data.data;
          this.courseTypeOptions = [columns] || [];
        } catch (error) {
          console.error('获取课程类型失败:', error);
          uni.showToast({
            title: '获取课程类型失败',
            icon: 'none'
          });
        } finally {
          this.courseModalLoading = false;
        }
      },
      // 日历确定
      onDateConfirm(e) {
        this.startStudyTime = formatTimestamp(e.value);
        this.questParams.startStudyTime = formatTimestamp(e.value);
        this.showDatePickerModal = false;
      },
      // 日期取消按钮
      onDateCancel() {
        this.questParams.startStudyTime = '';
        this.showDatePickerModal = false;
      },
      // 确定课程弹框
      confirmCourseModal(e) {
        console.log(e, '课程类型');
        this.questParams.curriculumTypeId = (e.value && e.value[0].id) || '';
        this.defaultCourseIndex = e.indexs || [0];
        this.showCourseTypeModal = false;
      },
      // 取消课程弹框
      cancelCourseModal() {
        this.questParams.curriculumTypeId = '';
        this.showCourseTypeModal = false;
      }
    }
  };
</script>
<style>
  page {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100vh;
  }
</style>
<style lang="scss" scoped>
  .m-lesson-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100vh;
  }

  .m-top-tabs {
    width: 100%;
    background: #fff;
  }

  .u-subsection {
    padding: 0 20rpx 10rpx 20rpx;
    flex-shrink: 0;
  }

  .u-tabs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60rpx;
    flex-shrink: 0;

    .u-tabs__wrapper {
      flex-wrap: nowrap !important;
      white-space: nowrap !important;
    }

    .u-tabs__item {
      flex-shrink: 0 !important;
    }
  }

  .u-tools {
    display: flex;
    font-size: 28rpx;
    padding: 0 18rpx;
    ::v-deep {
      .u-badge--dot {
        width: 10rpx;
        height: 10rpx;
      }
    }
  }

  .date,
  .all {
    min-width: 110rpx;
    height: 40rpx;
    color: #666;

    &.active {
      color: #428a6f;
    }
  }

  .m-lesson-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    flex: 1;
    overflow: auto;
  }
</style>
