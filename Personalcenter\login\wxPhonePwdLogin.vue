<template>
  <view style="min-height: 100vh; background-color: #fff">
    <!-- 这里是状态栏 -->
    <view class="status_bar">
      <!-- <image class="status_bar_image" :src="imgHost+'alading/correcting/login_bg1.png'" mode=""></image> -->
    </view>
    <uni-nav-bar class="nav-bar" color="#000" left-icon="left" title="用户名密码登录" :border="false" @clickLeft="back" />
    <view class="wechatapp">
      <image class="header" :src="imgHost + 'dxSelect/login-bgc.png'" mode="widthFix"></image>
    </view>

    <view class="login_btn_group1">
      <view class="login-box">
        <form id="#nform">
          <view class="phone-input">
            <image style="width: 28rpx; height: 29rpx" :src="imgHost + 'dxSelect/fourthEdition/icon_login_user.png'"></image>
            <input
              style="margin-left: 16rpx"
              type="number"
              :value="mobile"
              name="mobile"
              placeholder="请输入手机号"
              placeholder-class="phClass"
              class="input"
              maxlength="11"
              @input="changeInput($event)"
            />
          </view>

          <view class="code-input">
            <image style="width: 28rpx; height: 30rpx" :src="imgHost + 'dxSelect/fourthEdition/icon_login_ped.png'"></image>
            <input style="margin-left: 16rpx" type="password" v-model="passward" name="passward" placeholder="请输入密码" placeholder-class="phClass" class="input" />
          </view>
        </form>
      </view>

      <view class="tip" @click="changeischecked">
        <label class="radio" style="display: inline-block; transform: scale(0.6); margin-bottom: 20rpx">
          <radio value="r1" :checked="isChecked" color="#1D755C" />
        </label>
        我已阅读并同意
        <text @click.stop="goweburl('https://document.dxznjy.com/applet/agreeon/useragreement.html')">《用户服务协议》、</text>
        <text @click.stop="goweburl('https://document.dxznjy.com/applet/agreeon/privacypolicy.html')">《隐私政策》</text>
      </view>

      <button class="phone-btn" @tap.stop="getUserInfo">登录</button>
    </view>
  </view>
</template>

<script>
  import Superman from '@/common/superman.js';
  import UniNavBar from '@/components/uni-nav-bar/uni-nav-bar.vue';
  export default {
    components: {
      UniNavBar
    },
    data() {
      return {
        roleArray: [],
        roleSec: '',
        roleindex: 0,
        imgHost: getApp().globalData.imgHost,
        mobile: '',
        passward: '',
        MemberToken: '',
        wxCode: '',
        userInfo: {},
        count: 60,
        show: true, // 是否显示倒计时
        showBuyMember: false,
        showParentVipBuyMember: false,
        pwd: '',
        codeBtn: {
          text: '获取验证码',
          waitingCode: false,
          count: this.seconds
        },
        // isRegist: false, // 是否是注册
        isChecked: false,
        lastactive: false,
        isLogin: 'islogin', //是否登陆
        encryptedData: '',
        iv: '',
        code: '',
        token: '',
        userId: '',
        localip: '',
        count: '',
        imgHost: getApp().globalData.imgsomeHost,
        firstLogin: false
      };
    },
    watch: {
      mobile(val) {
        if (val.length == 11) {
          this.lastactive = true;
        } else {
          this.lastactive = false;
        }
      },
      passward(val) {
        if (val.length != 0) {
          this.lastactive = true;
        } else {
          this.lastactive = false;
        }
      }
    },

    onLoad() {},

    methods: {
      back() {
        uni.navigateBack();
      },
      //input输入
      changeInput(e) {
        console.log(e);
        this.mobile = e.target.value;
      },
      //验证表单
      validateForm: function () {
        if (this.mobile == '') {
          this.$util.alter('手机号码不能为空');
          return false;
        }
        if (this.mobile.length != 11) {
          this.$util.alter('手机号码必须是11位');
          return false;
        }
        if (this.passward == '') {
          this.$util.alter('密码不能为空');
          return false;
        }
        if (!this.isChecked) {
          this.$util.alter('请阅读并勾选下方协议');
          return false;
        }

        return true;
      },

      finishedOne(val) {
        this.passward = val;
        console.log(this.passward);
      },

      // 登录获取用户信息
      getUserInfo() {
        if (!this.validateForm()) {
          return false;
        }
        this.getkey();
      },

      getkey() {
        uni.showLoading({
          title: '登录中...'
        });
        var that = this;
        uni.login({
          provider: 'weixin',
          success: (res) => {
            that.code = res.code;
            if (that.code) {
              that.$httpUser
                .get('zx/common/decodeWechat', {
                  code: that.code,
                  mobile: that.mobile,
                  shareId: uni.getStorageSync('referee_id') || ''
                })
                .then((request) => {
                  let key = request.data.data.key;
                  if (key) {
                    that.getLogin(key);
                    //埋点-登录按钮
                    getApp().sensors.track('loginClick', {
                      name: '登录按钮'
                    });
                  } else {
                    that.$util.alter(res.data.message);
                  }
                })
                .catch(() => {
                  uni.hideLoading();
                });
            }
          },
          fail: (err) => {
            uni.hideLoading();
            console.log(err);
          }
        });
      },

      //wx 登录
      getLogin(key) {
        var that = this;
        // uni.showLoading();
        that.$httpUser
          .get('zx/common/ifFirstLogin', {
            mobile: this.mobile
          })
          .then((res) => {
            this.firstLogin = res.data.data;
          });
        that.$httpUser
          .get('new/security/login/mini', {
            wxCode: key,
            username: that.mobile,
            role: 'Member',
            password: that.passward
          })
          .then((res) => {
            uni.hideLoading();
            if (res.data.success) {
              uni.setStorageSync('token', res.data.data.token);
              uni.removeStorageSync('current');
              that.loginHandle();
            } else {
              that.$util.alter(res.data.message);
            }
          });
      },
      getLocalIPAddress() {
        return new Promise((resolve, reject) => {
          wx.getLocalIPAddress({
            success(res) {
              resolve(res.localip);
            },
            fail(err) {
              reject(err);
            }
          });
        });
      },
      async loginHandle() {
        let that = this;
        try {
          that.localip = await that.getLocalIPAddress();
        } catch (e) {
          //TODO handle the exception
        }

        // 扫码进入---绑定合伙人
        let needBind = uni.getStorageSync('scanInfo');
        if (needBind) {
          const merchantInfo = JSON.parse(needBind);
          let params = {
            id: merchantInfo.qrId,
            merchantCode: merchantInfo.merchantCode
          };
          let bindRes = await Superman.bindMerchantByScan(params);
          if (bindRes == 'success') {
            uni.removeStorageSync('scanInfo');
          }
        }

        let userinfo = await Superman.getUserInfo();
        let date = new Date();
        let year = date.getFullYear();
        let month = (date.getMonth() + 1).toString().padStart(2, '0');
        let day = date.getDate().toString().padStart(2, '0');
        date = `${year}-${month}-${day}`;
        date = new Date(date);
        const date2 = new Date(userinfo.expireTime);
        const diffTime = date2 - date;
        const diffDays = diffTime / (1000 * 60 * 60 * 24);
        /** 家长会员过期 */
        if (userinfo.parentMemberEndTime) {
          const vipDate2 = new Date(userinfo.parentMemberEndTime);
          const vipDiffTime = vipDate2 - date;
          const vipDiffDays = vipDiffTime / (1000 * 60 * 60 * 24);
          console.log(vipDiffDays);
          if (vipDiffDays <= 30) {
            this.showParentVipBuyMember = true;
            uni.setStorageSync('showParentVipBuyMember', this.showParentVipBuyMember);
          } else {
            this.showParentVipBuyMember = false;
            uni.setStorageSync('showParentVipBuyMember', this.showParentVipBuyMember);
          }
        }
        if (diffDays <= 30) {
          this.showBuyMember = true;
          uni.setStorageSync('showBuyMember', this.showBuyMember);
        } else {
          this.showBuyMember = false;
          uni.setStorageSync('showBuyMember', this.showBuyMember);
        }
        uni.setStorageSync('localip', that.localip);
        uni.setStorageSync('merchantCode', userinfo.merchantCode);
        uni.setStorageSync('user_id', userinfo.userId);
        uni.setStorageSync('user_code', userinfo.userCode);
        uni.setStorageSync('identityType', userinfo.identityType);
        uni.setStorageSync('parentMemberType', userinfo.parentMemberType);
        let shareid = uni.getStorageSync('shareId') ? uni.getStorageSync('shareId') : '';
        let activityid = uni.getStorageSync('activityId') ? uni.getStorageSync('activityId') : '';
        uni.setStorageSync('phone', userinfo.mobile);
        that.$util.alter('登录成功');
        uni.navigateBack({
          delta: 2
        });
        if (shareid === '' || this.firstLogin === false) {
          return;
        }
        that.$httpUser.post('zx/wap/invite/saveInviteData', {
          userId: shareid,
          activityId: activityid,
          type: '1',
          inviteeOpenId: userinfo.openId,
          inviteeNickName: userinfo.nickName,
          inviteePhone: userinfo.mobile,
          ipAddress: that.localip
        });
      },

      // 同意已阅读用户服务协议
      changeischecked() {
        this.isChecked = !this.isChecked;
        if (this.mobile.length == 11 && this.passward.length != 0 && this.isChecked) {
          this.lastactive = true;
        } else {
          this.lastactive = false;
        }
      },

      // 跳转H5用户服务协议，隐私政策
      goweburl(url) {
        uni.navigateTo({
          url: `/pages/index/web?url=${url}`
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .container {
    // text-align: center;
    padding: 0 80rpx;
  }

  .wechatapp {
    margin-top: 150rpx;
    margin-bottom: 120rpx;
  }

  .wechatapp .header {
    width: 100%;
    margin: 0rpx auto 0;
  }

  .auth-title {
    color: #585858;
    font-size: 40rpx;
    margin-bottom: 40rpx;
  }

  .title {
    color: #000;
    font-weight: 700;
    font-size: 40rpx;
  }

  .tip {
    font-size: 24rpx;
    text-align: center;
    // margin-top: 20rpx;
    color: #999999;
  }

  .login-top {
    display: block;
    width: 240rpx;
    height: 240rpx;
    margin: 0 auto !important;
  }

  .login-box {
    padding-bottom: 30rpx;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#7Fd2f9f6, endColorstr=#7Fd2f9f6);
    border-radius: 10rpx;
  }

  .phone-input {
    background: #fff;
    border-radius: 45rpx;
    height: 90rpx;
    font-size: 28rpx;
    color: #000;
    display: flex;
    padding-left: 40rpx;
    align-items: center;
    border: 3rpx solid #ececea;
  }

  .code-input {
    background: #fff;
    border-radius: 45rpx;
    font-size: 28rpx;
    color: #000;
    height: 90rpx;
    display: flex;
    padding: 0 40rpx;
    margin-top: 30rpx;
    align-items: center;
    border: 3rpx solid #ececea;
  }

  .verification {
    color: #ea6531;
    font-size: 26rpx;
    padding-left: 18rpx;
    border-left: 1px solid #c8c8c8;
    float: right;
  }

  .sendout {
    color: #999999;
  }

  /deep/.phone-btn {
    width: 100%;
    height: 90rpx;
    line-height: 90rpx;
    border-radius: 45rpx;
    font-size: 30rpx;
    margin-top: 76rpx;
    color: #fff;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
  }

  // .phClass {
  // 	color: #999999;
  // 	font-size: 20rpx;
  // }

  .auth-subtitle {
    color: #888;
    margin-bottom: 88rpx;
  }

  .login-btn {
    border: none;
    height: 88rpx;
    line-height: 88rpx;
    background: #04be01;
    /* #ifdef MP-ALIPAY */
    background: #1890ff;
    /* #endif */
    color: #fff;
    font-size: 11pt;
    border-radius: 999rpx;
  }

  .login-btn::after {
    display: none;
  }

  .login-btn.button-hover {
    box-shadow: inset 0 5rpx 30rpx rgba(0, 0, 0, 0.15);
  }

  .login-cancle {
    text-align: center;
    border: none;
    height: 88rpx;
    line-height: 88rpx;
    color: #a8a8a8;
    font-size: 11pt;
    border-radius: 999rpx;
  }

  .btn_flex {
    display: flex;
    justify-content: space-around;
    margin-top: 750rpx;
  }

  /deep/.card-btn {
    width: 100%;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 40rpx;
    font-size: 30rpx;
    padding-left: 14rpx;
    padding-right: 14rpx;
    color: #fff;
    background-color: #28c445;
  }

  /deep/.wx-btn {
    width: 35%;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 10rpx;
    font-size: 32rpx;
    padding-left: 14rpx;
    padding-right: 14rpx;
    color: #fff;
    background-color: #169bd5;
  }

  .nav-bar {
    position: fixed;
    width: 100%;
    z-index: 999;
  }

  /deep/.uni-nav-bar-text {
    font-size: 34rpx !important;
    font-weight: bold;
  }

  /deep/.uni-navbar__header {
    background-color: transparent !important;
  }

  /deep/.uni-navbar__content {
    background-color: transparent !important;
  }
</style>
