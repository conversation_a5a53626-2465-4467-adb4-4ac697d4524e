<template>
	<view class="">
		<scroll-view scroll-y="true" class="scroll-container">
			<view class="center-view">
				<view v-if="courseData.goodsList && courseData.goodsList.length > 0" class="course-container">
					<course-list v-for="(item,index) in courseData.goodsList" :key="index" :item="item" :index="index"
						:width="'326rpx'"
						@click="skintap('Coursedetails/productDetils?id=' + item.goodsId, item.goodsId)"></course-list>
				</view>
				<!-- <view v-else class="empty-data">
					<view class="empty-text">暂无数据</view>
				</view> -->
			</view>
		</scroll-view>
	</view>
</template>

<script>
	const {
		$navigationTo,
		$http
	} = require('@/util/methods.js');
	import courseList from '@/components/course-list/course-list.vue'
	export default {
		components: {
			courseList
		},
		data() {
			return {
				courseData: [],
				categoryId: '' //；了解更多id
			};
		},
		onLoad(option) {
			console.log(option, '了解更多-------')
			this.categoryId = option.categoryId

		},
		onShow() {
			this.getDetail()
		},
		methods: {
			async getDetail() {
				uni.showLoading({
					title: '加载中'
				});
				const res = await $http({
					url: 'zx/wap/homePage/category/detail',
					data: {
						categoryId: this.categoryId,
						userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
					}
				});
				console.log('完整返回数据:', res)
				if (res.code == 20000) {
					uni.setNavigationBarTitle({
						title: res.data.categoryName
					});
					this.courseData = res.data
					uni.hideLoading()
				} else {
					uni.hideLoading()
				}

			},
			skintap(url, goodsId) {
				// if (!uni.getStorageSync('token')) {
				//   uni.navigateTo({
				//     url: '/Personalcenter/login/login'
				//   });
				// } else {
				//埋点-课程/商品
				// getApp().sensors.track('selectionClick', {
				// 	name: this.goodsCase == 2 ? '课程' : '商品',
				// 	goodsId: goodsId
				// });
				$navigationTo(url);
				// }
			},




		}
	};
</script>

<style lang="scss" scoped>
	.center-view {
		width: 100%;
	}

	.title-center {
		text-align: center;
		margin-bottom: 20rpx;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}

	.course-container {
		padding: 10rpx 30rpx;
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		// gap: 24rpx;
		// margin: 0 auto;
		// max-width: 710rpx;
	}

	/* 滚动容器 */
	.scroll-container {
		height: 100vh;
	}

	/* 暂无数据样式 */
	// .empty-data {
	// 	width: 100%;
	// 	height: 500rpx;
	// 	display: flex;
	// 	flex-direction: column;
	// 	justify-content: center;
	// 	align-items: center;

	// 	// .empty-image {
	// 	// 	width: 200rpx;
	// 	// 	height: 200rpx;
	// 	// 	margin-bottom: 20rpx;
	// 	// }

	// 	.empty-text {
	// 		font-size: 28rpx;
	// 		color: #999999;
	// 	}
	// }
</style>