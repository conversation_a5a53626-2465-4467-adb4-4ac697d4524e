<!-- 学习打印页面 -->
<template>
  <view class="dictation_warpper">
    <view class="dictation_box" v-if="!startWrite">
      <view class="bg_head">
        <image src="https://document.dxznjy.com/applet/newimages/body_bg_head.png" class="" mode="widthFix" />
        <view class="Countdown" v-if="showTime">
          <image src="https://document.dxznjy.com/applet/newimages/countdown.png" class="countdown" mode="widthFix" />
          <div>{{ endTime + 's' }}</div>
        </view>
      </view>
      <view class="word_box">
        <div class="wordText">{{ wordList[answerIndex].word }}</div>
      </view>
      <view class="word_CN">
        <div class="wordText">{{ wordList[answerIndex].chinese }}</div>
      </view>
      <view class="Record_warpper">
        <view>
          <image v-if="!isRecording" @click="startRecord()" src="https://document.dxznjy.com/applet/newimages/record.png" class="record" />
          <image v-else @click="stopRecord()" src="https://document.dxznjy.com/applet/newimages/stop.png" class="record" />
        </view>
        <view>
          <image v-if="answerStaus == '1'" src="https://document.dxznjy.com/applet/newimages/right_active.png" class="right" />
          <image @click="changeStaus('1')" v-else src="https://document.dxznjy.com/applet/newimages/right.png" class="right" mode="widthFix" />
          <image v-if="answerStaus == '2'" src="https://document.dxznjy.com/applet/newimages/error_active.png" class="error" />
          <image @click="changeStaus('2')" v-else src="https://document.dxznjy.com/applet/newimages/error.png" class="error" mode="widthFix" />
        </view>
      </view>
    </view>
    <write-test
      v-if="startWrite"
      :wordList="wordList"
      :writeDuration="writeDuration"
      :studentCode="studentCode"
      :startTime="startTime"
      ref="writeTest"
      :key="writeKey"
    ></write-test>
    <view class="dictation_footer" v-if="!startWrite">
      <!-- 开始答题 -->
      <view class="dictation_btn">
        <view class="lastTest" @click="dontKnow">不会</view>
        <view class="startTest" @click="nextWord">下一题</view>
      </view>
      <view class="answerToast" v-if="showScore">
        <view class="suggestedScore">评分标准：{{ suggestedScore }}</view>
        <view class="wordScore">你的评分：{{ wordScore }}</view>
      </view>
    </view>
  </view>
</template>

<script>
  import sensors from 'sa-sdk-miniprogram';
  import writeTest from './writeTest.vue';
  export default {
    components: {
      writeTest
    },
    data() {
      return {
        intervalId: null,
        endTime: 0,
        useHeight: 0,
        recorderManager: null,
        showScore: false,
        suggestedScore: 100, // 发音评分标准
        wordScore: 0, // 发音评分标准
        isPause: false,
        isRecording: false,
        writeDuration: '',
        wordList: [],
        answerIndex: 0, //答题序号
        ishasPermission: false, //是否获取录音权限
        startWrite: false, //是否开始拼写,
        startTime: '', //开始答题时间
        notKnowNum: 0, //记录不会或者做错的次数
        readAnswer: {
          singleList: [],
          manyList: [],
          suffixList: []
        }, //发音答案
        studentCode: '', //学生code
        optionmatch: '',
        answerStaus: '0', //答题状态  0:未答题 ；1:答对 ；2:答错或不会
        hasChangeStaus: false,
        hasRecord: false, //有过发音练习
        showTime: false,
        writeKey: 0
        // hideStatus:false
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 560;
        }
      });
    },
    onLoad(option) {
      console.log(option, '传参');
      if (option != null && option.q != undefined) {
        let code = decodeURIComponent(option.q);
        var match = code.match(/[?&]studentCode=([^&]+)/);
        this.optionmatch = match[1];
      }
      /* if (option != null && option.studentCode != undefined) {
					this.studentCode = option.studentCode;
				} */
    },
    onHide() {
      clearInterval(this.intervalId);
      this.intervalId = null;
      // this.hideStatus=true
      if (this.startWrite) {
        this.$nextTick(() => {
          const child = this.$refs.writeTest;
          if (child) {
            uni.setStorageSync('WriteEndTime', child.endTime);
            clearInterval(child.timer);
          }
        });
      }
    },
    async onShow() {
      let result = await this.$httpUser.get('znyy/review/query/my/student');
      if (result.data.data.some((student) => student.studentCode === this.optionmatch)) {
        this.studentCode = this.optionmatch;
      } else {
        uni.showToast({
          icon: 'none',
          title: '登录用户无权限操作,请登录正确账号',
          duration: 3000
        });
        setTimeout(() => {
          uni.setStorageSync('token', '');
          uni.navigateTo({
            url: '/Personalcenter/login/login'
          });
        }, 3000);
        return;
      }
      if (!this.startWrite) {
        if (this.intervalId || this.endTime > 0) {
          clearInterval(this.intervalId);
        }
        await this.getConfigInfo();
        this.initOnlibeRecord();
        this.getRecordPermission();
        (this.readAnswer = {
          singleList: [],
          manyList: [],
          suffixList: []
        }),
          (this.notKnowNum = 0);
        this.answerIndex = 0;
      }
      if (this.startWrite) {
        this.$nextTick(() => {
          const child = this.$refs.writeTest;
          if (child) {
            clearInterval(child.timer);
            child.writeIndex = 0;
            child.endTime = Number(this.writeDuration) * 60;
            child.showTime = this.writeDuration == '0' ? false : true;
            child.homeData();
            child.startCountdown(child.endTime);
            child.initAudio();
            child.writeWord = '';
            child.notWriteNum = 0;
            child.writeAnswer = {
              singleList: [],
              manyList: [],
              suffixList: []
            };
          }
        });
      }
    },
    onUnload: function () {
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
      }
      this.writeKey = 0;
    },
    watch: {
      endTime(val) {
        if (val <= 0) {
          this.checkIsOvertest('1');
          if (this.recorderManager && this.hasRecord) {
            this.recorderManager.stop();
          }
        }
      }
    },
    methods: {
      checkIsOvertest(type) {
        //type为1是倒计时结束 2连续不会
        let _this = this;
        if (_this.answerIndex < _this.wordList.length - 1) {
          _this.wordList.forEach((item, index) => {
            if (_this.answerIndex <= index) {
              const studyType = item.studyType;
              item.isSuccess = '-1';
              switch (studyType) {
                case 'DYJ1':
                  _this.readAnswer.singleList.push(item);
                  break;
                case 'DYJ2':
                  _this.readAnswer.manyList.push(item);
                  break;
                case 'QHZ':
                  _this.readAnswer.suffixList.push(item);
                  break;
                default:
                  break;
              }
            }
          });
        }
        if (type === '1') {
          uni.showToast({
            icon: 'none',
            title: '倒计时结束,进入拼写测试',
            duration: 2000
          });
        } else {
          uni.showToast({
            icon: 'none',
            title: '您已进入拼写测试',
            duration: 2000
          });
        }

        uni.setStorageSync('readAnswer', _this.readAnswer);
        clearInterval(_this.intervalId);
        this.startWrite = true;
      },
      changeStaus(type) {
        this.hasChangeStaus = true;
        this.answerStaus = type;
      },
      //倒计时
      startCountdown(seconds) {
        console.log(seconds, '传入倒计时');
        let _this = this;
        // 确保秒数是整数
        // seconds = Math.round(seconds);
        // 检查秒数是否有效
        if (seconds < 0) {
          return;
        }
        // 定时器ID，用于稍后清除定时器
        // 倒计时函数
        function countdown() {
          _this.endTime = seconds;
          // 每秒执行一次
          seconds--;
          // 如果倒计时结束，清除定时器并打印消息
          if (seconds < 0) {
            _this.endTime = 0;
            clearInterval(_this.intervalId);
          }
        }
        // 启动定时器
        _this.intervalId = setInterval(countdown, 1000);
      },
      //获取测试配置
      async getConfigInfo() {
        let result = await this.$httpUser.get('znyy/pd/mobile/getConfigInfo');
        if (result != undefined && result != '') {
          if (result.data.data != null) {
            const courseMsg = result.data.data.data;
            this.suggestedScore = Number(courseMsg.suggestedScore);
            this.endTime = Math.ceil(Number(courseMsg.readDuration) * 60);
            this.showTime = courseMsg.readDuration == '0' ? false : true;
            this.writeDuration = courseMsg.writeDuration;
            this.getConfigWord(courseMsg.id);
          }
        }
      },
      //获取测试单词
      async getConfigWord(id) {
        let result = await this.$httpUser.get('znyy/pd/mobile/getAllConfigWord', {
          id: id
        });
        if (result != undefined && result != '') {
          if (result.data.data != null) {
            this.wordList = [];
            const wordObj = result.data.data.data;
            this.wordList = wordObj.singleList.concat(wordObj.manyList).concat(wordObj.suffixList);
            console.log(this.wordList, '单词数量');
          }
        }
      },
      // 初始化录音组件
      initOnlibeRecord() {
        this.recorderManager = uni.getRecorderManager();
        let _this = this;
        // 监听对象的stop事件

        // 监对象的start事件
        this.recorderManager.onStart(function (res) {
          console.log('录音start');
        });
        // 录音错误处理
        this.recorderManager.onError((error) => {
          console.error('录音错误:', error);
        });
      },
      //记录开始答题的时间
      initStartTime() {
        let _this = this;
        let date = new Date();
        let year = date.getFullYear();
        // 注意：JavaScript 中的月份是从 0 开始的，所以需要 +1
        let month = (date.getMonth() + 1).toString().padStart(2, '0');
        // 类似地，日期和小时、分钟、秒也可能只有一位数，所以需要 padStart
        let day = date.getDate().toString().padStart(2, '0');
        let hours = date.getHours().toString().padStart(2, '0');
        let minutes = date.getMinutes().toString().padStart(2, '0');
        let seconds = date.getSeconds().toString().padStart(2, '0');
        _this.startTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      },
      // 开始录音
      startRecord() {
        let _this = this;
        _this.isRecording = true;
        _this.hasRecord = true;
        _this.recorderManager.start({
          format: 'mp3',
          duration: 600000,
          success: function () {
            console.log('录音开始');
          },
          fail: function (err) {
            console.error('录音失败：', err);
          }
        });
      },
      //结束录音
      stopRecord() {
        let _this = this;
        _this.recorderManager.pause();
        _this.recorderManager.stop();
        _this.recorderManager.onStop(async (res) => {
          const { tempFilePath } = res;
          // 可以在这里处理录音文件，例如播放或上传
          let word = _this.wordList[_this.answerIndex].word;
          wx.getFileSystemManager().saveFile({
            tempFilePath: tempFilePath,
            success(res) {
              const savedFilePath = res.savedFilePath;
              const token = uni.getStorageSync('token');
              // 发送POST请求上传文件
              uni.uploadFile({
                url: 'https://gateway.dxznjy.com/znyy/pd/mobile/wordRecognition', // 例如：'https://example.com/upload'
                filePath: savedFilePath, // 这里直接使用文件路径，uni.uploadFile会自动处理文件读取和上传
                name: 'file', // 后端接收文件时对应的字段名
                formData: {
                  word: word,
                  type: 'audio/mp3' // 根据实际情况设置MIME类型
                }, // 如果需要额外添加其他表单字段
                header: {
                  anonymous_id: sensors.getAnonymousID(),
                  Token: token,
                  'x-www-iap-assertion': token,
                  'dx-source': 'ZHEN_XUAN##WX##MINIAPP'
                  // 'content-type': 'multipart/form-data', // 设置上传文件的Content-Type
                  // 你可以在这里添加其他自定义的HTTP头
                },
                success: (uploadFileRes) => {
                  // 处理上传成功后的逻辑
                  _this.isRecording = false;
                  _this.putAnswer(uploadFileRes);
                  _this.initOnlibeRecord();
                },
                fail: (err) => {
                  console.error('uploadFile fail: ', err);
                  _this.isRecording = false;
                  _this.initOnlibeRecord();
                  // 处理上传失败后的逻辑
                }
              });
            }
          });
        });
      },
      //收集答案
      putAnswer(uploadFileRes, handData) {
        let _this = this;
        const wordObj = _this.wordList[_this.answerIndex];
        let obj = {
          ...wordObj
        };
        const studyType = wordObj.studyType;
        if (handData) {
          let wordScore = Number(handData.data.pronAccuracy).toFixed(0);
          obj.isSuccess = wordScore >= _this.suggestedScore ? '1' : '-1';
        } else {
          let param = JSON.parse(uploadFileRes.data);
          obj.wordRecognitionInfo = JSON.stringify(param.data.data.words[0]);
          //判断发音是否达到评分标准
          let wordScore = Number(param.data.data.words[0].pronAccuracy).toFixed(0);
          obj.isSuccess = wordScore >= _this.suggestedScore ? '1' : '-1';
          if (wordScore) {
            this.wordScore = wordScore;
            this.showScore = true;
            setTimeout(() => {
              this.showScore = false;
            }, 2000);
          }
        }

        let index = -1;
        switch (studyType) {
          case 'DYJ1':
            index = _this.readAnswer.singleList.findIndex((item) => item.id === obj.id);
            if (index !== -1) {
              _this.readAnswer.singleList[index] = obj; // 替换对象
            } else {
              _this.readAnswer.singleList.push(obj); // 如果没找到，则添加对象
            }
            break;
          case 'DYJ2':
            index = _this.readAnswer.manyList.findIndex((item) => item.id === obj.id);
            if (index !== -1) {
              _this.readAnswer.manyList[index] = obj; // 替换对象
            } else {
              _this.readAnswer.manyList.push(obj); // 如果没找到，则添加对象
            }
            break;
          case 'QHZ':
            index = _this.readAnswer.suffixList.findIndex((item) => item.id === obj.id);
            if (index !== -1) {
              _this.readAnswer.suffixList[index] = obj; // 替换对象
            } else {
              _this.readAnswer.suffixList.push(obj); // 如果没找到，则添加对象
            }
            break;
          default:
            break;
        }
        uni.setStorageSync('readAnswer', this.readAnswer);
        _this.answerStaus = '0';
        _this.answerIndex++;
        if (_this.answerIndex > _this.wordList.length - 1) {
          clearInterval(_this.intervalId);
          _this.answerIndex = 0;
          _this.startWrite = true;
        }
      },
      startTest() {
        if (!this.startWrite) {
          uni.showToast({
            icon: 'none',
            title: '点击录音按钮开始答题',
            duration: 2000
          });
          console.log(this.endTime, '倒计时');
          this.startCountdown(this.endTime);
          this.initStartTime();
        }
      },
      //不会
      dontKnow() {
        let _this = this;
        _this.notKnowNum++;
        const wordObj = _this.wordList[_this.answerIndex];
        const studyType = wordObj.studyType;
        let obj = {
          ...wordObj
        };
        if (_this.notKnowNum === 5) {
          this.checkIsOvertest('2');
          return;
        }
        //判断发音是否达到评分标准
        obj.isSuccess = '-1';
        _this.readAnswer.singleList.push(obj);
        let index = -1;
        switch (studyType) {
          case 'DYJ1':
            index = _this.readAnswer.singleList.findIndex((item) => item.id === obj.id);
            if (index !== -1) {
              _this.readAnswer.singleList[index] = obj; // 替换对象
            } else {
              _this.readAnswer.singleList.push(obj); // 如果没找到，则添加对象
            }
            break;
          case 'DYJ2':
            index = _this.readAnswer.manyList.findIndex((item) => item.id === obj.id);
            if (index !== -1) {
              _this.readAnswer.manyList[index] = obj; // 替换对象
            } else {
              _this.readAnswer.manyList.push(obj); // 如果没找到，则添加对象
            }
            break;
          case 'QHZ':
            index = _this.readAnswer.suffixList.findIndex((item) => item.id === obj.id);
            if (index !== -1) {
              _this.readAnswer.suffixList[index] = obj; // 替换对象
            } else {
              _this.readAnswer.suffixList.push(obj); // 如果没找到，则添加对象
            }
            break;
          default:
            break;
        }
        uni.setStorageSync('readAnswer', this.readAnswer);
        console.log('33333', uni.getStorageSync('readAnswer'));
        _this.isRecording = false;
        if (_this.recorderManager) {
          _this.recorderManager.stop();
        }
        _this.answerStaus = '0';
        _this.answerIndex++;
        if (this.answerIndex > this.wordList.length - 1) {
          clearInterval(this.intervalId);
          this.answerIndex = 0;
          this.startWrite = true;
        }
      },
      //下一题
      nextWord() {
        let _this = this;
        if (_this.answerStaus === '1') {
          this.notKnowNum = 0;
        } else {
          this.notKnowNum++;
          if (this.notKnowNum === 5) {
            this.checkIsOvertest('2');
            return;
          }
        }
        if (this.hasChangeStaus) {
          const handData = {
            data: {
              pronAccuracy: this.answerStaus == '1' ? '100' : '0'
            }
          };
          this.putAnswer({}, handData);
          this.hasChangeStaus = false;
          this.answerStaus = '0';
          if (this.answerIndex > this.wordList.length - 1) {
            clearInterval(this.intervalId);
            this.answerIndex = 0;
            this.startWrite = true;
            this.isRecording = false;
            if (_this.recorderManager) {
              _this.recorderManager.stop();
            }
            return;
          }
          this.isRecording = false;
          if (_this.recorderManager) {
            _this.recorderManager.stop();
          }
          return;
        }
        if (this.isRecording) {
          uni.showToast({
            title: '正在录音中',
            icon: 'none'
          });
          return;
        }
        if (this.answerIndex == 0) {
          return;
        }
        uni.showToast({
          title: '尚未进行任何操作',
          icon: 'none'
        });
      },
      // 校验录音权限
      getRecordPermission() {
        let _this = this;
        uni.setStorageSync('readAnswer', {
          singleList: [],
          manyList: [],
          suffixList: []
        });
        console.log('4444', uni.getStorageSync('readAnswer'));
        uni.getSetting({
          success(res) {
            if (res.authSetting['scope.record']) {
              /* _this.onlineConf.onlineStep++; */
              _this.ishasPermission = true;
              _this.startTest();
              return true;
            } else {
              uni.authorize({
                scope: 'scope.record',
                success() {
                  _this.isFinishAudio = false;
                  _this.ishasPermission = true;
                  _this.startTest();
                  return true;
                },
                fail(res) {
                  uni.showToast({
                    title: '请点击右上角“…”功能菜单，进入设置界面，打开麦克风权限后，再重新录音',
                    icon: 'none',
                    duration: 2000
                  });
                  _this.ishasPermission = false;
                  return false;
                }
              });
            }
          }
        });
      },
      // 息屏处理
      destoryTimer() {}
    }
  };
</script>

<style lang="scss">
  .dictation_warpper {
    width: 100%;
    height: auto;
    min-height: 100vh;
    background: #f0f8f0;
  }

  .dictation_box {
    width: 686rpx;
    height: 830rpx;
    position: relative;
    background-image: url('https://document.dxznjy.com/applet/newimages/body_bg.png');
    background-size: cover;
    background-repeat: no-repeat;
    margin: 0 auto;
    box-sizing: border-box;
    margin-top: 188rpx;
    padding-top: 220rpx;

    .bg_head {
      position: absolute;
      left: 196rpx;
      top: -40rpx;

      image {
        width: 294rpx;
        height: 174rpx;
      }

      .Countdown {
        width: 294rpx;
        height: 50rpx;
        background: #f3ffe1;
        border-radius: 49rpx;
        display: flex;
        font-size: 24rpx;
        align-items: center;
        justify-content: center;

        image {
          width: 25rpx;
          height: 25rpx;
          margin-right: 10rpx;
        }
      }
    }

    .word_box {
      width: 523rpx;
      height: 126rpx;
      margin: 0 auto;
      margin-top: 30rpx;
      background: #f3f3f3;
      border-radius: 24rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .wordText {
        height: 84rpx;
        font-size: 60rpx;
        color: #333333;
        line-height: 84rpx;
        text-align: left;
      }
    }

    .word_CN {
      margin: 0 auto;
      margin-top: 24rpx;
      font-size: 32rpx;
      color: #666666;
      line-height: 44rpx;
      text-align: center;
    }

    .Record_warpper {
      margin: 0 auto;
      margin-top: 144rpx;
      width: 523rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .record {
        width: 164rpx;
        height: 164rpx;
      }

      .right,
      .error {
        width: 130rpx;
        height: 140rpx;
        margin-right: 20rpx;
      }
    }
  }

  .dictation_footer {
    width: 100%;
    margin-top: 60rpx;
    font-size: 40rpx;
    display: flex;
    justify-content: center;

    .dictation_btn {
      display: flex;
      justify-content: center;
    }

    .lastTest {
      width: 312rpx;
      height: 94rpx;
      line-height: 94rpx;
      margin-right: 30rpx;
      color: #931413;
      box-sizing: border-box;
      background-image: url('https://document.dxznjy.com/applet/newimages/lastTest.png');
      background-size: cover;
      background-repeat: no-repeat;
      text-align: center;
    }

    .startTest {
      width: 312rpx;
      height: 102rpx;
      line-height: 102rpx;
      color: #178071;
      box-sizing: border-box;
      background-image: url('https://document.dxznjy.com/applet/newimages/startTest.png');
      background-size: cover;
      background-repeat: no-repeat;
      text-align: center;
    }

    .dictation_btn {
      width: 100%;
      display: flex;
      align-items: center;
    }

    .answerToast {
      position: absolute;
      top: calc(50% - 180rpx);
      left: calc(50% - 150rpx);
      text-align: center;
      font-size: 32rpx;
      width: 300rpx;
      height: 180rpx;
      white-space: nowrap;
      background-color: rgba(0, 0, 0, 0.7);
      border-radius: 6rpx;
      display: flex;
      align-items: center;
      flex-direction: column;
      color: #ffffff;

      .suggestedScore {
        margin-top: 35rpx;
      }

      .wordScore {
        color: #6cb4fe;
        margin-top: 10rpx;
      }
    }
  }
</style>
