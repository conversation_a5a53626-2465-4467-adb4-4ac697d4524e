<template>
  <view class="funHead" :style="{ marginTop: top + 'rpx' }">
    <view class="funHead_left back_top backSize" @click="backPage()">
      <image
        style="height: 64rpx; width: 64rpx"
        :src="blue ? 'https://document.dxznjy.com/course/7540abf90c3b4bb4812350e4489ecc9e.png' : 'https://document.dxznjy.com/course/daf45b9c36a54361baddb17771c58f22.png'"
        mode=""
      ></image>
    </view>

    <view v-if="title != ''" class="funHead_center">
      <text :class="hasTitleBg ? (dark ? 'funHead_center_text_bg dark' : 'funHead_center_text_bg') : 'funHead_center_text'">{{ title }}</text>
    </view>
    <view v-if="hasRight" class="funHead_right" @click="clickHeadRight()">
      <text>{{ rightText }}</text>
    </view>
  </view>
</template>

<script>
  export default {
    props: {
      title: '',
      hasTitleBg: false,
      closeWhite: false,
      hasRight: false,
      rightText: '',
      dark: false,
      blue: false
    },
    data() {
      return {
        top: 50
      };
    },
    created() {
      let that = this;
      uni.getSystemInfo({
        success: (res) => {
          console.log(res.statusBarHeight);
          that.top = res.statusBarHeight * 2;
          console.log(that.top);
          // 可使用窗口高度，将px转换rpx
        }
      });
    },
    methods: {
      clickHeadRight() {
        this.$emit('clickHeadRight');
      },

      backPage() {
        this.$emit('backPage');
      }
    }
  };
</script>

<style lang="scss" scoped>
  .backSize {
    height: 70rpx;
    width: 60rpx;
  }
  .back_top {
    /* #ifdef APP-PLUS */
    padding-top: 50rpx;
    /* #endif */
  }

  .funHead {
    text-align: center;
    position: relative;
    height: 88rpx;
    line-height: 88rpx;
  }

  .funHead view {
    display: block;
    text-align: center;
    font-size: 26rpx;
    color: #ffffff;
  }

  .funHead_left {
    position: absolute;
    top: 5rpx;
    left: 10rpx;
  }

  .funHead_center {
    /* #ifdef APP-PLUS */
    padding-top: 40rpx;
    /* #endif */
  }

  .funHead_center_text {
    font-size: 34rpx;
    color: #ffffff;
  }

  .funHead_center_text_bg {
    display: inline-block;
    height: 60rpx;
    // width: 200rpx;
    margin-top: 10rpx;
    line-height: 60rpx;
    font-size: 34rpx;
    color: #ffffff;
    /* background-color: rgba(0, 0, 0, 0.4); */
    border-radius: 50rpx;
    font-weight: bold;

    &.dark {
      color: #555;
    }
  }

  .funHead_right {
    position: absolute;
    right: 0;
    top: 110rpx;
    width: 160rpx;
    height: 90rpx;
    line-height: 90rpx;
    background: url('https://document.dxznjy.com/applet/interesting/title_btn_l.png') no-repeat;
    background-size: 100% 100%;
  }
</style>
