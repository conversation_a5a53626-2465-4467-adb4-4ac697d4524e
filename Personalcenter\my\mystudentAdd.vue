<template>
  <view class="container mlr-30 bg-ff radius-20" :style="{ height: screenHeight + 'rpx' }">
    <form @submit="formSubmit">
      <view class="address">
        <view class="flex info ptb-30">
          <view class="label f-30 c-33">头像</view>
          <view class="flex-a-c">
            <button class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
              <image class="header-image" :src="headPortrait"></image>
            </button>
            <uni-icons type="right" size="20" color="#999999"></uni-icons>
          </view>
        </view>
        <view class="address-li">
          <text>学员姓名</text>
          <input type="text" :maxlength="12" placeholder="请填写学员姓名,最大长度不超过12" placeholder-class="phClass" name="studentName" />
        </view>
        <view class="address-li">
          <text>性别</text>
          <radio-group name="gender">
            <label>
              <radio value="0" checked color="#2E896F" style="transform: scale(0.7)" />
              男
            </label>
            <label>
              <radio value="1" color="#2E896F" style="transform: scale(0.7)" />
              女
            </label>
          </radio-group>
        </view>
        <view class="address-li" @click="show = true">
          <text>生日</text>

          <uni-datetime-picker style="flex: 1" type="date" :clear-icon="false" v-model="birthDate" />
        </view>
        <view class="address-li">
          <text>学员学校</text>
          <input type="text" maxlength="20" placeholder="请填写学员学校" placeholder-class="phClass" name="studentSchool" />
        </view>
        <view class="address-li">
          <text>学员年级</text>
          <picker class="grade-picker" @change="bindPickerChange" :value="index" :range="array" range-key="label" name="studentGrade">
            <view class="text01 f-30" :class="array[index] != null ? 'c-00' : 'c-99'">
              {{ array[index] != null ? array[index].label : '请选择学员年级' }}
            </view>
          </picker>
        </view>
        <view class="address-li">
          <text>登录密码</text>
          <input type="password" maxlength="20" placeholder="请设置学员登录密码" placeholder-class="phClass" name="userPassword" />
        </view>
      </view>
      <view class="tip mlr-20" style="display: flex; align-items: flex-start">
        <u-icon size="30" color="#EA6031" name="error-circle-fill"></u-icon>
        <!-- <image :src="imgHost+'alading/correcting/tip_icon.png'"></image> -->
        <view class="ml-10">一名会员最多可新增三名学员，账号一旦新建成功，无法删除。</view>
      </view>
      <view>
        <button class="determine" formType="submit">确定</button>
      </view>
    </form>
  </view>
</template>

<script>
  import uniDatetimePicker from '@/Personalcenter/components/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker.vue';
  import Config from '@/util/config.js';
  const { $showMsg } = require('@/util/methods.js');
  //来自 graceUI 的表单验证， 使用说明见手册 http://grace.hcoder.net/doc/info/73-3.html
  import graceChecker from '@/util/graceChecker.js';
  export default {
    data() {
      return {
        array: [],
        index: '',
        memberId: '',
        phone: '',
        stopClick: true,
        imgHost: getApp().globalData.imgsomeHost,
        screenHeight: 0, //屏幕高度
        showType: '',
        show: false,
        headPortrait: '',
        birthDate: ''
      };
    },
    components: {
      uniDatetimePicker
    },
    onLoad(option) {
      var that = this;
      this.memberId = option.memberId;
      console.log(option);
      this.showType = option.type;
      this.phone = option.phone || '';
      // 将 phone 存入缓存
      uni.setStorageSync('studentPhone', this.phone);
      this.loadgrade();
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          this.screenHeight = res.windowHeight * (750 / res.windowWidth) - 30;
          console.log(this.phoneHeight);
        }
      });
    },
    methods: {
      async loadgrade() {
        this.array = [
          {
            value: '18',
            label: '幼儿园',
            ext: '',
            children: null
          },
          {
            value: '1',
            label: '一年级',
            ext: '',
            children: null
          },
          {
            value: '2',
            label: '二年级',
            ext: '',
            children: null
          },
          {
            value: '3',
            label: '三年级',
            ext: '',
            children: null
          },
          {
            value: '4',
            label: '四年级',
            ext: '',
            children: null
          },
          {
            value: '5',
            label: '五年级',
            ext: '',
            children: null
          },
          {
            value: '6',
            label: '六年级',
            ext: '',
            children: null
          },
          {
            value: '7',
            label: '七年级',
            ext: '',
            children: null
          },
          {
            value: '8',
            label: '八年级',
            ext: '',
            children: null
          },
          {
            value: '9',
            label: '九年级',
            ext: '',
            children: null
          },
          {
            value: '10',
            label: '高中一年级',
            ext: '',
            children: null
          },
          {
            value: '11',
            label: '高中二年级',
            ext: '',
            children: null
          },
          {
            value: '12',
            label: '高中三年级',
            ext: '',
            children: null
          },
          {
            value: '13',
            label: '大一',
            ext: '',
            children: null
          },
          {
            value: '14',
            label: '大二',
            ext: '',
            children: null
          },
          {
            value: '15',
            label: '大三',
            ext: '',
            children: null
          },
          {
            value: '16',
            label: '大四',
            ext: '',
            children: null
          },

          {
            value: '17',
            label: '其他',
            ext: '',
            children: null
          }
        ];
        // let result = await this.$httpUser.get("znyy/bvstatus/GradeType");
        // if (result.data.data != null) {
        //     if (result.data.data.length == 0) {
        //         this.array = [];
        //     } else {
        //         if (result.data.data.length > 0 && result.data.data.length > 0) {
        //             this.array = [];
        //             this.array = this.array.concat(result.data.data);
        //             this.array.sort(this.compare("value"))

        //         }
        //     }
        // }
      },

      getTimeString(timestamp) {
        console.log(timestamp);
        const date = new Date(timestamp); // 将时间戳转换为 Date 对象，注意时间戳单位为秒，需要乘以1000转换为毫秒
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，所以加1，并补零
        const day = String(date.getDate()).padStart(2, '0'); // 日期补零
        return `${year}-${month}-${day}`;
      },
      // 年级排序
      compare(prop) {
        return function (obj1, obj2) {
          var val1 = obj1[prop];
          var val2 = obj2[prop];
          if (!isNaN(Number(val1)) && !isNaN(Number(val2))) {
            val1 = Number(val1);
            val2 = Number(val2);
          }
          if (val1 < val2) {
            return -1;
          } else if (val1 > val2) {
            return 1;
          } else {
            return 0;
          }
        };
      },
      onChooseAvatar(e) {
        let _this = this;
        uni.uploadFile({
          url: `${Config.DXHost}zx/common/uploadFile`,
          filePath: e.detail.avatarUrl,
          name: 'file',
          header: {
            Token: uni.getStorageSync('token')
          },
          success: function (res) {
            let data = JSON.parse(res.data);
            console.log(data);
            if (data.status == 1) {
              _this.headPortrait = data.data.fileUrl;
            } else {
              uni.showToast({
                title: data.message,
                icon: 'none'
              });
            }
          },
          fail: function (err) {
            $showMsg(err.errMsg);
          },
          complete: function (res) {
            uni.hideLoading();
          }
        });
      },
      bindPickerChange: function (e) {
        this.index = e.target.value;
      },
      formSubmit: function (e) {
        if (this.stopClick) {
          this.stopClick = false;
          var rule = [
            {
              name: 'studentName',
              checkType: 'notnull',
              checkRule: '',
              errorMsg: '请填写学员姓名'
            },
            {
              name: 'studentSchool',
              checkType: 'notnull',
              checkRule: '',
              errorMsg: '请填写学员学校'
            },
            {
              name: 'userPassword',
              checkType: 'notnull',
              checkRule: '',
              errorMsg: '请设置学员登录密码'
            }
          ];

          var data = e.detail.value;
          if (!e.detail.value.studentGrade) {
            this.$util.alter('请选择年级！');
            this.stopClick = true;
            return false;
          }
          data.studentGrade = this.array[e.detail.value.studentGrade].value;
          var checkRes = graceChecker.check(data, rule);
          if (!checkRes) {
            this.$util.alter(graceChecker.error);
            this.stopClick = true;
          } else {
            if (data.userPassword.length < 6) {
              this.$util.alter('登录密码不能少于6位！');
              this.stopClick = true;
              return false;
            }
            let httpUrl = '';
            let params = {
              studentName: data.studentName,
              gender: data.gender,
              birthday: this.birthDate,
              avatar: this.headPortrait,
              studentSchool: data.studentSchool,
              studentGrade: data.studentGrade,
              userPassword: data.userPassword
            };
            if (this.phone) {
              httpUrl = 'zx/student/new';
              params.phone = this.phone;
            } else {
              httpUrl = 'znyy/member/add/my/student';
            }
            this.$httpUser.post(httpUrl, params).then((result) => {
              if (result.data.success) {
                this.$util.alter('创建成功');
                if (this.showType == 1) {
                  uni.navigateBack();
                } else {
                  uni.navigateBack({
                    url: `/pkyMy/my/mystudent?memberId=` + this.memberId
                  });
                }

                this.disabled = false;
              } else {
                this.$util.alter(result.data.message);
                this.disabled = false;
              }
              this.stopClick = true;
            });
          }
        } else {
          this.$util.alter('请不要重复点击');
        }
      }
    }
  };
</script>

<style lang="scss">
  ::v-deep .uni-date {
    .uni-date-x--border {
      border: none;
    }
    text {
      width: auto !important;
    }
  }
  uni-radio {
    margin-left: 40rpx;
  }

  uni-picker {
    width: 75% !important;
  }
  .header-image {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
  }
  .info {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 120rpx;
    border-bottom: 1rpx solid #eee;
  }
  .avatar-wrapper {
    width: 100rpx;
    height: 100rpx;
    margin-right: 10rpx;
  }
  .tip image {
    width: 28rpx;
    height: 28rpx;
    float: left;
    margin-right: 10rpx;
  }

  .tip {
    font-size: 26rpx;
    color: #ea6031;
    margin-top: 20rpx;
  }

  .address {
    margin-left: 20rpx;
    margin-right: 20rpx;
    /* margin-top: 20rpx; */
    background: #ffffff;
    border-radius: 10rpx;
    position: relative;
    /*        padding-bottom: 40rpx;
        padding-top: 30rpx; */
  }

  .address-li {
    display: flex;
    align-items: center;
    height: 100rpx;
    line-height: 100rpx;
    border-bottom: 1px dashed #eeeeee;
  }

  .address-li text {
    font-size: 30rpx;
    display: block;
    width: 25%;
    margin-left: 10rpx;
    color: #333;
  }

  .address-li radio {
    transform: scale(0.7);
  }

  .address-li label {
    margin-right: 20upx;
  }

  .grade-picker {
    flex: 1;
  }

  .address-li input {
    color: #333;
    margin-left: 40rpx;
    width: 75%;
    height: 80rpx;
    line-height: 80rpx;
  }

  .phClass {
    color: #999;
    font-size: 30rpx;
  }

  .text01 {
    margin-left: 28rpx;
    margin-right: 20rpx;
    width: 100%;
  }

  .text {
    display: block;
    width: 100%;
    margin-top: 20rpx;
    padding-bottom: 20rpx;
    height: 160rpx;
    color: #c6c6c6;
    font-size: 30rpx;
  }

  .determine {
    width: 586upx;
    height: 80upx;
    background: linear-gradient(to bottom, #88cfba, #1d755c);
    border-radius: 45upx;
    position: absolute;
    bottom: 60upx;
    color: #ffffff;
    line-height: 80upx;
    font-size: 30upx;
    left: 80upx;
    text-align: center;
  }

  .input {
    margin-left: 70rpx !important;
  }
</style>
