<template>
  <!-- <page-meta :page-style="'overflow:' + (rollShow ? 'hidden' : 'visible')"></page-meta> -->
  <view>
    <view class="col-12 relative parent-box" style="position: fixed; top: 0; z-index: 9">
      <view class="header-img">
        <!-- <image src="https://document.dxznjy.com/course/c8677247823d4ddca911843bfddee04e.png"></image> -->
      </view>
      <view class="page_title bold c-00"></view>
      <view class="home_bg plr-32 t-c">
        <view class="flex-e mt-15">
          <view>
            <button class="customer" open-type="contact" hover-class="none">
              <image class="image-setting" src="https://document.dxznjy.com/course/300b0e06afe949eba8b270f1c2ad487e.png"
                mode="scaleToFill" />
            </button>
          </view>
          <view class="class-news relative" @click="$noMultipleClicks(skintap, 'splitContent/message/message', '信息中心')">
            <u-badge style="position: absolute" :isDot="isDot" type="error"></u-badge>
            <image class="image-setting" src="https://document.dxznjy.com/course/38f1e73753b242d59a75e2d9a5c7ff03.png"
              mode="scaleToFill" />
          </view>
          <view @click="skintap('splitContent/settings/index')">
            <image class="image-setting" src="https://document.dxznjy.com/course/b7a27bfded3a47ba82ed4d4cfb73230f.png"
              mode="scaleToFill" />
          </view>
        </view>
        <view class="userinfo_top_css">
          <view>
            <image :src="userinfo != null && userinfo.headPortrait ? userinfo.headPortrait : avaUrl"
              class="user_image radius-all" @click="$noMultipleClicks(skintap, 'Personalcenter/home/<USER>')"></image>
          </view>
          <view class="user_right">
            <view class="mt-30" style="display: flex; justify-content: space-between">
              <view v-if="userinfo && userinfo.nickName">{{ userinfo.nickName }}</view>
              <view v-else @tap.stop="$noMultipleClicks(skintap, 'Personalcenter/home/<USER>')">请登录</view>
              <view @tap="getIntegral" class="f-24 flex-a-c c-8896 mt-8">
                <view class="mr-8 text-colors" v-if='!showHasSignedInToday'>签到领取积分</view>
                <view class="mr-8" v-else>已签到</view>
                <u-icon name="arrow-right" color="#888896" size="28"></u-icon>
              </view>
            </view>
            <!-- 	<view class="f-24 c-8896 mt-8">
	  							<text>上级：</text>
	  							<text>{{ userinfo.shareUserName || '暂无上级'}}</text>
	  						</view> -->
            <view class="flex-a-c mt-10">
              <view v-if="userinfo != null && userinfo.identityType == 0" class="channel-level f-24">家长</view>
              <view v-if="userinfo != null && userinfo.identityType == 1" class="channel-level f-24">超人</view>
              <view v-if="userinfo != null && userinfo.identityType == 4" class="channel-level f-24">超级会员</view>
              <view v-if="userinfo != null && userinfo.parentMemberType == 5" class="channel-level f-24">家长会员</view>
              <view v-if="!isHideBClientFunc && userinfo != null && userinfo.merchantCode" class="channel-level">超级合伙人
              </view>
              <view v-if="!isHideBClientFunc && userinfo != null && userinfo.julebuCode" class="channel-level">超级俱乐部
              </view>
              <!-- 12.12修改 -->
              <!-- <view v-if="userinfo != null && userinfo.brandCode" class="channel-level">超级品牌</view> -->
            </view>
          </view>
        </view>
        <view v-if="!isHideBClientFunc && partnerTime" class="back_member">
          <view class="member_content">
            <view class="member_icon"></view>
            <view class="member_name f-28">尊贵的超级合伙人~</view>
          </view>
          <view class="member_time f-24">已持续{{ merchantDay }}天 有效期至{{ expireDate }}</view>
          <!-- <button class="button_style radius-8 f-24 c-ff">查看权益</button> -->
        </view>
        <view v-if="memberTime" class="back_member">
          <view class="member_content">
            <view class="member_icon"></view>
            <view class="member_name f-28">
              尊贵的超级会员~
              <span class="watchGood" @tap="getMember">查看权益</span>
            </view>
          </view>
          <view class="member_time f-24">已持续{{ userinfo.memberDay }}天 有效期至{{ expireTime }}</view>
          <button class="button_style radius-8 f-24 c-ff" @click="goMemberBuy" v-if="showBuyMember"></button>
        </view>
        <view v-if="parentVipTime" class="back_member">
          <view class="member_content">
            <view class="member_icon"></view>
            <view class="member_name f-28">
              <span>尊贵的家长会员~</span>
              <span v-if="userinfo != null && userinfo.parentMemberType == 5" class="watchGood"
                @tap="getParentVipMember">查看权益</span>
            </view>
          </view>
          <view class="member_time f-24" v-if="showParentVipBuyMember">当前会员已过期</view>
          <view class="member_time f-24" v-else>已持续{{ userinfo.parentMemberDay }}天
            有效期至{{ userinfo.parentMemberEndTime }}</view>
          <button class="button_style radius-8 f-24 c-ff" @click="goParentMemberBuy"
            v-if="showParentVipBuyMember"></button>
        </view>
        <!-- <view v-if="showtype == 4" class="become_member">
          <view class="become-text">成为超级品牌,享超值有礼</view>
          <image class="botton_css" @tap="$noMultipleClicks(getBecomBrand)" src="https://document.dxznjy.com/course/996cd3c24cce4b5c86d96f808398f4fc.png"></image>
        </view> -->
        <!-- <view v-if="showtype == 3" class="become_member">
          <view class="become-text">成为俱乐部,享超值权益</view>
          <image class="botton_css" @tap="$noMultipleClicks(getBecomClub)" src="https://document.dxznjy.com/course/996cd3c24cce4b5c86d96f808398f4fc.png"></image>
        </view> -->
        <view v-if="!isHideBClientFunc && showtype == 2" class="become_member">
          <view class="become-text">成为超级合伙人,享超值有礼</view>
          <image class="botton_css" @tap="$noMultipleClicks(getBecomPartner)"
            src="https://document.dxznjy.com/course/996cd3c24cce4b5c86d96f808398f4fc.png"></image>
        </view>
        <view v-if="showtype == 1" class="become_member1">
          <!-- <view class="become-text">成为超级会员，享超值权益~</view> -->
          <image class="botton_css" @tap="$noMultipleClicks(getBecomMember)"
            src="https://document.dxznjy.com/course/996cd3c24cce4b5c86d96f808398f4fc.png"></image>
          <!-- <view class="flex-a-c flex-x-s center-member">
            <image class="icon-image-css" src="https://document.dxznjy.com/course/b654529004e042f085bd15ffb9669a29.png" mode=""></image>
            <view class="ml-12 mr-40">
              <view class="lh-36 f-28">
                会员价
              </view>
              <view class="title-member lh-36 f-24">
                购买享808折
              </view>
            </view>
            <image class="icon-image-css" src="https://document.dxznjy.com/course/7d9c2495130042fea1af9de6cf9745c4.png" mode=""></image>
            <view  class="ml-12 mr-40">
              <view class="lh-36 f-28">
                会员券
              </view>
              <view class="title-member lh-36 f-24">
               1年12张
              </view>
            </view>
            <image class="icon-image-css" src="https://document.dxznjy.com/course/42a0f5fe71f046699fcafa2e3394027a.png" mode=""></image>
            <view  class="ml-12">
              <view class="lh-36 f-28">
                入会礼
              </view>
              <view class="title-member lh-36 f-24">
                1000元超值礼
              </view>
            </view>
            <image class="icon-image-css" src="https://document.dxznjy.com/course/d7cf431e64024697a6c59e9d1f2ab39f.png" mode=""></image>
          </view> -->
        </view>
        <!--     <view v-if="showtype == 1" class="my_price f-28">
          <view class="flexbox">
            <view class="fontWeight">我的收益</view>
            <view class="flex-a-c">
              <view class="f-24 mr-15" @tap="$noMultipleClicks(goIncomeDetails)">收益明细</view>
              <u-icon name="arrow-right" color="#555555" size="28"></u-icon>
            </view>
          </view>
          <view class="flex-a-c t-c">
            <view class="money_item">
              <view class="f-40 fontWeight money_item_top">
                {{ userCredit }}
              </view>
              <view class="lh-40 mt-8">积分余额</view>
            </view>
            <view class="money_item money_item-right" @tap="$noMultipleClicks(skintap, 'shoppingMall/index')">
              <view class="f-40 fontWeight money_item_top">{{ userDingbi }}</view>
              <view class="lh-40 mt-8">鼎币余额</view>
            </view>
          </view>
        </view> -->
        <view class="tab_center_css bg-ff mt-24 f-24 c-33">
          <view class="tab-item-css tab-item-right">
            <image class="image_css_icon wh52"
              src="https://document.dxznjy.com/course/52bfa55b436640ae8f8d0194cbbc88e9.png"></image>
            <view>我的钱包</view>
          </view>
          <!-- <view class="tab-item-css tab-item-right" @click="$noMultipleClicks(skintap, 'Personalcenter/my/myCollection', '我的收藏')">
            <image class="image_css_icon wh52" src="https://document.dxznjy.com/course/a93349447bbc46ecba57a127ae7df3a8.png"></image>
            <view>我的收藏</view>
          </view> -->
          <view @tap="$noMultipleClicks(goIncomeDetails)" class="tab-item-css tab-item-right">
            <view class="tab-title-css"> {{ userCredit }}</view>
            <view>积分</view>
          </view>
          <!-- <view  class="tab-item-css tab-item-right" @click="$noMultipleClicks(skintap, 'splitContent/order/order?type=-1', '我的订单')">
            <image class="image_css_icon wh52" src="https://document.dxznjy.com/course/6ffc668809d74fcba9776b00c8535643.png"></image>
            <view>我的订单</view>
          </view>
          <view>
            <view style="position: relative">
              <image @click="$noMultipleClicks(skintap, 'splitContent/message/message', '信息中心')" class="image_css_icon wh52" src="https://document.dxznjy.com/course/59cdff07c60d4e3c9018f9d868331792.png"></image>
              <u-badge style="position: absolute" :isDot="isDot" type="error"></u-badge>
            </view>
            <view>信息中心</view>
          </view> -->
          <!-- 收货地址 @click="$noMultipleClicks(skintap, 'splitContent/address/list/list', '收货地址')" -->
          <view @tap="$noMultipleClicks(skintap, 'shoppingMall/index')" class="tab-item-css tab-item-right">
            <view class="tab-title-css">
              {{ userDingbi }}
            </view>
            <view>鼎币</view>
          </view>
          <view class="tab-item-css" @click="skintap('coupons/CouponsList', '优惠券')">
            <view class="tab-title-css">{{couponCount}}</view>
            <view>优惠券</view>
          </view>
        </view>

        <!-- 待办提醒 -->
        <TodoReminder v-if="!isHideBClientFunc" ref="todoReminderRef" />

        <!--   <view v-if="
            userList &&
            userList.length > 0 &&
            !(userinfo.parentMemberType !== 5 && userinfo.identityType !== 4 && !userinfo.brandCode && !userinfo.julebuCode && !userinfo.merchantCode)
          " class="member_tab_content">
          <u-tabs :current="current" :list="userList" lineWidth="45" lineHeight="12" :activeStyle="{ color: '#333333', fontWeight: 'bold' }" :inactiveStyle="{
              color: '#5A5A5A ',
              transform: 'scale(1)',
              fontSize: '28rpx'
            }" itemStyle="padding-left:20rpx;height: 80rpx;" :lineColor="`url(${lineBg}) 100% 110%`" @click="tabsClick" keyName="name"></u-tabs>
          <view class="content-box">
            <view class="contract_popup" v-if="showContract">
              <image class="contract_bgc" src="https://document.dxznjy.com/course/3193e3d796e34c1db2619de20aed32dc.png" mode="widthFix"></image>
              <view class="contract_title" v-if="showSignStatus">根据平台规则，您需要完成合同签署方可使用全部功能</view>
              <image class="contract_go_sign" v-if="showSignStatus" src="https://document.dxznjy.com/course/206b240ca5194fa79afdd62744211928.png" @click="goAndSign(1)"></image>
              <view class="contract_desc f-24" v-if="showSignStatus">
                温馨提示：为避免影响您的业务收益，请尽快完成合同签署流程。未签署期间的功能限制可能导致部分收益无法获取，建议及时处理。
              </view>

              <view class="contract_title" v-if="!showSignStatus">签署倒计时即将结束！为确保您的收益不受影响，请立即完成合同签署。</view>
              <view class="contract_time_box" v-if="!showSignStatus">
                剩余时间仅剩
                <view class="contract_time">{{ remainingSignContract }}</view>
                天
              </view>
              <view class="contract_time_text f-24" v-if="!showSignStatus">逾期未签署将无法获得此期间收益</view>
              <view class="contract_time_button_box flex-s f-32" v-if="!showSignStatus">
                <view class="contract_time_button contract_time_button_1" @click="goAndSign(2)">去签署</view>
                <view class="contract_time_button contract_time_button_2" @click="goAndSign(3)">我已知晓</view>
              </view>

              <image class="contract_close" v-if="!mustSignContract" src="https://document.dxznjy.com/dxSelect/55db650b-f99e-40dd-8dec-3cd85746c65c.png" @click="closeContract"></image>
            </view>
            <view class="my_price f-28" v-if="PriceShow">
              <view class="flexbox">
                <view class="fontWeight">我的收益</view>
              </view>
              <view class="flex-a-c t-c">
                <view class="money_item">
                  <view class="f-40 fontWeight money_item_top">
                    {{ userCredit }}
                  </view>
                  <view class="lh-40 mt-8">积分余额</view>
                </view>
                <view class="money_item money_item-right" @tap="$noMultipleClicks(skintap, 'shoppingMall/index')">
                  <view class="f-40 fontWeight money_item_top">{{ userDingbi }}</view>
                  <view class="lh-40 mt-8">鼎币余额</view>
                </view>
              </view>
            </view>
            <view class="my_price f-28" v-if="!PriceShow && !isHideBClientFunc">
              <view class="flexbox">
                <view class="fontWeight">我的收益</view>
                <view class="flex-a-c">
                  <view class="f-24 mr-15" @tap="$noMultipleClicks(goWithdrawal)">去提现</view>
                  <u-icon name="arrow-right" color="#555555" size="28"></u-icon>
                </view>
              </view>
              <view class="flex-a-c t-c">
                <view class="money_item">
                  <view class="flex-y-s flex-y-c">
                    <view class="f-40 fontWeight money_item_top" v-if="showAmount">
                      {{ payAmount }}
                    </view>
                    <view class="f-40 fontWeight money_item_top" v-if="!showAmount">******</view>
                    <view class="ml-15 showMoney" v-if="showAmount" @click="$noMultipleClicks(showBalance)"></view>
                    <view class="ml-15 hideMoney" v-if="!showAmount" @click="$noMultipleClicks(showBalance)"></view>
                  </view>
                  <view class="lh-40 mt-8">余额（元）</view>
                </view>
                <view class="money_item money_item-right">
                  <view class="flex-y-s flex-y-c">
                    <view class="f-40 fontWeight money_item_top" v-if="showAmounts">
                      {{ totalAmount }}
                    </view>
                    <view class="f-40 fontWeight money_item_top" v-if="!showAmounts">******</view>
                    <view class="ml-15 showMoney" v-if="showAmounts" @click="$noMultipleClicks(showBalances)"></view>
                    <view class="ml-15 hideMoney" v-if="!showAmounts" @click="$noMultipleClicks(showBalances)"></view>
                  </view>
                  <view class="lh-40 mt-8">累计收益（元）</view>
                </view>
              </view>
            </view>
            <view class="tabs_center_css" style="position: relative; z-index: 1">
              <view v-if="showMask" class="mask-box-tab" @click="closeMask">
                <image class="bubble-box" src="https://document.dxznjy.com/course/2ce03dacce0d4d8ea7d0c334d062c4ec.png" mode="widthFix" @click.stop="closeMaskBtn"></image>
              </view>

              <view v-for="item in tabList" :key="item.name">
                <view class="item_content mt-40">
                  <view v-if="item.key == 1" @click="$noMultipleClicks(skintap, 'partnerApplication/levelMember?userCode=' + userCode1 + '&merchantType=' + merchantType1)" class="click_tab"></view>

                  <view v-if="item.key == 23" @click="$noMultipleClicks(skintap, 'partnerApplication/students')" class="click_tab" id="students-box" ref="claimButton"></view>
                  <view v-if="item.key == 9" @click="$noMultipleClicks(skintap, 'partnerApplication/partnerDetails?type=' + merchantType + '&userCode=' + userCode1)" class="click_tab"></view>
                  <view v-if="item.key == 9 && item.show == 1" @click="$noMultipleClicks(skintap, 'incomeDetails/index?type=1')" class="click_tab"></view>
                  <view v-if="item.key == 2" @click="$noMultipleClicks(open, 2)" class="click_tab"></view>
                  <view v-if="item.key == 3" @click="$noMultipleClicks(open, 3)" class="click_tab"></view>
                  <view v-if="item.key == 4 && item.show == 2" @click="$noMultipleClicks(skintap, 'partnerApplication/recomClub?userCode=' + userCode1)" class="click_tab"></view>
                  <view v-if="item.key == 16" @click="$noMultipleClicks(skintap, 'Personalcenter/Career/news')" class="click_tab"></view>
                  <view v-if="item.key == 18" @click="$noMultipleClicks(skintap, 'growth/centerList/growthCenter?userCode=' + userCode1, item.key)" class="click_tab"></view>
                  <view v-if="item.key == 4 && item.show == 3" @click="skintap('clubApplication/clubList?userCode=' + userCode1)" class="click_tab"></view>
                  <view v-if="item.key == 5" @click="$noMultipleClicks(open, 4)" class="click_tab"></view>
                  <view v-if="item.key == 6" @click="$noMultipleClicks(open, 1)" class="click_tab"></view>
                  <view v-if="item.key == 6 && item.show == 3" @click="skintap('partnerApplication/recomBrand?userCode=' + userCode1)" class="click_tab"></view>
                  <view v-if="item.key == 7" @click="skintap('clubApplication/levelProcurement?type=' + buyCodeType + '&userCode=' + userCode1)" class="click_tab"></view>
                  <image v-if="item.key == 2 && item.noShow" style="width: 57rpx; height: 48rpx" :src="item.src"></image>
                  <view v-if="item.key == 8 && item.show == 1" @click="skintap('partnerApplication/myProcurement?merchantType=' + merchantType + '&type=' + buyCodeType + '&userCode=' + userCode1)" class="click_tab"></view>
                  <view v-if="item.key == 8 && item.show == 2" @click="skintap('partnerApplication/codeProcurement?merchantType=' + merchantType + '&type=' + buyCodeType + '&userCode=' + userCode1)" class="click_tab"></view>
                  <view v-if="item.key == 10 && item.show == 3" @click="skintap('partnerApplication/levelPartners?userCode=' + userCode1 + '&merchantType=' + merchantType1)" class="click_tab"></view>
                  <view v-if="item.key == 10 && item.show == 2" @click="skintap('clubApplication/partnerList?userCode=' + userCode1)" class="click_tab"></view>
                  <view v-if="item.key == 10 && item.show == 1" @click="skintap('partnerApplication/recomPartners?userCode=' + userCode1)" class="click_tab"></view>
                  <view v-if="item.key == 15" @click="skintap('splitContent/authen/authen?type=1&userCode=' + userCode1)" class="click_tab"></view>
                  <view v-if="item.key == 17" @click="openCode" class="click_tab"></view>
                  <view v-if="item.key == 19" @click="open(5)" class="click_tab"></view>
                  <view v-if="item.key == 20" @click="$noMultipleClicks(skintap, 'Personalcenter/my/parentVipEquity?isPartner=1')" class="click_tab"></view>
                  <view v-if="item.key == 21" @click="$noMultipleClicks(skintap, `partnerApplication/anticipatedIncome?id=${roleVal}`)" class="click_tab"></view>
                  <view v-if="item.key == 22" @click="$noMultipleClicks(skintap, 'splitContent/poster/congratulations')" class="click_tab"></view>
                  <image v-if="item.key == 2 && item.noShow" style="width: 57rpx; height: 48rpx" :src="item.src"></image>
                  <image v-else class="wh48" :src="item.src"></image>
                  <view class="item_text_css f-24 c-66">{{ item.name }}</view>
                </view>
              </view>
            </view>
          </view>
        </view> -->
        <view class="bg-ff tool_services mt-8">
          <view class="tool_text f-28 fontWeight">工具服务</view>
          <view>
            <view class="tabs_center_css">
              <view class="item_content mt-40" v-for="item in toolTabList" :key="item.name">
                <!-- 我的订单 -->
                <view v-if="item.key == 16"
                  @click="$noMultipleClicks(skintap, 'splitContent/order/order?type=-1', '我的订单')" class="click_tab">
                </view>
                <!-- 收货地址 -->
                <view v-if="item.key == 17"
                  @click="$noMultipleClicks(skintap, 'splitContent/address/list/list', '收货地址')" class="click_tab">
                </view>
                <!-- 我的收藏 -->
                <view v-if="item.key == 18"
                  @click="$noMultipleClicks(skintap, 'Personalcenter/my/myCollection', '我的收藏')" class="click_tab">
                </view>
                <!-- 试课推荐 -->
                <view v-if="item.key == 1" @click="skintap('Trialclass/recommend', item.name)" class="click_tab"></view>
                <!-- 会员信息 -->
                <view v-if="item.key == 19" @click="$noMultipleClicks(skintap, 'Personalcenter/Career/news')"
                  class="click_tab"></view>
                <view v-if="item.key == 2" @click="$noMultipleClicks(getPayToken, item.name)" class="click_tab"></view>
                <view v-if="item.key == 3" @click="skintap('Recharge/onlineJoinTable/onlineJoinTable', item.name)"
                  class="click_tab"></view>
                <view v-if="item.key == 4" @tap="skintap('Personalcenter/suggest/index', item.name)" class="click_tab">
                </view>
                <view v-if="item.key == 5" @click="skintap('splitContent/authen/authen?type=1', item.name)"
                  class="click_tab"></view>
                <view v-if="item.key == 8" @click="skintap('Personalcenter/Career/news', item.name)" class="click_tab">
                </view>
                <view v-if="item.key == 9" @click="skintap('signature/contract/cManagement', item.name)"
                  class="click_tab"></view>
                <view v-if="item.key == 10" @click="skintap('growth/centerList/growthCenter', item.name)"
                  class="click_tab"></view>

                <navigator v-if="item.key == 6" url="plugin://qiyukf/chat" class="click_tab" @click="handleSensor"
                  hover-class="none"></navigator>
                <button v-if="item.key == 12" class="click_tab" @click="handleCallPhone" hover-class="none"></button>
                <!-- <view  v-if="item.key==7" @click="skintap('splitContent/authen/authen?type=1')" class="click_tab"></view> -->
                <view v-if="item.key == 7 && ShowActivity == '1'"
                  @click="skintap('InvitationGifts/index?activityid=' + activityId, item.name)" class="click_tab">
                </view>
                <view v-if="item.key == 7 && ShowActivity == '2'" @click="ShowTotal" class="click_tab"></view>
                <!-- @click="skintap('Personalcenter/vip/commission?type=2')" -->
                <view v-if="item.key == 13" @click="skintap('signature/protocol/ProtocolList', item.name)"
                  class="click_tab"></view>
                <view v-if="item.key == 14" @click="showCodePoup" class="click_tab"></view>
                <!-- 我的拼团 -->
                <view v-if="item.key == 15" @click="skintap('activity/groupBuying/myGroupBuying', item.name)"
                  class="click_tab"></view>
                <image class="wh64" :src="item.src"></image>
                <view class="item_text_css f-28 c-66">{{ item.name }}</view>
              </view>
            </view>
          </view>
        </view>
        <!--     <button class="exit_btn" type="default" plain="true" style="background-color: #fff; border: none" @click="Logout" v-if="loginShow">退出登录</button> -->
      </view>
      <!-- positionAbsolute -->
      <view class="w100">
        <view class="supermantime flex-s c-ff" v-if="userinfo != null">
          <view class="ml-30 f-32">学习超人</view>
          <view class="mr-30 c-fde f-28"
            v-if="userinfo.identityType == 1 && (userinfo.expireStatus == 0 || userinfo.expireStatus == 1)">
            有效期至{{ userinfo.expireTime }}
            <text class="c-ff f-28 renew ml-15" @tap.stop="skintap('pages/type=2')">去续费</text>
          </view>
          <view class="mr-30 c-fde f-28" v-if="userinfo.identityType == 1 && userinfo.expireStatus == 2">
            已到期
            <text class="c-ff f-28 renew ml-15" @tap.stop="skintap('supermanClub/superman/superman?type=2')">去续费</text>
          </view>
          <view class="mr-30 c-fde f-28" v-if="userinfo.identityType == 0">
            <text class="c-ff f-28 renew ml-15" @tap.stop="skintap('supermanClub/superman/superman?type=2')">去开通</text>
          </view>
          <view class="mr-30 c-ff f-28"
            v-if="userinfo.identityType == 3 && (userinfo.expireStatus == 0 || userinfo.expireStatus == 3)">
            有效期至{{ userinfo.expireTime }}，到期自动续约
          </view>
          <view class="mr-30 c-fde f-28" v-if="userinfo.identityType == 3 && userinfo.expireStatus == 4">
            暂无超人码，无法续约，
            <text class="f-28">先充超人码</text>
          </view>
        </view>
        <!-- positionAbsolute -->
        <view class="personal_withdraw displayflex personal_top">
          <view class="flex-dir-row flex-x-s flex-y-s tabs c-66 f-28">
            <view class="flex-col col-3" @tap="tab('2', 0)">
              <image v-if="tabindex != 0" :src="imgHost + 'dxSelect/fourthEdition/home-superman2.png'" class="img_s">
              </image>
              <image v-if="tabindex == 0" :src="imgHost + 'dxSelect/fourthEdition/home-superman1.png'" class="img_s">
              </image>
              <text class="mt-10 mb-10" :class="tabindex == 0 ? 'c-00' : ''">学习超人</text>
              <view v-if="tabindex == 0" class="changing-over"></view>
            </view>
            <view class="flex-col col-3" @tap="tab('3', 1)">
              <image v-if="tabindex != 1" :src="imgHost + 'dxSelect/fourthEdition/home-club2.png'" class="img_s">
              </image>
              <image v-if="tabindex == 1" :src="imgHost + 'dxSelect/fourthEdition/home-club1.png'" class="img_s">
              </image>
              <text class="mt-10 mb-10" :class="tabindex == 1 ? 'c-00' : ''">超人俱乐部</text>
              <view v-if="tabindex == 1" class="changing-over"></view>
            </view>
            <view class="flex-col col-3" @tap="tab('1', 2)">
              <image v-if="tabindex != 2" :src="imgHost + 'dxSelect/fourthEdition/home-user2.png'" class="img_s">
              </image>
              <image v-if="tabindex == 2" :src="imgHost + 'dxSelect/fourthEdition/home-user1.png'" class="img_s">
              </image>
              <text class="mt-10 mb-10" :class="tabindex == 2 ? 'c-00' : ''">用户中心</text>
              <view v-if="tabindex == 2" class="changing-over"></view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- class="home_con" -->
    <!-- <view>
      <view class="positionRelative" :style="{
          height: tabindex != 0 ? (tabindex == 1 ? altitude + 'rpx' : (level ? useHeight + 80 : useHeight + 10) + 'rpx') : useHeight + 15 + 'rpx'
        }"
        :class="userinfo != null && (userinfo.identityType == 1 || userinfo.identityType == 3) ? 'content_top' : 'content_tops'">
        <view style="height: 32rpx; background-color: red">1234556</view>
        <view v-if="tabindex == 0 || tabindex == 1" class="p-30 radius-15 mb-30" @click="goWithdrawal">
          <view class="flex-s">
            <view class="f-32 bold">我的钱包</view>
            <uni-icons type="right" size="18" color="c-00"></uni-icons>
          </view>

          <view class="flex-s mt-30">
            <view class="f-30">可提现</view>
            <view class="c-fea f-26">
              ￥
              <text class="f-34 bold">{{ userAccount.availableCashAmount || 0 }}</text>
            </view>
          </view>
        </view>

        <view v-if="tabindex == 0" class="radius-15 pt-30 f-26 c-66 mt-30 templateItem"
          :style="{ height: useHeight - 345 + 'rpx' }">
          <view class="t-c templates" @click="skintap('Personalcenter/vip/customer')">
            <image :src="imgHost + 'dxSelect/fourthEdition/home-crmgl.png'" class="w44"></image>
            <view>客户列表</view>
          </view>
          <view class="t-c templates" @click="skintap('Personalcenter/vip/commission?type=1')">
            <image :src="imgHost + 'dxSelect/fourthEdition/home-yjjl.png'" class="w44"></image>
            <view>佣金记录</view>
          </view>
        </view>

        <view v-if="tabindex == 1" class="ptb-30 plr-20 radius-15 mt-30">
          <view class="flex-s plr-50 c-66 f-26">
            <view class="t-c" @click="skintap('supermanClub/supermanSign/stockGoods')">
              <image :src="imgHost + 'dxSelect/fourthEdition/home-jh.png'" class="wid24"></image>
              <view>进货</view>
            </view>
            <view class="t-c" @click="upGradeLevel">
              <image :src="imgHost + 'dxSelect/fourthEdition/home-sj.png'" class="wid24"></image>
              <view>升级</view>
            </view>
            <view class="t-c" @click="open">
              <image :src="imgHost + 'dxSelect/fourthEdition/home-tj.png'" class="wid24"></image>
              <view>推荐</view>
            </view>
          </view>
          <view class="positionRelative">
            <view class="total-revenue shake" v-if="codeRemaining">超人码剩余</view>
            <view class="returned shake" v-if="suparmanClubAdd">下级俱乐部/本周新增</view>
            <view class="returneds shake" v-if="superhumanAdd">下级超人/本周新增</view>
            <view class="mt-30 border-t pt-30 flex-s c-33 f-28 plr-40">
              <view class="t-c positionRelative">
                <image :src="imgHost + 'dxSelect/fourthEdition/home-club_hint.png'" class="hint-img distance"
                  @click="hintClick(1)"></image>
                <image :src="imgHost + 'dxSelect/fourthEdition/home-club_residue.png'" class="img_s"></image>
                <view class="f-32 mt-15">{{ supermanAdd.haveCodeNum || 0 }}</view>
              </view>
              <view class="t-c positionRelative">
                <image :src="imgHost + 'dxSelect/fourthEdition/home-club_hint.png'" class="hint-img"
                  @click="hintClick(2)"></image>
                <image :src="imgHost + 'dxSelect/fourthEdition/home-club_add.png'" class="img_s"></image>
                <view class="f-32 mt-15">
                  {{ supermanAdd.merchantNum || 0 }}/
                  <text style="color: #4cc66c">+{{ supermanAdd.addWeekMerchantNum || 0 }}</text>
                </view>
              </view>
              <view class="t-c positionRelative">
                <image :src="imgHost + 'dxSelect/fourthEdition/home-club_hint.png'" class="hint-img"
                  @click="hintClick(3)"></image>
                <image :src="imgHost + 'dxSelect/fourthEdition/home-superman_add.png'" class="img_s"></image>
                <view class="f-32 mt-15">
                  {{ supermanAdd.userNum || 0 }}/
                  <text style="color: #4cc66c">+{{ supermanAdd.addWeekUserNum || 0 }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <view v-if="tabindex == 1" class="radius-15 pt-40 f-26 c-66 mt-30 templateItem"
          :style="{ height: (level ? altitude - 700 : altitude - 705) + 'rpx' }">
          <view class="t-c templates" @click="skintap('supermanClub/supermanSign/sign')">
            <image :src="imgHost + 'dxSelect/fourthEdition/home-crmgl.png'" class="w44"></image>
            <view>超人码管理</view>
          </view>
          <view class="t-c templates" @click="skintap('supermanClub/list')">
            <image :src="imgHost + 'dxSelect/fourthEdition/home-xjjlb.png'" class="w44"></image>
            <view>下级俱乐部列表</view>
          </view>
          <view class="t-c templates" @click="skintap('supermanClub/clubManagement/customerList')">
            <image :src="imgHost + 'dxSelect/fourthEdition/home-xjcr.png'" class="w44"></image>
            <view>下级超人列表</view>
          </view>

          <view v-if="userinfo.gradeLevel == 6" class="t-c templates"
            @click="skintap('supermanClub/supermanSign/tabulation')">
            <image :src="imgHost + 'dxSelect/fourthEdition/home-yjjl.png'" class="w44"></image>
            <view>B3平推列表</view>
          </view>

          <view class="t-c templates" @click="skintap('Personalcenter/vip/commission?type=2')">
            <image :src="imgHost + 'dxSelect/fourthEdition/home-yjjl.png'" class="w44"></image>
            <view>佣金记录</view>
          </view>
        </view>

        <view v-if="tabindex == 2" class="radius-15 p-30 f-26 c-66"
          :style="{ height: (level ? useHeight - 100 : useHeight - 177) + 'rpx' }">
          <view class="flex-s mt-10">
            <view class="t-c" @click="skintap('supermanClub/supermanSign/news')">
              <image :src="imgHost + 'dxSelect/fourthEdition/home-news.png'" class="w44"></image>
              <view>消息中心</view>
            </view>
            <view class="t-c" @click="skintap('splitContent/order/order?type=-1')">
              <image :src="imgHost + 'dxSelect/fourthEdition/home-order.png'" class="w44"></image>
              <view>我的订单</view>
            </view>
            <view class="t-c" @click="skintap('Trialclass/recommend')">
              <image :src="imgHost + 'dxSelect/fourthEdition/home-sktj.png'" class="w44"></image>
              <view>试课推荐</view>
            </view>
          </view>

          <view class="flex-s mt-40">
            <view class="t-c" @tap="skintap('Personalcenter/suggest/index')">
              <image :src="imgHost + 'dxSelect/fourthEdition/home-yjfk.png'" class="w44"></image>
              <view>意见反馈</view>
            </view>



            <view v-if="rechargeShow">
              <view class="t-c" @click="skintap('Recharge/onlineJoinTable/onlineJoinTable')">
                <image :src="imgHost + 'dxSelect/fourthEdition/home_jointable.png'" class="w44"></image>
                <view>上课信息对接表</view>
              </view>
            </view>

            <view :style="{ opacity: rechargeShow ? 1 : 0 }">
              <view class="t-c" @click="$noMultipleClicks(getPayToken)">
                <image :src="imgHost + 'dxSelect/recharge/home_kscz.png'" class="w44"></image>
                <view>学时充值</view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view> -->
    <!-- 选择分享类型弹窗（超人、俱乐部） -->
    <uni-popup ref="sharePopup" type="bottom">
      <view class="shareCard">
        <view class="reviewTitle bold">
          {{
            recommendIndex == 1 ? '会员' : recommendIndex == 2 ? '合伙人' : recommendIndex == 3 ? '俱乐部' : recommendIndex == 4 ? '品牌' : recommendIndex == 5 ? '会员' : ''
          }}分享
        </view>
        <view class="review_close" @click="closeDialog">
          <uni-icons type="clear" size="26" color="#B1B1B1"></uni-icons>
        </view>
        <view class="displayflexbetween mt-30 ptb-30" style="justify-content: space-around">
          <!-- #ifdef MP-WEIXIN -->
          <view class="t-c" @click="posterShare">
            <image :src="imgHost + 'dxSelect/share_img.png'" class="shareIcon" mode=""></image>
            <view class="f-26 mt-10 color_grey66">海报分享</view>
          </view>
          <view class="t-c">
            <button open-type="share" class="fillButton">
              <image :src="imgHost + 'dxSelect/share_lj.png'" class="shareIcon" mode=""></image>
              <view class="f-26 mt-10 color_grey66">{{ recommendIndex == 5 ? '家长会员分享' : '链接分享' }}</view>
            </button>
          </view>
          <!-- #endif -->

          <!-- #ifdef APP-PLUS -->
          <view class="t-c" @click="linkShare">
            <button class="fillButton">
              <image :src="imgHost + 'dxSelect/share_lj.png'" class="shareIcon" mode=""></image>
              <view class="f-26 mt-10 color_grey66">{{ recommendIndex == 5 ? '家长会员分享' : '链接分享' }}</view>
            </button>
          </view>
          <!-- #endif -->
        </view>
        <view class="bd-ee"></view>
      </view>
    </uni-popup>

    <uni-popup ref="popopChooseStudent" type="center">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image :src="dialog_iconUrl" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeDxnDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold pb-20">选择学员</view>
            <view class="dialogContent" @click="chooseStudentlist(item, index)" v-for="(item, index) in arrayStudent"
              :key="index" :class="isactive == index ? 'addclass' : 'not-selected'">
              {{ item.realName }}
            </view>
            <view class="review_dxn_btn" @click="confirmStudent()">确定</view>
          </view>
        </view>
      </view>
    </uni-popup>
    <uni-popup ref="code" type="center" @change="changePopup">
      <view class="shareCode">
        <view class="activityRule" style="font-size: 32rpx">编码</view>
        <view class="review_close" @click="closeTesting">
          <uni-icons type="clear" size="26" color="#B1B1B1"></uni-icons>
        </view>
        <view class="rule">
          <view style="color: #428a6f; font-size: 40rpx; display: flex; align-items: center; justify-content: center">
            {{ userCode1 }}
          </view>
          <view class="" style="width: 70%; margin: 20rpx auto; text-align: center; margin-bottom: 40rpx">请复制编码给到您的渠道商
          </view>
          <view class="mb-20 f-32"
            style="color: #fff; background-color: #428a6f; width: 342rpx; line-height: 92rpx; margin: auto; text-align: center; border-radius: 46rpx"
            @tap="copyCode">
            复制
          </view>
        </view>
      </view>
    </uni-popup>
    <uni-popup ref="codePoup" type="center">
      <view class="codeCard">
        <view class="review_close" @click="closeCodePoup">
          <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
        </view>
        <view class="reviewTitle bold pb-50">输入营销码</view>
        <u-code-input v-model="codeValue" size="70" fontSize="32"></u-code-input>
        <view class="code_dxn_btn" :class="codeValue.length >= 6 ? 'code-active' : ''" @click="confirmCode">确定</view>
      </view>
    </uni-popup>

    <user-dialog />
  </view>
</template>

<script>
  const {
    httpUser
  } = require('@/util/luch-request/indexUser.js');
  const {
    $navigationTo,
    $http
  } = require('@/util/methods.js');
  import Config from '@/util/config.js';
  import Superman from '@/common/superman.js';
  import UserDialog from '@/components/user-dialog/index.vue';
  import TodoReminder from '@/components/todoReminder.vue';
  import Util from '@/util/util.js';
  import getNotReadNews from '@/util/messages.js';
  import dayjs from 'dayjs';

  let count;
  export default {
    components: {
      UserDialog,
      TodoReminder
    },
    data() {
      return {
        data: {
          pageNum: 1,
          pageSize: 3
        },
        roleVal: -1, //0,1合伙人、俱乐部
        noClick: true, //防抖
        userInfoData: {},
        newsNumber: '', // 未读消息数量
        userList: [],
        tabsIndex: 0,
        merchantDay: 0,
        remainingSignContract: 0, //剩余签约天数
        lineBg: 'https://document.dxznjy.com/course/01c96465d9ea46f69aa97465b1201aef.png',
        current: 0,
        swiperDotIndex: 0,
        userCode1: '',
        expireDate: '',
        brandCode: '',
        julebuCode: '',
        merchantCode: '',
        dotsStyles: {
          backgroundColor: 'rgba(224, 224, 224,0.9)',
          border: '1px rgba(224, 224, 224,0.9) solid',
          color: '#fff',
          selectedBackgroundColor: 'rgba(67, 146, 134,0.9)',
          selectedBorder: '1px rgba(67, 146, 134,0.9) solid'
        },
        mode: 'round',
        show: false,
        pageShow: true,
        userinfo: null,
        code1: null,
        studyCentre: '',
        loginShow: false,
        userCode: '',
        studentCode: '',
        merchantCode: '',
        expireTime: '',
        tudentcode: '', // 学员进度详情code
        mobile: '', // 账号手机号
        avaUrl: 'https://document.dxznjy.com/dxSelect/home_avaUrl.png',
        newShow: false,
        isTrial: false,
        useHeight: 0, //除头部之外高度
        altitude: 0, // 俱乐部高度
        imgHost: getApp().globalData.imgsomeHost,
        background: ['color1', 'color2', 'color3'],
        indicatorDots: true,
        autoplay: true,
        tabindex: 2, // tab栏默认显示用户中心
        rollShow: false, //禁止滚动穿透
        isactive: 1, // 弹窗选中索引
        shareType: 2, //2学习超人  3超人俱乐部
        supermanAdd: {}, // 俱乐部本周新增
        // 分享内容
        shareContent: {
          type: 6,
          id: 'c499556f9da64dce19b723296c884bc4'
        },
        posterContent: {
          type: '2',
          id: ''
        },
        roleTag: '',
        sendDCode: '', // 跳转鼎学习传的code
        userAccount: {}, //俱乐部账号金额信息
        money: '',
        codeRemaining: false, // 剩余超人码
        suparmanClubAdd: false, // 俱乐部新增
        superhumanAdd: false, // 超人新增
        Withdrawable: '', // 已提现
        isactive: -1, // 选中索引
        arrayStudent: [],
        dxnStudentCode: '',
        screenHeight: '', // 屏幕高度
        level: false, // 高度
        rechargeShow: false, // 充值是否显示
        schoolType: '',
        ShowActivity: '0',
        activityId: '',
        brandShow: false,
        juiebuShow: false,
        merchantShow: false,
        memberShow: false,
        brandShow1: false,
        juiebuShow1: false,
        merchantShow1: false,
        memberShow1: false,
        partnerTime: false,
        parentVipTime: false,
        memberTime: false,
        PriceShow: false,
        showtype: 0,
        level: false, // 高度
        rechargeShow: false, // 充值是否显示
        schoolType: '',
        recommendIndex: 0,
        merchantType: '', //角色类型 4合伙人 5俱乐部 6品牌
        merchantType1: '', //角色类型 1合伙人 2俱乐部 3品牌
        buyCodeType: '', //角色类型 1合伙人 2俱乐部 全部
        subType: '', //角色类型 1下级合伙人 2下级俱乐部 3当前商户
        familyShow: false,
        showActivityIcon: false,
        userCredit: '0', //积分
        userDingbi: '0', //用户鼎币
        couponCount: '0',
        payAmount: '', //收益
        totalAmount: '', //收益
        payingAmount: '',
        disabledAuthen: false,
        showAmount: false,
        showAmounts: false,
        tabList: [],
        showBuyMember: false,
        /** 家长会员续费 */
        showParentVipBuyMember: false,
        isrenew: false,
        toolTabList: [],
        toolTabListCopyAndB: [{
            name: '我的订单',
            src: 'https://document.dxznjy.com/course/f9a4476e530240d59aded3440aeffe42.png',
            key: 16
          },
          {
            name: '收货地址',
            src: 'https://document.dxznjy.com/course/d9380eefb41e4aaabd341d18da4e24a3.png',
            key: 17
          },
          {
            name: '我的收藏',
            src: 'https://document.dxznjy.com/course/63a5c9ef40aa46c7862a19bee96a4aa2.png',
            key: 18
          },
          {
            name: '我的拼团',
            src: 'https://document.dxznjy.com/course/765261c19ade4130a1c8592944f6bf2d.png',
            key: 15
          },
          {
            name: '试课列表',
            src: 'https://document.dxznjy.com/course/b9478fdffe9d4ddda54768db35e7600f.png',
            key: 1
          },
          {
            name: '会员信息',
            src: 'https://document.dxznjy.com/course/95a3737008524009b44d51e541016392.png',
            key: 19
          }
          // {
          //   name: '试课推荐',
          //   src: 'https://document.dxznjy.com/course/17f83c1811e643289a0ba365592c41cf.png',
          //   key: 1
          // },
          // {
          //   name: '时长充值',
          //   src: 'https://document.dxznjy.com/course/e198033735d049c2bf0f08fa12438463.png',
          //   key: 2
          // },
          // {
          //   name: '上课信息 对接表',
          //   src: 'https://document.dxznjy.com/course/7ca2e2da44a4439b98e0e17bdc29f81e.png',
          //   key: 3
          // },
          // {
          //   name: '意见反馈',
          //   src: 'https://document.dxznjy.com/course/17cea5bae3a14f1294df6eb301ca64aa.png',
          //   key: 4
          // },
          // {
          //   name: '合同管理',
          //   src: 'https://document.dxznjy.com/course/dd23cb06263043938953d3f3ae3e902a.png',
          //   key: 9
          // },
          // {
          //   name: '联系客服',
          //   src: 'https://document.dxznjy.com/course/627bb549941e4de9a36fd131fb036682.png',
          //   key: 6
          // },
          // {
          //   name: '投诉电话',
          //   src: 'https://document.dxznjy.com/course/aebcb5c0aecb459a84fbafcc43eb4d33.png',
          //   key: 12
          // },
          // {
          //   name: '协议及政策',
          //   src: 'https://document.dxznjy.com/course/67967961663346e39d0d9b1a855bd5b8.png',
          //   key: 13
          // },
          // {
          //   name: '营销小组',
          //   src: 'https://document.dxznjy.com/dxSelect/index/icon_yingxiao.png',
          //   key: 14
          // },
          // {
          //   name: '我的拼团',
          //   src: 'https://document.dxznjy.com/dxSelect/index/2a2008ab-70ca-4f61-a96b-5f36e06a26c0.png',
          //   key: 15
          // }
        ],
        toolTabListCopy: [{
            name: '我的订单',
            src: 'https://document.dxznjy.com/course/f9a4476e530240d59aded3440aeffe42.png',
            key: 16
          },
          {
            name: '收货地址',
            src: 'https://document.dxznjy.com/course/d9380eefb41e4aaabd341d18da4e24a3.png',
            key: 17
          },
          {
            name: '我的收藏',
            src: 'https://document.dxznjy.com/course/63a5c9ef40aa46c7862a19bee96a4aa2.png',
            key: 18
          },
          {
            name: '我的拼团',
            src: 'https://document.dxznjy.com/course/765261c19ade4130a1c8592944f6bf2d.png',
            key: 15
          },
          {
            name: '试课列表',
            src: 'https://document.dxznjy.com/course/b9478fdffe9d4ddda54768db35e7600f.png',
            key: 1
          }
          // {
          //   name:'清除缓存',
          //   src:'https://document.dxznjy.com/course/a5612b00539a40f39a69314461d35cd3.png',
          //   key:3
          // }
          // {
          //   name: '试课推荐',
          //   src: 'https://document.dxznjy.com/course/17f83c1811e643289a0ba365592c41cf.png',
          //   key: 1
          // },
          // // {
          // //   name: '时长充值',
          // //   src: 'https://document.dxznjy.com/course/e198033735d049c2bf0f08fa12438463.png',
          // //   key: 2
          // // },
          // // {
          // //   name: '上课信息 对接表',
          // //   src: 'https://document.dxznjy.com/course/7ca2e2da44a4439b98e0e17bdc29f81e.png',
          // //   key: 3
          // // },
          // {
          //   name: '意见反馈',
          //   src: 'https://document.dxznjy.com/course/17cea5bae3a14f1294df6eb301ca64aa.png',
          //   key: 4
          // },
          // // {
          // // 	name: '实名认证',
          // // 	src: 'https://document.dxznjy.com/course/aeb46f7eb8dc42bfbcb827e4ef35d5dd.png',
          // // 	key: 5
          // // },
          // // {
          // //   name: '合同管理',
          // //   src: 'https://document.dxznjy.com/course/dd23cb06263043938953d3f3ae3e902a.png',
          // //   key: 9
          // // },
          // {
          //   name: '联系客服',
          //   src: 'https://document.dxznjy.com/course/627bb549941e4de9a36fd131fb036682.png',
          //   key: 6
          // },
          // {
          //   name: '投诉电话',
          //   src: 'https://document.dxznjy.com/course/aebcb5c0aecb459a84fbafcc43eb4d33.png',
          //   key: 12
          // },
          // {
          //   name: '协议及政策',
          //   src: 'https://document.dxznjy.com/course/67967961663346e39d0d9b1a855bd5b8.png',
          //   key: 13
          // },
          // // {
          // //   name: '营销小组',
          // //   src: 'https://document.dxznjy.com/dxSelect/index/icon_yingxiao.png',
          // //   key: 14
          // // },
          // {
          //   name: '我的拼团',
          //   src: 'https://document.dxznjy.com/dxSelect/index/2a2008ab-70ca-4f61-a96b-5f36e06a26c0.png',
          //   key: 15
          // }
        ],
        isDot: false,
        codePoup: false, //营销码弹框
        codeValue: '', //营销码
        showMask: false, //遮罩层
        showContract: false, //合同弹框
        mustSignContract: false, //禁止关闭弹窗
        showSignStatus: true, //展示去签署位置按钮
        showHasSignedInToday: false,
        // 是否隐藏b端功能
        isHideBClientFunc: false,
        hideZxShowNormalFunc: false
      };
    },
    onLoad() {},

    onTabItemTap(e) {
      getApp().sensors.track('tabBarClick', {
        pagePath: e.pagePath,
        title: e.text
      });
    },
    onReady() {
      this.getHeight();
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 915;
          this.altitude = h - 450;
          if (this.screenHeight <= 688 && this.screenHeight > 619) {
            this.useHeight = h - 790;
            this.altitude = h - 290;
            this.level = true;
          } else if (this.screenHeight <= 619) {
            this.useHeight = h - 770;
            this.altitude = h - 285;
            this.level = true;
          }
        }
      });
      // 创建监听器（注意：自定义组件内需用 this.$scope）
      const observer = wx.createIntersectionObserver(this);
      console.log('🚀 ~ onReady ~ observer:', observer);
    },

    async onShow() {
      let result = await httpUser.get(`${Config.ImguseHost}config/dxconfig.json`);
      //  zxShowNormalFunc true 正常显示学习等功能    false显示引导
      if (result.data && result.data.zxShowNormalFunc === false) {
        this.hideZxShowNormalFunc = true;
      }
      //  zxShowRole true 正常b端等功能    false不显示
      if (result.data && result.data.zxShowRole === false) {
        this.isHideBClientFunc = true;
      }
      uni.removeStorage({
        key: 'wxpay',
        success: (res) => {
          console.log('删除wxpay');
        }
      });

      let _this = this;
      _this.current = uni.getStorageSync('current') ? uni.getStorageSync('current') : 0;
      let token = uni.getStorageSync('token');

      //this.toolTabList = this.isHideBClientFunc || this.hideZxShowNormalFunc ? this.toolTabListCopy : this.toolTabListCopyAndB;
      this.toolTabList = this.toolTabListCopy;
      if (this.memberTime) {
        this.toolTabList = this.toolTabListCopyAndB;
      }
      if (token) {
        try {
          await getNotReadNews.getNotReadNews();
          if (uni.getStorageSync('notReadNews') || uni.getStorageSync('notReadNews') === 0) {
            this.isDot = uni.getStorageSync('notReadNews') > 0 ? true : false;
            console.log(uni.getStorageSync('notReadNews'), '信息中心', this.isDot);
          }
        } catch (error) {
          console.error('获取未读消息失败', error);
        }
        _this.$refs.todoReminderRef?.resetTodoReminderList();
        _this.$refs.todoReminderRef?.getTodoReminderList();
        _this.loginShow = true;
        _this.getSchoolMerchantByName();
        let tab = uni.getStorageSync('tab');
        if (tab == 1) {
          _this.tabindex = 0;
          this.tabHandle();
          uni.removeStorage({
            key: 'tab'
          });
        } else if (tab == 2) {
          _this.tabindex = 1;
          this.tabHandle();
          uni.removeStorage({
            key: 'tab'
          });
        }
        this.getHasSignedlnToday();
      } else {
        _this.loginShow = false;
      }

      // #ifdef MP-WEIXIN
      uni.login({
        success(res) {
          console.log(res.code);
          _this.code1 = res.code;
        }
      });
      // #endif
      // 检查活动是否开始
      if (uni.getStorageSync('token')) {
        await this.homeData();
        this.fetchDingbi();
        this.fetchMycredit();
        this.getCouponCount()
        // this.checkTime();
      }
    },
    onShareAppMessage() {
      if (this.recommendIndex == 1) {
        return {
          title: '叮，你的好友敲了你一下，赶紧过来看看',
          imageUrl: 'https://document.dxznjy.com/course/49fdbf70fab64b40a4be6366ab365326.jpg',
          //如果有参数的情况可以写path
          //2024-11-6 紧急修改 购买超级会员修改成购买家长会员
          path: `/Personalcenter/my/parentVipEquity?userId=${uni.getStorageSync('user_id')}`
          // path: `/Personalcenter/my/nomyEquity?userId=${uni.getStorageSync('user_id')}`
        };
      }
      if (this.recommendIndex == 2) {
        return {
          title: '叮，你的好友敲了你一下，赶紧过来看看',
          imageUrl: 'https://document.dxznjy.com/course/49fdbf70fab64b40a4be6366ab365326.jpg',
          //如果有参数的情况可以写path
          path: `/partnerApplication/index?userId=${uni.getStorageSync('user_id')}`
        };
      }
      if (this.recommendIndex == 3) {
        console.log(`/supermanClub/superman/superman?type=3&userId=${uni.getStorageSync('user_id')}`);
        return {
          title: '叮，你的好友敲了你一下，赶紧过来看看',
          imageUrl: 'https://document.dxznjy.com/course/49fdbf70fab64b40a4be6366ab365326.jpg',
          //如果有参数的情况可以写path
          path: `/supermanClub/clubManagement/clubUpgrade?userId=${uni.getStorageSync('user_id')}`
        };
      }
      if (this.recommendIndex == 4) {
        return {
          title: '叮，你的好友敲了你一下，赶紧过来看看',
          imageUrl: 'https://document.dxznjy.com/course/49fdbf70fab64b40a4be6366ab365326.jpg',
          //如果有参数的情况可以写path
          path: `/clubApplication/addSuperbrands?userId=${uni.getStorageSync('user_id')}`
        };
      }
      if (this.recommendIndex == 5) {
        return {
          title: '叮，你的好友敲了你一下，赶紧过来看看',
          imageUrl: 'https://document.dxznjy.com/course/49fdbf70fab64b40a4be6366ab365326.jpg',
          //如果有参数的情况可以写path
          path: `/Personalcenter/my/parentVipEquity?userId=${uni.getStorageSync('user_id')}`
        };
      }
    },

    methods: {
      safeJson(key, defaultValue = null) {
        const item = uni.getStorageSync(key);
        if (!item) return defaultValue;
        try {
          return JSON.parse(item);
        } catch {
          uni.removeStorageSync(key);
          return defaultValue;
        }
      },
      async getHasSignedlnToday() {
        const res = await $http({
          url: `zx/wap/credit/hasSignedInToday?userId=${uni.getStorageSync('user_id')}`,
        });
        if (res) {
          this.showHasSignedInToday = res.data
          console.log(res)
          console.log('666666666666666666666666666666666666666666666')
        }
      },
      /**
       * 打开合同签署弹窗
       */

      openContract(countDay) {
        if (this.isHideBClientFunc) {
          return;
        }
        console.time();
        const _this = this;
        // 打开合同的弹窗
        // 1.判断current是否是超级合伙人
        // 显示合同弹窗 计算剩余签署合同天数 30天内正常使用
        //  是否是超级合伙人 是否当前打开的是否是超级合伙人
        _this.current = uni.getStorageSync('current') ? uni.getStorageSync('current') : 0;
        const index = _this.userList.findIndex((item) => item.name === '超级合伙人');
        if (index == _this.current) {
          if (_this.userinfo.coursePromotionState != 2) {
            if (_this.userinfo != null && _this.userinfo.merchantCode) {
              //判断当天有没有打开且关闭这个弹窗
              try {
                const info = _this.safeJson('showContractInfo');
                console.log('🚀 ~ openContract ~ info:', info);
                // 关闭的时间是今天的话 那不打开弹窗 是今天之前的时间则继续打开
                const formattedDate = dayjs().format('YYYY-MM-DD'); // 当前时间戳
                if (info == null || (info && info.closeTime != formattedDate)) {
                  console.log('why?countDay', countDay);
                  // 打开合同签署的弹窗
                  _this.showContract = true;
                  if (countDay > 0) {
                    _this.showSignStatus = false;
                    _this.remainingSignContract = countDay;
                    return;
                  }
                  _this.mustSignContract = true;
                  _this.showSignStatus = true;
                  return;
                }
                console.log('🚀 ~ openContract ~今天关闭过这个弹窗?');
                _this.openPopup();
              } catch (error) {
                console.log('🚀 ~ openContract ~ error:', error);
              }
              return;
            }
            return;
          }
          _this.openPopup();
        }
      },

      /**
       * 打开气泡弹窗
       */
      openPopup() {
        const token = uni.getStorageSync('token');
        const showMaskStatus = uni.getStorageSync('showMaskStatus');
        if (token && !showMaskStatus) {
          this.showMask = true;
          setTimeout(() => {
            this.showMask = false;
          }, 10000);
        }
      },

      /**
       * 关闭合同弹窗
       */
      closeContract() {
        console.log(this.mustSignContract, 'this.mustSignContract');

        // 剩余签约天数=0 时 不允许关闭弹窗，只需要去签约
        if (this.mustSignContract) return;
        const date = new Date();
        const formattedDate = date.toISOString().split('T')[0];
        const showContractInfo = {
          closeTime: formattedDate //关闭的日期
        };
        uni.setStorageSync('showContractInfo', JSON.stringify(showContractInfo));
        this.showContract = false;
        this.openPopup();
      },
      /**
       * 去签署
       */
      goAndSign(status) {
        if (status === 1) {
          // this.showSignStatus = false;
          $navigationTo('signature/contract/cManagement');
          return;
        }
        if (status === 2) {
          $navigationTo('signature/contract/cManagement');
          return;
        }
        if (status === 3) {
          // 超过三十天不让关闭
          if (!this.remainingSignContract) return;

          this.showContract = false;
          const date = new Date();
          const formattedDate = date.toISOString().split('T')[0];
          const showContractInfo = {
            closeTime: formattedDate, //关闭的日期
            status: true
          };
          uni.setStorageSync('showContractInfo', JSON.stringify(showContractInfo));
          return;
        }
      },

      /**
       * 点击遮罩关闭
       */
      closeMask() {
        this.showMask = false;
        //
      },
      /**
       * 点击图片关闭
       */
      closeMaskBtn() {
        this.showMask = false;
        uni.setStorageSync('showMaskStatus', 1);
      },

      // 判断活动是否开始
      checkTime() {
        const inviteGiftActivity = {
          name: '邀请有礼',
          src: 'https://document.dxznjy.com/course/41bef1d2dca34eef96f91b107c97f42b.png',
          key: 7
        };
        this.$httpUser.get('zx/wap/invite/ifShowInvite').then((res) => {
          this.activityId = res.data.data.activityId;
          this.ShowActivity = res.data.data.ifShowInvite;
          if (res.data.data.ifShowInvite === '2' && this.showActivityIcon) {
            if (!this.toolTabList.some((activity) => activity.name === '邀请有礼')) {
              this.toolTabList.push(inviteGiftActivity);
              this.toolTabList.sort((a, b) => a.key - b.key);
            }
          } else if (res.data.data.ifShowInvite === '1' && this.showActivityIcon) {
            if (!this.toolTabList.some((activity) => activity.name === '邀请有礼')) {
              this.toolTabList.push(inviteGiftActivity);
              this.toolTabList.sort((a, b) => a.key - b.key);
            }
          } else {
            if (this.toolTabList.some((activity) => activity.name === '邀请有礼')) {
              this.toolTabList = this.toolTabList.filter((activity) => activity.name !== '邀请有礼');
            }
          }
        });
      },
      ShowTotal() {
        getApp().sensors.track('myTools&ServiceItemClick', {
          name: '邀请有礼'
        });
        uni.showToast({
          title: '活动未开始',
          duration: 2000
        });
      },
      //判断是否实名认证
      async getRealName() {
        let merchantNum = this.merchantCode != '' ? this.merchantCode : this.julebuCode != '' ? this.julebuCode : this
          .brandCode != '' ? this.brandCode : '';
        if (merchantNum != '') {
          let res = await httpUser.get('mps/user/info/user/code', {
            userCode: merchantNum
          });
          if (res.data.success) {
            if (res.data.data.signContractStatus != 1) {
              this.disabledAuthen = false;
            } else {
              this.disabledAuthen = true;
            }
          }
        }
      },
      showBalance() {
        if (this.showAmount === false) {
          if (this.disabledAuthen === false) {
            uni.showModal({
              title: '提示',
              content: '未实名认证，请先实名认证',
              showCancel: false
            });
            return;
          }
        }
        this.showAmount = !this.showAmount;
      },
      showBalances() {
        if (this.showAmounts === false) {
          if (this.disabledAuthen === false) {
            uni.showModal({
              title: '提示',
              content: '未实名认证，请先实名认证',
              showCancel: false
            });
            return;
          }
        }
        this.showAmounts = !this.showAmounts;
      },
      goWithdrawal() {
        console.log(this.userList[this.current].key, this.userList[this.current], 'PPPPPPPPPPPPPPPP');
        let roleVal = this.userList[this.current].name;
        $navigationTo(
          `Personalcenter/vip/wallet?type=${this.userList[this.current].key}&roleVal=${roleVal}&userinfo=${JSON.stringify(this.userinfo)}&userCode=${this.userCode1}&payAmount=${this.payAmount
        }&payingAmount=${this.payingAmount}`
        );
      },
      getHeight() {
        // 获取系统信息
        const systemInfo = uni.getSystemInfoSync();
        // 获取屏幕高度
        this.screenHeight = systemInfo.windowHeight;
      },
      // 切换tab
      tab(type, e) {
        if (!uni.getStorageSync('token')) {
          $navigationTo('Personalcenter/login/login');
          return;
        }
        let that = this;
        if (e == 0) {
          if (that.userinfo.identityType != 1 && that.userinfo.identityType != 3) {
            that.skintap(`supermanClub/superman/superman?type=${type}`); //2
          } else {
            that.tabindex = e;
            this.tabHandle();
          }
        } else if (e == 1) {
          if (that.userinfo.identityType != 2 && that.userinfo.identityType != 3) {
            that.skintap(`supermanClub/superman/superman?type=${type}`); //3
          } else {
            that.tabindex = e;
            that.tabHandle();
          }
        } else {
          that.tabindex = e;
        }
      },

      // tab变换
      async tabHandle() {
        this.userAccount = await Superman.getAccount(this.tabindex, this.userinfo);
        if (this.userAccount == '') {
          this.userAccount = {};
        }
        this.userAccount.totalMoney = Util.Fen2Yuan(this.userAccount.totalMoney || 0); // 总收益
        this.userAccount.paymentIn = Util.Fen2Yuan(this.userAccount.paymentIn || 0); // 待返佣
        this.userAccount.paymentOut = (Number(this.userAccount.totalMoney) - Number(this.userAccount.paymentIn) || 0)
          .toFixed(2); // 已返佣
        this.userAccount.availableCashAmount = Util.Fen2Yuan(this.userAccount.availableCashAmount || 0); // 已提现
        // this.userAccount.availableCashAmount = Util.Fen2Yuan(this.userAccount.availableCashAmount || 0); // 可提现
        // this.userAccount.waitAllocateFunds = Util.Fen2Yuan(this.userAccount.waitAllocateFunds || 0); // 待结算
        await this.getMoney();
      },

      // 获取超人、俱乐部可提现金额
      async getMoney() {
        const resdata = await $http({
          url: 'zx/user/userWithdrawMoney',
          data: {
            type: this.tabindex + 1
          }
        });
        if (resdata) {
          this.Withdrawable = Number(resdata.data).toFixed(2); // 可提现
          console.log(this.Withdrawable);
        }
      },
      getIntegral() {
        getApp().sensors.track('integralCenterClick', {
          name: '积分中心'
        });
        $navigationTo('Personalcenter/my/myIntegral');
      },

      // 获取公告用户id和角色信息
      async getPaperUserInfo(sum) {
        const res = await $http({
          url: 'train/wap/token/getUserInfo',
          data: {
            merchantCode: sum
          }
        });
        this.userInfoData = res.data;
      },

      // 点击海报
      posterShare() {
        this.$refs.sharePopup.close();
        uni.navigateTo({
          url: `/splitContent/poster/index?type=${this.posterContent.type}&id=${this.posterContent.id}`
        });
      },

      // 上级变更通知
      ownerChangeNotice() {
        uni.navigateTo({
          url: '/supermanClub/owner/ownerChange'
        });
      },

      ///app链接分享 报错 图片太大
      linkShare() {
        setTimeout(() => {
          this.$refs.sharePopup.close();
        }, 2000);
        console.log(this.shareContent.id);
        uni.share({
          provider: 'weixin',
          scene: 'WXSceneSession',
          type: 5,
          title: '叮，你的好友敲了你一下，赶紧过来看看',
          imageUrl: this.shareContent.imgurl, //分享封面
          miniProgram: {
            id: Config.miniOriginalId,
            path: '/pages/beingShared/index?scene=' + uni.getStorageSync('user_id') + '&type=' + this.shareContent
              .type + '&id=' + this.shareContent.id,
            type: 0,
            webUrl: Config.webUrl
          },
          success: (ret) => {
            console.log(JSON.stringify(ret));
            uni.showToast({
              icon: 'none',
              title: '分享成功'
            });
          },
          fail: (ret) => {
            console.log(JSON.stringify(ret));
            uni.showToast({
              icon: 'none',
              title: '分享失败'
            });
          }
        });
      },

      goNews(item) {
        if (item.experienceId != '' && item.experienceId != null) {
          uni.navigateTo({
            url: '/Trialclass/result?expId=' + item.experienceId
          });
        } else {
          uni.navigateTo({
            url: '/pages/home/<USER>/news'
          });
        }
      },
      // 未读消息数量
      async getNewslist() {
        let res = await this.$httpUser.get('deliver/app/parent/getUnreadNotify');
        if (res.data.success) {
          this.newsNumber = res.data.data;
        }
      },

      // 退出登录
      async Logout() {
        await uni.removeStorage({
          key: 'log_userCode'
        });
        uni.removeStorage({
          key: 'token',
          success: (res) => {
            console.log('删除token');
            uni.reLaunch({
              url: '/pages/home/<USER>/index'
            });
          }
        });
        uni.setStorageSync('identityType', 0);
        uni.removeStorageSync('club');
        uni.removeStorageSync('Partner');
        uni.removeStorageSync('brand');
        uni.removeStorageSync('user_id');
        uni.removeStorageSync('currentStatus');
        uni.removeStorageSync('showContractInfo');
        uni.removeStorageSync('showMaskStatus');
      },
      getMember() {
        $navigationTo('Coursedetails/my/myEquity');
      },
      /** 跳转家长会员权益 */
      getParentVipMember() {
        $navigationTo('Coursedetails/my/parentEquity');
      },
      getBecomMember() {
        //2024-11-6 紧急修改 购买超级会员修改成购买家长会员
        // $navgationTo('Personalcenter/my/nomyEquity');
        $navigationTo('Personalcenter/my/parentVipEquity');
      },
      getBecomPartner() {
        $navigationTo('partnerApplication/index');
      },
      getBecomClub() {
        // $navigationTo('supermanClub/superman/superman?type=4');
        $navigationTo('supermanClub/clubManagement/clubUpgrade');
      },
      getBecomBrand() {
        $navigationTo('clubApplication/addSuperbrands');
      },
      async tabsClick(item) {
        this.current = item.index;
        uni.setStorageSync('current', this.current);
        this.showMask = false;
        this.showContract = false;
        if (item.name === '家长会员') {
          this.PriceShow = true;
          this.parentVipTime = true;
          this.memberTime = false;
          this.partnerTime = false;
          this.tabList = [{
              name: '会员分享',
              src: 'https://document.dxznjy.com/course/208c2af693ae43d9bc68f4bdb9c34fa5.png',
              key: 19
            },
            {
              name: '收益明细',
              src: 'https://document.dxznjy.com/course/bc08ad9912bf4666905e4af14ea5da80.png',
              key: 9,
              show: 1
            }
          ];
        }
        if (item.name === '超级会员') {
          this.PriceShow = true;
          this.memberTime = true;
          //超级会员多一个会员信息
          this.toolTabList = this.toolTabListCopyAndB;
          this.parentVipTime = false;
          this.partnerTime = false;
          if (!this.merchantShow) {
            this.merchantShow1 = true;
          }
          this.tabList = [{
              name: '推荐会员',
              src: 'https://document.dxznjy.com/course/3edc6498b7f94921974007b047f8f674.png',
              key: 6
            },
            {
              name: '收益明细',
              src: 'https://document.dxznjy.com/course/bc08ad9912bf4666905e4af14ea5da80.png',
              key: 9,
              show: 1
            },
            {
              name: '会员消息',
              src: 'https://document.dxznjy.com/dxSelect/fourthEdition/icon_huiyuanxinxi.png',
              key: 16
            }
            // {
            //   name: '预估收益',
            //   src: 'https://document.dxznjy.com/course/9b25556a80f04e2b936aca84e63c0c97.png',
            //   key: 21,
            //   userType: 0
            // }
          ];
        }
        if (item.name === '超级合伙人') {
          console.log('测试超级合伙人');

          // this.showContract = true;
          this.roleVal = 0;
          uni.setStorageSync('certificateRole', 'zxPartner'); // 生成证书所需角色信息
          uni.showLoading({
            title: '正在加载...',
            mask: true
          });
          this.sendDCode = this.merchantCode;
          this.roleTag = 'zxPartner';
          this.PriceShow = false;
          this.partnerTime = true;
          this.memberTime = false;
          this.parentVipTime = false;
          if (!this.juiebuShow) {
            this.juiebuShow1 = true;
          }
          this.tabList = [
            // 和会员分享冲突隐藏一个
            // {
            //   name: '推荐会员',
            //   src: 'https://document.dxznjy.com/course/3edc6498b7f94921974007b047f8f674.png',
            //   key: 6
            // },
            {
              name: '学员认领',
              src: 'https://document.dxznjy.com/dxSelect/264d175f-9419-47de-8029-a5e0d2624b7f.png',
              key: 23,
              show: 1,
              noShow: true
            },
            //2024-11-14 紧急修改
            {
              name: '会员',
              // name: '下级会员',
              src: 'https://document.dxznjy.com/course/b85b470ccabc4bdb8fdbe185574eceb1.png',
              key: 1,
              show: 1
            },
            //2024-11-14 紧急修改
            // {
            //   name: '推荐合伙人',
            //   src: 'https://document.dxznjy.com/course/fec85f9d26134b5397ce3c874679c725.png',
            //   key: 2,
            //   show: 1
            // },
            // {
            //   name: '已推合伙人',
            //   src: 'https://document.dxznjy.com/course/afe940cf53e742c89996c457eddc6656.png',
            //   key: 10,
            //   show: 1,
            //   noShow: true
            // },

            {
              name: '我的采购',
              src: 'https://document.dxznjy.com/course/8e2138aac5984c469f27e75601f29f90.png',
              key: 8,
              show: 1,
              noShow: true
            },
            {
              name: '收益明细',
              src: 'https://document.dxznjy.com/course/bc08ad9912bf4666905e4af14ea5da80.png',
              key: 9
            },
            {
              name: '实名认证',
              src: this.imgHost + 'dxSelect/fourthEdition/home-smrz.png',
              key: 15
            },
            {
              name: '会员消息',
              src: 'https://document.dxznjy.com/dxSelect/fourthEdition/icon_huiyuanxinxi.png',
              key: 16
            },
            {
              name: '编码',
              src: 'https://document.dxznjy.com/course/3fb471252e9d4adaabc773e5002fa983.png',
              key: 17
            },
            {
              name: '成长中心',
              src: 'https://document.dxznjy.com/dxSelect/icon_growUp.png',
              key: 18
            },
            {
              name: '会员分享',
              src: 'https://document.dxznjy.com/course/208c2af693ae43d9bc68f4bdb9c34fa5.png',
              key: 19
            },
            {
              name: '购买会员',
              src: 'https://document.dxznjy.com/course/7cc97d6835c54811bf98e84dba2cf257.png',
              key: 20
            },
            {
              name: '预估收益',
              src: 'https://document.dxznjy.com/course/d0b93bdcab1845c88de47d4485f7edd6.png',
              key: 21,
              roleVal: 0
            },
            {
              name: '喜报',
              src: 'https://document.dxznjy.com/course/e9a7a3bb3b4a405181e718d1fe8a384a.png',
              key: 22,
              userType: 1
            }
          ];
          this.merchantType = '4';
          this.merchantType1 = '1';
          this.userCode1 = this.merchantCode;
          let res1 = await $http({
            url: 'zx/user/allIncome',
            data: {
              fieldType: '0',
              userCode: this.merchantCode
            }
          });
          uni.hideLoading();
          // this.getPaperUserInfo(this.merchantCode);
          if (res1) {
            this.payAmount = res1.data.payAmount;
            this.totalAmount = res1.data.totalAmount;
            this.payingAmount = res1.data.payingAmount;
          }
        }
        if (item.name === '超级俱乐部') {
          this.roleVal = 1;
          uni.setStorageSync('certificateRole', 'zxClub'); // 生成证书所需角色信息
          uni.showLoading({
            title: '正在加载...',
            mask: true
          });
          this.sendDCode = this.julebuCode;
          this.roleTag = 'zxClub';
          this.partnerTime = false;
          this.memberTime = false;
          this.parentVipTime = false;
          this.PriceShow = false;
          if (!this.brandShow) {
            this.brandShow1 = true;
          }
          (this.tabList = [
            //2024-11-14 紧急修改s
            {
              name: '会员',
              // name: '下级会员',
              src: 'https://document.dxznjy.com/course/b85b470ccabc4bdb8fdbe185574eceb1.png',
              key: 1,
              show: 1
            },
            //2024-11-14 紧急修改
            // {
            //   name: '推荐合伙人',
            //   src: 'https://document.dxznjy.com/course/fec85f9d26134b5397ce3c874679c725.png',
            //   key: 2,
            //   show: 1
            // },s
            // {
            //   name: '下级合伙人',
            //   src: 'https://document.dxznjy.com/course/73df4b8dc2384e2fae2e25b61ac48021.png',
            //   key: 10,
            //   show: 2,
            //   noShow: true
            // },
            // {
            //   name: '推荐俱乐部',
            //   src: 'https://document.dxznjy.com/course/8ff1511aba2c4cb0a80729098ea2bed0.png',
            //   key: 3,
            //   show: 2
            // },
            // {
            //   name: '已推俱乐部',
            //   src: 'https://document.dxznjy.com/course/afe940cf53e742c89996c457eddc6656.png',
            //   key: 4,
            //   show: 2,
            //   noShow: true
            // },
            {
              //2024-11-14 紧急修改s
              name: '采购单',
              // name: '下级采购单',
              src: 'https://document.dxznjy.com/course/6b168b47031e45c69375fb9587706bcb.png',
              key: 7,
              show: 2
            },
            // {
            //   name: '邀请码采购',
            //   src: 'https://document.dxznjy.com/course/e120e678e41140b19cfa162347f93d34.png',
            //   key: 8,
            //   show: 2
            // },
            {
              name: '收益明细',
              src: 'https://document.dxznjy.com/course/bc08ad9912bf4666905e4af14ea5da80.png',
              key: 9
            },
            {
              name: '实名认证',
              src: this.imgHost + 'dxSelect/fourthEdition/home-smrz.png',
              key: 15
            },
            {
              name: '编码',
              src: 'https://document.dxznjy.com/course/3fb471252e9d4adaabc773e5002fa983.png',
              key: 17
            },
            {
              name: '成长中心',
              src: 'https://document.dxznjy.com/dxSelect/icon_growUp.png',
              key: 18
            },
            {
              name: '预估收益',
              src: 'https://document.dxznjy.com/course/d0b93bdcab1845c88de47d4485f7edd6.png',
              key: 21,
              roleVal: 1
            }
          ]),
          (this.merchantType = '5');
          this.merchantType1 = '2';
          this.buyCodeType = '1';
          this.userCode1 = this.julebuCode;
          let res1 = await $http({
            url: 'zx/user/allIncome',
            data: {
              fieldType: '0',
              userCode: this.julebuCode
            }
          });
          uni.hideLoading();
          // this.getPaperUserInfo(this.julebuCode);
          if (res1) {
            this.payAmount = res1.data.payAmount;
            this.totalAmount = res1.data.totalAmount;
            this.payingAmount = res1.data.payingAmount;
          }
        }
        if (item.name === '超级品牌') {
          uni.setStorageSync('certificateRole', 'zxBrand'); // 生成证书所需角色信息
          uni.showLoading({
            title: '正在加载...',
            mask: true
          });
          this.sendDCode = this.brandCode;
          this.roleTag = 'zxBrand';
          this.partnerTime = false;
          this.memberTime = false;
          this.parentVipTime = false;
          this.PriceShow = false;
          (this.tabList = [{
              //2024-11-14 紧急修改
              name: '会员',
              // name: '下级会员',
              src: 'https://document.dxznjy.com/course/b85b470ccabc4bdb8fdbe185574eceb1.png',
              key: 1,
              show: 1
            },
            // {
            // 	name: '推荐合伙人',
            // 	src: 'https://document.dxznjy.com/course/fec85f9d26134b5397ce3c874679c725.png',
            // 	key: 2,
            // 	show: 1
            // },
            //2024-11-14 紧急修改
            // {
            //   name: '下级合伙人',
            //   src: 'https://document.dxznjy.com/course/73df4b8dc2384e2fae2e25b61ac48021.png',
            //   key: 10,
            //   show: 3
            // },
            // {
            //   name: '推荐俱乐部',
            //   src: 'https://document.dxznjy.com/course/8ff1511aba2c4cb0a80729098ea2bed0.png',
            //   key: 3,
            //   show: 2
            // },
            //2024-11-14 紧急修改
            // {
            //   name: '下级俱乐部',
            //   src: 'https://document.dxznjy.com/course/afe940cf53e742c89996c457eddc6656.png',
            //   key: 4,
            //   show: 3
            // },
            // {
            //   name: '推荐品牌',
            //   src: 'https://document.dxznjy.com/course/0c208bf85aaf40c4b7609f34bc88a993.png',
            //   key: 5,
            //   show: 3
            // },
            // {
            //   name: '已推品牌',
            //   src: 'https://document.dxznjy.com/course/f6d0a6ba862d46289d41a1e33da9071d.png',
            //   key: 6,
            //   show: 3
            // },
            {
              //2024-11-14 紧急修改
              name: '采购单',
              // name: '下级采购单',
              src: 'https://document.dxznjy.com/course/6b168b47031e45c69375fb9587706bcb.png',
              key: 7,
              show: 2
            },
            {
              name: '邀请码采购',
              src: 'https://document.dxznjy.com/course/e120e678e41140b19cfa162347f93d34.png',
              key: 8,
              show: 2
            },
            {
              name: '收益明细',
              src: 'https://document.dxznjy.com/course/bc08ad9912bf4666905e4af14ea5da80.png',
              key: 9
            },
            {
              name: '实名认证',
              src: this.imgHost + 'dxSelect/fourthEdition/home-smrz.png',
              key: 15
            },
            {
              name: '编码',
              src: 'https://document.dxznjy.com/course/3fb471252e9d4adaabc773e5002fa983.png',
              key: 17
            },
            {
              name: '成长中心',
              src: 'https://document.dxznjy.com/dxSelect/icon_growUp.png',
              key: 18
            },
            {
              name: '预估收益',
              src: 'https://document.dxznjy.com/course/d0b93bdcab1845c88de47d4485f7edd6.png',
              key: 21,
              userType: 3
            }
          ]),
          (this.merchantType = '6');
          this.merchantType1 = '3';
          this.buyCodeType = '2';
          this.userCode1 = this.brandCode;
          let res1 = await $http({
            url: 'zx/user/allIncome',
            data: {
              fieldType: '0',
              userCode: this.brandCode
            }
          });
          uni.hideLoading();
          // this.getPaperUserInfo(this.brandCode);
          if (res1) {
            this.payAmount = res1.data.payAmount;
            this.totalAmount = res1.data.totalAmount;
            this.payingAmount = res1.data.payingAmount;
          }
        }
        console.log(this.userinfo, '没有this.userinfo');

        // 判断是否走获取弹窗信息的逻辑
        const currentIndex = this.userList.findIndex((item) => item.name === '超级合伙人');
        if (currentIndex == this.current) {
          console.log('判断是否是新老门店123123');

          // 判断是否是新老门店
          await this.getOldOrNew();
          const coursePromotionState = this.userinfo.coursePromotionState || 1; // 1未签署 2已签署
          // console.log('测试🚀 ~ tabsClick ~ this.userinfo.partnerDay:', this.userinfo.partnerDay);

          const countDay = 30 - this.userinfo.partnerDay;
          const popupSettings = this.safeJson('popupSettings');
          if (popupSettings == null || popupSettings == '') {
            console.log('没有我的页面的弹窗');
            if (coursePromotionState != 2) {
              // 1未签署 2已签署
              console.log('没有弹窗的未签署合同===这个时候要打开合同签署弹窗');
              this.openContract(countDay);
              return;
            }
            console.log('没有弹窗的已签署合同');
            this.openPopup();
            return;
          }

          // 是否显示
          console.log('有我的页面的弹窗');
          if (coursePromotionState != 2) {
            this.openContract(countDay);
            return;
          }
          console.log('如果已签署合同');
          this.openPopup();
        } else {
          console.log(this.userinfo, '麻蛋this.userinfo');
          this.merchantDay = this.userinfo.partnerDay;
          console.log('🚀 ~ tabsClick ~ this.merchantDay:', this.merchantDay);
        }
      },
      //获取是否是新老门店
      async getOldOrNew() {
        const res = await this.$httpUser.get('znyy/operations/v2/judgeMerchant', {
          merchantCode: uni.getStorageSync('merchantCode')
        });
        if (res.data.code === 20000) {
          //根据当前的时间 判断是否是超过30天
          if (res.data.data.isOld == 1) {
            const remainingDay = res.data.data.remainingDay;
            console.log('🚀 ~1 getOldOrNew ~ time:', remainingDay);
            this.userinfo.partnerDay = remainingDay >= 30 ? 50 : 30 - remainingDay;
            console.log('🚀 ~2 getOldOrNew ~ this.userinfo.partnerDay:', this.userinfo.partnerDay);
            // this.merchantDay = 30 - remainingDay;
          }
        }
      },

      async getJump() {
        // 合伙人  zxPartner
        // 俱乐部 zxClub
        // 品牌 zxBrand
        let _this = this;
        console.log('_this.userinfo', _this.userinfo);
        this.$httpUser.post(
          `train/web/common/noSecret/direct?loginUserName=${_this.userinfo.mobile}&roleTag=${_this.roleTag}`).then((
          res) => {
          if (res && res.data.success) {
            _this.jumpAccount = res.data;
            console.log(this.sendDCode, this.userinfo.mobile, this.roleTag);
            console.log(this.userinfo.merchantCode, this.userinfo.mobile, this.roleTag);
            _this.jumpDingStudy();
          } else {
            uni.showToast({
              title: res.data.message,
              icon: 'none',
              duration: 3000
            });
          }
        });
      },
      // 点击升级
      upGradeLevel() {
        if (this.userinfo && this.userinfo.gradeLevel == 6) {
          uni.showToast({
            icon: 'none',
            title: '您已是最高等级，无需再升'
          });
        } else {
          this.goUrl('/supermanClub/superman/superman?type=4');
        }
      },
      async skintap(url, key) {
        console.log(url, 'url');

        if (!uni.getStorageSync('token')) {
          if (url == 'Personalcenter/home/<USER>') {
            getApp().sensors.track('avatarClick', {
              name: '请登录'
            });
          }
          $navigationTo('Personalcenter/login/login');
        } else {
          console.log(key, 'key---------');
          if (url == 'shoppingMall/index') {
            getApp().sensors.track('balanceClick');
          }
          if (url == 'Personalcenter/home/<USER>') {
            getApp().sensors.track('avatarClick');
          }
          if (['我的收藏', '我的订单', '信息中心', '收货地址', '优惠券'].includes(key)) {
            // 常用功能埋点
            getApp().sensors.track('myCommonFunctionClick', {
              name: key
            });
          } else if (key && key != 18) {
            console.log('🚀 ~ skintap ~ key:', key);

            // 常用功能埋点
            getApp().sensors.track('myTools&ServiceItemClick', {
              name: key
            });
          }

          if (key && key != 18) {
            console.log(key, 'key != 18');

            $navigationTo(url);
          } else if (key == 18) {
            console.log(key, '海域');
            this.getJump(); //如果是成长中心就跳转到鼎学习小程序
          } else {
            // if (url.includes('clubApplication/partnerList') || url.includes('partnerApplication/levelPartners')) {
            // 	const res = await $http({
            // 		url: "train/web/exam/course/checkCourseCompleted",
            // 		data: {
            // 			userId: this.userInfoData.bvAdminId,
            // 			roleTag: this.userInfoData.roleTag
            // 		}
            // 	});

            // 	if (!res.data) return uni.showToast({ title: '暂不支持开展业务,请前往我的成长中心完成学习任务', icon: 'none', duration: 2000 })
            // }
            // if (url.includes('clubApplication/clubList')) {
            // 	const res = await $http({
            // 		url: "train/web/exam/course/checkCourseCompleted",
            // 		data: {
            // 			userId: this.userInfoData.bvAdminId,
            // 			roleTag: this.userInfoData.roleTag
            // 		}
            // 	});
            // 	// console.log(res, '孙康');

            // 	if (!res.data) return uni.showToast({ title: '暂不支持开展业务,请前往我的成长中心完成学习任务', icon: 'none', duration: 2000 })
            // }
            // if (url.includes('partnerApplication/codeProcurement')) {
            // 	const res = await $http({
            // 		url: "train/web/exam/course/checkCourseCompleted",
            // 		data: {
            // 			userId: this.userInfoData.bvAdminId,
            // 			roleTag: this.userInfoData.roleTag
            // 		}
            // 	});
            // 	// console.log(res, '孙康');

            // 	if (!res.data) return uni.showToast({ title: '暂不支持开展业务,请前往我的成长中心完成学习任务', icon: 'none', duration: 2000 })
            // }
            // console.log(url, 'url222');
            console.log('真的嘛', url);

            $navigationTo(url);
          }
        }
      },
      async getPayToken() {
        getApp().sensors.track('myTools&ServiceItemClick', {
          name: '时长充值'
        });
        console.log(this.userinfo.identityType);
        if (!this.rechargeShow) {
          return;
        }
        let token = uni.getStorageSync('token');
        let data = {
          memberToken: token
        };
        // uni.showLoading();
        let res = await this.$httpUser.get('new/security/login/school/member/token', data);
        uni.setStorageSync('payToken', res.data.data.token);
        await this.getNewsStore();
        // uni.hideLoading();
        //在起始页面跳转到test.vue页面并传递参数
        uni.navigateTo({
          url: '/Recharge/index?schoolType=' + this.schoolType + '&merchantCode=' + this.merchantCode
        });
      },
      handleSensor() {
        // 联系客服埋点
        getApp().sensors.track('myTools&ServiceItemClick', {
          name: '联系客服'
        });
      },
      // 400电话
      handleCallPhone() {
        getApp().sensors.track('myTools&ServiceItemClick', {
          name: '投诉电话'
        });
        uni.makePhoneCall({
          phoneNumber: '************',
          success: (res) => {
            console.log(res);
          },
          fail: (err) => {
            console.log(err);
          }
        });
      },

      async getNewsStore() {
        let res = await $http({
          url: 'znyy/school/currentAdmin'
        });
        if (res) {
          this.schoolType = res.data.schoolType;
          this.merchantCode = res.data.merchantCode;
        }
      },

      goIncomeDetails() {
        getApp().sensors.track('incomeDetailsClick');
        $navigationTo('incomeDetails/index?type=0');
      },
      close() {
        this.show = false;
      },
      // 去提现
      withdrawl_s() {
        uni.navigateTo({
          url: '../vip/index?money=' + this.userinfo.takeAmount
        });
      },
      // 手机号授权
      async onGetPhoneNumber(e) {
        let _this = this;
        uni.showLoading({
          title: '请等待',
          mask: false
        });
        // 检查登录态是否过期
        wx.checkSession({
          success: async function(res) {
            const encryptedData = e.detail.encryptedData;
            const iv = e.detail.iv;
            if (e.detail.errMsg == 'getPhoneNumber:ok') {
              const resdata = await $http({
                url: 'zx/common/decodeWechatPhone',
                method: 'POST',
                data: {
                  code: _this.code1,
                  encryptedData: encryptedData,
                  iv: iv
                }
              });
              if (resdata) {
                _this.show = false;
                uni.showToast({
                  title: '手机号码授权成功',
                  icon: 'none'
                });
                _this.homeData();
              }
            } else {
              uni.showToast({
                title: '请重新获取',
                icon: 'none'
              });
            }
          },
          fail(err) {
            uni.login({
              success: (res) => {
                _this.code1 = res.code;
              }
            });
          }
        });
      },
      // 获取首页信息
      async homeData() {
        console.time();
        let _this = this;
        this.userList = [];
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.pageShow = false;
          _this.userinfo = res.data;
          let date = new Date();
          let year = date.getFullYear();
          let month = (date.getMonth() + 1).toString().padStart(2, '0');
          let day = date.getDate().toString().padStart(2, '0');
          date = `${year}-${month}-${day}`;
          date = new Date(date);
          /** 家长会员过期 */
          if (_this.userinfo.parentMemberEndTime) {
            const vipDate2 = new Date(_this.userinfo.parentMemberEndTime);
            const vipDiffTime = vipDate2 - date;
            const vipDiffDays = vipDiffTime / (1000 * 60 * 60 * 24);
            console.log(vipDiffDays);
            if (vipDiffDays <= 30) {
              this.showParentVipBuyMember = true;
              uni.setStorageSync('showParentVipBuyMember', this.showParentVipBuyMember);
            } else {
              this.showParentVipBuyMember = false;
              uni.setStorageSync('showParentVipBuyMember', this.showParentVipBuyMember);
            }
          }
          if (_this.userinfo.brandCode) {
            _this.showtype = 5;
          } else if (_this.userinfo.julebuCode) {
            _this.showtype = 4;
          } else if (_this.userinfo.merchantCode) {
            _this.showtype = 3;
          } else if (_this.userinfo.identityType == 4) {
            _this.showtype = 2;
          } else if (_this.userinfo.parentMemberEndTime) {
            _this.showtype = 6;
          } else {
            _this.showtype = 1;
          }
          // ,{name:'超级董事',key:4}
          this.tabsIndex = _this.userinfo.identityType;
          this.brandCode = _this.userinfo.brandCode;
          this.julebuCode = _this.userinfo.julebuCode;
          this.merchantCode = _this.userinfo.merchantCode;
          this.expireTime = _this.userinfo.expireTime;
          this.merchantDay = _this.userinfo.partnerDay;
          console.log('哈哈哈哈🚀 ~ homeData ~ _this.userinfo:', _this.userinfo);
          console.log(_this.userinfo, '用户信息');
          console.log('🚀 ~ homeData ~ this.merchantDay:', this.merchantDay);

          this.expireDate = _this.userinfo.partnerExpireTime;
          await this.getRealName();
          // const news = {
          // 			name: '会员消息',
          // 			src: 'https://document.dxznjy.com/dxSelect/fourthEdition/icon_huiyuanxinxi.png',
          // 			key: 8
          // 		}
          if (_this.userinfo.identityType >= 2) {
            this.tabindex == _this.userinfo.identityType - 2;
            this.tabHandle();
          }
          if (!res.data.mobile) {
            _this.show = true;
          }
          // identityType = 2,3 已经不使用
          // if ((_this.userinfo != null && _this.userinfo.identityType == 2) || _this.userinfo.identityType == 3) {
          //   _this.clubAdd();
          // }

          if (_this.userinfo != null && (_this.userinfo.identityType == '' || _this.userinfo.identityType == 0)) {
            _this.familyShow = true;
            _this.showActivityIcon = true;
          }
          if (_this.userinfo != null && _this.userinfo.parentMemberEndTime) {
            _this.showActivityIcon = true;
            this.userList.push({
              name: '家长会员',
              key: 4
            });
          }
          if (_this.userinfo != null && _this.userinfo.identityType == 4) {
            _this.memberShow = true;
            _this.showActivityIcon = true;
            this.userList.push({
              name: '超级会员',
              key: 0
            });
          }
          if (!this.isHideBClientFunc && _this.userinfo != null && _this.userinfo.merchantCode != '' && _this.userinfo
            .merchantCode != null) {
            uni.setStorageSync('Partner', 1);
            this.userList.push({
              name: '超级合伙人',
              key: 1
            });
            _this.merchantShow = true;
            _this.showActivityIcon = false;
          }
          if (!this.isHideBClientFunc && _this.userinfo != null && _this.userinfo.julebuCode != '' && _this.userinfo
            .julebuCode != null) {
            uni.setStorageSync('club', 1);
            this.userList.push({
              name: '超级俱乐部',
              key: 2
            });
            _this.juiebuShow = true;
            _this.showActivityIcon = false;
          }
          //  12.12修改
          // if (_this.userinfo != null && _this.userinfo.brandCode != '' && _this.userinfo.brandCode != null) {
          //   uni.setStorageSync('brand', 1);
          //   this.userList.push({
          //     name: '超级品牌',
          //     key: 3
          //   });
          //   _this.brandShow = true;
          //   _this.showActivityIcon = false;
          // }
          console.log('this.userList', this.userList);
          if (this.userList.length > 0) {
            this.userList.forEach((item, index) => {
              item.index = index;
            });
            this.current = uni.getStorageSync('current') ? uni.getStorageSync('current') : 0;
            this.tabsClick(this.userList[this.current]);
          }

          // console.log("🚀 ~ homeData ~ currentIndex:", currentIndex)

          this.showBuyMember = uni.getStorageSync('showBuyMember');
          uni.setStorageSync('showBuyMember', this.showBuyMember);
          this.showParentVipBuyMember = uni.getStorageSync('showParentVipBuyMember');
          uni.setStorageSync('showParentVipBuyMember', this.showParentVipBuyMember);
          uni.setStorageSync('user_id', res.data.userId);
          uni.setStorageSync('isBindPayPhone', res.data.isBindPayPhone || 0); //是否绑定手机号
          uni.setStorageSync('isMember', res.data.isMember);
          uni.setStorageSync('identityType', res.data.identityType);
          // identityType
          uni.setStorageSync('phone', res.data.mobile);
          // uni.setStorageSync('certStatus', res.data.certStatus) //0未认证  1已认证
          uni.setStorageSync('merchantId', res.data.merchantId);
          uni.setStorageSync('headPortrait', res.data.headPortrait);
          uni.setStorageSync('nickName', res.data.nickName);
          uni.setStorageSync('merchantCode', res.data.merchantCode);
          uni.setStorageSync('isBusinessMerchant', res.data.merchantCode || res.data.julebuCode); // 是否是b端商户
          uni.setStorageSync('user_code', res.data.userCode);
          uni.setStorageSync('avaUrl', res.data != null && res.data.headPortrait ? res.data.headPortrait : this
            .avaUrl);
          uni.setStorageSync(
            'certificateInfo',
            JSON.stringify({
              mobile: res.data.mobile,
              userName: res.data.nickName
            })
          );
          console.timeEnd();
        }
      },
      //查询我的鼎币
      fetchDingbi() {
        this.$httpUser.get('zx/wap/invite/getDingBi').then((res) => {
          this.userDingbi = res.data.data;
          uni.setStorageSync('userDingbi', this.userDingbi);
        });
      },
      //获取我的积分
      fetchMycredit() {
        this.$httpUser.get(`zx/wap/credit/getCreditCount?userId=${uni.getStorageSync('user_id')}`).then((res) => {
          this.userCredit = res.data.data;
          uni.setStorageSync('userCredit', this.userCredit);
        });
      },
      //获取优惠劵 zx/wap/coupon/user/getCount
      getCouponCount() {
        this.$httpUser.get(`zx/wap/coupon/user/getCount?userId=${uni.getStorageSync('user_id')}&useStatus=1`).then((
          res) => {
          this.couponCount = res.data.data;
          uni.setStorageSync('userCredit', this.userCredit);
        });
      },
      infobtn(type) {
        // uni.navigateTo({
        // 	url: '../authen/authen?type='+type
        // })
        uni.navigateTo({
          url: 'splitContent/authen/authen?type=' + type
        });
      },

      goUrl(url) {
        uni.navigateTo({
          url: url
        });
      },

      //app客服
      contactApp() {
        uni.share({
          provider: 'weixin',
          scene: 'WXSceneSession',
          openCustomerServiceChat: true,
          corpid: Config.contactId,
          customerUrl: Config.contactUrl,
          success: function(res) {
            console.log('success');
            console.log(res);
          },
          fail: function(res) {
            console.log('fail');
            console.log(res);
          }
        });
      },

      // 禁止滚动穿透
      changePopup(e) {
        this.rollShow = e.show;
      },

      // 打开弹框
      open(index) {
        this.recommendIndex = index;
        this.$refs.sharePopup.open();
      },

      //关闭弹窗
      closeDialog() {
        this.$refs.sharePopup.close();
      },

      shareActive(index) {
        this.isactive = index;
      },

      changeShareType(item) {
        if (this.isactive == 1) {
          this.$util.alter('请选择分享类型');
          return;
        }
        this.shareContent.type = this.isactive;
        this.isactive == 2 ? (this.shareContent.id = item.mealId) : (this.shareContent.id = '');
        this.isactive == 2 ? (this.shareContent.imgurl = Config.supermanShareImage) : (this.shareContent.imgurl = Config
          .supermanClubShareImage);
        this.shareType = this.isactive;
        this.$refs.sharePopup.open();
      },

      // 获取俱乐部本周新增超人、俱乐部数量
      async clubAdd() {
        let _this = this;
        const res = await $http({
          url: 'zx/merchant/getUserAddMerchantNumVo'
        });
        if (res) {
          _this.supermanAdd = res.data;
        }
      },
      goMemberBuy() {
        //2024-11-6 紧急修改 购买超级会员修改成购买家长会员
        // uni.navigateTo({
        //   url: '/Personalcenter/my/nomyEquity?expire=1'
        // });
        uni.navigateTo({
          url: '/Personalcenter/my/parentVipEquity?expire=1'
        });
      },
      goParentMemberBuy() {
        uni.navigateTo({
          url: '/Personalcenter/my/parentVipEquity?expire=1'
        });
      },
      hintClick(val) {
        if (val == 1) {
          this.codeRemaining = true;
        } else if (val == 2) {
          this.suparmanClubAdd = true;
        } else {
          this.superhumanAdd = true;
        }
        setTimeout(() => {
          this.codeRemaining = false;
          this.suparmanClubAdd = false;
          this.superhumanAdd = false;
        }, 1500);
      },

      // 获取学员列表
      async getStudent() {
        let that = this;
        let loginMobile = uni.getStorageSync('LoginMobile');
        let result = await httpUser.get('znyy/review/query/my/student');
        if (result != undefined && result != '') {
          if (result.data.data != null) {
            that.arrayStudent = result.data.data;
          }
        }
      },

      gotoDingxueneng() {
        let that = this;
        // 选择校区弹窗
        if (that.arrayStudent.length == 0) {
          uni.showToast({
            title: '您还没有学员或没有正式学员哦',
            icon: 'none',
            duration: 3000
          });
          return;
        }
        this.$refs.popopChooseStudent.open();
      },

      //点击选择学员
      chooseStudentlist(item, index) {
        this.isactive = index;
        this.dxnStudentCode = item.studentCode;
      },

      //关闭弹窗
      closeDxnDialog() {
        this.isactive = -1;
        this.dxnStudentCode = '';
        this.$refs.popopChooseStudent.close();
      },

      confirmStudent() {
        if (this.dxnStudentCode == '') {
          uni.showToast({
            title: '请选择学员',
            icon: 'none',
            duration: 3000
          });
          return;
        }
        this.jumpDingXueNeng();
        this.closeDxnDialog();
      },
      //跳转鼎学习
      jumpDingStudy() {
        // console.log('jumpDingStudy------this.roleTag', this.roleTag);
        wx.navigateToMiniProgram({
          appId: 'wx66f5485bad377cca',
          path: 'pages/learn/growthCenter',
          extraData: {
            isNoSecretLogin: true,
            appSource: 'ZHEN_XUAN',
            merchantCode: this.sendDCode,
            loginUserName: this.userinfo.mobile,
            roleTage: this.roleTag
          },
          // envVersion: 'develop',//开发版
          // envVersion: 'trial',//体验版
          envVersion: 'release', //正式版
          success: (res) => {
            console.log('打开成功', res);
          },
          fail: (err) => {
            console.log('打开失败', err);
          }
        });
      },
      //跳转鼎学能
      jumpDingXueNeng() {
        wx.navigateToMiniProgram({
          appId: 'wxcbd506017e4956dc',
          extraData: {
            token: uni.getStorageSync('token'),
            code: this.dxnStudentCode
          },
          envVersion: 'develop',
          success: (res) => {
            console.log('打开成功', res);
          },
          fail: (err) => {
            console.log('打开失败', err);
          }
        });
      },

      async getSchoolMerchantByName() {
        let _this = this;
        const res = await $http({
          url: 'znyy/school/recharge/getSchoolMerchantByName'
        });
        if (res) {
          this.rechargeShow = res.data;
        }
      },
      copyCode() {
        uni.setClipboardData({
          data: this.userCode1,
          success: function(res) {
            uni.getClipboardData({
              success: function(res) {
                uni.showToast({
                  title: '复制成功'
                });
              }
            });
          }
        });
      },
      async openCode() {
        // const res = await $http({
        // 	url: "train/web/exam/course/checkCourseCompleted",
        // 	data: {
        // 		userId: this.userInfoData.bvAdminId,
        // 		roleTag: this.userInfoData.roleTag
        // 	}
        // });

        // if (!res.data) return uni.showToast({ title: '暂不支持开展业务,请前往我的成长中心完成学习任务', icon: 'none', duration: 2000 })

        this.$refs.code.open();
      },
      closeTesting() {
        this.$refs.code.close();
      },
      /* 营销码弹窗 */
      // 打开弹框
      showCodePoup() {
        getApp().sensors.track('myTools&ServiceItemClick', {
          name: '营销小组'
        });
        if (!uni.getStorageSync('token'))
          return uni.showToast({
            title: '请先登录',
            icon: 'error',
            duration: 2000
          });
        this.codeValue = '';
        this.$refs.codePoup.open();
      },
      // 关闭弹框
      closeCodePoup() {
        this.$refs.codePoup.close();
        this.codeValue = '';
      },
      // 确认校验
      confirmCode() {
        if (this.codeValue !== '184887')
          return uni.showToast({
            title: '请输入正确的营销码',
            icon: 'error',
            duration: 2000
          });
        this.$refs.codePoup.close();
        uni.navigateTo({
          url: '/middlePage/callPhone'
        });
      }
    }
  };
</script>

<style>
  .shake {
    animation-name: slidein;
    animation-duration: 0.5s;
  }

  @keyframes slidein {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }
</style>

<style lang="scss" scoped>
  .page_title {
    position: absolute;
    top: 80upx;
    width: 100%;
    text-align: center;
  }

  /* 遮罩层 */
  .mask-box {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    /* 半透明黑色 */
    // display: flex;
    // justify-content: center;
    // align-items: center;
    z-index: 999;
    /* 确保在最上层 */

    /* 气泡图片 */
    .bubble {
      width: 80%;
      /* 按需调整宽度 */
      width: 380rpx;
      height: 174rpx;
      max-width: 300px;
      /* 避免过大 */
      margin-top: 1220rpx;
      // margin-top: 1620rpx;
      margin-left: 8%;
      animation: float 2s ease-in-out infinite;
      /* 添加浮动动画 */
    }

    /* 气泡浮动动画 */
    // @keyframes float {
    //   0%,
    //   100% {
    //     transform: translateY(0);
    //   }

    //   50% {
    //     transform: translateY(-20px);
    //   }
    // }
  }

  .channel-level {
    color: #006f57;
    font-size: 24rpx;
    height: 34rpx;
    line-height: 34rpx;
    border-radius: 8rpx;
    white-space: nowrap;
    padding: 0rpx 16rpx;
    background-color: #dfffe4;
    margin-right: 8rpx;
  }

  .home_bg {
    width: 686rpx;
    // height: 160rpx;
    height: calc(100vh - 162rpx) !important;
    overflow-y: scroll;
    position: absolute;
    overflow-x: hidden;
    top: 160rpx;
    left: 0;
    padding-bottom: 200rpx;

    .image-setting {
      width: 40rpx;
      height: 40rpx;
    }

    .class-news {
      width: 100rpx;
    }

    .user_right {
      text-align: left;
      margin-left: 20rpx;
      flex: 1;

      .text-colors {
        color: #006739;
      }
    }

    .userinfo_top_css {
      display: flex;
      justify-content: flex-start;
      align-items: center;

      .user_image {
        width: 128rpx;
        height: 128rpx;
      }
    }

    .back_member {
      background: url('https://document.dxznjy.com/course/d097783723f84cdda7ba02366dfc77b9.png') no-repeat;
      background-size: 100%;
      text-align: left;
      position: relative;
      width: 686rpx;
      margin-top: 32rpx;

      //height: 82rpx;
      //box-sizing: content-box;
      .member_content {
        height: 84rpx;
        display: flex;
        align-items: center;

        .member_icon {
          width: 50rpx;
          height: 50rpx;
          margin-left: 30rpx;
          margin-right: 8rpx;
          background: url('https://document.dxznjy.com/course/7a6b35ac21ef42c182f34b42959ee144.png') no-repeat;
          background-size: 100%;
        }
      }

      .member_name {
        color: #2d2424;
        font-weight: bold;
      }

      .member_time {
        display: flex;
        align-items: center;
        line-height: 38rpx;
        padding-left: 152rpx;

        color: #64543f;
      }

      .button_style {
        position: absolute;
        top: 24rpx;
        right: 42rpx;
        width: 142rpx;
        height: 48rpx;
        background: url('https://document.dxznjy.com/course/8c856650f33940e2a418a4bce3803888.png') no-repeat;
        background-size: 100% 100%;
        // background-color: #339378;
        // line-height: 34rpx;
        // padding: 8rpx 12rpx;
      }
    }

    .become_member1 {
      background: url('https://document.dxznjy.com/course/d7dd938346ac4a47bd42f6a65b42c6cb.png') no-repeat;
      background-size: 100%;
      width: 750rpx;
      height: 230rpx;
      position: relative;
      margin-left: -32rpx;
      padding-top: 98rpx;
      box-sizing: border-box;

      .become-text {
        color: #f0d6bb;
        font-size: 28rpx;
        font-weight: bold;
        text-align: left;
        margin-left: 60rpx;
      }

      .center-member {
        width: 686rpx;
        padding-left: 32rpx;
        padding-top: 60rpx;
        color: #0f3b2f;

        .title-member {
          width: 150rpx;
          height: 34rpx;
          background: linear-gradient(270deg, rgba(238, 238, 238, 0.35) 0%, #fff1e3 100%);
          color: #b4683d;
          margin-top: 12rpx;
        }
      }

      .icon-image-css {
        width: 40rpx;
        height: 40rpx;
      }

      .botton_css {
        position: absolute;
        width: 200rpx;
        height: 86rpx;
        top: 90rpx;
        right: 62rpx;
      }

      .become_image {
        width: 40rpx;
        height: 40rpx;
        margin-right: 12rpx;
      }

      .become_main {
        position: absolute;
        bottom: 16rpx;
        left: 32rpx;
      }

      .become_content {
        margin-right: 42rpx;
        text-align: left;

        .text_coloe {
          font-size: 28rpx;
          font-weight: bold;
          color: #0f3b2f;
          line-height: 40rpx;
        }

        .preferential_css {
          color: #b4683d;
          font-size: 24rpx;
          line-height: 34rpx;
          background: linear-gradient(270deg, rgba(238, 238, 238, 0.35) 0%, #fff1e3 100%);
          padding: 0 6rpx;
        }
      }

      .more_css {
        width: 44rpx;
        height: 44rpx;
      }
    }

    .my_price {
      background: url('https://document.dxznjy.com/course/598850ffbb244a3395f67f104a429ae5.png') no-repeat;
      background-size: 100%;
      width: 638rpx;
      padding: 32rpx 24rpx;
      color: #174f3f;
      margin-top: 32rpx;

      .money_item {
        width: 326rpx;
        text-align: left;
        margin-top: 32rpx;
        position: relative;

        .money_item_top {
          line-height: 56rpx;
        }
      }

      .money_item-right {
        padding-left: 78rpx;
      }

      .money_item-right::after {
        content: ' ';
        border-left: 2rpx solid #efefef;
        position: absolute;
        height: 96rpx;
        left: 0;
        top: 10rpx;
      }

      .momey_bottom_css {
        border-top: 2rpx solid #efefef;
        text-align: left;
        padding-top: 20rpx;
        margin-top: 30rpx;
        line-height: 34rpx;

        .item_bottom {
          // margin-right: 32rpx;
        }
      }
    }
  }

  .tab_center_css {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 686rpx;
    padding: 32rpx 0rpx;
    border-radius: 22rpx;

    .tab-item-right {
      border-right: 1rpx solid #f0f0f0;
      box-sizing: border-box;
    }

    .tab-item-css {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
    }

    .tab-title-css {
      color: #006738;
      font-size: 36rpx;
      line-height: 52rpx;
      font-weight: 600;
    }

    .wh52 {
      width: 52rpx;
      height: 52rpx;
    }

    .image_css_icon {}
  }

  .wh64 {
    width: 64rpx;
    height: 64rpx;
  }

  .member_tab_content {
    background: url('https://document.dxznjy.com/course/2f28ea682c054a9a974110dfc14754e5.png') no-repeat;
    background-size: 100%;
    margin-top: 20rpx;
    padding-top: 20rpx;
    padding-bottom: 42rpx;
    width: 686rpx;
    border-radius: 16rpx;
    background-color: #fff;

    /deep/.u-tabs__wrapper__nav__line {
      font: uni-bg-color;
    }
  }

  .content-box {
    position: relative;
    /* 签署合同弹窗 */

    .contract_popup {
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      /* 相对于父元素高度 */
      left: 0;
      /* 水平起始位置 */
      // position: fixed;
      background-color: rgba(0, 0, 0, 0.5);
      /* 半透明黑色 */
      z-index: 999;

      .contract_box {
        width: 632rpx;
        height: 414rpx;
        position: relative;
        /* 脱离文档流 */
        top: 0%;
        /* 相对于父元素高度 */
        left: 0%;
        /* 水平起始位置 */
      }

      .contract_bgc {
        position: relative;
        width: 632rpx;
        height: 414rpx;
        // margin-top: 50%;
        margin-left: 4%;
        display: block;
      }

      .contract_title {
        position: absolute;
        height: 64rpx;
        top: 30%;
        text-align: center;
        left: 14%;
        width: 536rpx;
        height: 96rpx;
        font-family: AlibabaPuHuiTi_3_65_Medium;
        font-size: 32rpx;
        color: #555555;
        line-height: 48rpx;
        text-align: center;
        font-style: normal;
      }

      .contract_go_sign {
        position: absolute;
        top: 56%;
        left: 20%;
        width: 436rpx;
        height: 100rpx;
      }

      .contract_desc {
        box-sizing: border-box;
        position: absolute;
        top: 72%;
        left: 40rpx;
        width: 608rpx;
        height: 134rpx;
        background: #fff9f5;
        border-radius: 20rpx;
        padding: 16rpx 20rpx 10rpx;
        color: #fd954e;
        text-align: left;
      }

      .contract_time_box {
        box-sizing: border-box;
        position: absolute;
        top: 46%;
        // left: 72rpx;
        left: 25%;
        // margin-left: 140rpx;
        width: 354rpx;
        height: 108rpx;
        display: flex;
        justify-content: center;
        align-items: flex-end;
        padding-bottom: 10rpx;

        .contract_time {
          font-size: 90rpx;
          height: 90rpx;
          line-height: 90rpx;
          color: #fd954e;
          margin-left: 8rpx;
          margin-right: 8rpx;
        }
      }

      .contract_time_text {
        position: absolute;
        top: 64%;
        // left: 72rpx;
        left: 22%;
        width: 404rpx;
        height: 56rpx;
        background: #fff9f5;
        border-radius: 28rpx;
        // margin-left: 112rpx;
        text-align: center;
        line-height: 56rpx;
        color: #fd954e;
      }

      .contract_time_button_box {
        box-sizing: border-box;
        position: absolute;
        top: 78%;
        left: 72rpx;
        width: 568rpx;
        height: 80rpx;
        // margin-left: 22rpx;

        .contract_time_button {
          width: 276rpx;
          height: 80rpx;
          border-radius: 44rpx;
          border: 2rpx solid #339378;
          text-align: center;
          line-height: 80rpx;
        }

        .contract_time_button_1 {
          color: #28886f;
        }

        .contract_time_button_2 {
          width: 276rpx;
          height: 80rpx;
          border-radius: 44rpx;
          border: 2rpx solid #339378;
          color: #ffffff;
          text-align: center;
          line-height: 80rpx;
          width: 276rpx;
          height: 80rpx;
          background: linear-gradient(270deg, #185847 0%, #28866e 51%, #185847 100%);
          border-radius: 44rpx;
        }
      }

      .contract_close {
        position: absolute;
        width: 64rpx;
        height: 64rpx;
        top: 1%;
        right: 5rpx;
      }
    }
  }

  .tabs_center_css {
    display: flex;
    flex-flow: row wrap;
    text-align: center;

    .mask-box-tab {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 999;
      /* 确保在最上层 */

      /* 气泡图片 */
      .bubble-box {
        width: 80%;
        /* 按需调整宽度 */
        width: 380rpx;
        height: 174rpx;
        max-width: 300px;
        margin-top: 5%;
        margin-left: -36%;
      }
    }

    .item_content {
      margin-left: 24rpx;
      margin-right: 27rpx;
      position: relative;

      .click_tab {
        position: absolute;
        width: 100%;
        height: 100%;
      }

      .item_text_css {
        width: 120rpx;
        margin-top: 16rpx;
      }
    }
  }

  .tool_services {
    text-align: left;
    margin-top: 24rpx;
    padding: 32rpx 0;
    width: 686rpx;

    .tool_text {
      padding-left: 24rpx;
    }
  }

  /deep/.mlr-20 {
    margin-right: 0 !important;
  }

  .box {
    position: absolute;
    right: -15upx;
    top: -15upx;
  }

  /deep/.uni-badge {
    padding: 0 !important;
  }

  /deep/.uni-badge--error.data-v-26a60cd2 {
    background-color: #d90000 !important;
  }

  /deep/.wid36 {
    width: 38upx;
    height: 38rpx;
  }

  .home_con {
    position: relative;
    margin-top: 800upx;
    border-radius: 20upx 20upx 0 0;
    padding: 30upx;
  }

  .supermantime {
    position: absolute;
    height: 96rpx;
    line-height: 96rpx;
    width: 92.4%;
    top: -295rpx;
    left: 30rpx;
    border-top-left-radius: 45rpx;
    border-top-right-radius: 45rpx;
  }

  .renew {
    width: 108rpx;
    height: 48rpx;
    line-height: 48rpx;
    border-radius: 45rpx;
    border: 1rpx solid #fff;
    padding: 5rpx 12rpx;
  }

  .cash {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    color: #90621a;
  }

  /deep/.swiper-box {
    height: 100rpx !important;
  }

  /deep/.data-v-1fff95d2 {
    height: 100rpx !important;
  }

  /deep/.u-tabs__wrapper__nav__line {
    margin-left: 20rpx !important;
  }

  /deep/.u-badge--not-dot.data-v-662d25bf {
    padding: 2rpx 6rpx;
  }

  /deep/.uni-swiper__warp swiper {
    height: 196rpx !important;
  }

  .personal_withdraw {
    position: absolute;
    left: 30upx;
    width: 690upx;
    backdrop-filter: blur(10upx);
    border-radius: 15rpx;
    box-sizing: border-box;
    z-index: 9;
  }

  .personal_top {
    top: -130upx;
  }

  .border-t {
    border-top: 1px dashed #eee;
  }

  .content_top {
    margin-top: 40rpx;
    overflow: auto;
  }

  .content_tops {
    margin-top: 40rpx;
    overflow: auto;
  }

  .border-b {
    border-bottom: 1rpx dashed #e2e2e2;
  }

  .total-revenue {
    position: absolute;
    top: 40rpx;
    left: 110rpx;
    color: #333;
    font-size: 28rpx;
    border-radius: 10rpx;
    padding: 8rpx 10rpx;
    background-color: #fff;
    box-shadow: 0rpx 3rpx 10rpx #c7c7c7;
  }

  .returned {
    position: absolute;
    top: 40rpx;
    left: 360rpx;
    color: #333;
    font-size: 28rpx;
    border-radius: 12rpx;
    padding: 8rpx 10rpx;
    background-color: #fff;
    z-index: 9;
    box-shadow: 0rpx 3rpx 10rpx #c7c7c7;
  }

  .returneds {
    position: absolute;
    top: 40rpx;
    left: 300rpx;
    color: #333;
    font-size: 28rpx;
    border-radius: 12rpx;
    padding: 8rpx 10rpx;
    background-color: #fff;
    z-index: 9;
    box-shadow: 0rpx 3rpx 10rpx #c7c7c7;
  }

  .wid24 {
    width: 42rpx;
    height: 42rpx;
  }

  .customer {
    flex-direction: column;
    font-size: 28rpx;
    color: #666;
  }

  .uni-bg-red {
    position: relative;
  }

  .shike-img {
    position: absolute;
    width: 45rpx;
    top: 30rpx;
    right: 140rpx;
  }

  .templateItem {
    width: 100%;
    text-align: center;
    display: flex;
    align-content: flex-start;
    flex-flow: row wrap;
  }

  .template {
    flex: 0 0 25%;
    margin-bottom: 30rpx;
  }

  .templates {
    flex: 0 0 33.33%;
    margin-bottom: 40rpx;
  }

  .changing-over {
    background-color: #ea6031;
    width: 30rpx;
    height: 4rpx;
  }

  .w44 {
    width: 44rpx;
    height: 44rpx;
  }

  .tabs {
    width: 100%;
    height: 135rpx;
    display: flex;
    justify-content: space-between;
  }

  .img_s {
    width: 40rpx;
    height: 40rpx;
  }

  .exit_btn {
    width: 694rpx;
    height: 90rpx;
    color: #666;
    line-height: 90rpx;
    border-radius: 18rpx;
    font-size: 30rpx;
    margin: 30rpx auto;
  }

  .flex_s {
    display: flex;
    align-items: inherit;
    flex-direction: column;
    justify-content: center;
  }

  .Withdrawal {
    width: 586rpx;
    height: 80rpx;
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    border-radius: 40rpx;
    line-height: 80rpx;
    margin: 40rpx auto 0 auto;
  }

  .upgradation {
    height: 40rpx;
    display: flex;
    align-items: center;
    text-align: center;
    border-radius: 10rpx;
    padding: 3rpx 12rpx;
    margin-left: 20rpx;
    background-color: #dee1e4;
  }

  .record {
    width: 25%;
    text-align: center;
  }

  // 分享选项弹出层
  .dialogBG {
    width: 100%;
    /* height: 100%; */
  }

  .reviewCard_box {
    width: 670rpx;
    /* height: 560rpx; */
    position: relative;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }

  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 45upx 65upx;
    box-sizing: border-box;
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 145rpx;
    z-index: -1;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }

  .review_btn {
    width: 250upx;
    height: 80upx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 80upx;
    justify-content: center;
    text-align: center;
  }

  .dialogContent {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }

  .review_dxn_btn {
    width: 250upx;
    height: 80upx;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 80upx;
    margin: 60rpx auto 0 auto;
    justify-content: center;
    text-align: center;
  }

  .close_btn {
    width: 250upx;
    height: 80upx;
    color: #2e896f;
    font-size: 30upx;
    line-height: 80upx;
    text-align: center;
    border-radius: 45upx;
    box-sizing: border-box;
    border: 1px solid #2e896f;
  }

  .addclass {
    width: 100%;
    height: 70rpx;
    color: #2e896f;
    font-size: 30rpx;
    text-align: center;
    line-height: 70rpx;
    border-radius: 35rpx;
    border: 1px solid #2e896f;
  }

  .not-selected {
    color: #000;
    width: 100%;
    height: 70rpx;
    font-size: 30rpx;
    text-align: center;
    line-height: 70rpx;
    border-radius: 35rpx;
    border: 1px solid #c8c8c8;
  }

  .border_b {
    border-bottom: 1px solid #eee;
  }

  .fe9 {
    color: #e9e9e9;
  }

  .superior {
    padding: 3rpx 15rpx;
    border-radius: 40rpx;
    border: 1rpx solid #fff;
    background: rgba(45, 130, 106, 0.3);
  }

  .header-img {
    width: 100%;
    height: 400rpx;
    background: linear-gradient(to bottom, #eefff5, #f3f8fc);
  }

  .hint-img {
    width: 24rpx;
    height: 24rpx;
    position: absolute;
    top: -10rpx;
    right: -10rpx;
  }

  .distance {
    right: -25rpx;
  }

  .c-8896 {
    color: #888896;
  }

  .become_member {
    background: url('https://document.dxznjy.com/course/78410fe62cf84f3ab88649243f367649.png') no-repeat;
    background-size: 100%;
    width: 750rpx;
    height: 200rpx;
    position: relative;
    background-position-y: 0;
    margin-left: -32rpx;
    padding-top: 98rpx;
    box-sizing: border-box;

    .become-text {
      color: #f0d6bb;
      font-size: 28rpx;
      font-weight: bold;
      text-align: left;
      margin-left: 60rpx;
    }

    .botton_css {
      position: absolute;
      width: 200rpx;
      height: 86rpx;
      top: 80rpx;
      right: 52rpx;
    }

    .become_image {
      width: 40rpx;
      height: 40rpx;
      margin-right: 12rpx;
    }

    .become_main {
      position: absolute;
      bottom: 16rpx;
      left: 32rpx;
    }

    .become_content {
      margin-right: 42rpx;
      text-align: left;

      .text_coloe {
        font-size: 28rpx;
        font-weight: bold;
        color: #0f3b2f;
        line-height: 40rpx;
      }

      .preferential_css {
        color: #b4683d;
        font-size: 24rpx;
        line-height: 34rpx;
        background: linear-gradient(270deg, rgba(238, 238, 238, 0.35) 0%, #fff1e3 100%);
        padding: 0 6rpx;
      }
    }

    .more_css {
      width: 44rpx;
      height: 44rpx;
    }
  }

  .watchGood {
    margin-left: 20rpx;
    font-size: 24rpx;
    font-weight: normal;
    color: #5a4f40;
    border-bottom: 1px solid #5a4f40;
  }

  //弹窗
  .shareCode {
    position: relative;
    background: #ffffff;
    color: #000;
    padding-top: 50upx;
    box-sizing: border-box;
    overflow: hidden;
    width: 90vw;
  }

  .activityRule {
    text-align: center;
    font-weight: bold;
    position: absolute;
    top: 40rpx;
    left: 0;
    width: 100%;
    background: white;
    z-index: 10;
  }

  .review_close {
    position: absolute;
    /* 固定在右上角 */
    top: 40rpx;
    right: 20rpx;
    z-index: 10;
    /* 确保在上层 */
  }

  .rule {
    padding: 20rpx;
    line-height: 50rpx;
    margin-top: 60rpx;
    /* 确保不被固定元素遮挡 */
    overflow-y: auto;
    /* 允许滚动 */
    height: calc(100% - 40px);
    /* 适应滚动区域的高度 */
  }

  .showMoney {
    background: url('https://document.dxznjy.com/course/8995027e8dea444db245f394cb778624.png') no-repeat;
    background-size: '100%';
    width: 50rpx;
    height: 50rpx;
  }

  .hideMoney {
    background: url('https://document.dxznjy.com/course/d32d0bd861fd443393daf8f1708d7100.png') no-repeat;
    background-size: '100%';
    width: 50rpx;
    height: 50rpx;
  }

  ::v-deep .u-badge--error {
    position: absolute;
    top: 0;
    right: 18rpx;
    width: 16rpx !important;
    height: 16rpx !important;
    background-color: #fb4545 !important;
  }

  /* 营销码 */
  .codeCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 45upx 65upx;
    box-sizing: border-box;
  }

  .code_dxn_btn {
    // width: 250upx;
    height: 80upx;
    background: #e5e5e5;
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 80upx;
    margin: 40rpx auto 0 auto;
    justify-content: center;
    text-align: center;
  }

  .code-active {
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
  }
</style>