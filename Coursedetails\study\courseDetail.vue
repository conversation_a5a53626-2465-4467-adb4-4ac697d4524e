<template>
  <view class="video_main">
    <view class="video_css">
      <!-- 1723c88563aeacd1044215e78b07e449_1 -->
      <!--  :isAllowSeek="audition ? yes : no" -->
      <polyv-player
        :ref="'polyv_player' + videoInfo.id"
        :id="'polyv_player' + videoInfo.id"
        @timeupdate="onBindtimeupdate"
        :autoplay="false"
        :startTime="startTime"
        :playerId="playerIdcont"
        :vid="videoInfo.videoUrl"
        :width="width"
        :height="height"
        :ts="ts"
        :sign="sign"
        :isAllowSeek="audition ? yes : no"
        :preview="true"
        @pause="bindpause"
        @playing="bindplaying()"
        @loadedmetadata="bindloadedmetadata"
        @statechange="statechange"
        @fullscreenchange="bindfullscreenchange"
      >
        <!-- @timeupdate="onBindtimeupdate"  -->
        <!-- <view slot="custom" class="test">自定义插槽</view> -->
      </polyv-player>
      <view class="no_wifi_css" v-if="audition">
        <block>
          <view class="wifi_title">试看视频已播放完，继续观看需要购买</view>
          <button class="ban_css" @click="purchaseRecordedLessons" type="default">立即购买</button>
        </block>
      </view>
      <view class="no_wifi_css" v-if="wifiShow">
        <block>
          <view class="wifi_title">非wifi网络播放将会产生流量费用，是否继续播放？</view>
          <button class="ban_css" @click="playVideo" type="default">继续播放</button>
        </block>
      </view>
    </view>
    <view class="video_title twolist">{{ courseInfo.goodsName }}</view>
    <view class="course_content">
      <view class="title_icon pt-10 pb-25">课程目录</view>
      <courseCatalog
        v-for="(item, index) in courseInfo.piUserRecordCourseCatalogueList"
        @getVideoUrl="getVideoUrl"
        :key="item.id"
        :index="index"
        :item="item"
        :type="courseType"
        @changeDown="changeDown"
      ></courseCatalog>
      <!-- 	<view v-for="item in videoList" :key="item.id">
				<view class="video_top_css" v-if="item.key==0"> 
					<image v-if="item.play" mode="widthFix" class="image_css" src="https://document.dxznjy.com/course/e68e7c737ae84ec88832288da8a081a9.png"></image>
					<image v-else mode="widthFix" class="image_css" src="https://document.dxznjy.com/course/5547f897939d493d8a151137c3a56241.png"></image>
					<text>{{item.name}}</text>
				</view>
				<view v-else>
					<view class="flex-space-between video_top_css">
						<view>{{item.name}}</view>
						<view>
							<u-icon v-if="item.down" @click="changeDown(item)" name="arrow-up" color="#575757" size="32"></u-icon>
							<u-icon v-else name="arrow-down"  @click="changeDown(item)" color="#575757" size="32"></u-icon>
						</view>
					</view>
					<view v-if="item.down">
						<view v-for="info in item.child" class="course_two_css">
							<image v-if="info.play" mode="widthFix" class="image_css" src="https://document.dxznjy.com/course/e68e7c737ae84ec88832288da8a081a9.png"></image>
							<image v-else mode="widthFix" class="image_css" src="https://document.dxznjy.com/course/5547f897939d493d8a151137c3a56241.png"></image>
							<text class="title_css">{{info.name}}</text>
						</view>
					</view>
				</view>
			</view> -->
    </view>
    <view>
      <uni-popup ref="popupCenter" :maskClick="false">
        <view class="popup_content">
          <view class="message_title">温馨提示</view>
          <view class="message_content">由于录播课的特殊性，您在累计观看三个视频后，将无法退款</view>
          <button class="bottom_css" @click="iKnow" type="default">确定</button>
          <view class="bottom_btn_text">点击确认按钮，确认您已清楚</view>
        </view>
      </uni-popup>
    </view>
  </view>
</template>

<script>
  const MD5 = require('../../util/md5.js');
  // 修改下面的 vid 和 secretkey
  let secretkey = 'Jkk4ml1Of8';
  let vid = '';
  let ts = new Date().getTime();
  let sign = '';
  import courseCatalog from '../components/courseCatalog';
  const { $http } = require('@/util/methods.js');
  export default {
    components: {
      courseCatalog
    },
    data() {
      return {
        domId: 'polyvPlayer',
        playerIdcont: 'polyvPlayercont',
        vid: vid,
        ts: ts,
        sign: sign,
        width: '100%',
        height: '100%',
        timer: null,
        polyvPlayerContext: '',
        courseInfo: {},
        videoInfo: {},
        videoList: [],
        startTime: 0,
        onloadInfo: {},
        wifiShow: true,
        studyNumber: 0,
        audition: false,
        courseType: 1,
        type: 0,
        watchTime: [],
        endTime: 180,
        alreadyWatch: true,
        direction: '',
        fullScreen: false
      };
    },
    onLoad(e) {
      this.onloadInfo = e;
      uni.getNetworkType({
        success: (res) => {
          if (res.networkType == 'wifi') {
            this.wifiShow = false;
          } else {
            this.wifiShow = true;
          }
        }
      });
      this.getVideoList(e, true);
    },
    onUnload() {
      let polyvPlayerContext = this.selectComponent('#polyv_player' + this.videoInfo.id);
      this.getRecord(polyvPlayerContext.getCurrentTime());
    },
    methods: {
      //监听视频全屏/半屏
      bindfullscreenchange(e) {
        console.log(e);
        this.direction = e.detail.direction;
        this.fullScreen = e.detail.fullScreen;
      },

      async getRecord(studyTime, info) {
        if (!(studyTime > 0)) {
          return;
        }
        clearInterval(this.timer);

        let parms = {
          courseCatalogueId: this.videoInfo.id,
          courseId: this.onloadInfo.courseId,
          studentCode: this.onloadInfo.studentCode,
          memberCode: uni.getStorageSync('user_code'),
          studyDuration: this.studyNumber,
          userId: uni.getStorageSync('user_id')
        };
        if (this.onloadInfo.studentCode == uni.getStorageSync('user_id')) {
          // delete parms.studentCode;
          parms.studentCode = '';
        }
        console.log(studyTime, '	studyTimestudyTimestudyTimestudyTimestudyTime');
        let _this = this;
        if (this.videoInfo.id == '') return;
        const res = await $http({
          url: 'zx/wap/course/user/record/study/modify',
          method: 'POST',
          data: {
            growthRecordSaveDto: parms,
            recordId: this.videoInfo.id,
            studyTime: studyTime.toFixed(0)
          }
        });
        if (res) {
          this.getVideoList(this.onloadInfo, false);
        }
      },
      //记录成长记录
      async getGrowthInof() {
        const res = await $http({
          url: 'zx/wap/course/user/record/growth/save',
          method: 'POST',
          data: {
            courseCatalogueId: this.videoInfo.id,
            courseId: this.onloadInfo.courseId,
            studentCode: this.onloadInfo.studentCode,
            memberCode: uni.getStorageSync('user_code'),
            studyDuration: this.studyNumber,
            userId: uni.getStorageSync('user_id')
          }
        });
        if (res) {
        }
      },

      getTimeInfo(time) {
        this.$nextTick(function () {
          let polyvPlayerContext = this.selectComponent('#polyv_player' + this.videoInfo.id);
          if (polyvPlayerContext) {
            this.getChangeVideo(polyvPlayerContext, this.videoInfo.videoUrl, time);
          } else {
            let that = this;
            setTimeout(function () {
              that.getTimeInfo(time);
            }, 200);
          }
        });
      },
      async getVideoList(e, show) {
        let _this = this;
        const res = await $http({
          url: 'zx/wap/course/user/record/catalogue/list',
          data: {
            courseId: e.courseId,
            studentCode: e.studentCode
          }
        });
        if (res) {
          this.courseInfo = res.data;
          this.watchTime = res.data.watchedCatalogueIdList;
          console.log(this.watchTime);
          //试听type == 0
          this.courseType = res.data.payStatus ? 1 : 0;

          // 清北学霸学习营家长会员可直接观看
          if (uni.getStorageSync('parentMemberType') == 5 && res.data.goodsName.includes('清北学霸')) {
            this.courseType = 1;
          }
          this.courseInfo.piUserRecordCourseCatalogueList.forEach((iem) => {
            iem.play = false;
            iem.down = true;
          });
          if (show) {
            if (this.courseInfo.piUserRecordCourseCatalogueList[0].catalogueType == 'FILE') {
              this.videoInfo = {};
              this.videoInfo = { ...this.courseInfo.piUserRecordCourseCatalogueList[0] };
              this.videoInfo.key = 1;

              this.courseInfo.piUserRecordCourseCatalogueList[0].play = true;
              if (this.videoInfo.studyTime != 0) {
                // this.startTime=Number(this.videoInfo.studyTime)
                this.getTimeInfo(Number(this.videoInfo.studyTime));
              }
            } else {
              this.videoInfo = {};
              this.videoInfo = { ...this.courseInfo.piUserRecordCourseCatalogueList[0].children[0] };
              this.courseInfo.piUserRecordCourseCatalogueList[0].children[0].play = true;
              this.videoInfo.key = 2;
              console.log(Number(this.videoInfo.studyTime));

              if (this.videoInfo.studyTime != 0) {
                // this.startTime=Number(this.videoInfo.studyTime)
                this.getTimeInfo(Number(this.videoInfo.studyTime));
              }
            }
            console.log(this.videoInfo, 'shuju');
          } else {
            this.courseInfo.piUserRecordCourseCatalogueList.forEach((iem) => {
              iem.play = false;
              iem.down = true;
              if (this.videoInfo.key == 1) {
                if (iem.id == this.videoInfo.id) {
                  this.$set(iem, 'play', true);
                }
              }
              iem.children.forEach((info, i) => {
                info.play = false;
                if (this.videoInfo.key == 2) {
                  if (info.id == this.videoInfo.id) {
                    this.$set(info, 'play', true);
                  }
                }
              });
            });
          }
        }
      },
      changeStudyTime(videoInfo, time) {
        this.courseInfo.piUserRecordCourseCatalogueList.forEach((item) => {
          if (videoInfo.key == 1) {
            if (item.id == videoInfo.id) {
              this.$set(item, 'studyTime', time);
            }
          }
          item.children.forEach((info, i) => {
            if (videoInfo.key == 2) {
              if (info.id == videoInfo.id) {
                this.$set(info, 'studyTime', time);
              }
            }
          });
        });
      },
      getVideoUrl(infoL) {
        console.log('infoL', infoL);
        if (this.courseType == 0) return;
        let oldVideoInfo = { ...this.videoInfo };
        let polyvPlayerContext = this.selectComponent('#polyv_player' + oldVideoInfo.id);
        let getCurrentTime = polyvPlayerContext.getCurrentTime();
        if (getCurrentTime != oldVideoInfo.studyTime) {
          this.getRecord(getCurrentTime, oldVideoInfo);
        }
        this.$nextTick(function () {
          this.videoInfo = {};
          this.videoInfo = { ...infoL.info };
          // this.startTime=Number(this.videoInfo.studyTime)
          this.getTimeInfo(Number(this.videoInfo.studyTime));
          this.videoInfo.key = infoL.key;
          this.courseInfo.piUserRecordCourseCatalogueList.forEach((item) => {
            item.play = false;
            if (infoL.key == 1) {
              if (item.id == this.videoInfo.id) {
                this.$set(item, 'play', true);
              }
            }
            item.children.forEach((info, i) => {
              info.play = false;
              if (infoL.key == 2) {
                if (info.id == this.videoInfo.id) {
                  this.$set(info, 'play', true);
                }
              }
            });
          });
        });
      },
      // 切换视频
      getChangeVideo(polyvPlayerContext, vid, time) {
        console.log(vid);
        console.log(time);
        console.log(polyvPlayerContext, '打算');
        const ts = new Date().getTime();
        const sign = MD5.md5(`${secretkey}${vid}${ts}`);
        this.startTime = time;
        polyvPlayerContext.changeVid({
          vid: vid,
          ts,
          sign
        });
      },
      bindloadedmetadata() {
        let polyvPlayerContext = this.selectComponent('#polyv_player' + this.videoInfo.id);
        polyvPlayerContext.pause();
        if (Number(this.startTime) > Number(polyvPlayerContext.rDuration)) {
          this.startTime = Number(polyvPlayerContext.rDuration);
        }
        polyvPlayerContext.seek(Number(this.startTime));
      },
      statechange(e) {
        let id = '';
        let _this = this;
        if (e.detail.newstate == 'playing' && e.detail.oldstate != 'playing') {
          id = e.currentTarget.id;
          clearInterval(this.timer);
          _this.studyNumber = 0;
          _this.timer = setInterval(function () {
            _this.studyNumber += 1;
            console.log(_this.studyNumber);
          }, 1000);
        }
        if (e.detail.newstate == 'ended' && e.detail.oldstate != 'loading') {
          this.getRecord(e.timeStamp, this.videoInfo);
        }
      },

      bindplaying(e) {
        let _this = this;
        let id = '';
        id = e.currentTarget.id;
        if (_this.watchTime.includes(id.slice(12).toString())) {
          _this.alreadyWatch = false;
          console.log(_this.alreadyWatch);
        } else {
          _this.alreadyWatch = true;
        }
        /**
         * 1.courseType： 0：试看，1：正式
         * 2.正式课且接口返回已经看过
         *
         */
        let times = false;
        if (_this.watchTime.length < 3) {
          times = true;
        } else {
          times = false;
        }
        const polyvPlayer = this.$refs['polyv_player' + this.videoInfo.id];
        if (this.onloadInfo.sourse) return;
        if (_this.alreadyWatch && times && _this.courseType == 1) {
          if (this.fullScreen) {
            polyvPlayer.exitFullScreen();
          }
          _this.$refs.popupCenter.open();
          _this.bindpause(e);
        }
      },
      bindpause(e) {
        let polyvPlayerContext = this.selectComponent('#polyv_player' + this.videoInfo.id);
        polyvPlayerContext.pause();
        clearInterval(this.timer);
        this.getRecord(e.timeStamp, this.videoInfo);
      },
      changeDown(index) {
        let page = { ...this.courseInfo.piUserRecordCourseCatalogueList[Number(index)] };
        page.down = !page.down;
        this.courseInfo.piUserRecordCourseCatalogueList[Number(index)] = page;
        // this.$set(this.courseInfo.piUserRecordCourseCatalogueList[Number(index)],'down' ,!this.courseInfo.piUserRecordCourseCatalogueList[Number(index)].down)
        console.log(this.courseInfo.piUserRecordCourseCatalogueList[Number(index)]);
        console.log('--------------------------------------------------------------------');
      },
      playVideo() {
        this.wifiShow = false;
        const polyvPlayer = this.$refs['polyv_player' + this.videoInfo.id];
        polyvPlayer.autoplay = true;
        polyvPlayer.play();
      },
      purchaseRecordedLessons() {
        uni.$emit('purchasePop-up'); // 触发自定义事件
        uni.navigateBack({
          delta: 1 // 返回上一个页面
        });
      },
      onBindtimeupdate(currentTime) {
        let _this = this;
        if (_this.courseType == 0) {
          //改为 _this.endTime = 180
          const polyvPlayer = this.$refs['polyv_player' + this.videoInfo.id];

          if (currentTime.target.currentTime >= 180) {
            if (this.fullScreen) {
              polyvPlayer.exitFullScreen();
            }

            _this.wifiShow = false;
            _this.audition = true;
            _this.bindpause();
          }
        }
      },
      // 视频提示
      iKnow() {
        //通知后端用户播放了当前视频
        this.alreadyWatch = true;
        this.$refs.popupCenter.close();
        const polyvPlayer = this.$refs['polyv_player' + this.videoInfo.id];
        polyvPlayer.autoplay = true;
        polyvPlayer.play();
      }
    }
  };
</script>

<style lang="scss" scoped>
  .video_main {
    background-color: #fff;
    min-height: 100vh;
    .video_css {
      height: 422rpx;
      background-color: #e4e4e4;
    }
    .video_title {
      color: #333333;
      font-size: 30rpx;
      padding: 22rpx 32rpx;
      // overflow: hidden;
      // text-overflow: ellipsis;  /* 超出部分省略号 */
      // word-break: break-all;  /* break-all(允许在单词内换行。) */
      // display: -webkit-box; /* 对象作为伸缩盒子模型显示 */
      // -webkit-box-orient: vertical; /* 设置或检索伸缩盒对象的子元素的排列方式 */
      // -webkit-line-clamp: 2; /* 显示的行数 */
    }
    .course_content {
      .title_icon {
        color: #333333;
        font-size: 28rpx;
        padding: 0 32rpx;
        vertical-align: middle;
        margin-bottom: 24rpx;
      }
      .title_icon::before {
        content: ' ';
        display: inline-block;
        width: 4rpx;
        height: 26rpx;
        background-color: #489981;
        border-radius: 2rpx;
        margin-right: 12rpx;
        vertical-align: middle;
      }
      // .video_top_css{
      // 	background-color: #F6F7F9;
      // 	padding:24rpx 32rpx;
      // 	color:#555555 ;
      // 	font-weight: bold;
      // 	font-size: 28rpx;
      // }
      // .image_css{
      // 	width:24rpx;
      // 	margin-right:16rpx;
      // }
      // .flex-space-between{
      // 	display: flex;
      // 	justify-content: space-between;
      // }
      // .course_two_css{
      // 	padding:32rpx;
      // 	.title_css{
      // 		color:#555555 ;
      // 		font-size: 28rpx;
      // 	}
      // 	border-bottom:2rpx solid #F6F7F9 ;
      // }
      // .course_two_css:last-child{
      // 	border:none;
      // }
    }
    .no_wifi_css {
      width: 100%;
      height: 422rpx;
      background-color: rgba(0, 0, 0, 0.8);
      font-size: 24rpx;
      color: #fff;
      text-align: center;
      position: fixed;
      top: 0;
      left: 0;
      .wifi_title {
        width: 376rpx;
        margin: 0 auto;
        padding-top: 132rpx;
        line-height: 34rpx;
      }
    }
    .ban_css {
      width: 168rpx;
      height: 56rpx;
      border-radius: 8rpx;
      background-color: #45d670;
      margin: 0 auto;
      font-size: 24rpx;
      color: #fff;
      line-height: 55rpx;
      margin-top: 34rpx;
    }
    .popup_content {
      display: felx;
      justify-content: space-around;
      align-items: center;
      flex-direction: column;
      width: 500rpx;
      height: 490rpx;
      background: #ffffff;
      border-radius: 48rpx;
      .bottom_css {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 280rpx;
        height: 80rpx;
        background: #428a6f;
        border-radius: 48rpx;
        font-size: 32rpx;
        color: #ffffff;
      }
      .bottom_btn_text {
        margin-top: -40rpx;
        font-size: 28rpx;
        color: #a4adb3;
      }
    }
  }
</style>
