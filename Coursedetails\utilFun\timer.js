	const countdownUtil = {
		// 参数：结束时间、定时器、倒计时显示数据结构、倒计时结束回调
			start(endTime, countdownTimer, countdown, callback) {
				const handleCountdownEnd = () => {
					// 倒计时结束时的处理逻辑
					callback('1');
				}
				let timer = null;
				const updateCountdown = () => {
					let endTimeStr = endTime.replace(/-/g, '/');
				  const endTimeMil = new Date(endTimeStr).getTime();
				  const now = new Date().getTime();
				  const timeLeft = endTimeMil - now;
		
				  if (timeLeft <= 0) {
					// clearInterval(countdownTimer);
					this.clear(timer);
					countdown = {
					  hours: '00',
					  minutes: '00',
					  seconds: '00',
					  status: 'finish'
					};
					handleCountdownEnd();
					return;
				  }
		
				  const hours = Math.floor((timeLeft / (1000 * 60 * 60)));
				  const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
				  const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
		
				  countdown = {
					hours: String(hours).padStart(2, '0'),
					minutes: String(minutes).padStart(2, '0'),
					seconds: String(seconds).padStart(2, '0')
				  };
				  callback('2', countdown);
				};
				updateCountdown();
				timer = setInterval(updateCountdown, 1000);
				return {
				  clear: () => this.clear(timer)
				};
		},
		clear(timer) {
			if (timer) {
				clearInterval(timer);
				timer = null;
			}
		}
	}
	
  
  module.exports = {
	  countdownUtil
  }