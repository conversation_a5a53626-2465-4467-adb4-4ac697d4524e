<template>
  <div>
    <!-- 静态弹窗 -->
    <uni-popup ref="popup" type="center" title="温馨提示" :safe-area="false">
      <view class="popup-container">
        <view class="popup-title">温馨提示</view>
        <view class="popup-content">
          <view class="message">{{ tipsText }}</view>
          <view class="dialog-btn" @click.stop="closePopup">确认</view>
        </view>
      </view>
    </uni-popup>
  </div>
</template>

<script>
  export default {
    name: 'TipsDownloadPopup',
    props: {
      //提示文案
      tipsText: {
        type: String,
        default: '请下载鼎校甄选APP进行上课'
      }
    },
    data() {
      return {};
    },
    methods: {
      // 关闭弹窗
      closePopup() {
        this.$refs.popup.close();
      },
      openPopup() {
        this.$nextTick(() => {
          this.$refs.popup.open();
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .popup-container {
    display: flex;
    flex-direction: column;
    justify-items: center;
    align-items: center;
    width: 75vw;
    padding: 40rpx;
    background-color: #fff;
    border-radius: 24rpx;
    text-align: center;
    box-shadow: 0 0 20rpx rgba(0, 0, 0, 0.2);
  }
  .popup-title {
    font-size: 38rpx;
    margin-bottom: 40rpx;
    font-weight: 600;
  }

  .popup-content {
    display: flex;
    flex-direction: column;
    justify-items: center;
    align-items: center;
  }

  .message {
    font-size: 32rpx;
    color: #333;
    line-height: 1.5;
    margin-bottom: 30rpx;
  }

  .dialog-btn {
    margin-top: 50rpx;
    width: 70%;
    height: 80rpx;
    line-height: 80rpx;
    background-color: #339378;
    color: #fff;
    font-size: 30rpx;
    font-weight: bold;
    border-radius: 40rpx;
    text-align: center;
  }
</style>
