<template>
  <view class="">
    <view class="flex-a-c nav-bar plr-20">
      <uni-icons type="left" size="24" @click="returnBack"></uni-icons>
      <view class="nav_title f-28">试课推荐</view>
    </view>
    <view class="plr-30 pb-20 single">
      <view class="content plr-30 f-30 mt-10">
        <form id="#nform">
          <view class="information ptb-15 border-bottom">
            <view style="width: 35%" class="bold">学员姓名：</view>
            <view class="phone-input">
              <input type="text" v-model="trialname" name="trialname" placeholder="请输入学员姓名" class="input c-00" @blur="nameSearch" confirm-type="search" />
            </view>
          </view>
          <view class="information ptb-15 border-bottom">
            <view style="width: 35%" class="bold">联系方式：</view>
            <view class="phone-input">
              <input type="number" v-model="mobile" name="mobile" placeholder="请输入联系方式" class="input c-00" maxlength="11" @blur="nameSearch" />
            </view>
          </view>
          <view class="information ptb-15">
            <view style="width: 35%" class="bold">试课状态：</view>
            <view class="phone-input">
              <uni-data-select class="uni-select w100" v-model="value" :localdata="range" @change="change" placeholder="请选择试课状态"></uni-data-select>
            </view>
          </view>
        </form>
      </view>
      <!--      <view class="flex-a-c mb-30" @click="skintap('Trialclass/collectionProcess')">
        <image :src="imgHost + 'alading/correcting/prompt_icon.png'" class="prompt_icon"></image>
        <view class="ml-20 c-99 f-28">红包领取流程</view>
      </view> -->
      <scroll-view
        v-if="trialList.list != undefined && trialList.list.length != 0"
        scroll-y="true"
        :scroll-top="scrollTop"
        :style="{ height: myHeight + 'rpx' }"
        class="pb-30"
        @scrolltolower="scrolltolower"
      >
        <view class="detailed" v-for="item in trialList.list" @click="shopNews(item)">
          <view class="flex refferList_item_top" :class="!item.isRefund ? 'pb-30 border-bottom' : ''">
            <image class="infopic" :src="item.portrait == '' ? avaUrl : item.portrait"></image>
            <view>
              <view class="personl mb-5 lh-60">
                <view class="nameEllipsis">学员姓名：{{ item.name }}</view>
                <view ref="myNode" v-if="item.status == -1 && !item.isRefund && item.hasChat != 1" class="classPayStatus_btn" style="background-color: rgb(53, 168, 247)">
                  已购试课券
                </view>
                <view
                  ref="myNode"
                  v-if="(item.status == 0 || item.status == 1) && !item.isRefund && item.hasChat != 1"
                  class="classPayStatus_btn"
                  style="background-color: rgb(67, 146, 134)"
                >
                  待排课
                </view>
                <view ref="myNode" v-if="item.status == 2 && !item.isRefund && item.hasChat != 1" class="classPayStatus_btn" style="background-color: rgb(45, 192, 50)">
                  已试课
                </view>
                <view ref="myNode" v-if="item.isRefund && item.hasChat != 1" class="classPayStatus_btn" style="background-color: rgb(198, 198, 198)">已退款</view>
                <view ref="myNode" v-if="item.status == 3 && !item.isRefund && item.hasChat != 1" class="classPayStatus_btn" style="background-color: rgb(143, 129, 254)">
                  已排课
                </view>
                <view ref="myNode" v-if="item.hasChat == 1" class="classPayStatus_btn" style="background-color: rgb(253, 132, 66)">已建群</view>
              </view>
              <view class="personl flex-s">
                <view>试课人联系方式：{{ item.mobile }}</view>
                <view v-if="item.status != -1 && !item.isRefund">
                  <uni-icons type="forward" size="16" color="#C7C7C7"></uni-icons>
                </view>
              </view>
            </view>
          </view>

          <view v-if="item.hasChat == 1 || item.status == 3" class="border-bottom">
            <view class="courseInfo">
              <view class="mb-5 mt-10 lh-60">
                <text class="gray_tit">交付中心</text>
                {{ item.deliverName == '' ? '数据暂无' : item.deliverName }}
              </view>
              <view class="mb-5 lh-60">
                <text class="gray_tit">交付小组</text>
                {{ item.teamName == '' ? '数据暂无' : item.teamName }}
              </view>
              <view class="mb-5 lh-60">
                <text class="gray_tit">组长联系方式</text>
                {{ item.teamLeaderMobile == '' ? '数据暂无' : item.teamLeaderMobile }}
              </view>
            </view>
          </view>

          <view class="refferList_item_bottom" :class="[!item.isRefund ? 'mt-20' : '', 'flex-y-c']" style="justify-content: flex-end">
            <!-- :style="{ 'justify-content': item.redPacket != null && item.redPacket != 0 ? 'space-between' : 'flex-end' }" -->
            <!--   <view class="" v-if="item.redPacket != null && item.redPacket != 0" style="display: flex; align-items: center">
              <image :class="item.redPacket == 2 ? 'redbagIcon2' : 'redbagIcon'" :src="imgHost + 'alading/correcting/redbag_status' + item.redPacket + '.png'"></image>
              <text :class="'redbagText' + item.redPacket">{{ item.redPacket != 1 ? (item.redPacket == 2 ? '红包已领' : '红包已失效') : ' 红包已发' }}</text>
            </view>
            <view v-if="item.redPacket != null && item.redPacket == 3 && item.canSendRedPacket == 1" class="flex-c sending" @click.stop="reviewsend(item)">
              <image class="redbagIcon" :src="imgHost + 'alading/correcting/redbag_status1.png'"></image>
              <text class="redbagText1">重新发送红包</text>
            </view>
            <view v-if="item.status == 2 && item.redPacket == 0 && item.canSendRedPacket == 1" class="flex-c sending mr-30" @click.stop="sendredEnvelopes(item)">
              <image class="redbagIcon" :src="imgHost + 'alading/correcting/redbag_status1.png'"></image>
              <text class="redbagText1">点击发送红包</text>
            </view> -->

            <template v-if="item.isOneToMany == 1">
              <view v-if="isBusinessMerchant" class="btntrial1 plr-20" @tap.stop="goToAPPDownload(2)" style="width: auto">请下载鼎星星App进行填写</view>
              <view v-else class="btntrial1 plr-20" @tap.stop="goToAPPDownload(1)" style="width: auto">请下载鼎校甄选App进行填写</view>
            </template>
            <template v-else>
              <view v-if="item.status == -1 && !item.isRefund" class="btntrial" @tap.stop="gotrial(item)">填写试课单</view>
              <view v-if="item.status != -1 && !item.hasCharge" class="btntrial2" @tap.stop="editTrial(item)">修改试课单</view>
              <view v-if="item.status != -1 && !item.isRefund" class="btntrial1" @tap.stop="gotrial(item)">查看试课单</view>
            </template>
          </view>
        </view>
        <view v-if="no_more && trialList.list != undefined && trialList.list.length > 0">
          <u-divider text="到底了"></u-divider>
        </view>
      </scroll-view>
      <view v-if="trialList.list != undefined && trialList.list.length == 0" class="t-c flex-col" :style="{ height: useHeight + 'rpx' }">
        <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s" mode="widthFix"></image>
        <view style="color: #bdbdbd">暂无数据</view>
        <view style="color: #bdbdbd" class="mt-10">立即邀请学员试课吧</view>
      </view>
      <view class="plr-50 pt-10" v-if="trialList.list != undefined && trialList.list.length == 0">
        <button class="phone-btn" @tap="skintap('/pages/selectCourse/selectCourse', 1)">邀请试课</button>
      </view>

      <!-- 红包失效弹窗 -->
      <!--      <uni-popup ref="redBagLoseEfficacy" type="center" :maskClick="true" :classBG="''">
        <commonDialog :showCancel="false" :title="'红包失效'" :content="dialogContent" @confirm="reviewsend" @closeDialog="closeRedPacker"></commonDialog>
      </uni-popup> -->

      <!-- 红包发送成功 -->
      <!--      <uni-popup ref="redEnvelopepopup" type="center">
        <view class="bg-ff radius-25 ptb-60 plr-40 t-c" style="width: 590rpx">
          <image :src="imgHost + 'dxSelect/icon_green_yes.png'" class="green_yes"></image>
          <view class="f-30 c-33 bold mt-20 mb-10">红包发送成功</view>
          <text class="c-66 f-30">请您24小时内去“鼎校智能教育”公众号领取，超过时间红包会失效需要重新发送哦～</text>
        </view>
      </uni-popup> -->

      <uni-popup ref="failpopup" type="top" mask-background-color="rgba(0,0,0,0)">
        <view class="flex-c fail_content">
          <view class="bg-ff radius-50 ptb-30 plr-40 flex-c fail_shadow" style="width: 540rpx">
            <image :src="imgHost + 'dxSelect/image/fail.png'" class="fail_icon"></image>
            <text class="c-33 f-30 ml-20">发送失败，请稍后重试</text>
          </view>
        </view>
      </uni-popup>
    </view>
  </view>
</template>
<script>
  import Util from '@/util/util.js';
  const { $navigationTo, $getSceneData, $showError, $showMsg, $http } = require('@/util/methods.js');
  import commonDialog from '@/components/commonDialog.vue';
  // import { error } from '../../../util/graceChecker';
  export default {
    components: {
      commonDialog
    },
    data() {
      return {
        // 试课状态
        value: '',
        range: [
          {
            value: -1,
            text: '已购试课券'
          },
          {
            value: 1,
            text: '待排课'
          },
          {
            value: 3,
            text: '已排课'
          },
          {
            value: 2,
            text: '已试课'
          }
        ],
        trialname: '', // 昵称
        mobile: '', // 试课人手机号
        // ph: '', // 窗口高度
        svHeight: 0, // 暂无数据距离底边距离
        infolist: [], // 个人信息列表
        resultShow: false,
        show: false,
        no_more: false,
        trialList: {}, // 试课推荐列表
        page: 1,
        expId: '',
        imgHost: getApp().globalData.imgsomeHost,
        dialogContent: '红包超24小时未领取已失效，将为您重新发送推荐红包，请注意在鼎校智能教育（服务号）公众号查收！ ',
        chooseRecommendItem: {}, //选中的列表
        avaUrl: Util.getCachedPic('https://document.dxznjy.com/dxSelect/home_avaUrl.png', 'home_avaUrl_path'),
        scrollTop: 0,
        useHeight: 0, //除头部之外高度
        myHeight: 0,

        infoLists: {}, // 邀请试课数据
        shareContent: {},
        isBusinessMerchant: false //是否是是否是b端商户
      };
    },
    onReady() {
      let that = this;
      uni.getSystemInfo({
        //调用uni-app接口获取屏幕高度
        success(res) {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          that.useHeight = h - 480;
          that.myHeight = h - 390;
        }
      });
    },
    onReachBottom() {
      this.scrolltolower();
    },
    onShow() {
      this.page = 1;
      this.homeData();
      this.clickHandle();
      this.getTriallist(false, this.page); // 会员试课推荐列表
      // this.$refs.redEnvelopepopup.open();
      // this.course()
    },
    onLoad() {
      // 是否为是否是b端商户
      this.isBusinessMerchant = uni.getStorageSync('isBusinessMerchant') || false;
    },
    // onShareAppMessage(res) {
    // 	let url = `/pages/beingShared/index?scene=${uni.getStorageSync('user_id')}&type=${this.shareContent.type}&id=${this.shareContent.id}`;
    // 	console.log(111111111111)
    // 	console.log(url)
    // 	setTimeout(()=>{
    // 		this.$refs.sharePopup.close();
    // 	},2000)
    // 	return {
    // 		title: '叮，你的好友敲了你一下，赶紧过来看看',
    // 		imageUrl: this.shareContent.imgurl,
    // 		path: `/pages/beingShared/index?scene=${uni.getStorageSync('user_id')}&type=${this.shareContent.type}&id=${this.shareContent.id}`
    // 	}
    // },
    methods: {
      goToAPPDownload(type) {
        console.log('🚀 ~ goToAPPDownload ~ type:', type);
        // type 1 鼎校甄选  2 鼎星星
        uni.navigateTo({
          url: `/Trialclass/APPDownload?type=${type}`
        });
      },
      editTrial(item) {
        let orderId = item.orderId;
        uni.navigateTo({
          url: `/Trialclass/index?orderId=${orderId}&curriculumId=${item.curriculumId}&payStatus=1&isEdit=true`
        });
      },
      returnBack() {
        uni.navigateBack();
      },
      // 会员专享分享
      shareVip(type, item) {
        console.log(item);
        console.log('会员专享分享');
        this.shareContent.type = type;
        let id = '',
          imgurl = '';
        //type 1课程  2学习超人（会员） 3超人俱乐部
        if (type == '1') {
          id = item.courseId;
          imgurl = item.courseImage;
        } else {
          id = item.mealId;
          imgurl = item.mealImage;
        }
        this.shareContent.id = id;
        this.shareContent.imgurl = imgurl;
        this.posterShare();
      },

      // 点击海报
      posterShare() {
        uni.navigateTo({
          url: `/splitContent/poster/index?type=${this.shareContent.type}&id=${this.shareContent.id}`
        });
      },
      async course() {
        let _this = this;
        const res = await $http({
          url: 'zx/course/courseList',
          data: {
            indexShow: 1,
            cityCode: '',
            cateId: '',
            page: 1,
            cateType: 1
          }
        });
        if (res) {
          for (let i = 0; i < res.data.list.length; i++) {
            if (res.data.list[i].courseId == '1116761476733865984') {
              _this.infoLists = res.data.list[i];
              console.log(_this.infoLists);
            }
          }
        }
      },
      // 试课反馈下拉框
      change(e) {
        console.log('e:', e);
        this.value = e;
        this.getTriallist();
      },
      skintap(url, key) {
        if (key == 1) {
          console.log(url);
          uni.switchTab({
            url: url
          });
        } else {
          $navigationTo(url);
        }
      },
      shopNews(item) {
        let that = this;
        that.id = item.id;
        if (item.isRefund) {
          return;
        }
        let statusText = '';
        // statusText = this.$refs.myNode.innerText;
        // console.log(statusText);
        // return;
        if (item.status == -1 && !item.isRefund && item.hasChat != 1) {
          statusText = '已购试课券';
        } else if ((item.status == 0 || item.status == 1) && !item.isRefund && item.hasChat != 1) {
          statusText = '待排课';
        } else if (item.status == 2 && !item.isRefund && item.hasChat != 1) {
          statusText = '已试课';
        } else if (item.isRefund && item.hasChat != 1) {
          statusText = '已退款';
        } else if (item.status == 3 && !item.isRefund && item.hasChat != 1) {
          statusText = '已排课';
        } else if (item.hasChat == 1) {
          statusText = '已建群';
        }
        if (item.status == 0) {
          uni.showToast({
            icon: 'none',
            title: '该订单未提交哦'
          });
        } else if (item.status > 0) {
          console.log(statusText, '11111111111111111');
          uni.navigateTo({
            url: `/Trialclass/shopnews?id=${that.id}&&statusText=${statusText}`
          });
        }
      },
      //会员试课推荐列表
      async getTriallist(isPage, page) {
        let _this = this;
        uni.showLoading();
        const res = await $http({
          url: 'zx/exp/getPageList',
          data: {
            name: _this.trialname,
            mobile: _this.mobile,
            status: _this.value,
            page: page ? page : 0
          }
        });
        uni.hideLoading();
        if (res) {
          if (isPage) {
            let old = _this.trialList.list;
            _this.trialList.list = [...old, ...res.data.list];
          } else {
            _this.trialList = res.data;
          }
        }
      },
      //查询
      nameSearch(e) {
        if (this.mobile.trim() || this.mobile == '') {
          this.getTriallist();
        }
        if (this.trialname.trim() || this.trialname == '') {
          this.getTriallist();
        }
      },
      async homeData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          _this.infolist = res.data;
        }
      },

      //试课单
      gotrial(item) {
        let orderId = item.orderId;
        // 1 填写试课单  2查看试课单
        let payStatus = 1;
        if (item.status != -1) {
          payStatus = 2;
        }
        uni.navigateTo({
          url: '/Trialclass/index?orderId=' + orderId + '&payStatus=' + payStatus + '&curriculumId=' + item.curriculumId
        });
      },

      // 滚动到最底部
      scrolltolower() {
        if (this.page >= this.trialList.totalPage) {
          this.no_more = true;
          return false;
        }
        this.getTriallist(true, ++this.page);
      },

      // 红包弹窗关闭
      closeRedPacker() {
        this.$refs.redBagLoseEfficacy.close();
      },

      // 确定重新发送红包按钮
      async reviewsend(item) {
        console.log('重新发送');
        this.chooseRecommendItem = item;
        let res = await this.$httpUser.post(`v2/mall/wxmp/redPacketTimeOutSendRetry?orderId=${this.chooseRecommendItem.expId}`);
        if (res.data.success) {
          this.$refs.redEnvelopepopup.open();
          setTimeout(() => {
            this.$refs.redEnvelopepopup.close();
          }, 3000);
          this.getTriallist(); // 会员试课推荐列表
          this.closeRedPacker();
        } else {
          console.log('发送失败');
        }
      },

      // 滚动条回到顶部
      clickHandle() {
        this.scrollTop = this.scrollTop === 0 ? 1 : 0;
      },

      // 点击发送红包
      async sendredEnvelopes(item) {
        let data = {
          expId: item.expId
        };
        uni.showLoading();
        let res = await this.$httpUser.get('zx/exp/sendRedPacket', data);
        uni.hideLoading();
        if (res.data.success) {
          this.$refs.redEnvelopepopup.open();
          setTimeout(() => {
            this.$refs.redEnvelopepopup.close();
          }, 3000);
          this.getTriallist();
        } else {
          console.log('发送失败');
        }
      }
    }
  };
</script>
<style lang="scss" scoped>
  .gray_tit {
    color: #a7a7a7;
    margin-right: 20rpx;
  }
  .single {
    padding-top: 160rpx;
  }

  .content {
    background-color: #fff;
    border-radius: 14rpx;
    margin-bottom: 30rpx;
  }

  .information {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .border-bottom {
    border-bottom: 1px solid #efefef;
  }

  .phone-input {
    background: #fff;
    border-radius: 8rpx;
    width: 100%;
    height: 70rpx;
    font-size: 28rpx;
    color: #999999;
    display: flex;
    padding-left: 30rpx;
    align-items: center;

    input {
      font-size: 28rpx;
    }
  }

  .name-input {
    background: #fff;
    border-radius: 8rpx;
    font-size: 28rpx;
    color: #999999;
    height: 70rpx;
    display: flex;
    justify-content: space-between;
    padding: 0 30rpx;
    margin-top: 30rpx;
    align-items: center;
    border: 3rpx solid #ececea;
  }

  /deep/.uni-select {
    padding: 0 10rpx 0 0;
    border: 0;
  }

  /deep/.uni-select__input-placeholder {
    font-size: 28rpx;
  }

  .uni-list-cell-db {
    background: #fff;
    border-radius: 8rpx;
    width: 100%;
    height: 70rpx;
    font-size: 28rpx;
    color: #999999;
    display: flex;
    padding-left: 30rpx;
    align-items: center;
    border: 3rpx solid #ececea;
  }

  .tips {
    position: relative;
    width: 100%;
  }

  /deep/.phone-btn {
    width: 100%;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 45rpx;
    font-size: 30rpx;
    color: #fff;
    background: linear-gradient(to bottom, #88cfba, #1d755c);
  }

  .detailed {
    background-color: #fff;
    padding: 30rpx;
    border-radius: 14rpx;
    margin-bottom: 30rpx;
  }

  .infopic {
    width: 100rpx;
    height: 100rpx;
    margin-right: 10rpx;
    border-radius: 50%;
    // background-color: pink;
  }

  .refferList_item_top {
    display: flex;
    width: 100%;
    // padding-bottom: 32upx;
    // border-bottom: 1px solid #EEEEEE;
  }

  .refferList_item_bottom {
    width: 100%;
    display: flex;
  }

  .personl {
    font-size: 28rpx;
    display: flex;
    width: 500upx;
    justify-content: space-between;
    align-items: center;
  }

  .courseInfo {
    font-size: 28rpx;
    display: flex;
    width: 550upx;
    flex-direction: column;
    justify-content: flex-start;
    letter-spacing: 2rpx;
    // align-items: center;
  }

  .nameEllipsis {
    width: 300rpx;
    /* 定好宽度 */
    overflow: hidden;
    width-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    font-size: 30upx;
  }

  /deep/.btn {
    padding: 3rpx 10rpx;
    border-radius: 4rpx;
    font-size: 26rpx;
    line-height: 36rpx;
  }

  .stay_btn {
    background-color: #439286;
    color: #fff;
    padding: 3rpx 10rpx;
    border-radius: 4rpx;
    font-size: 26rpx;
    line-height: 36rpx;
  }

  .wh100 {
    width: 100rpx;
    height: 100rpx;
    background-color: palegoldenrod;
    border-radius: 50%;
    margin-right: 30rpx;
  }

  .img_s {
    width: 160rpx;
  }

  .classPayStatus_btn {
    display: inline-block;
    padding: 2upx 6upx;
    text-align: center;
    border-radius: 4upx;
    color: #fff;
    font-size: 24upx;
    height: 35upx;
    line-height: 35upx;
  }

  .btntrial {
    width: 180rpx;
    height: 60rpx;
    line-height: 60rpx;
    border-radius: 35rpx;
    font-size: 30rpx;
    text-align: center;
    color: #ea6031;
    border: 1px solid #ea6031;
  }

  .btntrial2 {
    width: 180rpx;
    height: 60rpx;
    line-height: 60rpx;
    border-radius: 35rpx;
    font-size: 30rpx;
    text-align: center;
    color: #2e896f;
    margin-right: 10rpx;
    border: 1px solid #2e896f;
  }
  .btntrial1 {
    width: 180rpx;
    height: 60rpx;
    line-height: 60rpx;
    border-radius: 35rpx;
    font-size: 30rpx;
    text-align: center;
    color: #fff;
    background: #2e896f;
  }

  .redbagIcon {
    width: 44upx;
    height: 40upx;
    margin-right: 10upx;
  }

  .redbagIcon2 {
    width: 44upx;
    height: 50upx;
    margin-right: 10upx;
  }

  .redbagText1 {
    font-size: 26upx;
    color: #ea6031;
  }

  .redbagText2 {
    font-size: 26upx;
    color: #f7931e;
  }

  .redbagText3 {
    font-size: 26upx;
    color: #999999;
  }

  .prompt_icon {
    width: 30rpx;
    height: 30rpx;
  }

  .sending {
    width: 245rpx;
    height: 60rpx;
    line-height: 60rpx;
    border: 1px solid #ea6031;
    border-radius: 45rpx;
  }

  .green_yes {
    width: 110rpx;
    height: 110rpx;
  }

  .fail_icon {
    width: 38rpx;
    height: 38rpx;
  }

  .nav-bar {
    position: fixed;
    background-color: #f3f8fc;
    width: 100%;
    height: 100rpx;
    padding-top: 30rpx;
    z-index: 9;
  }

  .nav_title {
    margin-left: 32%;
  }

  .fail_content {
    margin-top: 80rpx;
  }

  .fail_shadow {
    box-shadow: 0 0 10rpx 0 rgba(0, 0, 0, 0.2);
  }
</style>
