<template>
  <view>
    <view class="video_top_css" v-if="itemInfo.videoUrl" @click="getVideoUrl(itemInfo, 1)">
      <image v-if="itemInfo.play" mode="widthFix" class="image_css" src="https://document.dxznjy.com/course/e68e7c737ae84ec88832288da8a081a9.png"></image>
      <image v-else mode="widthFix" class="image_css" src="https://document.dxznjy.com/course/5547f897939d493d8a151137c3a56241.png"></image>
      <text>{{ itemInfo.catalogueName }}</text>
    </view>
    <view v-else>
      <view class="flex-space-between video_top_css">
        <view>{{ itemInfo.catalogueName }}</view>
        <view>
          <u-icon v-if="itemInfo.down" @click="changeDown(itemInfo)" name="arrow-up" color="#575757" size="32"></u-icon>
          <u-icon v-else name="arrow-down" @click="changeDown(itemInfo)" color="#575757" size="32"></u-icon>
          <!-- arrow-down -->
        </view>
      </view>
      <view v-if="itemInfo.down">
        <view v-for="info in itemInfo.children" :key="info.id" class="course_two_css" @click="getVideoUrl(info, 2)">
          <image v-if="info.play" style="height: 22rpx" class="image_css" src="https://document.dxznjy.com/course/e68e7c737ae84ec88832288da8a081a9.png"></image>
          <image v-else mode="widthFix" class="image_css" src="https://document.dxznjy.com/course/5547f897939d493d8a151137c3a56241.png"></image>
          <text class="title_css">{{ info.catalogueName }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    props: ['item', 'index', 'type', 'courseId', 'userCode'],
    data() {
      return {
        itemInfo: {}
      };
    },
    watch: {
      // item(newVal) {
      // 	console.log(newVal)
      // 	this.itemInfo=newVal
      // 	console.log('---------------newValnewValnewValnewValnewVal-----------------------')
      // },
      item: {
        immediate: true,
        handler(newVal) {
          this.itemInfo = { ...newVal };
        }
      }
    },

    methods: {
      changeDown(item) {
        // uni.navigateTo({
        //   url: '/Coursedetails/study/courseDetail?type=0'
        // });
        // item.down=!item.down
        this.$set(item, 'down', !item.down);
        this.$emit('changeDown', this.index);
      },
      getVideoUrl(info, key) {
        // type == 0 为试看，只跳转第一次
        console.log('info         ', this.courseId);
        console.log('key', key);
        if (this.courseId) {
          uni.navigateTo({
            url: `/Coursedetails/study/courseDetail?type=0&courseId=${this.courseId}&studentCode=${this.userCode}`
          });
        } else {
          console.log('333333333333333333', info);
          this.$emit('getVideoUrl', { info: info, key: key });
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .video_top_css {
    background-color: #f6f7f9;
    padding: 24rpx 32rpx;
    color: #555555;
    font-weight: bold;
    font-size: 28rpx;
  }
  .image_css {
    width: 24rpx;
    height: 10rpx;
    margin-right: 16rpx;
  }
  .flex-space-between {
    display: flex;
    justify-content: space-between;
  }
  .course_two_css {
    padding: 32rpx;
    .title_css {
      color: #555555;
      font-size: 28rpx;
    }
    border-bottom: 2rpx solid #f6f7f9;
  }
  .course_two_css:last-child {
    border: none;
  }
</style>
