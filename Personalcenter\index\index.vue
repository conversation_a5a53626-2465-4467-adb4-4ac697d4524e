<template>
  <view class="schedule-card">
    <view class="periods">
      <view class="left">
        <picker @change="bindPickerChange" :value="index" :range="columns" range-key="periods">
          <view v-if="stageShow">
            第{{ nowWeek || '' }}期
            <uni-icons type="bottom" size="14" style="margin-left: 10rpx"></uni-icons>
          </view>
        </picker>
        <view v-if="stageShow" class="time-range">{{ startTime || '' }}至{{ endTime || '' }}</view>
        <view v-if="!stageShow" @click="isShow = true">
          {{ date }}
          <uni-icons type="bottom" size="16" style="margin-left: 10rpx"></uni-icons>
        </view>

        <u-datetime-picker :show="isShow" v-model="value1" mode="year-month" @confirm="confirm" @cancel="dateClose"></u-datetime-picker>
      </view>
      <view class="right" @click="unFold">联系交付中心</view>
    </view>
    <view class="content" :style="stageShow ? 'margin-bottom:124rpx' : 'margin-bottom:0'">
      <view class="content-item">
        <luncCalendar
          ref="timetable"
          :dateList="dates"
          @changeNowWeek="changeDate"
          @changePriodsWeek="changePriodsWeek"
          :showShrink="true"
          shrinkState="week"
          :callback="onCallback"
        ></luncCalendar>
      </view>
      <view class="curriculum">
        <view class="row" v-for="(item, index) in timetableType" :key="index">
          <view class="time-item">
            <view class="time">
              <text>{{ item.name }}</text>
            </view>
          </view>
        </view>
        <view class="table" style="height: 2024rpx">
          <view class="content" v-for="(item, index) in subjectList" :key="index" style="padding: 0">
            <view v-for="course in item.courseList">
              <view class="course-news" :style="{ height: course.height + 'rpx', 'margin-top': course.top + 'rpx' }">
                {{ course.courseName + '@' + course.teacherName }}
              </view>
            </view>

            <!-- <view class="time-table" v-for="ids in 17"> -->
            <!-- <view class="time-table-inner" v-for="idx in 6">
								<view style="background-color: #94D6F3; height: 100%; width: 100%;box-sizing:content-box" v-if="checkShow(item.courseList, ids, idx)"></view>
								<view style="background-color: #fff; height: 100%; width: 100%;" v-else></view>
							</view> -->
            <!-- </view> -->
          </view>
        </view>
      </view>
    </view>

    <view class="payment" v-if="stageShow">
      <view class="left" v-if="payShow">
        <view class="mb-15">
          <text class="c-fea">{{ payInformation.course || '' }}</text>
          学时
        </view>
        <view>
          <text class="c-fea">{{ payInformation.deliverHours || '' }}</text>
          交付学时
        </view>
      </view>
      <view class="left" v-if="!payShow">
        <view class="mb-15">
          <text class="c-fea">{{ payInformation.course || '' }}</text>
          学时
          <text class="ml-15" v-if="payInformation.isMember">
            <text class="c-fea">{{ payInformation.useCurrency }}</text>
            鼎学币
          </text>
        </view>
        <view>
          <text class="c-fea">{{ payInformation.deliverHours || '' }}</text>
          交付学时
          <text class="ml-15">
            <text class="c-fea">{{ payInformation.amount || '' }}</text>
            元
          </text>
        </view>
      </view>

      <view class="right">
        <view class="right-pay" v-if="payShow">
          <view>
            共计：
            <text class="pay_money">￥{{ payInformation.amount || '' }}</text>
          </view>
          <button class="paid-btn">已支付</button>
        </view>
        <button class="pay-btn" @click="payNews" v-if="!payShow">立即付款</button>
      </view>
    </view>
    <view class="positionRelative">
      <u-popup :show="show" :round="24" mode="center" @touchmove.stop.prevent="moveHandle" class="pay-content">
        <view>
          <image class="cartoom_image" src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
        </view>
        <view class="review_close" @click="close">
          <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
        </view>
        <view class="orderinfo">
          <!-- <uni-icons class="close-icon" @click="close" type="closeempty" size="18" color="#909399"></uni-icons> -->
          <view class="title">支付订单</view>
          <view class="container f-30" v-if="payInformation.isMember">
            <view class="mb-30">学时：{{ payInformation.course }}节</view>
            <view class="mb-30" v-if="!payInformation.payment">您当前鼎学币剩余：{{ payInformation.haveCurrency }}</view>
            <view class="mb-30" v-if="payInformation.payment">您当前学时剩余：{{ payInformation.haveCourse }}节</view>
            <view class="mb-30" v-if="!payInformation.payment">本次使用：{{ payInformation.useCurrency }}</view>
            <view class="mb-30" v-if="payInformation.payment">本次使用：{{ payInformation.useCourse }}节</view>
            <view class="mb-30">现金支付：{{ payInformation.moneyCourse }}元</view>
            <view class="mb-30">交付学时：{{ payInformation.deliverHours }}节</view>
            <view class="mb-40">现金支付：{{ payInformation.moneyHours }}元</view>
          </view>
          <view class="container f-30" v-else>
            <view class="mb-30">学时：{{ payInformation.course }}节</view>
            <view class="mb-30">您当前学时剩余：{{ payInformation.haveCourse }}节</view>
            <view class="mb-30">交付学时：{{ payInformation.deliverHours }}节</view>
            <view class="mb-40">现金支付：{{ payInformation.moneyHours }}元</view>
          </view>
          <view class="pay-price line_s">
            <view class="mt-40 t-c">
              <view>
                共计：
                <text class="c-fea f-36">￥{{ payInformation.amount }}</text>
              </view>
            </view>
            <view class="mt-35" style="margin-left: 32.1%" v-if="payInformation.isMember">
              <button class="pay-btn" @click="payMent">支付</button>
            </view>
            <view class="mt-35" style="margin-left: 32.1%" v-if="!payInformation.isMember && payInformation.isCourse">
              <button class="pay-btn" @click="payMent">支付</button>
            </view>
            <view class="pay-tips" v-if="!payInformation.isMember && !payInformation.isCourse">
              <u-icon name="error-circle" size="34" color="#F64C53"></u-icon>
              <text class="ml-10 content_wrap">您的剩余学时不足，请您联系门店充值学时后再来支付吧</text>
            </view>
            <view class="contact-s" v-if="!payInformation.isMember && !payInformation.isCourse">
              <button class="store-btn" @click="goStore()">联系门店</button>
              <button class="no-btn">支付</button>
            </view>
          </view>
        </view>
      </u-popup>
      <u-popup :show="shows" :round="24" mode="center" @touchmove.stop.prevent="moveHandle" class="pay-content">
        <!-- <view class="orderinfo">
					<uni-icons class="close-icon" @click="openUp" type="closeempty" size="18" color="#909399">
					</uni-icons>
					<view class="title" v-if="contactStore">联系门店</view>
					<view class="title" v-else>联系交付中心</view>
					<view class="container paddingTop36 paddingBottom46">
						<view class="paylists center_s">{{deliverNews.name + ':'+ deliverNews.mobile}}</view>
					</view>
					<view class="pay-price">
						<view style="margin-top: 33rpx;margin-left: 32.10%;">
							<button class="pay-btn" @click="goPhoneBtn()">立即拨打</button>
						</view>
					</view>
				</view> -->
        <common-dialog
          :title="contactStore ? '联系门店' : '联系交付中心'"
          :content="deliverNews.name + ':' + deliverNews.mobile"
          :operate="'立即拨打'"
          @confirm="goPhoneBtn"
          @closeDialog="openUp"
        ></common-dialog>
      </u-popup>
    </view>
  </view>
</template>
<script>
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  const { $http } = require('@/util/methods.js');
  import dayjs from 'dayjs';
  import commonDialog from '@/components/commonDialog.vue';
  import luncCalendar from '../components/lunc-calendar/components/lunc-calendar/lunc-calendar.vue';
  export default {
    components: {
      commonDialog,
      luncCalendar
    },
    data() {
      return {
        payInfo: {},
        flag1: false,
        datetime: '',
        weeks: '',
        tMonth: '',
        show: false, // 支付订单信息弹窗
        shows: false, // 联系方式弹窗
        isShow: false,
        value1: Number(new Date()),
        childValue: '',
        date: '', // 默认当前月份
        dates: '',
        studentCode: '',
        subjectList: [],
        planId: 0,
        type: '',
        deliverNews: '',
        payShow: false, // 订单是否已支付
        contactStore: false, // 联系门店
        stageShow: false, // 是否显示期数
        nowWeek: 0, //第几期
        index: 0,
        periodsShow: false,
        startTime: '',
        endTime: '',
        columns: [], // 期数集合
        ClassStyle: '',
        collectionList: '',
        switchDate: false,
        switching: false, // 是否已经切换期数
        payInformation: {}, // 订单支付信息
        tableList: [
          {
            index: '1',
            name: '星期一'
          },
          {
            index: '2',
            name: '星期二'
          },
          {
            index: '3',
            name: '星期三'
          },
          {
            index: '4',
            name: '星期四'
          },
          {
            index: '5',
            name: '星期五'
          },
          {
            index: '6',
            name: '星期六'
          },
          {
            index: '7',
            name: '星期日'
          }
        ],
        timetableType: [
          {
            index: '1',
            name: '08:00'
          },
          {
            index: '2',
            name: '09:00'
          },
          {
            index: '3',
            name: '10:00'
          },
          {
            index: '4',
            name: '11:00'
          },
          {
            index: '5',
            name: '12:00'
          },
          {
            index: '6',
            name: '13:00'
          },
          {
            index: '7',
            name: '14:00'
          },
          {
            index: '8',
            name: '15:00'
          },
          {
            index: '9',
            name: '16:00'
          },
          {
            index: '10',
            name: '17:00'
          },
          {
            index: '11',
            name: '18:00'
          },
          {
            index: '12',
            name: '19:00'
          },
          {
            index: '13',
            name: '20:00'
          },
          {
            index: '14',
            name: '21:00'
          },
          {
            index: '14',
            name: '22:00'
          },
          {
            index: '15',
            name: '23:00'
          },
          {
            index: '16',
            name: '00:00'
          }
        ]
      };
    },
    onLoad(option) {
      if (option.planId != null && option.planId != undefined && option.planId != '') {
        this.planId = option.planId;
        this.stageShow = true;
        this.initSubject();
      } else {
        this.stageShow = false;
        this.studentCode = option.studentCode;
        this.initSubject();
      }
    },
    onShow() {
      if (this.flag1) {
        uni.$tlpayResult(this.sucees, this.fail, this.payInfo.orderId);
      }
    },
    methods: {
      sucees() {
        this.flag1 = false;
        this.show = false;
        this.payShow = true;
      },
      fail() {
        this.flag1 = false;
      },
      fails() {
        uni.showToast({
          title: '支付失败',
          icon: 'none',
          duration: 2000
        });
        this.flag1 = false;
      },
      initSubject() {
        let year = dayjs().format('YYYY');
        let month = dayjs().format('YYYY-MM').slice(6, 7);
        this.date = year + '年' + month + '月';
        if (this.planId != '' && this.planId != undefined && this.planId != null) {
          this.getPlanidList();
        } else {
          if (this.studentCode != undefined && this.studentCode != '') {
            console.log('走了true');
            this.getCourse();
          } else {
            console.log('走了else' + 444444444);
          }
        }
      },

      checkShow(list, hours, minute) {
        if (list.length == 0) {
          return false;
        }
        hours = hours + 8;
        minute = minute + 1;
        minute = hours * 60 + minute * 10;
        let flag = false;
        for (var i = 0; i < list.length; i++) {
          let item = list[i];
          if (hours >= item.startHours && hours <= item.endHours) {
            let s = item.startHours * 60 + item.startMinute;
            let e = item.endHours * 60 + item.endMinute;
            if (minute >= s && minute <= e) {
              flag = true;
              return true;
            }
          }
        }
        return flag;
      },
      // 选择期数
      bindPickerChange(e) {
        this.index = e.detail.value;
        this.nowWeek = Number(this.index) + 1;
        this.planId = this.columns[e.detail.value].planId;
        this.periodsShow = false;
        this.switching = true;
        if (this.switching) {
          let year = this.startTime.slice(0, 4);
          let month = this.startTime.slice(5, 7);
          let day = this.startTime.slice(8, 10);
          let fullTime = year + '-' + month + '-' + day;
          this.$refs.timetable.setPriodsWeek(fullTime, this.switching);
        }
        if (this.planId != '' && this.planId != undefined && this.planId != null) {
          this.getCoursePlan();
        } else {
          console.log('周期切换错误');
        }
      },
      // 子组件传过来的周期和时间
      changeDate(needDate) {
        let months = needDate.month < 10 ? '0' + needDate.month : needDate.month;
        this.date = needDate.year + '年' + needDate.month + '月';
        this.datetime = needDate.year + '-' + months;
        this.weeks = needDate.week;
        if (this.planId != '' && this.planId != undefined && this.planId != null) {
          this.getCoursePlan();
        } else {
          if (this.studentCode != undefined && this.studentCode != '') {
            console.log('走了true');
            this.getCourse();
          } else {
            console.log('走了else' + 33333333333);
          }
        }
      },

      changePriodsWeek(priodsDate) {
        let months = priodsDate.month < 10 ? '0' + priodsDate.month : priodsDate.month;
        this.date = priodsDate.year + '年' + priodsDate.month + '月';
        this.datetime = priodsDate.year + '-' + months;
        this.weeks = priodsDate.week;
        if (this.planId != '' && this.planId != undefined && this.planId != null) {
          this.getCoursePlan();
        }
      },

      //根据年月和周获取课程表信息
      async getCourse() {
        uni.showLoading();
        let res = await this.$httpUser.get('deliver/app/parent/getCourseDateList', {
          date: this.datetime,
          studentCode: this.studentCode,
          week: this.weeks
        });
        if (res.data.success) {
          this.subjectList = res.data.data;
          for (let i = 0; i < this.subjectList.length; i++) {
            let courseList = this.subjectList[i].courseList;
            //上一次盒子高度
            let lastTop = 0;
            for (let j = 0; j < courseList.length; j++) {
              let item = courseList[j];
              let hours = item.endHours - item.startHours;
              let minute = item.endMinute - item.startMinute;
              let e = (hours * 60 + minute) * 1.984;
              let top = ((item.startHours - 8) * 60 + item.startMinute) * 1.984;
              top = top - lastTop;
              lastTop += top;
              this.subjectList[i].courseList[j].height = e;
              this.subjectList[i].courseList[j].top = top;
            }
          }
          uni.hideLoading();
        } else {
          this.$util.alter(res.data.message);
          console.log(res.data.message);
        }
      },
      //预排课id集合
      async getPlanidList() {
        let res = await this.$httpUser.get('deliver/app/parent/getDateListByPlanId', {
          planId: this.planId
        });
        if (res.data.success) {
          this.collectionList = res.data.data;
          this.startTime = this.collectionList.planStartTime;
          this.endTime = this.collectionList.planEndTime;
          this.nowWeek = this.collectionList.periods;
          this.index = this.collectionList.periods - 1;
          this.$refs.timetable.setDateList(this.startTime);
          if (this.collectionList.payStatus == 1) {
            this.payShow = false;
            this.getPlanInfo(); //获取排课支付信息
          } else {
            this.payShow = true;
            console.log(res.data.message);
          }
        } else {
          this.$util.alter(res.data.message);
        }
      },
      //根据年月和周获取预排课课程表信息
      async getCoursePlan() {
        let data = {
          date: this.datetime,
          planId: String(this.planId),
          week: this.weeks
        };
        uni.showLoading();
        let res = await this.$httpUser.get('deliver/app/parent/getCourseDateListByPlan', data);
        if (res.data.success) {
          this.subjectList = res.data.data;
          for (let i = 0; i < this.subjectList.length; i++) {
            let courseList = this.subjectList[i].courseList;
            let lastTop = 0;
            for (let j = 0; j < courseList.length; j++) {
              let item = courseList[j];
              let hours = item.endHours - item.startHours;
              let minute = item.endMinute - item.startMinute;
              let e = (hours * 60 + minute) * 1.984;
              let top = ((item.startHours - 8) * 60 + item.startMinute) * 1.984;
              top = top - lastTop;
              lastTop += top;
              this.subjectList[i].courseList[j].height = e;
              this.subjectList[i].courseList[j].top = top;
            }
          }
          uni.hideLoading();
        } else {
          this.$util.alter(res.data.message);
        }
      },

      //根据排课获取联系方式
      async getContact() {
        uni.showLoading();
        let res = await this.$httpUser.get('deliver/app/parent/getContactInfo', {
          planId: this.planId,
          type: this.type
        });
        uni.hideLoading();
        if (res.data.success) {
          this.deliverNews = res.data.data;
          this.shows = true;
        } else {
          this.$util.alter(res.data.message);
        }
      },
      //根据学员编号获取联系方式
      async getContactBy() {
        uni.showLoading();
        let res = await this.$httpUser.get('deliver/app/parent/getContactByStudentCode', {
          studentCode: this.studentCode
        });
        uni.hideLoading();
        if (res.data.success) {
          this.deliverNews = res.data.data;
          this.shows = true;
        } else {
          this.$util.alter(res.data.message);
          console.log(res.data.message);
        }
      },
      // 获取排课支付信息
      async getPlanInfo() {
        let _this = this;
        const res = await $http({
          url: 'zx/order/getPlanCourseInfo',
          data: {
            planId: _this.planId
          }
        });
        if (res) {
          _this.payInformation = res.data;
          this.getPeriods(); // 获取期数集合
        } else {
          this.$util.alter(res.data.message);
        }
      },
      // 根据学员编号获取期数合集
      async getPeriods() {
        let that = this;
        uni.showLoading();
        let res = await that.$httpUser.get('deliver/app/parent/getPlanPeriodsList', {
          studentCode: that.payInformation.studentCode
        });
        uni.hideLoading();
        if (res.data.success) {
          let periodsList = res.data.data;
          let gather = [];
          if (gather.length == 0) {
            periodsList.forEach((item) =>
              gather.push({
                periods: item.periods,
                planId: item.planId
              })
            );
            this.columns = JSON.parse(JSON.stringify(gather));
            console.log(this.columns);
            // this.columns = JSON.parse(JSON.stringify([gather]))
          }
        } else {
          this.$util.alter(res.data.message);
          console.log(res.data.message);
        }
      },
      // 排课支付订单
      async payPlanCourse() {
        let _this = this;
        uni.showLoading();
        const res = await $http({
          url: 'zx/order/payPlanCourse',
          data: {
            planId: _this.planId
          }
        });
        uni.hideLoading();
        if (res) {
          _this.payBtn(res.data);
        } else {
          this.$util.alter(res.data.message);
        }
      },
      // 支付
      async payBtn(data) {
        let _this = this;
        let resdata = await httpUser.post('mps/line/collect/order/unified/collect', data);
        let res = resdata.data.data;
        _this.disabled = false;
        if (res) {
          if (res.openAllinPayMini) {
            this.flag1 = true;
            this.payInfo = res;
            uni.$payTlian(res);
          } else {
            uni.requestPayment({
              provider: 'wxpay',
              timeStamp: res.payInfo.timeStamp,
              nonceStr: res.payInfo.nonceStr,
              package: res.payInfo.packageX,
              signType: res.payInfo.signType,
              paySign: res.payInfo.paySign,
              success: function (ress) {
                debugger;
                console.log('支付成功');
                _this.show = false;
                _this.payShow = true;
              },
              fail: function (err) {
                console.log('支付失败' + err);
                uni.showToast({
                  title: '支付失败',
                  icon: 'none',
                  duration: 2000
                });
              }
            });
          }
        }
      },
      // 选择年月切换
      confirm(e) {
        let year = dayjs(e.value).format('YYYY');
        let month = Number(dayjs(e.value).format('YYYY-MM').slice(5, 7));
        this.date = year + '年' + month + '月'; // 课程表显示的时间
        let months = month < 10 ? '0' + month : month;
        this.dates = year + '-' + months + '-' + '01'; // 子组件需要的时间格式
        this.isShow = false;
        this.$refs.timetable.setDateList(this.dates);
      },
      // 取消选择年月
      dateClose() {
        this.isShow = false;
      },
      // 立即付款获取排课信息
      payNews() {
        // this.getPlanInfo()
        this.show = true;
      },
      // 关闭弹窗
      close() {
        this.show = false;
      },
      // 联系交付中心
      unFold() {
        this.type = 2;
        if (this.planId != '' && this.planId != undefined && this.planId != null) {
          this.getContact();
        } else {
          this.getContactBy();
        }
      },

      // 关闭联系门店（交付中心）弹窗
      openUp() {
        this.shows = false;
      },
      payMent() {
        this.payPlanCourse();
      },
      // 联系门店
      goStore() {
        this.type = 1;
        this.show = false;
        this.contactStore = true;
        this.shows = true;
        this.getContact();
      },
      // 跳转手机拨号页面
      goPhoneBtn() {
        uni.makePhoneCall({
          // 手机号
          phoneNumber: this.deliverNews.mobile,
          success: (res) => {
            console.log(res);
          },
          fail: (err) => {
            console.log(err);
          }
        });
      }
    }
  };
</script>

<style>
  page {
    background-color: #fff;
  }
</style>

<style lang="scss" scoped>
  .content {
    padding-bottom: 30rpx;
    color: #fff;

    .course-news {
      width: 100%;
      color: #fff;
      border-radius: 10rpx;
      font-size: 24rpx;
      // padding: 0 1rpx;
      background-color: #94d6f3;
    }

    .curriculum {
      position: relative;
      border-top: 1px solid #e4e4e4;
      background-color: #fff;
    }

    .content-item {
      border-top: 2px solid #fff;

      .title {
        font-size: 28rpx;
        line-height: 32rpx;
        padding: 0 0 18rpx;
        color: #fff;
      }

      .lunc-calendar {
        border-radius: 20rpx;
      }

      .operation {
        display: flex;
        flex-direction: row;
        margin-top: 30rpx;

        .button {
          color: #fff;
          text-decoration: underline;
          margin-right: 30rpx;
        }
      }
    }

    .content-item:first-child {
      border-top: none;
    }
  }

  .periods {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25rpx 30rpx;
    background-color: #fff;
    border-top: 1px solid #e4e7ed;

    .left {
      font-size: 34rpx;
      font-weight: 700;

      .time-range {
        font-size: 28rpx;
        color: #666;
        font-weight: normal;
        margin-top: 10rpx;
      }
    }

    .right {
      width: 210rpx;
      height: 70rpx;
      border: 1px solid #1d755c;
      border-radius: 45rpx;
      font-size: 30rpx;
      color: #1d755c;
      text-align: center;
      line-height: 70rpx;
    }
  }

  .payment {
    position: fixed;
    bottom: 0;
    right: 0;
    left: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    background-color: #fff;

    .left {
      font-size: 28rpx;
    }

    .right {
      /deep/.pay-btn {
        width: 180rpx;
        height: 70rpx;
        line-height: 70rpx;
        border-radius: 45rpx;
        font-size: 30rpx;
        color: #fff !important;
        background: linear-gradient(to bottom, #88cfba, #1d755c);
      }

      .right-pay {
        display: flex;
        align-items: center;
        font-size: 32rpx;
      }

      .pay_money {
        color: #439286;
        font-size: 36rpx;
      }
    }
  }

  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 145rpx;
    z-index: -1;
  }

  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .orderinfo {
    position: relative;
    width: 670rpx;
    font-size: 30rpx;
    border-radius: 24rpx;
    padding: 50rpx 0;
    background-color: #fff;
  }

  .close-icon {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
  }

  .container {
    padding: 0 80rpx;
  }

  .title {
    text-align: center;
    color: #303133;
    font-size: 34rpx;
    font-weight: 700;
    margin-bottom: 40rpx;
  }

  .pay-price {
    font-size: 32rpx;

    .pay-tips {
      display: flex;
      align-items: flex-start;
      padding: 20rpx 80rpx 0;
      color: #f64c53;
      font-size: 28rpx;
    }

    .content_wrap {
      white-space: pre-wrap;
    }

    /deep/.pay-btn {
      width: 240rpx;
      height: 80rpx;
      line-height: 80rpx;
      border-radius: 45rpx;
      text-align: center;
      font-size: 30rpx;
      color: #fff;
      background: linear-gradient(to bottom, #88cfba, #1d755c);
    }
  }

  .contact-s {
    display: flex;
    padding: 40rpx 80rpx 0;
  }

  /deep/.store-btn {
    width: 240rpx;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 45rpx;
    font-size: 30rpx;
    margin-right: 15rpx;
    color: #fff !important;
    background: linear-gradient(to bottom, #88cfba, #1d755c);
  }

  /deep/.contact-btn {
    width: 240rpx;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 45rpx;
    font-size: 30rpx;
    margin-left: 15rpx;
    color: #f64c53 !important;
    border: 1px solid #f64c53;
  }

  .marginTop76 {
    margin-top: 76rpx;
  }

  .line_s {
    border-top: 1px dashed #c8c8c8;
  }

  .center_s {
    text-align: center;
  }

  /deep/.u-safe-area-inset-bottom {
    padding-bottom: 0 !important;
  }

  .row {
    display: flex;
    height: 120rpx;
    padding-left: 14rpx;
    position: relative;
    border-bottom: 1px solid #e4e7ed;
    box-sizing: border-box;

    &:after {
      content: '';
      height: 0;
      width: 100%;
      position: absolute;
      bottom: 0;
      left: 0;
    }

    .time-item {
      height: 120rpx;
      width: 70rpx;
      text-align: center;
      line-height: 120rpx;

      .index {
        padding-bottom: 8rpx;
        padding-top: 16rpx;
        font-size: 36rpx;
      }

      .time {
        font-size: 24rpx;
        color: #666;
      }
    }
  }

  .time-table {
    width: 100%;
    height: 120rpx;
  }

  .time-table-inner {
    width: 100%;
    height: 20rpx;
    text-align: center;
    font-size: 16upx;
  }

  .course-container {
    position: absolute;
    top: 0;
    left: 75rpx;
    width: calc(100% - 75rpx);
    height: 100%;
    display: flex;

    .week {
      width: 100%;
      border-left: 1px solid #e4e7ed;
      padding-left: 3rpx;

      .courseList {
        width: 90rpx;
        word-break: break-all;
        color: white;
        overflow: hidden;
        font-size: 30rpx;
        text-align: center;

        .course {
          border-radius: 16rpx;
          text-align: center;
          width: 100%;
        }
      }
    }
  }

  .table {
    width: 648rpx;
    display: flex;
    position: absolute;
    top: 0;
    margin-left: 90rpx;
    margin-right: 14rpx;
    box-sizing: border-box;

    .content {
      width: 14.3%;
      text-align: center;
      border-left: 1px solid #e4e4e4;
      color: #000;
    }
  }

  /deep/.paid-btn {
    width: 180rpx;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 45rpx;
    text-align: center;
    font-size: 30rpx;
    margin-left: 20rpx;
    color: #2e896f !important;
    border: 1px solid #2e896f;
  }

  /deep/.no-btn {
    width: 240rpx;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 45rpx;
    text-align: center;
    font-size: 30rpx;
    margin-left: 20rpx;
    color: #2e896f !important;
    border: 1px solid #2e896f;
  }
</style>
