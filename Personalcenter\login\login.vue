<template>
  <view class="content">
    <!-- 这里是状态栏 -->
    <view class="status_bar">
      <!-- <image class="status_bar_image" :src="imgHost+'alading/correcting/login_bg1.png'" mode=""></image> -->
    </view>
    <uni-nav-bar class="nav-bar" color="#000" left-icon="left" title="鼎校甄选登录" :border="false" @clickLeft="back" />
    <view class="wechatapp">
      <image class="header" :src="imgHost + 'dxSelect/login-bgc.png'" mode="widthFix"></image>
    </view>

    <!-- #ifdef MP-WEIXIN -->
    <!-- <view class="login-btn t-c" @tap.stop="authorLogin">微信授权登录</view> -->
    <view class="login-button">
      <button class="phone-btn" @click="goPhone">手机号验证码登录</button>
      <button class="phone-pwd-btn mt-40" @click="goWxPhonePwd">用户名密码登录</button>
      <view class="mt-40">
        <button class="wx-btn1" @click="showPromptUsers">一键登录</button>
        <!-- <button class="wx-btn" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">微信一键登录</button>		 -->
      </view>
      <view class="m-40 password-change" @click="goPasswordChange">点击修改密码</view>
    </view>
    <!-- #endif -->

    <!-- #ifdef APP-PLUS || H5 -->
    <button class="phone-btn" @click="goPhone">手机号登录</button>
    <view class="mt-50"><button class="wx-btn" @click="weixinLogin">微信一键登录</button></view>
    <!-- #endif -->

    <u-popup :show="promptUsers" mode="center" :round="10" :closeable="true" :safeAreaInsetBottom="false" @close="close">
      <view class="phonecon t-c">
        <view class="tit f-32 bold mb-40">手机号码快捷登录</view>
        <view class="pb-30 f-30 phoneContent">由于您的账号还没有绑定手机号，请先绑定手机号后方可登录</view>
        <view class="login_btn_group f-32 c-99">
          <view class="login_btn" @click="close">取消</view>
          <button class="login_btn" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber">确认授权</button>
        </view>
      </view>
    </u-popup>

    <uni-popup ref="popup" :mask-click="false">
      <view class="bg-ff radius-15 mlr-60 p-20">
        <view class="t-r">
          <uni-icons type="closeempty" size="22" color="#999"></uni-icons>
        </view>
        <view class="">
          <view class="tit f-32 bold mb-40 t-c">用户隐私保护协议</view>
          <view class="pb-30 f-30 plr-40 lh-50">
            &nbsp;感谢您使用本产品，您使用本产品前应当仔细阅读并同意
            <text class="c-fea" @click="handleOpenPrivacyContract">{{ urlTitle }}</text>
            ，当您点击同意使用产品服务时，即表示您已理解并了解该条款内容。
          </view>
          <view class="flex-s f-32 c-99 flex-x-c mtb-30">
            <view class="close_btn mr-30" @click="close">拒绝</view>
            <button class="review_btn" open-type="agreePrivacyAuthorization" @bindagreeprivacyauthorization="handleAgreePrivacyAuthorization">同意</button>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  //var myModule = uni.requireNativePlugin("myModule")
  const { $http } = require('@/util/methods.js');
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  import Superman from '@/common/superman.js';
  // import Rsa from '@/util/rsa.js'
  const app = getApp();
  export default {
    data() {
      return {
        encryptedData: '',
        iv: '',
        personal: {}, // 个人信息
        isLogin: 'islogin', //是否登陆
        isRegist: false, //是否是注册
        code: '',
        pageShow: true,
        imgHost: getApp().globalData.imgsomeHost,
        promptUsers: false,
        showBuyMember: false, //是否续费
        showParentVipBuyMember: false, //家长会员是否续费
        openId: '',
        tmpToken: '',
        wxAppCode: '',
        privacy: false, // 隐私保护提示
        urlTitle: '',
        firstLogin: false
      };
    },

    onShow() {
      uni.setStorageSync('jump', '');
      // #ifdef MP-WEIXIN
      uni.login({
        success: (res) => {
          this.code = res.code;
          console.log(this.code);
        },
        fail: (err) => {
          console.log(err);
        }
      });
      // #endif
    },

    onLoad() {
      // wx.getPrivacySetting({
      //   success: res => {
      // 	console.log(res) // 返回结果为: res = { needAuthorization: true/false, privacyContractName: '《xxx隐私保护指引》' }
      // 	if (res.needAuthorization) {
      // 	  // 需要弹出隐私协议
      // 	  this.privacy = true;
      // 	  this.urlTitle = res.privacyContractName;
      // 	  this.$refs.popup.open()
      // 	} else {
      // 	  // 用户已经同意过隐私协议，所以不需要再弹出隐私协议，也能调用已声明过的隐私接口
      // 	  // wx.getUserProfile()
      // 	  // wx.chooseMedia()
      // 	  // wx.getClipboardData()
      // 	  // wx.startRecord()
      // 	}
      //   },
      //   fail: () => {},
      //   complete: () => {}
      // })
      // #ifdef APP-PLUS
      uni.$on('bindPhoneSuccess', (data) => {
        this.tmpToken = data;
      });
      //页面监听持久性事件 android
      plus.globalEvent.addEventListener('TestEvent', function (e) {
        console.log('---TestEvent---');
        console.log(e);
        uni.showToast({
          title: 'TestEvent收到：' + e.msg
        });
      });
      // #endif
    },

    methods: {
      handleOpenPrivacyContract() {
        // 打开隐私协议页面
        wx.openPrivacyContract({
          success: (res) => {
            console.log(res);
          }, // 打开成功
          fail: () => {
            that.$util.alter('出错了，请稍后再试');
          }, // 打开失败
          complete: () => {}
        });
      },
      handleAgreePrivacyAuthorization() {
        console.log('用户同意隐私协议');
        this.privacy = false;
        this.$refs.popup.close();
        // 用户同意隐私协议事件回调
        // 用户点击了同意，之后所有已声明过的隐私接口和组件都可以调用了
        // wx.getUserProfile()
        // wx.chooseMedia()
        // wx.getClipboardData()
        // wx.startRecord()
      },
      back() {
        uni.navigateBack();
      },
      // 获取手机号
      getPhoneNumber(e) {
        console.log(e);
        let code;
        this.encryptedData = e.detail.encryptedData;
        this.iv = e.detail.iv;
        if (e.detail.code) {
          this.sendLoginWxReq(this.code);
          //埋点-登录按钮
          getApp().sensors.track('loginClick', {
            name: '登录按钮'
          });
        } else {
          uni.showToast({
            title: '您已拒绝了授权',
            icon: 'none'
          });
          uni.switchTab({
            url: '/pages/index/index'
          });
        }
      },

      // 获取key和手机号
      sendLoginWxReq(code) {
        if (!code) return;
        httpUser
          .post(`zx/common/decodeWechatPhone`, {
            code: code,
            encryptedData: this.encryptedData,
            iv: this.iv,
            shareId: uni.getStorageSync('referee_id') || ''
          })
          .then((res) => {
            console.log(res);
            this.personal = res.data.data;
            this.getLogin();
            // this.GetsmsCode(this.personal)
          });
      },
      getLocalIPAddress() {
        return new Promise((resolve, reject) => {
          wx.getLocalIPAddress({
            success(res) {
              resolve(res.localip);
            },
            fail(err) {
              reject(err);
            }
          });
        });
      },
      // 登录
      getLogin() {
        var that = this;
        uni.showLoading();
        that.$httpUser
          .get('zx/common/ifFirstLogin', {
            mobile: this.personal.mobile
          })
          .then((res) => {
            this.firstLogin = res.data.data;
          });
        that.$httpUser
          .get('new/security/login/mini', {
            wxCode: this.personal.key,
            role: '9',
            usePwd: 'false',
            username: this.personal.mobile
          })
          .then((res) => {
            uni.hideLoading();
            if (res.data.success) {
              uni.setStorageSync('token', res.data.data.token);
              that.loginHandle();
              that.$util.alter('登录成功');
              uni.removeStorageSync('current');
              // that.getuserId()
              uni.navigateBack();
            } else {
              that.$util.alter(res.data.message);
              uni.login({
                success: (res) => {
                  this.code = res.code;
                  console.log(this.code);
                },
                fail: (err) => {
                  console.log(err);
                }
              });
            }
          });
      },
      async loginHandle() {
        let shareid = uni.getStorageSync('shareId') ? uni.getStorageSync('shareId') : '';
        let that = this;
        try {
          that.localip = await that.getLocalIPAddress();
        } catch (e) {
          //TODO handle the exception
        }

        let userinfo = await Superman.getUserInfo();
        let date = new Date();
        let year = date.getFullYear();
        let month = (date.getMonth() + 1).toString().padStart(2, '0');
        let day = date.getDate().toString().padStart(2, '0');
        date = `${year}-${month}-${day}`;
        date = new Date(date);
        const date2 = new Date(userinfo.expireTime);
        const diffTime = date2 - date;
        const diffDays = diffTime / (1000 * 60 * 60 * 24);
        /** 家长会员过期 */
        if (userinfo.parentMemberEndTime) {
          const vipDate2 = new Date(userinfo.parentMemberEndTime);
          const vipDiffTime = vipDate2 - date;
          const vipDiffDays = vipDiffTime / (1000 * 60 * 60 * 24);
          if (vipDiffDays <= 30) {
            this.showParentVipBuyMember = true;
            uni.setStorageSync('showParentVipBuyMember', this.showParentVipBuyMember);
          } else {
            this.showParentVipBuyMember = false;
            uni.setStorageSync('showParentVipBuyMember', this.showParentVipBuyMember);
          }
        }
        if (diffDays <= 30) {
          this.showBuyMember = true;
          uni.setStorageSync('showBuyMember', this.showBuyMember);
        } else {
          this.showBuyMember = false;
          uni.setStorageSync('showBuyMember', this.showBuyMember);
        }
        uni.setStorageSync('user_id', userinfo.userId);
        uni.setStorageSync('merchantCode', userinfo.merchantCode);
        uni.setStorageSync('identityType', userinfo.identityType);
        uni.setStorageSync('parentMemberType', userinfo.parentMemberType);
        uni.setStorageSync('nickName', userinfo.nickName);
        uni.setStorageSync('phone', userinfo.mobile);
        uni.setStorageSync('localip', that.localip);
        if (shareid === '' || this.firstLogin === false) {
          return;
        }
        let activityid = uni.getStorageSync('activityId') ? uni.getStorageSync('activityId') : '';
        that.$httpUser.post('zx/wap/invite/saveInviteData', {
          userId: shareid,
          activityId: activityid,
          type: '1',
          inviteeOpenId: userinfo.openId,
          inviteeNickName: userinfo.nickName,
          inviteePhone: userinfo.mobile,
          ipAddress: that.localip
        });
      },
      // 获取首页信息
      // async homeData() {
      // 	let _this = this
      // 	const res = await $http({
      // 		url: 'zx/user/userInfoNew',
      // 	})
      // 	if (res) {
      // 		_this.pageShow = false
      // 		_this.info = res.data

      // 	}
      // },

      // 判断用户是否注册
      // GetsmsCode(e) {
      // 	console.log(e);
      // 	var that = this;
      // 	that.$httpUser.get('new/security/check/registered', {
      // 		username: that.personal.mobile,
      // 		cid: 'dx_znyy_resource'
      // 	}).then((res) => {
      // 		console.log(res)
      // 		debugger
      // 		if (res.data.success) {
      // 			if (res.data.data) {
      // 				that.isRegist = false;
      // 				that.getLogin()
      // 			}else{
      // 				that.registerApp()
      // 			}
      // 		} else {
      // 			that.$util.alter(res.data.message)
      // 		}
      // 	})
      // },

      // 帮用户注册
      // registerApp() {
      // 	var that = this;
      // 	this.$httpUser.post('znyy/user/register', {
      // 		mobile: that.personal.mobile,
      // 	}).then((result) => {
      // 		if (result.data.success) {
      // 			// that.pwd = that.mobile.substr(that.mobile.length - 6);
      // 			// 走登陆流程
      // 			that.getLogin()
      // 			// that.wxuserinfo.isRegistered = true;
      // 		} else {
      // 			that.$util.alter(result.data.message)
      // 		}
      // 	});
      // },

      goPhone() {
        // #ifdef MP-WEIXIN
        uni.navigateTo({
          url: '/Personalcenter/login/phoneLogin'
        });
        // #endif
      },

      goWxPhonePwd() {
        uni.navigateTo({
          url: '/Personalcenter/login/wxPhonePwdLogin'
        });
      },
      goPasswordChange() {
        uni.redirectTo({
          url: '/Personalcenter/password/PasswordChange'
        });
      },

      /**
       * 授权登录
       */
      //打开提示用户弹窗
      showPromptUsers() {
        // this.$refs.promptUsers.open();
        this.privacy = false;
        if (this.privacy) {
          this.$refs.popup.open();
        } else {
          this.promptUsers = true;
        }
      },

      close() {
        this.promptUsers = false;
        this.$refs.popup.close();
      },

      authorLogin(e) {
        let _this = this;
        wx.showLoading({
          title: '正在登录',
          mask: true
        });
        // 执行微信登录
        uni.getUserProfile({
          lang: 'zh_CN',
          desc: '用于完善用户昵称',
          success: async function (res2) {
            console.log(res2);
            uni.hideLoading();
            const [err, res] = await uni.login();
            // 发送用户信息
            const res1 = await $http({
              url: 'common/userLogin',
              method: 'post',
              data: {
                code: res.code,
                headPortrait: res2.userInfo.avatarUrl,
                nickName: res2.userInfo.nickName,
                shareId: wx.getStorageSync('referee_id') || ''
              }
            });
            wx.setStorageSync('token', res1.data.token);
            wx.setStorageSync('user_id', res1.data.userId);
            uni.setStorageSync('identityType', res1.data.identityType);
            uni.setStorageSync('phone', res1.data.mobile);
            wx.hideLoading();
            _this.navigateBack();
          },
          fail(err) {}
        });
      },
      // cancel_get: function() {
      //     uni.switchTab({
      //         url: '/pages/index/index'
      //     })
      // },
      /**
       * 授权成功 跳转回原页面
       */
      navigateBack: function () {
        uni.navigateBack();
      },

      //app微信sdk登录
      weixinLogin() {
        // myModule.goToNativePage();
        var that = this;
        uni.login({
          provider: 'weixin',
          onlyAuthorize: true, // 微信登录仅请求授权认证
          success: function (event) {
            console.log(event);
            // uni.getUserInfo({
            //     provider: 'weixin',
            //     success: function(info) {
            //         console.log(info);
            //         // 获取用户信息成功, info.authResult保存用户信息
            //     }
            // })
            that.sendCodeToService(event);
          },
          fail: function (err) {
            // err.code错误码
            console.log(err);
            console.log(err.code);
          }
        });
      },

      sendCodeToService(event) {
        this.wxAppCode = event.code;
        // console.log(event.code);
        // var that = this;
        // uni.showLoading();
        // that.$httpUser.get('new/security/v2/user/third/party/code/parse', {
        //     code: event.code,
        // }).then((res) => {
        //     uni.hideLoading();
        //     console.log(res);
        //     if (res.data.success) {
        //         this.openId = res.data.data.openId;
        //         this.appLogin();
        //     } else {
        //         that.$util.alter(res.data.message)
        //     }
        // })
      }
    }
  };
</script>

<style lang="scss" scoped>
  .content {
    height: 100vh;
    background-color: white;
  }

  .container {
    text-align: center;
    padding: 0 80rpx;
  }

  .wechatapp {
    margin-top: 180rpx;
    margin-bottom: 200rpx;
  }

  .auth-title {
    color: #585858;
    font-size: 40rpx;
    margin-bottom: 40rpx;
  }

  .title {
    color: #000;
    font-weight: 700;
    font-size: 40rpx;
    margin-bottom: 40rpx;
  }

  .auth-subtitle {
    color: #888;
    margin-bottom: 88rpx;
  }

  .login-btn {
    border: none;
    height: 88rpx;
    line-height: 88rpx;
    background: #04be01;
    /* #ifdef MP-ALIPAY */
    background: #1890ff;
    /* #endif */
    color: #fff;
    font-size: 11pt;
    border-radius: 999rpx;
  }

  .login-btn::after {
    display: none;
  }

  .login-btn.button-hover {
    box-shadow: inset 0 5rpx 30rpx rgba(0, 0, 0, 0.15);
  }

  .login-cancle {
    text-align: center;
    border: none;
    height: 88rpx;
    line-height: 88rpx;
    color: #a8a8a8;
    font-size: 11pt;
    border-radius: 999rpx;
  }

  /deep/.phone-pwd-btn {
    width: 100%;
    /* #ifdef APP-PLUS */
    width: 90%;
    transform: translateX(5%);
    /* #endif */
    height: 90rpx;
    line-height: 90rpx;
    border-radius: 45rpx;
    font-size: 30rpx;
    color: #fff;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
  }

  /deep/.wx-btn1 {
    width: 100%;
    /* #ifdef APP-PLUS */
    width: 90%;
    transform: translateX(5%);
    /* #endif */
    height: 90rpx;
    line-height: 90rpx;
    border-radius: 45rpx;
    font-size: 30rpx;
    color: #999999;
    background: #fff;
    border: 1upx solid #c8c8c8;
  }

  /deep/.wx-btn {
    width: 100%;
    /* #ifdef APP-PLUS */
    width: 90%;
    transform: translateX(5%);
    /* #endif */
    height: 90rpx;
    line-height: 90rpx;
    border-radius: 45rpx;
    font-size: 30rpx;
    color: #fff;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
  }

  .login-button {
    padding: 0 100rpx;
  }

  /deep/.phone-btn {
    width: 100%;
    /* #ifdef APP-PLUS */
    width: 90%;
    transform: translateX(5%);
    /* #endif */
    height: 90rpx;
    line-height: 90rpx;
    border-radius: 45rpx;
    font-size: 30rpx;
    color: #fff;
    background: linear-gradient(180deg, #f08c34 0%, #ea6531 100%);
  }

  .phonecon {
    padding-top: 50upx;
    width: 590upx;
  }

  .phoneContent {
    line-height: 50upx;
    padding: 0 70upx;
  }

  /deep/.swiper-box {
    height: 100rpx !important;
  }

  /deep/.data-v-1fff95d2 {
    height: 100rpx !important;
  }

  /deep/.u-badge--not-dot.data-v-662d25bf {
    padding: 2rpx 6rpx;
  }

  .login_btn_group {
    width: 100%;
    height: 90upx;
    border-top: 1upx solid #efefef;
    display: flex;
    align-items: center;
    margin-top: 50upx;
  }

  .login_btn {
    width: 50%;
    text-align: center;
    height: 100%;
    line-height: 90upx;
    font-size: 30upx;
  }

  .login_btn:first-child {
    border-right: 1upx solid #efefef;
    box-sizing: border-box;
  }

  .nav-bar {
    position: fixed;
    width: 100%;
    z-index: 999;
  }

  /deep/.uni-nav-bar-text {
    font-size: 34rpx !important;
    font-weight: bold;
  }

  /deep/.uni-navbar__header {
    background-color: transparent !important;
  }

  /deep/.uni-navbar__content {
    background-color: transparent !important;
  }

  .review_btn {
    width: 240upx;
    height: 80upx;
    border-radius: 45upx;
    font-size: 30upx;
    color: #ffffff;
    line-height: 80upx;
    justify-content: center;
    text-align: center;
    background: linear-gradient(180deg, #88cfba 0%, #1d755c 100%);
  }

  .close_btn {
    width: 240upx;
    height: 80upx;
    color: #2e896f;
    font-size: 30upx;
    line-height: 80upx;
    text-align: center;
    border-radius: 45upx;
    box-sizing: border-box;
    border: 1px solid #2e896f;
  }

  .password-change {
    text-align: center;
    font-size: 30rpx;
    color: #999999;
  }
</style>
