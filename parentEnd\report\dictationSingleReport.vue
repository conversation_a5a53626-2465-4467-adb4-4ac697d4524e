<template>
  <!-- <view class="report_Container" > -->
  <view class="report_Container" style="background-image: url('https://document.dxznjy.com/applet/newimages/report/reportBg.png')">
    <!-- 顶部箭头返回按钮 -->
    <!-- <view class="arrow">
			<uni-icons type="left" size="20" color="#FFFFFF" @click="back"></uni-icons>
		</view> -->
    <!-- 顶部文字 -->
    <view class="title">
      <view class="title-text">
        <image src="https://document.dxznjy.com/applet/newimages/report/report_HeadText.png" mode=""></image>
      </view>
    </view>
    <!-- 学员信息盒子 -->
    <view class="information" style="background-image: url('https://document.dxznjy.com/applet/newimages/report/report_HeadBg.png')">
      <view class="information-bootom">
        <view class="studentInfo">
          <image src="https://document.dxznjy.com/applet/newimages/report/studentInfo.png" mode=""></image>
        </view>
        <view>
          <text>姓名：</text>
          <text class="green">{{ handleTextLength(reportList.realName) }}</text>
          <text style="margin-left: 80rpx">学校：</text>
          <text class="green">{{ handleTextLength(reportList.school) }}</text>
        </view>
        <view>
          <text>年级：</text>
          <text class="green">{{ reportList.gradeName || '' }}</text>
        </view>
        <view>
          <text>登录账号：</text>
          <text class="green">{{ reportList.loginName || '' }}</text>
        </view>
        <view>
          <text>号码：</text>
          <text class="green">{{ reportList.phone || '' }}</text>
        </view>
        <view>
          <text>创建时间：</text>
          <text class="green">{{ reportList.levelTime || '' }}</text>
        </view>
      </view>
    </view>
    <!-- 学习回顾-->
    <view class="studyReview boxbg">
      <view class="report_Review">
        <image src="https://document.dxznjy.com/applet/newimages/report/report_Review.png" mode=""></image>
      </view>
      <view class="report_Review1" style="margin-top: 24rpx">
        <image src="https://document.dxznjy.com/applet/newimages/report/report_Review1.png" mode=""></image>
      </view>
      <view class="reviewMsg">
        <view class="msgItem">
          <view>{{ reportList.worTotal || 0 + '个' }}</view>
          <view class="msgText">题目数量</view>
        </view>
        <view class="msgItem">
          <view>{{ reportList.rightRate || 0 + '%' }}</view>
          <view class="msgText">正确率</view>
        </view>
        <view class="msgItem">
          <view>{{ reportList.answerTime || 0 + 's' }}</view>
          <view class="msgText">答题时长</view>
        </view>
      </view>
      <view class="failWord_Head">
        <view class="greenTitle">发音错误单词</view>
        <view class="isOpenFail" @click="handleFold('0')">
          <!-- {{isOpenFail ?  '收起' : '展开' }} -->
          <image v-if="showReadFail" src="https://document.dxznjy.com/applet/newimages/report/down.png" mode=""></image>
          <image v-else src="https://document.dxznjy.com/applet/newimages/report/up.png" mode=""></image>
        </view>
      </view>
      <view class="FailListBox" :style="!showReadFail ? 'max-height: 240rpx;' : ''">
        <view v-for="(item, index) in reportList.readFailList" class="singleFailWord" :key="index">
          <view>{{ item.word }}</view>
          <view class="chinese">{{ item.chinese }}</view>
          <view @click="sayWord(item.word)">
            <image src="https://document.dxznjy.com/applet/newimages/report/vioce.png" mode=""></image>
          </view>
        </view>
      </view>
      <view class="failWord_Head">
        <view class="greenTitle">拼写错误单词</view>
        <view class="isOpenFail" @click="handleFold('1')">
          <!-- {{isOpenFail ?  '收起' : '展开' }} -->
          <image v-if="showWriteFail" src="https://document.dxznjy.com/applet/newimages/report/down.png" mode=""></image>
          <image v-else src="https://document.dxznjy.com/applet/newimages/report/up.png" mode=""></image>
        </view>
      </view>
      <view class="FailListBox" :style="!showWriteFail ? 'max-height: 240rpx;' : ''">
        <view v-for="(item, index) in reportList.writeFailList" class="singleFailWord" :key="index">
          <view>{{ item.word }}</view>
          <view class="chinese">{{ item.chinese }}</view>
          <view @click="sayWord(item.word)">
            <image src="https://document.dxznjy.com/applet/newimages/report/vioce.png" mode=""></image>
          </view>
        </view>
      </view>
    </view>
    <!-- 发音水平 -->
    <view class="voice_Level boxbg">
      <view class="level_Icon">
        <image src="https://document.dxznjy.com/course/91f1f24b22fb4d0cb4d261978fc05d55.png" mode=""></image>
      </view>
      <view class="level_Text" style="margin-top: 16rpx">您当前的单词发音水平为{{ reportList.readRankAnalysisVo.wordRank }}级</view>
      <view class="charts-box">
        <qiun-data-charts type="line" :opts="opts" :chartData="chartData" />
        <!-- <l-echart ref="chartRef" @finished="getServerData" style="width: 100%; height: 100%"></l-echart> -->
      </view>
      <view class="level_Info">
        <!-- <view style="margin-bottom: 10rpx;margin-top: 16rpx;">WHAT YOU CAN DO</view> -->
        <!-- <view class="analysis">{{reportList.readRankAnalysisVo.analysis}}</view> -->
        <view class="analysis">这个水平可以做......</view>
        <view v-for="(item, index) in reportList.readRankList" :key="index">
          <view class="analysisText" :class="{ overLevel: IsOverLevels(reportList.readRankAnalysisVo.wordRank, item.wordRank) }">
            {{ item.wordRank + ' ' + item.analysis }}
          </view>
        </view>
      </view>
    </view>
    <!-- 拼写水平 -->
    <view class="voice_Level boxbg">
      <view class="level_Icon">
        <image src="https://document.dxznjy.com/course/91f1f24b22fb4d0cb4d261978fc05d55.png" mode=""></image>
      </view>
      <view class="level_Text" style="margin-top: 16rpx">您当前的单词拼写水平为{{ reportList.writeRankAnalysisVo.wordRank }}级</view>
      <view class="charts-box">
        <qiun-data-charts type="line" :opts="opts" :chartData="writeChartData" />
        <!-- <l-echart ref="chartRefWrite" @finished="getServerData" style="width: 100%; height: 100%"></l-echart> -->
      </view>
      <view class="level_Info">
        <!-- <view style="margin-bottom: 10rpx;margin-top: 16rpx;">WHAT YOU CAN DO</view> -->
        <!-- <view class="analysis">{{reportList.writeRankAnalysisVo.analysis}}</view> -->
        <view class="analysis">这个水平可以做......</view>
        <view v-for="(item, index) in reportList.writeRankList" :key="index">
          <view class="analysisText" :class="{ overLevel: IsOverLevels(reportList.writeRankAnalysisVo.wordRank, item.wordRank) }">
            {{ item.wordRank + ' ' + item.analysis }}
          </view>
        </view>
      </view>
    </view>
    <!-- 发音单词描述 -->
    <view class="voice_Improvw boxbg">
      <view class="Improvw_Icon">
        <image src="https://document.dxznjy.com/course/93b75485bd2e43d690dc1fa99e58759b.png" mode=""></image>
      </view>
      <view class="Improvw_Text" style="margin-top: 16rpx">发音单词</view>
      <view class="radarBox">
        <!-- 雷达图 -->
        <view class="charts-box">
          <qiunDataCharts type="radar" :opts="opts1" :chartData="radarData" />
        </view>
      </view>

      <view class="voice_analysis">
        <view v-for="(item, index) in reportList.readTypeAnalysisVoList" class="singleReadType" :key="index">
          <view class="studyType greenTitle">
            {{ item.studyType == 'DYJ1' ? '单音节单词' : item.studyType == 'DYJ2' ? '多音节单词' : '前后缀单词' }}
          </view>
          <view class="description">
            <view class="flexDot">
              <view class="greenDot"></view>
              描述
            </view>
            <view>{{ item.description }}</view>
          </view>
          <view class="analysis" v-if="false">
            <view class="flexDot">
              <view class="greenDot"></view>
              测评分析
            </view>
            <view>{{ item.analysis }}</view>
          </view>
          <view class="learningAdvice">
            <view class="flexDot">
              <view class="greenDot"></view>
              学习建议
            </view>
            <view>{{ item.learningAdvice }}</view>
          </view>
        </view>
      </view>
    </view>
    <!-- 拼写单词描述 -->
    <view class="voice_Improvw boxbg">
      <view class="Improvw_Icon">
        <image src="https://document.dxznjy.com/applet/newimages/report/report_Improvw.png" mode=""></image>
      </view>
      <view class="Improvw_Text" style="margin-top: 16rpx">拼写单词</view>
      <view class="radarBox">
        <!-- 雷达图 -->
        <view class="charts-box">
          <qiunDataCharts type="radar" :opts="opts1" :chartData="writeRadarData" />
        </view>
      </view>
      <view class="voice_analysis">
        <view v-for="(item, index) in reportList.writeTypeAnalysisVoList" class="singleReadType" :key="index">
          <view class="studyType greenTitle">
            {{ item.studyType == 'DYJ1' ? '单音节单词' : item.studyType == 'DYJ2' ? '多音节单词' : '前后缀单词' }}
          </view>
          <view class="description">
            <view class="flexDot">
              <view class="greenDot"></view>
              描述
            </view>
            <view>{{ item.description }}</view>
          </view>
          <view class="analysis" v-if="false">
            <view class="flexDot">
              <view class="greenDot"></view>
              测评分析
            </view>
            <view>{{ item.analysis }}</view>
          </view>
          <view class="learningAdvice">
            <view class="flexDot">
              <view class="greenDot"></view>
              学习建议
            </view>
            <view>{{ item.learningAdvice }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 发别识别详情 -->
    <view class="RecognitionInfo boxbg" v-if="RecognitionInfoList.length > 0">
      <view style="font-weight: bold; font-size: 32rpx; color: #173525; margin-bottom: 32rpx">单词音节发音详情</view>
      <view v-for="(item, index) in RecognitionInfoList" class="singleDetails" :key="index">
        <view class="">
          <view class="wordText">{{ item.word }}</view>
          <view class="gridTable tableHead">
            <view class="OneRow">音节</view>
            <!-- <view class="inline">语音时间 （ms）</view>
						<view class="inline">发音准度（-1,100）</view>
						<view class="inline">音节重音</view>
						<view class="inline">音节检测重音</view> -->
            <view class="TwoRow">
              <view>语音时间</view>
              <view>（ms）</view>
            </view>
            <view class="TwoRow">
              <view>发音准度</view>
              <view>（-1,100）</view>
            </view>
            <view class="TwoRow">
              <view>音节</view>
              <view>重音</view>
            </view>
            <view class="TwoRow">
              <view>音节检测</view>
              <view>重音</view>
            </view>
          </view>
          <view v-for="(detail, index) in item.phoneInfos" :key="index" class="gridTable bborder">
            <view>{{ detail.phone }}</view>
            <view>{{ detail.memBeginTime + '~' + detail.memEndTime }}</view>
            <view>{{ detail.pronAccuracy.toFixed(0) }}</view>
            <view>{{ detail.stress ? '是' : '否' }}</view>
            <view>{{ detectedstress ? '是' : '否' }}</view>
          </view>
        </view>
      </view>
    </view>

    <view class="dictation_btn" @click="back">
      <view class="lastTest">返回首页</view>
      <!-- <view class="startTest" @click="toTest">开始检测</view> -->
    </view>
  </view>
</template>
<script>
  import CryptoJS from 'crypto-js';
  const { $http } = require('@/util/methods.js');
  import qiunDataCharts from '../components/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue';
  export default {
    components: {
      qiunDataCharts
    },
    data() {
      return {
        timbre: 'W', // 音色默认女声 M  W
        pronunciationType: 0, // 1英式  0美式  默认美式
        playType: 1, // 版本
        innerAudioContext: null,
        sg: '',
        data: [0, 1, 2, 3, 4], //折线图渲染的数据
        studentCode: '', //学生code
        itemId: '', //传述过来的id,报告id
        reportList: {}, //报告列表
        loadingType: 'nodata', //加载更多状态
        lineChartData: [],
        showReadFail: false, // 显示发音错误列表
        showWriteFail: false, // 显示拼写错误列表
        RecognitionInfoList: [], //发音识别详情
        radarData: {}, //发音雷达图数据
        writeRadarData: {}, //拼写雷达图数据
        chartData: {}, //发音折线图数据
        writeChartData: {}, //拼写折线图数据。
        opts: {
          color: ['#30B094'],
          padding: [15, 10, 0, 0],
          dataLabel: false,
          dataPointShape: false,
          enableScroll: false,
          legend: {
            show: false // 设置不显示图例
          },
          xAxis: {
            disableGrid: true,
            boundaryGap: 'justify',
            min: 0
          },
          yAxis: {
            gridType: 'dash',
            dashLength: 2,
            data: [
              {
                min: 0,
                max: 4
              }
            ],
            splitNumber: 4,
            axisLabel: {
              //添加这一段
              formatter: ''
            }
          },
          extra: {
            line: {
              show: false,
              type: 'straight',
              width: 2,
              activeType: 'hollow',
              linearType: 'custom'
            },
            tooltip: {
              showBox: false
            }
          }
        },
        opts1: {
          color: ['#91CB74'],
          padding: [5, 5, 5, 5],
          dataLabel: false,
          enableScroll: false,
          legend: {
            show: false
          },
          extra: {
            radar: {
              // radius:80, //雷达图半径
              dataLabel: false,
              dataPointShape: false,
              enableScroll: false,
              gridType: 'radar',
              gridColor: '#E1F9E7', //雷达图网格颜色
              gridCount: 3, //雷达图网格数量
              opacity: 1,
              max: 100,
              labelShow: true,
              border: true
            }
          }
        }
      };
    },
    onLoad(option) {
      // 获取传递过来的id参数
      let itemId = option.id;
      let studentCode = option.studentCode;
      this.itemId = itemId;
      this.studentCode = studentCode;
    },
    onReady() {
      this.homeData();
      this.initAudio();
    },
    mounted() {
      this.$nextTick(() => {
        let newPromise = new Promise((resolve) => {
          resolve();
        });
        //然后异步执行echarts的初始化函数
        newPromise.then(() => {
          //  此dom为echarts图标展示dom
          this.getinitData();
        });
      });
    },
    methods: {
      //用于比较等级
      getLevelValue(levelName) {
        switch (levelName) {
          case 'Lv0':
            return 0;
          case 'Lv1':
            return 1;
          case 'Lv2':
            return 2;
          case 'Lv3':
            return 3;
          case 'Lv4':
            return 4;
          case 'Lv5':
            return 5;
          default:
            '';
        }
      },
      //比较level1是否大于或等于level2
      IsOverLevels(level1, level2) {
        const value1 = this.getLevelValue(level1);
        const value2 = this.getLevelValue(level2);
        let isOver = false;
        if (value1 >= value2) {
          isOver = true;
        } else if (value1 < value2) {
          isOver = false;
        }
        return isOver;
      },
      async getServerData() {
        let _this = this;
        let wordRankArray = [];
        let writeRankArray = [];
        let colorList = [];
        let writeColorList = [];
        wordRankArray.push('Lv0');
        colorList.push([0, '#30B094']);
        if (this.reportList.readRankList) {
          this.reportList.readRankList.forEach((item, index) => {
            console.log('item', item);
            let wordRank = item.wordRank;
            wordRankArray.push(wordRank);
            let isOver = _this.IsOverLevels(_this.reportList.readRankAnalysisVo.wordRank, wordRank);

            if (index >= 0) {
              let color = [];
              color[0] = 0.25 * index;
              // color[1] = isOver ? "#30B094" : "#E4FCEA"
              color[1] = isOver ? '#30B094' : '#ffffff';
              colorList.push(color);
            }
          });
          console.log(colorList, 'colorList');
          writeRankArray.push('Lv0');
          writeColorList.push([0, '#30B094']);
          this.reportList.writeRankList.forEach((item, index) => {
            let wordRank = item.wordRank;
            writeRankArray.push(wordRank);
            let isOver = _this.IsOverLevels(_this.reportList.writeRankAnalysisVo.wordRank, wordRank);
            if (index > 0) {
              let color = [];
              color[0] = 0.25 * index;
              // color[1] = isOver ? "#30B094" : "#E4FCEA"
              color[1] = isOver ? '#30B094' : '#ffffff';
              writeColorList.push(color);
            }
          });
        }
        let res1 = {
          categories: wordRankArray,
          series: [
            {
              name: '',
              linearColor: colorList,
              data: this.data,
              yAxis: {
                disabled: false
              },
              startPoint: {
                radius: 5, // 圆点半径
                color: '#088368' // 圆点颜色
                // ... 其他起始点样式配置
              }
            }
          ]
        };
        let res2 = {
          categories: writeRankArray,
          series: [
            {
              name: '',
              linearColor: writeColorList,
              data: this.data,
              startPoint: {
                radius: 5, // 圆点半径
                color: '#088368' // 圆点颜色
                // ... 其他起始点样式配置
              }
            }
          ]
        };
        this.chartData = JSON.parse(JSON.stringify(res1));
        this.writeChartData = JSON.parse(JSON.stringify(res2));
      },
      getRadarData() {
        // 请求数据返回后，调用组件方法渲染
        this.$refs.radarRef.setRadarData(this.radarData);
        this.$refs.writeRadarRef.setRadarData(this.writeRadarData);
      },
      // 顶部箭头点击返回上一页
      back() {
        uni.navigateBack({
          url: '/parentEnd/dictation/dictationReport?studentCode=' + 'null'
        });
      },
      toTest() {
        uni.navigateTo({
          url: '/parentEnd/dictation/dictation'
        });
      },
      // 获取报告数据
      async getinitData() {
        let itemId = this.itemId;
        let res = await this.$httpUser.get('znyy/pd/mobile/getStudentLevel', {
          studentCode: this.studentCode,
          wordLevelId: this.itemId
        });
        if (res) {
          this.reportList = res.data.data.data;
          console.log('this.reportList', this.reportList);
          //渲染折线图
          await this.getServerData();
          let jsonMap = [];
          this.reportList.wordRecognitionList.forEach((item) => {
            let jsonObj = JSON.parse(item.wordRecognitionInfo);
            jsonMap.push(jsonObj);
          });
          this.RecognitionInfoList = jsonMap;
          //渲染雷达图
          this.radarData = {
            categories: [],
            series: []
          };
          let starData = [];
          this.reportList.readTypeRadarVoList.forEach((item) => {
            let name = '';
            name = item.name == 'DYJ1' ? '单音节单词' : item.name == 'DYJ2' ? '多音节单词' : '前后缀单词';
            this.radarData.categories.push(name);
            starData.push(item.star);
          });
          this.writeRadarData = {
            categories: [],
            series: []
          };
          let starData2 = [];
          this.reportList.writeTypeRadarVoList.forEach((item) => {
            let name = '';
            name = item.name == 'DYJ1' ? '单音节单词' : item.name == 'DYJ2' ? '多音节单词' : '前后缀单词';
            this.writeRadarData.categories.push(name);
            starData2.push(item.star);
          });

          let typeList = ['DYJ1', 'DYJ2', 'QHZ'];
          // 补充缺少的数据
          for (let type of typeList) {
            // 标记是否找到该类型
            let found = false;
            // 遍历 readTypeRadarVoList
            for (let item of this.reportList.readTypeRadarVoList) {
              // 检查 name 是否包含当前 type
              if (item.name === type) {
                found = true;
                break; // 如果找到则跳出内层循环
              }
            }
            // 如果没有找到该类型，则添加到 categories
            if (!found) {
              let name = type == 'DYJ1' ? '单音节单词' : type == 'DYJ2' ? '多音节单词' : '前后缀单词';
              this.radarData.categories.push(name);
              starData.push(0);
            }

            // 标记是否找到该类型
            let found2 = false;
            // 遍历 writeTypeRadarVoList
            for (let item of this.reportList.writeTypeRadarVoList) {
              // 检查 name 是否包含当前 type
              if (item.name === type) {
                found2 = true;
                break; // 如果找到则跳出内层循环
              }
            }
            // 如果没有找到该类型，则添加到 categories
            if (!found2) {
              let name = type == 'DYJ1' ? '单音节单词' : type == 'DYJ2' ? '多音节单词' : '前后缀单词';
              this.writeRadarData.categories.push(name);
              starData2.push(0);
            }
          }
          this.radarData.series.push({
            name: '',
            data: starData
          });
          this.writeRadarData.series.push({
            name: '',
            data: starData2
          });
          // await this.getRadarData();
          if (res.data.data.length == 0) {
            that.loadingType = 'nodata';
            uni.showToast({
              icon: 'none',
              title: '暂无更多内容了！',
              duration: 2000
            });
          }
        } else {
          uni.showToast({
            icon: 'none',
            title: '暂无数据！',
            duration: 2000
          });
        }
      },
      handleTextLength(text) {
        if (text && text.length > 5) {
          return text.substring(0, 7) + '...';
        }
        return text || '';
      },
      handleFold(type) {
        if (type == '0') {
          this.showReadFail = !this.showReadFail;
        } else {
          this.showWriteFail = !this.showWriteFail;
        }
      },
      //初始化音频文本
      async initAudio() {
        console.log('initAudio');
        // 创建一个内部音频上下文对象
        this.innerAudioContext = uni.createInnerAudioContext();
        // 设置音频地址
        /* const audioUrl = ''; // 替换为你的音频URL
  		 this.innerAudioContext.src = audioUrl; */
        // 可选：设置音频的其他属性，如是否自动播放、音量等
        this.innerAudioContext.autoplay = false; // 默认不自动播放
        this.innerAudioContext.volume = 1; // 音量范围 0~1

        this.innerAudioContext.onPlay(() => {
          console.log('音频开始播放');
        });
        // 监听播放结束事件
        this.innerAudioContext.onEnded(() => {
          console.log('音频播放结束');
          // 在这里可以添加播放结束后的处理逻辑
        });
        // 播放音频
      },
      //获取用户信息
      async homeData() {
        let _this = this;
        const res = await $http({
          url: 'zx/user/userInfoNew'
        });
        if (res) {
          let data = res.data.userCode + 'L0anhf';
          this.sg = CryptoJS.SHA1(data).toString();
          // this.getWordversion();
        }
      },
      //播放单词
      sayWord(word) {
        var that = this;
        // this.$playVoice(word, false,this.timbre,this.pronunciationType,this.playType,this.studentCode)
        let data = that.studentCode + 'L0anhf';
        let sg = CryptoJS.SHA1(data).toString();
        that.$httpUser
          .get('znyy/app/query/word/voice', {
            word: word,
            v: that.playType,
            rp: that.pronunciationType == 1 ? true : false,
            sex: that.timbre,
            sg: that.sg
          })
          .then((result) => {
            if (result.data.success) {
              let voiceUrl;
              let url;
              if (that.playType == 1) {
                url = 'https://document.dxznjy.com/' + encodeURIComponent(result.data.data);
              } else {
                url = result.data.data;
              }
              this.innerAudioContext.autoplay = false; // 默认不自动播放
              this.innerAudioContext.volume = 1; // 音量范围 0~1
              this.innerAudioContext.src = url;
              this.innerAudioContext.play();
              console.log('voiceUrl', url);
            } else {
              that.$util.alter(result.data.message);
            }
          })
          .catch((err) => {
            console.log('err', err);
          });
      },
      async initChart() {}
    }
  };
</script>

<style lang="scss" scoped>
  .boxbg {
    background: #f1fef7;
  }

  .report_Container {
    width: 750rpx;
    height: auto;
    min-height: 1176rpx;
    // background-image: url('../../static/dictation/report/reportBg.png');
    // background-size: cover;
    background-size: 375px 588px;
    background-repeat: no-repeat;
    // background-position: 50% 50%;
    padding-top: 68rpx;
    padding-bottom: 48rpx;
    padding-left: 24rpx;
    box-sizing: border-box;
    background-color: #b4ebcd;

    // 顶部文字
    .title {
      margin-top: 65rpx;
      margin-bottom: 60rpx;
      width: 710rpx;
      height: 258rpx;
      display: flex;
      justify-content: space-between;

      .title-text {
        font-size: 46rpx;
        font-family: AlibabaPuHuiTiM;
        color: #ffffff;

        image {
          width: 720rpx;
          height: 262rpx;
        }
      }

      .title-img image {
        width: 200rpx;
        height: 260rpx;
      }
    }

    // 学员信息盒子
    .information {
      .studentInfo {
        image {
          width: 180rpx;
          height: 56rpx;
        }
      }

      margin-top: 470rpx;
      width: 704rpx;
      height: 426rpx;
      // background-image:url('../../static/dictation/report/report_HeadBg.png');
      background-size: cover;
      background-repeat: no-repeat;

      .information-bootom {
        line-height: 60rpx;
        padding-left: 32rpx;
        padding-top: 32rpx;
        font-size: 30rpx;
        font-weight: bold;
        color: #1c3a2b;

        .bootom-text {
          display: flex;
          justify-content: space-between;
        }

        .green {
          color: #3ac544;
        }
      }
    }

    // 学习回顾
    .studyReview {
      width: 702rpx;
      height: auto;
      margin-top: 32rpx;
      border-radius: 16rpx;
      box-sizing: border-box;
      padding: 42rpx 18rpx 48rpx 22rpx;

      .report_Review {
        image {
          width: 180rpx;
          height: 36rpx;
        }
      }

      .report_Review1 {
        image {
          width: 662rpx;
          height: 132rpx;
        }
      }

      .reviewMsg {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-around;

        .msgItem {
          width: 211rpx;
          height: 192rpx;
          box-sizing: border-box;
          padding-top: 40rpx;
          margin-right: 10rpx;
          border-radius: 8rpx;
          text-align: center;
          line-height: 40rpx;
          background: linear-gradient(to bottom, #f0fef6, #e2fded);

          .msgText {
            margin-top: 12rpx;
          }
        }
      }

      .failWord_Head {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 30rpx;
        font-weight: bold;

        .isOpenFail {
          display: flex;
          align-items: center;

          image {
            margin-left: 12rpx;
            width: 26rpx;
            height: 20rpx;
          }
        }
      }

      .FailListBox {
        overflow-y: scroll;
      }

      .singleFailWord {
        width: 100%;
        height: 96rpx;
        background: #f4fef8;
        box-shadow: 0rpx 6rpx 16rpx 2rpx #d5f8db;
        border-radius: 56rpx;
        border: 2rpx solid #469781;
        margin-top: 24rpx;
        padding: 0 40rpx;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;

        image {
          width: 32rpx;
          height: 30rpx;
        }

        .chinese {
          width: 150rpx;
          white-space: nowrap;
          position: absolute;
          left: calc(50% - 60rpx);
          text-align: center;
        }
      }
    }

    //发音水平
    .voice_Level {
      width: 702rpx;
      height: auto;
      border-radius: 16rpx;
      margin-top: 32rpx;
      box-sizing: border-box;
      padding: 42rpx 18rpx 48rpx 22rpx;

      .charts-box {
        width: 100%;
        height: 200px;
      }

      .level_Icon {
        image {
          width: 258rpx;
          height: 36rpx;
        }
      }

      .level_Text {
        margin-top: 16rpx;
        color: #35c017;
      }

      .level_Info {
        .analysis {
          font-size: 32rpx;
          font-family: bold;
          color: #35c017;
          line-height: 40rpx;
          margin-top: 24rpx;
        }

        .analysisText {
          font-size: 28rpx;
          color: #a7c8b6;
          line-height: 40rpx;
          margin-top: 24rpx;
        }

        .overLevel {
          color: #333333;
          font-weight: bold;
        }
      }
    }

    //发音单词描述
    .voice_Improvw {
      width: 702rpx;
      height: auto;
      border-radius: 16rpx;
      margin-top: 32rpx;
      box-sizing: border-box;
      padding: 42rpx 18rpx 48rpx 22rpx;

      .radarBox {
        width: 100%;
      }

      .Improvw_Icon {
        image {
          width: 358rpx;
          height: 36rpx;
        }
      }

      .Improvw_Text {
        font-size: 28rpx;
        color: #35c017;
      }

      .voice_analysis {
        .studyType {
          font-size: 28rpx;
          margin: 30rpx 0;
        }

        .greenDot {
          width: 16rpx;
          height: 16rpx;
          border-radius: 50%;
          background: #35c017;
          margin-right: 3rpx;
        }

        .description,
        .analysis,
        .learningAdvice {
          font-family: AlibabaPuHuiTi_2_55_Regular;
          font-size: 24rpx;
          color: #555555;
          line-height: 36rpx;
          margin-top: 24rpx;

          .flexDot {
            display: flex;
            align-items: center;
            color: #35c017;
          }
        }
      }
    }

    .greenTitle {
      width: 370rpx;
      height: 56rpx;
      line-height: 56rpx;
      text-align: left;
      padding-left: 16rpx;
      font-weight: bold;
      font-size: 28rpx;
      color: #173525;
      background: linear-gradient(to right, #68ff75, #e1fdea);
    }

    //发音识别结果
    .RecognitionInfo {
      width: 702rpx;
      height: auto;
      margin: 0 auto;
      margin-left: 0;
      margin-top: 32rpx;
      border-radius: 16rpx;
      box-sizing: border-box;
      padding: 42rpx 24rpx 24rpx;

      .singleDetails {
        width: 652rpx;
        height: auto;
        background: #f4fef8;
        border-radius: 18rpx;
        border: 2rpx solid #67a590;
        margin-bottom: 24rpx;

        .wordText {
          height: 88rpx;
          line-height: 88rpx;
          text-align: center;
        }

        .gridTable {
          height: 88rpx;
          font-size: 24rpx;
          color: #555555;
          display: grid;
          grid-template-columns: repeat(5, 1fr);
          /* 定义五列，每列宽度为1fr */
          text-align: center;
        }

        .bborder {
          border-top: 1rpx solid #afd5c2;
          line-height: 88rpx;
        }

        .tableHead {
          height: 96rpx !important;
          background: #b3ebcd;

          .OneRow {
            line-height: 96rpx;
          }

          .TwoRow {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
          }
        }
      }
    }

    .dictation_btn {
      width: 702rpx;
      margin: 0 auto;
      margin-top: 40rpx;
      display: flex;
      justify-content: center;

      .lastTest {
        width: 312rpx;
        height: 94rpx;
        line-height: 94rpx;
        color: #931413;
        box-sizing: border-box;
        background-image: url('https://document.dxznjy.com/applet/newimages/lastTest.png');
        background-size: cover;
        background-repeat: no-repeat;
        text-align: center;
      }

      .startTest {
        width: 312rpx;
        height: 102rpx;
        line-height: 102rpx;
        color: #178071;
        box-sizing: border-box;
        background-image: url('https://document.dxznjy.com/applet/newimages/startTest.png');
        background-size: cover;
        background-repeat: no-repeat;
        text-align: center;
      }
    }

    .uni-ec-canvas {
      width: 100%;
      height: 100%;
      display: block;
    }
  }
</style>
