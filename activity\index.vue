<!-- 活动页 -->
<template>
  <page-meta :page-style="`background: ${activityInfo.colour || '#f3f8fc'}`"></page-meta>
  <view>
    <view class="content" :style="{ height: `${contentHeight}px` }">
      <image class="activity-image" :src="activityInfo.activityImage" style="width: 100%" mode="widthFix"></image>
      <view class="invite-info t-r" @click="handleTo">邀请记录</view>

      <!-- <view class="goods-content" :style="{ top: `${activityInfo.height}rpx` }"> -->
      <view class="goods-content" :style="{ top: `${activityInfo.height}%`, visibility: goodsContentShow ? 'visible' : 'hidden' }">
        <view class="goods-list" v-if="dataList.length > 0">
          <view class="waterfall-box h-flex-x h-flex-2" v-if="activityInfo.type == 1">
            <view>
              <helang-waterfall v-for="(item, index) in dataList.filter((i, idx) => idx % 2 === 0)" :key="index" :item="item" tag="left" :index="index" :identityType="identityType" @shareVip="shareVip" @height="onHeight" @tap="skinTap(`Coursedetails/productDetils?id=${item.goodsId}&activityId=${this.activityId}&shareUserCode=${this.shareUserCode}&bannerId=${this.bannerId}`)"></helang-waterfall>
            </view>
            <view style="margin-left: 24rpx">
              <helang-waterfall v-for="(item, index) in dataList.filter((i, idx) => idx % 2 !== 0)" :key="index" :item="item" :identityType="identityType" @shareVip="shareVip" @height="onHeight" @tap="skinTap(`Coursedetails/productDetils?id=${item.goodsId}&activityId=${this.activityId}&shareUserCode=${this.shareUserCode}&bannerId=${this.bannerId}`)" tag="right" :index="index"></helang-waterfall>
            </view>
          </view>
          <view class="courseList" v-if="activityInfo.type == 2">
            <block v-for="(item, index) in dataList" :key="index">
              <view class="courseItem radius-20 pb-10 positionRelative" @tap.stop="handleToMeet(item)">
                <view class="courseimg relative">
                  <image :src="item.courseImage" class="wh100" mode="widthFix"></image>
                </view>
                <view class="plr-30 mtb-20">
                  <view class="bold f-30">{{ item.courseName }}</view>
                  <view class="color_red font12 mtb-15 displayflex displayflexbetween">
                    <view>
                      会员价
                      <span class="bold f-34">￥{{ item.memberPrice }}</span>
                    </view>
                  </view>
                  <view class="displayflex color_grey f-24" style="justify-content: space-between">
                    <view>
                      原价
                      <text style="text-decoration: line-through">￥{{ item.originalPrice }}</text>
                    </view>
                    <view>{{ item.studyNumber }}+人付款</view>
                  </view>
                </view>
              </view>
            </block>
          </view>
        </view>
      </view>
    </view>
    <view class="foot-content">
      <button class="join" :open-type="userInfo && userInfo.userCode ? 'share' : ''" @click="handleShareMessage"></button>
    </view>
  </view>
</template>

<script>
const { $http, $navigationTo } = require('@/util/methods.js');
import helangWaterfall from '@/components/helang-waterfall/helang-waterfall';
export default {
  data() {
    return {
      imgHost: getApp().globalData.imgsomeHost,
      userInfo: {},
      dataList: [],
      leftHeight: 0,
      rightHeight: 0,
      activityId: '',
      shareUserCode: '',
      identityType: '',
      activityInfo: {},
      goodsContentDOMData: {},
      activityImageDOMData: {},
      goodsContentShow: false
    };
  },
  components: {
    helangWaterfall
  },
  computed: {
    contentHeight() {
      const top = Math.min(this.goodsContentDOMData.top, this.activityImageDOMData.top);
      const bottom = Math.max(this.goodsContentDOMData.bottom, this.activityImageDOMData.bottom);
      const contentHeight = bottom - top;
      if (!isNaN(contentHeight)) {
        setTimeout(() => {
          this.goodsContentShow = true;
        }, 300);
      }
      return isNaN(contentHeight) ? 0 : contentHeight;
    }
  },
  methods: {
    skinTap(url) {
      $navigationTo(url);
    },

    async geActivityData() {
      const res = await $http({
        url: 'zx/wap/activity/goods',
        showError: 1,
        data: {
          id: this.activityId,
          userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
        }
      });
      this.getHeight();
      if (res) {
        if (this.shareUserCode) {
          this.saveInviteInfo();
        }
        this.activityInfo = res.data;
        if (res.data.type == 1) {
          // type  1.商品 2.会议
          this.dataList = res.data.piGoodsRecommendationWapVO || [];
        } else {
          this.dataList = res.data.courseListVoList || [];
        }
      } else {
        uni.showModal({
          title: '温馨提示',
          content: '活动不存在或活动已下架',
          showCancel: false,
          confirmText: '返回',
          success(res) {
            if (res.confirm) {
              uni.switchTab({
                url: '/pages/index/index'
              });
            }
          }
        });
      }
    },

    handleToMeet(item) {
      uni.navigateTo({
        url: `/meeting/meetIndex?id=${item.courseId}&activityId=${this.activityId}&shareUserCode=${this.shareUserCode}`
      });
    },
    async getUserInfo() {
      const res = await $http({
        url: 'zx/user/userInfoNew'
      });

      if (res) {
        this.userInfo = res.data;
      }
    },
    async saveInviteInfo() {
      const res = await $http({
        url: 'zx/wap/activity/invitation/record/save',
        method: 'post',
        data: {
          activityId: this.activityId,
          shareUserCode: this.shareUserCode
        }
      });
    },
    handleShareMessage() {
      getApp().sensors.track('activitySharingClick', {
        activityId: this.activityId
      });
      let token = uni.getStorageSync('token');
      if (!token) {
        uni.showModal({
          title: '提示',
          content: '需要登录才可以使用分享哦~',
          showCancel: false
        });
      }
    },
    getHeight() {
      setTimeout(() => {
        const query = uni.createSelectorQuery().in(this);
        query
          .select('.goods-content')
          .boundingClientRect((data) => {
            this.goodsContentDOMData = data;
          })
          .exec();
        query
          .select('.activity-image')
          .boundingClientRect((data) => {
            this.activityImageDOMData = data;
          })
          .exec();
      }, 1000);
    },
    // 监听高度变化
    onHeight(height, tag) {
      if (tag == 'left') {
        this.leftHeight += height;
      } else {
        this.rightHeight += height;
      }
    },
    handleTo() {
      getApp().sensors.track('activityInvitationClick', {
        activityId: this.activityId
      });
      let token = uni.getStorageSync('token');
      if (!token) {
        return uni.showModal({
          title: '提示',
          content: '需要登录才可以查看邀请记录哦~',
          showCancel: false
        });
      }
      $navigationTo(`activity/invite?activityId=${this.activityId}`);
    }
  },
  onShareAppMessage() {
    // 活动分享图 activityShareImage this.activityInfo.activityShareImage
    return {
      title: '叮，你的好友敲了你一下，赶紧过来看看',
      imageUrl: this.activityInfo.activityShareImage || 'https://document.dxznjy.com/course/cc6d6e023c7d4317af6d98bf83719d58.png',
      //如果有参数的情况可以写path
      path: '/pages/beingShared/index?&type=10&source=app&path=/activity/index&shareUserCode=' + this.userInfo.userCode + '&activityId=' + this.activityId + '&bannerId=' + this.bannerId
    };
  },
  onShow() {
    if (uni.getStorageSync('token')) {
      uni.showLoading({
        title: '加载中...'
      });
      this.getUserInfo();
    }
    this.identityType = uni.getStorageSync('identityType');
  },

  onLoad(options) {
    this.activityId = options.activityId;
    this.bannerId = options.bannerId;
    if (options.shareUserCode) {
      this.shareUserCode = options.shareUserCode;
    }

    //埋点-商品详情页
    getApp().sensors.track('$MPViewScreen', {
      pageName: '活动页',
      activityId: this.activityId,
      bannerId: this.bannerId
    });
    this.geActivityData();
  }
};
</script>

<style lang="scss" scoped>
.content {
  position: relative;
  width: 100%;
  padding-bottom: 140rpx;
  .invite-info {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    font-size: 32rpx;
    z-index: 1;
  }
}

.goods-content {
  position: absolute;
  width: 100%;
  padding-bottom: 140rpx;

  .goods-list {
    padding: 0 30rpx;
  }
}

.foot-content {
  position: fixed;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  bottom: 0;
  width: 100%;
  height: 140rpx;
  z-index: 9;
  justify-content: center;
  background: rgba(255, 255, 255, 0.5);
}

.join {
  width: 686rpx;
  height: 104rpx;
  background: transparent;
  background-image: url('https://document.dxznjy.com/course/a7b59abd1d7c4896822affc650eb589a.png');
  background-size: 100% 100%;
}

.courseList {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.courseItem {
  width: 330upx;
  border-radius: 20upx;
  background-color: #fff;
  margin-bottom: 30rpx;
}
</style>
