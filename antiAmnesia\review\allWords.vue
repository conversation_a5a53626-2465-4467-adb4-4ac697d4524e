<template>
  <view>
    <!-- <view class="positioning" @click="goback">
  <view>
    <!-- <view class="positioning" @click="goback">
			<uni-icons type="left" size="24" color="#000"></uni-icons>
		</view> -->
    <view class="center-banner">
      <view v-if="reviewType !== 'grammar'" :class="topShow ? 'center_boxs' : 'center-box'" :style="{ height: useHeight + 'rpx' }">
        <image src="https://document.dxznjy.com/applet/newimages/fuxi@2x_new.png" mode="widthFix" class="niuimg" style="width: 434rpx"></image>
        <view class="f-32 green" :class="topShow ? 'words-tops' : 'words-top'">
          <view class="remind mb-20 c-00">今日共有{{ wordList.totalWordCount || 0 }}词需复习</view>
          <view v-if="wordList.todayWordCount != 0" class="mb-20" @click="getWordPreview(1)">当日{{ wordList.todayWordCount || 0 }}词</view>
          <view v-if="wordList.beforeWordCount != 0" @click="getWordPreview(2)">往期{{ wordList.beforeWordCount || 0 }}词</view>
        </view>
        <view class="plr-50" :class="topShow ? 'start-tops' : 'start_top'">
          <button class="start" @click="startReview(0)">开始复习</button>
          <view class="temporarily mt-30" @click="closeReview()">暂不复习</view>
        </view>
      </view>
      <view v-else :class="topShow ? 'center_boxs' : 'center-box'" :style="{ height: useHeight + 'rpx' }">
        <image src="https://document.dxznjy.com/applet/newimages/fuxi@2x_new.png" mode="widthFix" class="niuimg" style="width: 434rpx"></image>
        <view class="f-32 green" :class="topShow ? 'words-tops' : 'words-top'">
          <view class="remind mb-20 c-00">今日共有{{ graList.totalNum || 0 }}个语法知识点需复习</view>
          <view v-if="graList.todayNum != 0" class="mb-20" @click="getGraPreview(1)">当日{{ graList.todayNum || 0 }}个语法知识点</view>
          <view v-if="graList.formerNum != 0" @click="getGraPreview(2)">往期{{ graList.formerNum || 0 }}个语法知识点</view>
        </view>
        <view class="plr-50" :class="topShow ? 'start-tops' : 'start_top'">
          <button class="start" @click="starGrammar(0)">开始复习</button>
          <view class="temporarily mt-30" @click="closeReview()">暂不复习</view>
        </view>
      </view>
    </view>

    <!-- 选择复习方式弹窗 -->
    <uni-popup ref="popopChooseType" :mask-click="false" type="center">
      <view class="dialogBG">
        <view class="reviewCard_box positionRelative">
          <view class="cartoom_image">
            <image src="https://document.dxznjy.com/dxSelect/dialog_icon.png" mode="widthFix"></image>
          </view>
          <view class="review_close" @click="closeDialog">
            <uni-icons type="clear" size="28" color="#B1B1B1"></uni-icons>
          </view>
          <view class="reviewCard">
            <view class="reviewTitle bold pb-10">选择复习方式</view>
            <view class="dialogContent" @click="chooseType(item, index)" v-for="(item, index) in arrayType" :class="isactive == index ? 'addclass' : 'not-selected'">
              <view class="flex-a-c" style="justify-content: center" v-if="item.title == 'AI模式'">
                <view class="" style="width: 70rpx"></view>
                <text class="mr-8">{{ item.title }}</text>
                <image src="https://document.dxznjy.com/course/b3084b45968a453a965c5e2af9ec099c.png" style="width: 70rpx" mode="widthFix"></image>
              </view>
              <view v-else>
                <text>{{ item.title }}</text>
              </view>
            </view>
            <view class="mask-footer">
              <button class="confirm-button" @click="confirmType()">确定</button>
              <button class="cancel-button" @click="closeDialog">取消</button>
            </view>
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        wordList: {}, // 单词
        graList: {}, // 语法
        studentCode: '', //学员Code
        topShow: false, //判断向上高度
        useHeight: 0,
        reviewType: '', //复习类型
        arrayType: [
          {
            title: 'AI模式',
            value: '1'
          },
          {
            title: '原始模式',
            value: '2'
          }
        ],
        isactive: -1
      };
    },
    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          if (h < 1500) {
            this.topShow = true;
          }

          this.useHeight = h - 190;
        }
      });
    },
    onLoad(options) {
      console.log(options.reviewType, 'reviewType');
      this.studentCode = options.studentCode;
      this.reviewType = options.reviewType;
    },
    onShow() {
      if (this.reviewType !== 'grammar') {
        this.getWords();
      } else {
        this.getGra();
      }
    },
    methods: {
      //点击选择学员
      chooseType(item, index) {
        getApp().sensors.track('popupReviewMethodClick', {
          name: item.title
        });
        this.isactive = index;
        this.typeInfo = { ...item };
      },
      async confirmType() {
        let that = this;
        if (that.typeInfo.value == 1) {
          let res = await that.$httpUser.post('znyy/word/review/generate-id');
          console.log(res);
          if (res && res.data.success) {
            let batchId = res.data.data;
            that.closeDialog();
            uni.navigateTo({
              url: `/antiAmnesia/review/aiReviewList?studentCode=${that.studentCode}&batchId=${batchId}`
            });
          }
        } else {
          if (this.wordCount == 0) {
            this.$util.alter('暂无可复习单词！');
          } else {
            uni.navigateTo({
              url: '/antiAmnesia/review/list?studentCode=' + this.studentCode
            });
          }
        }
      },
      closeDialog() {
        this.isactive = -1;
        this.$refs.popopChooseType.close();
      },
      // 进入语法复习页
      starGrammar() {
        getApp().sensors.track('antiAmnesiaClick', {
          name: '开始复习'
        });
        uni.navigateTo({
          url: '/antiAmnesia/review/grammarReview?studentCode=' + this.studentCode
        });
      },
      // 获得单词数量
      async getWords() {
        uni.showLoading({
          title: '加载中'
        });
        let res = await this.$httpUser.get('znyy/review/query/fun/word/count', {
          studentCode: this.studentCode
        });
        uni.hideLoading();
        // console.log(res)
        if (res.data.success) {
          this.wordList = res.data.data;
        }
      },

      // 获得语法数量
      async getGra() {
        uni.showLoading({
          title: '加载中'
        });
        let res = await this.$httpUser.get('dyf/wap/applet/todayReviewStatistics', {
          // todo 学员 code 固定值 用于测试
          studentCode: this.studentCode
          // studentCode: '6231217888',
        });
        uni.hideLoading();
        // console.log(res)
        if (res.data.success) {
          this.graList = res.data.data;
        }
      },

      // 单词页
      getWordPreview(type) {
        uni.navigateTo({
          url: '/antiAmnesia/review/wordPreview?studentCode=' + this.studentCode + '&type=' + type
        });
      },
      // 语法页
      getGraPreview(type) {
        uni.navigateTo({
          url: '/antiAmnesia/review/grammarPreview?studentCode=' + this.studentCode + '&type=' + type
        });
      },
      closeReview() {
        getApp().sensors.track('antiAmnesiaClick', {
          name: '暂不复习'
        });
        uni.navigateBack();
      },
      // goback(){
      // 	uni.navigateBack()
      // },
      // 开始复习
      startReview(type) {
        getApp().sensors.track('antiAmnesiaClick', {
          name: '开始复习'
        });
        this.$refs.popopChooseType.open();
        // if (type == 0) {
        //   if (this.wordCount == 0) {
        //     this.$util.alter('暂无可复习单词！');
        //   } else {
        //     uni.navigateTo({
        //       url: '/antiAmnesia/review/list?studentCode=' + this.studentCode
        //     });
        //   }
        // }
      }
    }
  };
</script>

<style lang="scss" scoped>
  .positioning {
    position: fixed;
    top: 100rpx;
    left: 30rpx;
  }

  .center-banner {
    margin: 0 auto;
    margin-bottom: 50rpx;
    width: 690rpx;
    height: 1408rpx;
    background: #ffffff;
    border-radius: 20rpx;
  }

  .center-box {
    width: 100%;
    text-align: center;
    margin-top: 200rpx;
    margin-bottom: 50rpx;
  }

  .center_boxs {
    width: 100%;
    text-align: center;
    // margin-top: 260rpx;
  }

  .words-tops {
    margin-top: 60rpx;
  }

  .words-top {
    margin-top: 120rpx;
  }

  .start {
    border-radius: 50rpx;
    color: #fff;
    font-size: 30rpx;
    height: 90rpx;
    background: #2e896f;
    line-height: 90rpx;
  }

  .start_top {
    margin-top: 208rpx;
  }

  .start-tops {
    margin-top: 148rpx;
  }

  .temporarily {
    font-size: 30rpx;
    color: #999;
    text-align: center;
  }

  .green {
    color: #2e896f;
  }

  .remind {
    font-weight: bold;
    font-size: 32rpx;
  }

  .niuimg {
    margin-top: 180rpx;
  }
  /* 弹窗样式 */
  .dialogBG {
    width: 100%;
    /* height: 100%; */
  }
  /* 21天结束复习弹窗样式 */
  .reviewCard_box {
    width: 670rpx;
    /* height: 560rpx; */
    position: relative;
  }

  .reviewCard_box image {
    width: 100%;
    height: 100%;
  }
  .reviewCard {
    position: relative;
    width: 100%;
    height: 100%;
    background: #ffffff;
    color: #000;
    border-radius: 24upx;
    padding: 50upx 55upx;
    box-sizing: border-box;
  }
  .cartoom_image {
    width: 420rpx;
    position: absolute;
    top: -250rpx;
    left: 145rpx;
    z-index: -1;
  }
  .review_close {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    z-index: 1;
  }

  .reviewTitle {
    width: 100%;
    text-align: center;
    font-size: 34upx;
    display: flex;
    justify-content: center;
  }

  .dialogContent {
    box-sizing: border-box;
    font-size: 32upx;
    line-height: 45upx;
    text-align: center;
    margin-top: 40rpx;
  }
  .addclass {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #2e896f;
    border: 1px solid #2e896f;
    border-radius: 35rpx;
  }
  /* 底部按钮样式 */
  .mask-footer {
    margin-top: 20px;
    display: flex;
    justify-content: space-around;
  }

  .mask-footer button {
    width: 250rpx;
    height: 80rpx;
    font-size: 30rpx;
    border-radius: 45rpx;
  }
  .not-selected {
    width: 100%;
    height: 70rpx;
    line-height: 70rpx;
    font-size: 30rpx;
    color: #000;
    border: 1px solid #c8c8c8;
    border-radius: 35rpx;
  }
  .confirm-button {
    display: flex;
    justify-content: space-around;
    align-items: center;
    /* 文本垂直居中对齐 */
    background: #2e896f;
    color: #ffffff !important;
  }

  .cancel-button {
    display: flex;
    justify-content: space-around;
    align-items: center;
    /* 文本垂直居中对齐 */
    color: #2e896f !important;
    border: 1rpx solid #2e896f !important;
  }
</style>
