<template>
  <view class="container" :style="{ height: useHeight - 130 + 'rpx' }">
    <u-navbar title=" " :safeAreaInsetTop="true" placeholder>
      <view class="u-nav-slot1" slot="left" @click="goBack">
        <u-icon name="arrow-left" color="#000" bold size="24"></u-icon>
      </view>
      <view class="u-nav-slot" slot="center">
        <view class="u-nav-slot-center">设置</view>
      </view>
    </u-navbar>
    <!-- 设置项列表 -->
    <view class="settings-list">
      <!-- 单选切换项 -->
      <view class="title">单词播放设置</view>
      <view class="setting-item1">
        <view class="setting-item">
          <text>手动复习下一个单词</text>
          <view :class="form.manualReviewNext == 1 ? 'active' : 'radio'" @click="changeRadio('manualReviewNext')"></view>
        </view>
      </view>

      <view class="setting-item1">
        <view class="setting-item">
          <text>自动复习下一个单词</text>
          <view :class="form.autoReviewNext == 1 ? 'active' : 'radio'" @click="changeRadio('autoReviewNext')"></view>
        </view>
        <!-- 输入框 -->
        <view class="setting-item">
          <text>每个生词的播放遍数</text>
          <!-- <input type="number" v-model="playCount" class="input-number" /> -->
          <!-- <u-number-box v-model="form.playbackCount"></u-number-box> -->
          <view class="numberInput">
            <!-- <view class="left">+</view> -->
            <!-- <view class="right">-</view> -->
            <u-icon name="plus-circle-fill" size="40" labelColor="#DADDE3" @click="changeNumber('add')"></u-icon>
            <text style="width: 60rpx; text-align: center" class="input">{{ form.playbackCount }}</text>
            <u-icon name="minus-circle-fill" size="40" labelColor="#DADDE3" @click="changeNumber('del')"></u-icon>
          </view>
        </view>
      </view>
      <!-- <view class="title">单词显示设置</view> -->
      <!-- 开关项 -->
      <!--   <view class="setting-item1">
        <view class="setting-item">
          <text>显示中文</text>
          <u-switch size="32" activeColor="#428A6F" :activeValue="1" :inactiveValue="0" v-model="form.showChinese"></u-switch>
        </view>
      </view> -->
      <!-- <view class="title">无录音处理</view>
      <view class="setting-item1">
        <view class="setting-item">
          <text>提示弹窗</text>
          <view :class="form.promptPopup == 1 ? 'active' : 'radio'" @click="changeRadio('promptPopup')"></view>
        </view>
      </view>
      <view class="setting-item1">
        <view class="setting-item">
          <text>自动跳过</text>
          <view :class="form.autoSkip == 1 ? 'active' : 'radio'" @click="changeRadio('autoSkip')"></view>
        </view>
      </view> -->
    </view>

    <!-- 底部保存按钮 -->
    <view class="footer">
      <button class="save-btn" hover-class="button-hover" @click="handleSave">保存</button>
    </view>
    <u-modal :show="modalShow" title=" " showCancelButton>
      <view class="slot-content">
        <view class="modal-title">保存成功</view>
      </view>
      <view class="confirmButton1" slot="confirmButton">
        <view class="btn confirm" @click="changeConfig">好的</view>
      </view>
    </u-modal>

    <u-modal :show="toastShow" title=" " showCancelButton>
      <view class="slot-content">
        <view class="modal-title">您还未保存本次设置，确认返回么？</view>
      </view>
      <view class="confirmButton" slot="confirmButton">
        <view class="btn cancel" @click="confirmFn(0)">返回</view>
        <view class="btn confirm" @click="confirmFn(1)">取消</view>
      </view>
    </u-modal>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        studentCode: '',
        form: {
          id: 0,
          manualReviewNext: 1, //手动复习下一个单词,1开启0关闭
          autoReviewNext: 0, //自动复习下一个单词,1开启0关闭
          playbackCount: 1, //每次生词的播放遍数
          // showChinese: 1, //显示中文,1开启0关闭
          // promptPopup: 1, //提示弹窗,1开启0关闭
          // autoSkip: 0, //自动跳过,1开启0关闭
          studentCode: '', //学员编号
          isFirst: true
        },
        reviewType: 'manual', // 单选值：manual/auto
        playCount: 24, // 输入框数值
        // showChinese: true, // 中文显示开关
        showPopup: false, // 弹窗开关
        useHeight: 0,
        modalShow: false,
        toastShow: false,
        oldConfig: {}
      };
    },
    onReady() {
      let that = this;
      uni.getSystemInfo({
        //调用uni-app接口获取屏幕高度
        success(res) {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          that.useHeight = h;
        }
      });
    },
    onLoad(e) {
      if (e.studentCode) {
        this.studentCode = e.studentCode;
        this.getConfigDetail();
      }
    },
    methods: {
      confirmFn(type) {
        if (type == 0) {
          uni.navigateBack();
        } else {
          this.toastShow = false;
        }
      },
      goBack() {
        if (this.isEqual(this.oldConfig, this.form)) {
          // console.log(this.oldConfig, this.form, this.isEqual(this.oldConfig, this.form));
          uni.navigateBack();
        } else {
          this.toastShow = true;
        }
      },
      isEqual(obj1, obj2) {
        // 如果类型不同，直接返回 false
        if (typeof obj1 !== typeof obj2) return false;
        // 如果是基本类型，直接比较值
        if (typeof obj1 !== 'object' || obj1 === null || obj2 === null) {
          return obj1 === obj2;
        }
        const keys1 = Object.keys(obj1);
        const keys2 = Object.keys(obj2);

        // 检查属性数量是否相同
        if (keys1.length !== keys2.length) return false;

        // 检查每个属性的值是否严格相等
        for (const key of keys1) {
          if (obj1[key] !== obj2[key]) return false;
        }

        return true;
      },
      changeConfig() {
        uni.navigateBack();
      },
      changeNumber(type) {
        if (type == 'add') {
          this.form.playbackCount++;
        } else {
          if (this.form.playbackCount <= 1) {
            return;
          } else {
            this.form.playbackCount--;
          }
        }
      },
      changeRadio(name) {
        if (this.form[name] == 1) {
          return;
        } else {
          if (name == 'autoReviewNext') {
            this.form.autoReviewNext = 1;
            this.form.manualReviewNext = 0;
          } else if (name == 'manualReviewNext') {
            this.form.autoReviewNext = 0;
            this.form.manualReviewNext = 1;
          } else if (name == 'promptPopup') {
            this.form.promptPopup = 1;
            this.form.autoSkip = 0;
          } else {
            this.form.promptPopup = 0;
            this.form.autoSkip = 1;
          }
        }
      },
      async getConfigDetail() {
        let res = await this.$httpUser.get('znyy/word/review/play-config', {
          // todo 学员 code 固定值 用于测试
          studentCode: this.studentCode
          // studentCode: '6231217888',
        });
        if (res && res.data.success) {
          console.log(res.data.data);
          const converted = Object.fromEntries(Object.entries(res.data.data).map(([key, value]) => [key, typeof value === 'boolean' ? (value ? 1 : 0) : value]));
          console.log(converted);
          this.oldConfig = JSON.parse(JSON.stringify(converted));
          this.form = converted;
          uni.setStorageSync('aiConfigData', this.form);
        }
      },
      // 处理单选切换
      handleRadio(type) {
        this.reviewType = type;
      },
      // 处理开关切换
      handleSwitch(key, e) {
        this[key] = e.detail.value;
      },
      // 保存配置
      async handleSave() {
        uni.showLoading({
          title: '保存中'
        });
        let res = await this.$httpUser.post('znyy/word/review/play-config', this.form);
        if (res && res.data.success) {
          this.getConfigDetail();
          this.modalShow = true;
          uni.hideLoading();
        } else {
          uni.hideLoading();
        }
        // 这里可添加提交到服务器的逻辑
      }
    }
  };
</script>

<style lang="scss">
  page {
    background-color: #fff;
  }
  .container {
    padding: 20rpx;
    background-color: #fff;

    .settings-list {
      background: white;
      border-radius: 12rpx;
      padding: 0 24rpx;
      .title {
        font-size: 28rpx;
        color: #333333;
        font-weight: 600;
        padding: 15rpx;
      }
      .setting-item1 {
        padding: 0 20rpx;
        background: #f7f8fb;
        border: 1rpx solid #eee;
        margin-top: 16rpx;
      }
      .setting-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // width: 686rpx;
        height: 80rpx;
        background: #f7f8fb;
        font-size: 26rpx;
        border-radius: 10rpx;
        &:last-child {
          border-bottom: none;
        }

        .input-number {
          width: 120rpx;
          text-align: right;
          font-size: 28rpx;
        }
      }
    }
    .numberInput {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .left,
      .right {
        width: 40rpx;
        height: 40rpx;
        line-height: 40rpx;
        text-align: center;
        border-radius: 50%;
        background: #dadde3;
        color: #000;
      }
    }
    .radio {
      width: 38rpx;
      height: 38rpx;
      border-radius: 50%;
      background: #ffffff;
      border: 2rpx solid #979797;
    }
    .active {
      width: 32rpx;
      height: 32rpx;
      border-radius: 50%;
      background: #ffffff;
      border: 8rpx solid #428a6f;
    }
    .footer {
      position: fixed;
      bottom: 40rpx;
      left: 24rpx;
      right: 24rpx;

      /* 优化后的样式 */
      .save-btn {
        background: #428a6f;
        color: white;
        border-radius: 48rpx;
        font-size: 34rpx;
        height: 88rpx;
        line-height: 88rpx;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;

        &::after {
          border: none !important; /* 去除微信默认边框 */
        }

        .icon-check {
          margin-left: 12rpx;
          font-size: 36rpx;
        }
      }

      /* 按压态效果 */
      .button-hover {
        transform: scale(0.98);
        opacity: 0.9;
        box-shadow: 0 4rpx 12rpx rgba(7, 193, 96, 0.2);
      }

      /* 适配暗色模式 */
      @media (prefers-color-scheme: dark) {
        .save-btn {
          background: linear-gradient(135deg, #09d469, #07b359);
        }
      }
    }
  }
  .modal-title {
    font-size: 28rpx;
    color: #555555;
    margin-bottom: 10rpx;
  }
  .modal-title {
    font-size: 28rpx;
    color: #555555;
    margin-bottom: 10rpx;
  }
  .confirmButton1 {
    display: flex;
    justify-content: space-around;
    align-items: center;
    .btn {
      width: 510rpx;
      height: 82rpx;
      background: #428a6f;
      border-radius: 12rpx;
      line-height: 82rpx;
      text-align: center;
    }
    .confirm {
      background: #428a6f;
      color: #fff;
    }
  }
  .confirmButton {
    display: flex;
    justify-content: space-around;
    align-items: center;
    .btn {
      width: 264rpx;
      height: 82rpx;
      border-radius: 12rpx;
      line-height: 82rpx;
      text-align: center;
    }
    .cancel {
      background: #f1f1f1;
      color: #555;
    }
    .confirm {
      background: #428a6f;
      color: #fff;
    }
  }
</style>
