<template>
	<view class="plr-30">
		<view class="bg-ff radius-15">
			<view class="p-30" v-for="(items,idx) in list" :key="idx" :class="idx == 1 ? 'even' : ''">
				<view class="flex-s flex-y-s">
					<view class="flex-a-c flex-y-s">
						<view v-if="list.length>1">
							<image v-if="items.type==1" :src="items.headPortrait" class="head-img"></image>
						</view>
						<image v-else :src="items.headPortrait" class="head-img"></image>
						<view>
							<view class="c-66 flex-a-c">
								<view class="mr-30 f-30">{{items.userName}}</view>
							</view>
							<view class="c-99 f-28 mt-8">{{items.createTime.slice(0,11)}} {{items.type==1?'首次评价':'追评'}}</view>
						</view>
					</view>
					<view class="deleteEvaluation" @click="delEvaluate(1)">删除评价</view>
				</view>
				<view class="mt-40">
					<uni-rate v-if="items.evaluateGrade!=''" v-model="items.evaluateGrade" active-color="#E57126" size="20"/>
				</view>
				<view class="mt-20 c-33 lh-50 f-30">
					<text style="word-wrap: break-word">
						{{items.evaluateContent || '该用户未填写评价内容'}}
					</text>
				</view>
				<!-- <image @click="previewSqs(firstimg,item)" class="img" v-if="firstimg.length>0" v-for="(item,index) in firstimg" :key="index" :src="item"></image> -->
				<image v-if="items.photoUrl" @click="previewSqs(items.photoUrl,item)" class="img" v-for="(item,index) in handleImg(items.photoUrl)" :key="index" :src="item"></image>
			</view>
			<!-- <view v-if="list.length>1">
				<view class="p-30">
					<view class="flex-s">
						<view class="c-99 f-28">{{list[1].createTime}} 追评</view>
						<view class="deleteEvaluation" @click="delEvaluate(2)">删除评价</view>
					</view>
					<view class="mt-40">
						<uni-rate v-model="list[1].evaluateGrade" active-color="#E57126" size="20"/>
					</view>
					<view class="mt-20 c-33 lh-50">{{list[1].evaluateContent}}</view>
					<image @click="previewSqs(lastimg,item)" class="img" v-if="lastimg.length>0" v-for="(item,index) in lastimg" :key="index" :src="item"></image>
				</view>
			</view> -->
		</view>
		
		<!-- 商品推荐 -->
		<view class="mt-40 flex-c">
			<u-line color="#ccc" length="20%"></u-line>
			<text class="mlr-20 f-32 bold">热门推荐</text>
			<u-line color="#ccc" length="20%"></u-line>
		</view>
		<view class="courseList pt-30">
			<block v-for="(item,index) in infoLists.list" :key="index">
				<view class="courseItem radius-20 pb-10 positionRelative"
					@tap.stop="skintap('Coursedetails/productDetils?id='+item.courseId)">
					<view class="courseimg relative">
						<image :src="item.courseImage" class="wh100" mode="widthFix"></image>
					</view>
					<view class="positionAbsolute courseItem_tig">
						<image v-if="item.courseId=='f2cf0e76538473a7179c993674e09bdf'" :src="imgHost+'alading/correcting/mustBuy_icon.png'" class="wh100" mode="widthFix"></image>
						<image v-if="item.courseId=='c499556f9da64dce19b723296c884bc4'" :src="imgHost+'alading/correcting/recommend_icon.png'" class="wh100" mode="widthFix"></image>
					</view>
					<view class="mtb-20 pl-20 pr-20">
						<view class="bold f-28">{{ item.courseName }}</view>
						<view class="color_red font12 mtb-16">会员价 <span class="bold f-34">￥{{item.memberPrice}}</span></view>
						<view class="displayflex color_grey f-24" style="justify-content: space-between;">
							<view class="">原价<text style="text-decoration: line-through;">￥{{item.originalPrice}}</text></view>
							<view class="">{{item.studyNumber}}+人付款</view>
						</view>
					</view>
		
				</view>
			</block>
			<view v-if="no_more && infoLists.list.length>0" style="width: 100%;text-align: center;">
				<u-divider text="到底了"></u-divider>
			</view>
		</view>
		
		<!-- 删除评价 -->
		<uni-popup ref="delectDialog" type="center" :maskClick="false" :classBG="'white'">
			<view class="dialogBG">
				<view class="reviewCard_box positionRelative">
					<view class="cartoom_image">
						<image :src="dialog_iconUrl" mode="widthFix"></image>
					</view>

					<view class="reviewCard">
						<view class="dialogContent">是否删除此条评价</view>
			
						<view class="flex-s plr-30">
							<view class="common_btn_two common_btn_orange_active" @click="confirmStock">确定</view>
							<view class="common_btn_two common_btn_orange" @click="closeDialog">取消</view>
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>

<script>
    import Util from '@/util/util.js'
	const { $http, $showSuccess, $showMsg } = require('@/util/methods.js')
	export default {
		data() {
			return {
				avaUrl: Util.getCachedPic("https://document.dxznjy.com/dxSelect/home_avaUrl.png","home_avaUrl_path"),
				rateValue: 1, // 评价评分
				radio:2, // 追加评价
				infoLists: {},
				page: 1,
				no_more: false,
				imgHost: getApp().globalData.imgsomeHost,
				orderNo:"", // 订单编号
				type:1 , // 1第一次评价   2追评
				list:{}, // 评价 
				firstimg: [], // 第一次评价图片
				lastimg:[], // 追评图片
				id:'' , // 删除评价id
                
                dialog_iconUrl:Util.getCachedPic("https://document.dxznjy.com/dxSelect/dialog_icon.png","dialog_icon_path"),
			};
		},
		onShow() {
			// this.$refs.delectDialog.open()
		},
		onLoad(e) {
			this.orderNo = e.orderNo;
			this.type = Number(e.type);
			this.course();
			this.viewComments();
		},
		onReachBottom() {
			if (this.page >= this.infoLists.totalPage) {
				this.no_more = true
				return false;
			}
			this.course(true, ++this.page);
		},
		methods:{
			async course(isPage, page) {
				let _this = this
				const res = await $http({
					url: 'zx/course/courseList',
					data: {
						indexShow: 1,
						cityCode: '',
						cateId: '',
						page: page || 1,
                        cateType:1,
					}
				})
				if (res) {
					if (isPage) {
						let old = _this.infoLists.list
						_this.infoLists.list = [...old, ...res.data.list]
					} else {
						_this.infoLists = res.data
					}
				}
			},
			
			closeDialog(){
				this.$refs.delectDialog.close()
			},
			
			async viewComments(){
				let _this = this;
				const res = await $http({
					url: 'zx/order/evaluate/selOrderEvaluateVo',
					data: {
						orderNo: _this.orderNo,
						type:_this.type
					}
				})
				if (res) {
					_this.list = res.data;
					for (let i = 0; i < _this.list.length; i++) {
						if(_this.list[i].type==1){
							if(_this.list[i].photoUrl !=''){
								if (_this.list[i].photoUrl.includes(',')) {
								    _this.firstimg = _this.list[i].photoUrl.split(",");
								} else {
								    _this.firstimg.push(_this.list[i].photoUrl);
								}
							}
						}else{
							if(_this.list[i].photoUrl !=''){
								if (_this.list[i].photoUrl.includes(',')) {
								    _this.lastimg = _this.list[i].photoUrl.split(",");
								} else {
								    _this.lastimg.push(_this.list[i].photoUrl);
								}
							}
						}
					}
					console.log(_this.firstimg)
					console.log(_this.lastimg)
					console.log(_this.list)
				}
			},
			
			handleImg(item){
				const jsonRegex = /^(\{.*\}|\[.*\])$/;
				if(jsonRegex.test(item)){
					console.log('是');
					return JSON.parse(item);
				}else{
					console.log('否');
					return item;
				}
			},
			
			// 图片预览
			previewSqs(previewImg,index) {
				let imgData = JSON.parse(decodeURIComponent(previewImg));
				console.log('index',previewImg, index)
				console.log('预览')
				let _this = this;
				let imgsArray = [];
				//在这遍历的还是图片数据，也就是tabledata下的imgs_arr属性
				for (let i = 0; i < imgData.length; i++) {
					imgsArray.push(imgData[i])
				}
				console.log(imgsArray,'------------');
 
				uni.previewImage({
					current: index,//当前所点击预览的图片地址
					urls: imgsArray,//这就是当前行图片数据，注意一定要是数组格式
					indicator: 'number',
					loop: true
				});
			},
			
			delEvaluate(item){
				console.log(item)
				if(item==1){
					this.id = this.list[0].id;
				}else{
					this.id = this.list[1].id;
				}
				this.$refs.delectDialog.open(item);
			},
			
			// 删除评价
			async confirmStock(){
				debugger
				const res = await $http({
					url: `zx/order/evaluate/delEvaluate/${this.id}`,
					method: 'delete',
					data: {
						id:this.id
					}
				})
				
				if (res) {
					this.$refs.delectDialog.close();
					setTimeout(()=>{
						this.$util.alter('删除成功')
					})
					setTimeout(()=>{
						uni.navigateBack()
					},1500)
				}
			},
			
		}
	}
</script>

<style lang="scss" scoped>
	// 商品评价
	.head-img{
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		margin-right: 20rpx;
	}
	
	.golden{
		color: #886A34;
		height: 38rpx;
		font-size: 26rpx;
		padding: 0 8rpx;
		line-height: 38rpx;
		text-align: center;
		border-radius: 6rpx;
		background: linear-gradient(to right, #F5EBD6, #DEC288);
	}
	
	.deleteEvaluation{
		color: #2E896F;
		width: 150rpx;
		height: 50rpx;
		font-size: 30rpx;
		text-align: center;
		line-height: 50rpx;
		border-radius: 30rpx;
		border: 1px solid #2E896F;
	}
	
	.img{
		width: 180rpx;
		height: 180rpx;
		border-radius: 20rpx;
		margin-top: 30rpx;
		margin-right: 30rpx;
		// background-color: #2E896F;
	}
	
	.courseList {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
	}
	
	.courseItem {
		width: 330upx;
		border-radius: 20upx;
		background-color: #fff;
		margin-bottom: 30rpx;
	}
	
	/deep/.u-popup__content{
		position: relative;
		background-color: transparent !important;
	}
	
	.close_icon{
		width: 44rpx;
		position: absolute;
		bottom: 96rpx;
		left: 47%;
	}
	
	// 删除评价
	.dialogBG {
		width: 100%;
		height: 100%;
	}
	
	/* 21天结束复习弹窗样式 */
	.reviewCard_box {
		width: 670rpx;
		/* height: 560rpx; */
		position: relative;
	}
	
	.reviewCard_box image {
		width: 100%;
		height: 100%;
	}
	
	.reviewCard {
		width: 100%;
		height: 100%;
		background: #FFFFFF;
		border-radius: 24upx;
		padding: 30upx 30upx 60upx 30upx;
		box-sizing: border-box;
		position: relative;
	}
	
	.cartoom_image {
		width: 420rpx;
		position: absolute;
		top: -250rpx;
		left: 145rpx;
		z-index: -1;
	}
	
	.review_close {
		position: absolute;
		top: 20rpx;
		right: 20rpx;
		z-index: 1;
	}
	
	.reviewTitle {
		margin-top: 20rpx;
		width: 100%;
		text-align: center;
		font-size: 34upx;
		display: flex;
		justify-content: center;
	}
	.dialogContent{
		margin: 90upx 0 76upx 0;
		font-size: 32upx;
		text-align: center;
	}
	
	.even{
		border-top: 1px solid #eee;
	}
</style>
