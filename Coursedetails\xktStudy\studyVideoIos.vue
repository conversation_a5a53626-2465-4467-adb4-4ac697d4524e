<template>
  <div>
    <web-view v-if="showWebview" :src="webUrl" ref="webview" />
  </div>
</template>

<script>
  export default {
    data() {
      return {
        webUrl: '',
        showWebview: true
      };
    },
    onLoad(e) {
      let token = uni.getStorageSync('token');

      // let baseUrl = 'http://*************:8081/#/pages/zx/playVideo';
      let baseUrl = 'https://document.dxznjy.com/qyWechat/#/pages/zx/playVideo';
      this.webUrl = baseUrl + '?sendData=' + e.sendData + '&dataInfo=' + e.dataInfo + '&token=' + token + '&isIos=1&noNavbar=1';
    },
    onUnload() {}
  };
</script>

<style lang="scss" scoped></style>
