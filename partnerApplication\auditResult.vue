<template>
  <view class="audit-detail">
    <view :style="{ backgroundColor: showStatusBgc, color: showStatusColor }" v-if="isShow" class="show-result f-28">{{ showStatusText }}</view>
    <view class="partner p-30">
      <view class="top">
        <h3 class="top-title mb-25"><span class="ml-15">学员信息</span></h3>
        <div class="mb-25 c-55">学员名称：{{ info.studentName }}</div>
        <div class="mb-40 c-55">学员手机号：{{ info.mobile }}</div>
      </view>
      <view class="top mb-15">
        <h3 class="top-title mb-25"><span class="ml-15">已绑定信息：</span></h3>
        <div class="mb-25 c-55">合伙人名称：{{ auditInfo.partnerRealName }}</div>
        <div class="mb-25 c-55">合伙人编码：{{ auditInfo.partnerMerchantCode }}</div>
        <div class="mb-40 c-55">合伙人手机号：{{ auditInfo.partnerMobile }}</div>
      </view>
      <view class="top mb-40">
        <h3 class="top-title mb-25"><span class="ml-15">文字描述</span></h3>
        <u--textarea v-model="auditInfo.appealReason" disabled height="300" :autoHeight="false"></u--textarea>
      </view>
      <view class="top mb-15">
        <h3 class="top-title mb-25"><span class="ml-15">图片</span></h3>
        <view class="mb-25 c-55 image-upload flex flex-x-s">
          <view v-for="(item, index) in auditInfo.evidenceImages" :key="index" class="image-item flex" style="width: 160rpx; height: 160rpx" @click="previewImage(item)">
            <u--image :showLoading="true" :src="item" width="80px" height="80px" style="margin-right: 4rpx; display: flex"></u--image>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  export default {
    name: 'auditResult',

    data() {
      return {
        auditInfo: {
          createTime: '',
          studentMobile: '', //学员手机号
          showStatus: 1,
          applyTime: '',
          auditTime: '',
          partnerRealName: '', // 合伙人名称
          partnerMobile: '',
          partnerMerchantCode: '',
          evidenceImages: [] // 申诉图片
        },
        info: {
          studentName: '', // 学员姓名
          studentMobile: '', // 学员手机号
          appealId: '1' // 申诉id
        },
        appealId: '', // 申诉id
        image: [],
        showClaimModal: false, // 是否显示弹窗
        isShow: false, //
        showStatusColor: '', //
        showStatusBgc: '', //
        showStatusText: '' //
      };
    },
    onLoad(options) {
      Object.assign(this.info, JSON.parse(options.info) || {});
      console.log('1🚀 ~ onLoad ~ this.info:', this.info);
      this.appealId = this.info.appealId || ''; // 申诉id
      this.getAppealResult();
    },

    methods: {
      showStatusTextFunc() {
        const showStatus = parseInt(this.auditInfo?.appealStatus);
        const statusMap = {
          0: '审核中',
          1: '申诉成功',
          2: '申诉失败'
        };
        let text = statusMap[showStatus];
        this.showStatusText = text;
        console.log('🚀 ~ showStatusTextFunc ~ this.showStatusText1:', this.showStatusText);
      },
      // 申诉状态
      showStatusColorFunc() {
        const showStatus = parseInt(this.auditInfo?.appealStatus);
        const statusMap = {
          0: '#31B871',
          1: '#31B871',
          2: '#F36E60'
        };
        let color = statusMap[showStatus];
        this.showStatusColor = color;
      },
      showStatusBgcFunc() {
        const showStatus = parseInt(this.auditInfo?.appealStatus);
        const statusMap = {
          0: '#81E2AF',
          1: '#81E2AF',
          2: '#FFF5F1'
        };
        let text = statusMap[showStatus];
        this.showStatusBgc = text;
      },
      // 获取投诉结果信息
      async getAppealResult() {
        const res = await httpUser.get('zx/wap/claim/appeal/result', {
          appealId: this.appealId
        });
        if (res.data.data == null) {
          this.isShow = false;
          return;
        }

        this.isShow = true;
        console.log('111111111111111111🚀 ~ getAppealResult ~ this.isShow:', this.isShow);
        Object.assign(
          this.auditInfo,
          res.data.data || {
            createTime: '',
            studentMobile: '', //学员手机号
            showStatus: 1,
            applyTime: '',
            auditTime: '',
            partnerRealName: '', // 合伙人名称
            partnerMobile: '', // 合伙人手机号
            partnerMerchantCode: '', // 合伙人编码
            evidenceImages: [] //  申诉图片
          }
        );
        this.auditInfo.studentMobile = this.info.mobile || ''; // 学员手机号
        this.showStatusBgcFunc();
        this.showStatusColorFunc();
        this.showStatusTextFunc();
        // this.auditInfo.evidenceImages = [
        //   'https://document.dxznjy.com/dxSelect/7b12b0d2-a30e-4cd0-8370-df106ad75082.png',
        //   'https://document.dxznjy.com/dxSelect/7b12b0d2-a30e-4cd0-8370-df106ad75082.png',
        //   'https://document.dxznjy.com/dxSelect/7b12b0d2-a30e-4cd0-8370-df106ad75082.png',
        //   'https://document.dxznjy.com/dxSelect/7b12b0d2-a30e-4cd0-8370-df106ad75082.png',
        //   'https://document.dxznjy.com/dxSelect/7b12b0d2-a30e-4cd0-8370-df106ad75082.png',
        //   'https://document.dxznjy.com/dxSelect/7b12b0d2-a30e-4cd0-8370-df106ad75082.png',
        //   'https://document.dxznjy.com/dxSelect/7b12b0d2-a30e-4cd0-8370-df106ad75082.png',
        //   'https://document.dxznjy.com/dxSelect/7b12b0d2-a30e-4cd0-8370-df106ad75082.png',
        //   'https://document.dxznjy.com/dxSelect/7b12b0d2-a30e-4cd0-8370-df106ad75082.png'
        // ];
      },

      previewImage(currentUrl) {
        // 如果要预览多张图片，可以传入 urls 数组
        uni.previewImage({
          current: currentUrl, // 当前显示图片的链接
          urls: [currentUrl] // 需要预览的图片链接列表
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
  .audit-detail {
    width: 100%;
    height: 100vh;
  }

  .show-result {
    width: 100%;
    height: 80rpx;
    line-height: 80rpx;
    padding: 0 24rpx;
    box-sizing: border-box;
    text-align: center;
  }

  .partner {
    width: 100vw;
    height: calc(100vh - 80rpx);
    background-color: #fff;
    box-sizing: border-box;

    .top {
      .top-title {
        width: 80%;
        height: 40rpx;
        line-height: 40rpx;
        border-radius: 4rpx;
        border-left: 6rpx solid #339378;
      }

      .image-upload {
        min-height: 200rpx;
      }
    }
  }

  .uni-input {
    height: 64rpx;
    padding-left: 32rpx;
    background-color: #f3f8fc;
  }
  .submit {
    background: #339378;
    height: 88rpx;
    border-radius: 44upx;
    line-height: 88upx;
  }

  .modal-content {
    width: 622rpx;
    height: 344rpx;
    background: url('https://document.dxznjy.com/dxSelect/5f09dfac-9808-4c2c-a474-0cb702a1df5d.png') no-repeat center / 100%;
    padding: 0;
    border-radius: 40rpx;
    box-sizing: border-box;

    .close-icon {
      width: 40rpx;
      height: 40rpx;
      background: url('https://document.dxznjy.com/dxSelect/55db650b-f99e-40dd-8dec-3cd85746c65c.png') no-repeat center / 100%;
      position: absolute;
      top: 24rpx;
      right: 24rpx;
    }

    .smile-box {
      width: 112rpx;
      height: 112rpx;
      display: block;
      margin: 28rpx auto 0;
    }

    .show-title {
      height: 50rpx;
      color: #3f4a2f;
      line-height: 50rpx;
      text-align: center;
    }
    .show-desc {
      width: 490rpx;
      // height: 50rpx;
      margin: 12rpx auto 0;
      color: #a4a59a;
      // line-height: 50rpx;
      text-align: center;
      word-wrap: break-word; /* 旧版属性（兼容性更好） */
      overflow-wrap: break-word; /* 新版标准属性 */
    }

    .submit-btn {
      width: 368rpx;
      height: 84rpx;
      line-height: 84rpx;
      text-align: center;
      background: linear-gradient(90deg, #ef9d4f 0%, #ea643e 100%);
      border-radius: 42rpx;
      margin: 0 auto;
    }
  }

  .submitButton {
    width: 686rpx;
    line-height: 74rpx;
    margin: 0 auto;
    background: #339378;
    border-radius: 38rpx;
    position: fixed;
    bottom: 32rpx;
  }
</style>
