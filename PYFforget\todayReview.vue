<template>
	<view>
		<view class="bg-h">
			<view class="positioning" @click="goback">
				<uni-icons type="left" size="24" color="#000" ></uni-icons>
			</view>
			<view class="word-position t-c col-12">
				<view  class="f-34">今日复习</view>
			</view>
		</view>
		<view class="center-banner">
			<!-- <view :class="topShow ?'center_boxs':'center-box'" :style="{height:useHeight +'rpx'}"> -->
			<view :class="topShow ?'center_boxs':'center-box'">
				<image src="https://document.dxznjy.com/applet/newimages/fuxi@2x_new.png" mode="widthFix" class="niuimg"
					style="width: 434rpx;"></image>
				<view class="f-32 green" :class="topShow ? 'words-tops':'words-top'">
					<view class="remind mb-20 c-00">今日共有{{wordList.sumReviewCount || 0}}节课需复习</view>
					<view v-if="wordList.todayReviewCount !=0" class="mb-20" @click="getWordPreview(1)">
						当日{{wordList.todayReviewCount || 0}}节课程
					</view>
					<view v-if="wordList.pastReviewCount !=0" @click="getWordPreview(2)">
						往期{{wordList.pastReviewCount || 0}}节课程
					</view>
				</view>
				<view class="plr-50" :class="topShow ?'start-tops':'start_top'">
					<button class="start" @click="startReview(0)">开始复习</button>
					<view class="temporarily mt-30" @click="closeReview()">暂不复习</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				wordList: {}, // 单词
				studentCode: '', //学员Code
				topShow: false, //判断向上高度
				useHeight: 0
			};
		},
		onReady() {
			uni.getSystemInfo({
				success: (res) => {
					// 可使用窗口高度，将px转换rpx
					let h = (res.windowHeight * (750 / res.windowWidth));
					if (h < 1500) {
						this.topShow = true
					}

					this.useHeight = h - 190;
				}
			})

		},
		onLoad(options) {
			this.studentCode = options.studentCode;
		},
		onShow() {
			this.getWords();
		},
		methods: {
			// 获得单词数量
			async getWords() {
				uni.showLoading({
					title: '加载中'
				});
				let res = await this.$httpUser.post('znyy/pd/planReview/queryReviewedCount', {
					studentCode: this.studentCode
				});
				uni.hideLoading()
				console.log(res)
				if (res.data.success) {
					this.wordList = res.data.data
				}
			},

			// 单词页
			getWordPreview(type) {
				//当日课程预览
				uni.navigateTo({
					url: '/PYFforget/dayLessonPreview?type=' + type
				})
			},

			closeReview() {
				uni.navigateBack();
			},
			goback(){
				uni.redirectTo({
				  url: "/PYFforget/forgetReview",
				});
			},
			// 开始复习
			startReview(type) {
				if (type == 0) {
					if (this.wordCount == 0) {
						this.$util.alter('暂无可复习单词！');
					} else {
						uni.navigateTo({
							url: '/PYFforget/lessonPreview?studentCode=' + this.studentCode
						});
					}
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.positioning {
		position: fixed;
		top: 100rpx;
		left: 30rpx;
	}

	.center-banner {
		margin: 0 auto;
		// margin-bottom: 50rpx;
		// width: 690rpx;
		// height: 1408rpx;
		min-height: 83vh;
		background: #FFFFFF;
		border-radius: 20rpx;
	}

	.center-box {
		width: 100%;
		text-align: center;
		margin-top: 200rpx;
		margin-bottom: 50rpx;
	}

	.center_boxs {
		width: 100%;
		text-align: center;
		// margin-top: 260rpx;
	}


	.words-tops {
		margin-top: 60rpx;
	}

	.words-top {
		margin-top: 120rpx;
	}

	.start {
		border-radius: 50rpx;
		color: #fff;
		font-size: 30rpx;
		height: 90rpx;
		background: #2E896F;
		line-height: 90rpx;
	}

	.start_top {
		margin-top: 208rpx;
	}

	.start-tops {
		margin-top: 148rpx;
	}

	.temporarily {
		font-size: 30rpx;
		color: #999;
		text-align: center;
	}

	.green {
		color: #2E896F;
	}

	.remind {
		font-weight: bold;
		font-size: 32rpx;
	}

	.niuimg {
		margin-top: 180rpx;
	}
	// 导航栏样式
	.positioning {
		position: fixed;
		top: 110rpx;
		left: 30rpx;
		z-index: 9;
	}
	
	.word-position {
		position: fixed;
		top: 0;
		left: 0;
		background-color: #f3f8fc;
		height: 190rpx;
		padding-top: 110rpx;
		box-sizing: border-box;
	}
</style>