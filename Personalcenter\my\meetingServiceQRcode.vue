<!-- 添加会议负责人二维码 -->
<template>
  <view class="">
    <view class="content">
      <view class="userCard">
        <view class="tip_text text-container" v-if="meetingQrCodeDesc!=''">
          {{meetingQrCodeDesc}}
        </view>
        <view class="tip_text" v-else>
          <view class="">想了解更多关于会议的事宜</view>
          <view class="">
            <span class="tip_red">长按识别二维码</span>
            添加鼎校甄选相关
          </view>
          人员企业微信了解
        </view>
        <image class="ewm" :src="codeImg" :show-menu-by-longpress="true" mode=""></image>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        codeImg: '', // 二维码
        meetingQrCodeDesc: ''
      };
    },
    onLoad(options) {
      this.codeImg = options.qrCodeImage;
      this.meetingQrCodeDesc = options.meetingQrCodeDesc

    },
    onShow() {},
    methods: {}
  };
</script>

<style>
  .content {
    width: 100%;
    height: 100vh;
    text-align: center;
    background-color: rgb(244, 245, 247);
    justify-content: center;
    flex-wrap: wrap;
  }

  .userCard {
    height: 96vh;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    align-items: center;
    margin: 1vh auto;
  }

  .ewm {
    width: 400upx;
    height: 400upx;
    margin-bottom: 460upx;
  }

  .tip_text {
    font-size: 30upx;
    width: 500upx;
    line-height: 45upx;
    color: #333333;
    margin-top: 230upx;
  }

  .text-container {
    width: 15ch;
    /* 限制每行15个字符宽度 */
    text-align: center;
    /* 文字居中 */
    word-break: break-all;
    /* 强制换行（避免长单词/连续字符溢出） */
    margin: 0 auto;
    /* 容器居中（可选） */
    line-height: 1.5;
    /* 行高（可选） */
  }

  .tip_red {
    color: #ea6031;
  }

  .trialStyle {
    line-height: 50upx;
    margin: 100upx auto 0 auto;
    width: 500upx;
    height: 120upx;
    color: #666666;
  }
</style>