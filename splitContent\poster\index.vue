<template>
  <view class="plr-30 pb-30">
    <!-- 非会员 -->
    <!-- <view class="t-c f-30 marginTop100">
            <image :src="imgHost+'alading/correcting/non_member.png'" class="wh160" mode="widthFix"></image>
            <view class="w100 flex-col mt-30" v-for="(item,index) in mealLists" :key="index">
                <view style="width: 240rpx;color: #BDBDBD;">请开通会员后查看 立即去
                    <text class="colour" @tap.stop="skintap('pages/index/mealDetail?id='+item.mealId)">开通会员</text>
                </view>
            </view>
            <u-empty mode="history" icon="https://cdn.uviewui.com/uview/empty/list.png" width="300" marginTop="300" text="请开通会员后查看" textSize="30"/>
        </view> -->
    <!-- 会员未认证 -->
    <!-- <view v-else-if="vipStatus&&!authStatus">
			<u-empty mode="history" icon="https://cdn.uviewui.com/uview/empty/list.png" text="请实名认证后查看">
			</u-empty>
		</view> -->
    <!-- 非会员认证 -->
    <!-- <view v-else-if="!vipStatus&&authStatus">
			<u-empty mode="history" icon="https://cdn.uviewui.com/uview/empty/list.png" text="请开通会员后查看">
			</u-empty>
		</view> -->
    <!-- 会员 -->
    <view class="bg-ff radius-15">
      <view class="plr-20 ptb-30 mb-10">
        <view class="sharetop mb-40">
          <view class="flex-dir-row flex-y-c">
            <text class="f-30 bold">分享好友</text>
          </view>
        </view>
        <view class="bg-f4 plr-30 ptb-40 radius-15" v-if="slogan_text">
          <view class="flex-box f-30 c-33">{{ slogan_text }}</view>
          <view class="flex mt-30 f-32">
            <view class="flex-dir-row flex-y-c" v-if="slogan_text" @tap="slogan()">
              <image :src="imgHost + 'alading/correcting/icon_hyh.png'" class="icon_hyh" mode="widthFix"></image>
              <text class="orange ml-10">换一换</text>
            </view>
            <view class="copy t-c" @tap="copy">复制</view>
          </view>
        </view>
      </view>
      <canvas canvas-id="poster" class="poster_canvas"></canvas>
      <view>
        <swiper class="swiper" :autoplay="false" interval="8000" duration="1000" previous-margin="75rpx" next-margin="75rpx" :circular="circular" @change="swiperBindchange">
          <block v-for="(item, index) in bbimg" :key="index">
            <swiper-item>
              <view class="swiper_item">
                <image :src="item" class="slide-image" :class="currentSwiperIndex == index ? 'zoom-in' : 'zoom-out'" mode="widthFix" lazy-load="true" />
              </view>
            </swiper-item>
          </block>
        </swiper>
      </view>
      <view class="flex-dir-row flex-x-c mt-10 pb-30">
        <button class="savebtn f-30 c-ff t-c" @tap="save">保存图片</button>
      </view>

      <!-- #ifdef APP-PLUS -->
      <view class="flex-dir-row flex-x-c mt-10 pb-30">
        <button class="sharebtn f-30 c-ff t-c" @tap="share">分享微信好友</button>
      </view>
      <view class="flex-dir-row flex-x-c mt-10 pb-30">
        <button class="sharebtn f-30 c-ff t-c" @tap="shareBig">分享微信朋友圈</button>
      </view>
      <!-- #endif -->
    </view>
  </view>
</template>

<script>
  import Util from '@/util/util.js';
  const { $http, $navigationTo, $showMsg } = require('@/util/methods.js');
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  var ctx = null;
  var _this;
  export default {
    data() {
      return {
        type: 1, //1课程  2学习超人（会员） 3俱乐部   9会议
        courseId: '', //课程id
        circular: true,
        // vipStatus: false,
        // authStatus: false,
        bbimg: [],
        previousMargin: '30px', //前边距，可用于露出前一项的一小部分，接受 px 和 rpx 值
        nextMargin: '30px', //后边距，可用于露出后一项的一小部分，接受 px 和 rpx 值
        currentSwiperIndex: 0,
        banner: [],
        code: null,
        giftListNum: 0,
        posterbg: null,
        shopname: null, //昵称
        headimg: 'https://zixunshi.oss-cn-zhangjiakou.aliyuncs.com/dxShop/69f770a9-0dce-4a66-af40-202ac58fe164头像.png', //头像
        pageShow: false,
        canvasimg: null,
        slogan_text: '',
        userS: {},
        signContractStatus: '',
        userId: '',
        imgHost: getApp().globalData.imgsomeHost,
        mealLists: [],
        isClose: false, //是否关闭当前页
        code_s: '',
        isMeeting: false
      };
    },
    onLoad(options) {
      this.type = options.type;
      this.isMeeting = options.isMeeting == 'true' ? true : false;
      //课程
      if (this.type == 1) {
        this.courseId = options.id;
      } else {
        //学习超人（会员）
        this.courseId = options.id;
      }
    },
    created() {},
    onReady() {
      ctx = uni.createCanvasContext('poster', this);

      //app端只初始加载一次
      // #ifdef APP-PLUS
      if (uni.getStorageSync('token')) {
        this.homeData();
        this.slogan();
        // this.meal()
      } else {
        uni.navigateTo({
          url: '/Personalcenter/login/login'
        });
      }
      // #endif
    },
    onShow() {
      // #ifdef MP-WEIXIN
      if (uni.getStorageSync('token')) {
        this.homeData();
        this.slogan();
        // this.meal()
      } else {
        uni.showModal({
          title: '提示',
          content: '海报信息需要登录才能显示哦，可以使用链接分享哦~',
          showCancel: false
        });
        // uni.navigateTo({
        //     url: '/pagePersonalcenters/login/login'
        // })
      }
      // #endif
    },
    onUnload() {
      uni.hideLoading();
    },
    onHide() {
      uni.hideLoading();
    },
    onShareAppMessage() {
      console.log(uni.getStorageSync('user_id'));
      return {
        title: this.slogan_text,
        path: `/pages/index/index?scene=` + uni.getStorageSync('user_id')
      };
      // 去除只有vip用户才能携带参数跳转
      // if (this.userS.identityType != 0) {
      // 	return {
      // 		title: this.slogan_text,
      // 		path: `/pages/index/index?scene=` + uni.getStorageSync('user_id')
      // 	}
      // } else {
      // 	return {
      // 		title: this.slogan_text,
      // 		path: `/pages/index/index`
      // 		// path: `/pages/index/index?scene=` + uni.getStorageSync('user_id')
      // 	}
      // }
    },
    methods: {
      // 获取非会员
      // async meal() {
      //     let _this = this
      //     const res = await $http({
      //         url: 'zx/meal/mealList',
      //         data: {
      //             indexShow: 0,
      //             cityCode: '',
      //             page: 1,
      //             pageSize: 20
      //         }
      //     })
      //     if (res) {
      //         _this.mealLists = res.data.list
      //     }
      // },
      // 获取文案
      async slogan() {
        let _this = this;
        if (_this.type == 6) {
          const res = await $http({
            url: 'zx/wap/goods/share/text',
            data: {
              // type:_this.type,
              goodsId: _this.courseId
            }
          });
          if (res) {
            if (res.data && res.data.length > 0) {
              _this.slogan_text = res.data[0];
            }
          }
        } else {
          const res = await $http({
            url: 'zx/common/copyDetail',
            data: {
              type: _this.type,
              courseId: _this.type == 1 ? _this.courseId : ''
            }
          });
          if (res) {
            if (res.data) {
              _this.slogan_text = res.data.title;
            }
          }
        }
      },
      skintap(url) {
        $navigationTo(url);
      },
      async homeData() {
        let _this = this;
        _this.giftListNum = 0;
        _this.bbimg = [];
        _this.currentSwiperIndex = 0;
        const res = await $http({
          url: `zx/user/userInfoNew`
        });
        if (res) {
          _this.pageShow = true;
          _this.userS = res.data;
          // if (_this.userS.identityType != 0) { // 会员

          // }
          uni.setStorageSync('user_id', res.data.userId);
          uni.setStorageSync('identityType', res.data.identityType);
          uni.setStorageSync('phone', res.data.mobile);
          // _this.vipStatus = true;
          // _this.authStatus = true;
          _this.list();
          // // 会员且实名认证
          // if (res.data.identityType != 0) {
          // 	_this.vipStatus = true;
          // 	_this.authStatus = true;
          // 	_this.list();
          // }
          // // 非会员认证
          // if (res.data.identityType == 0 ) {
          // 	_this.vipStatus = false;
          // 	// _this.authStatus = true；
          // 	uni.hideShareMenu();
          // }
        }
      },

      async list() {
        let _this = this;
        if (_this.type == 6) {
          const res = await $http({
            url: 'zx/user/share/qrcode',
            data: {
              id: _this.courseId,
              type: _this.type
            }
          });
          if (res) {
            console.log(res);
            console.log('----------------------------------');
            if (res.data.bgImageUrl) {
              _this.banner = res.data.bgImageUrl;
            } else {
              $showMsg('暂无分享海报');
            }

            // $showMsg('暂无分享文案');
            _this.code = res.data.qrCode;
            if (res.data.headPortrait) {
              _this.headimg = res.data.headPortrait;
            } else {
              _this.headimg = 'https://document.dxznjy.com/dxSelect/home_avaUrl.png';
            }
            if (!res.data.userName) {
              _this.shopname = '我是昵称';
            } else {
              _this.shopname = res.data.userName.length > 8 ? res.data.userName.substring(0, 8) + '...' : res.data.userName;
            }
            _this.canvasall();
          }
        } else {
          const res = await $http({
            url: 'zx/user/getShareQrCode',
            data: {
              courseId: _this.courseId,
              type: _this.isMeeting ? 9 : _this.type
            }
          });
          if (res) {
            if (res.data.bgImageUrl && res.data.bgImageUrl.length > 0) {
              _this.banner = res.data.bgImageUrl;
            } else {
              $showMsg('暂无分享海报');
              return;
            }
            _this.code = res.data.qrCode;
            if (res.data.headPortrait) {
              _this.headimg = res.data.headPortrait;
            } else {
              _this.headimg = 'https://document.dxznjy.com/dxSelect/home_avaUrl.png';
            }
            if (!res.data.userName) {
              _this.shopname = '我是昵称';
            } else {
              _this.shopname = res.data.userName.length > 8 ? res.data.userName.substring(0, 8) + '...' : res.data.userName;
            }
            _this.canvasall();
          }
        }
      },
      canvasall() {
        let _this = this;
        _this.drawCanvas(_this.banner[_this.currentSwiperIndex]);
        // _this.drawCanvas(_this.banner[0])
      },
      getDetail() {
        let _this = this;
        if (_this.giftListNum == _this.banner.length) {
          uni.hideLoading();
          return;
        }
        _this.canvasimg = null;
        ctx.clearRect(0, 0, 750, 1334);
        _this.drawCanvas(_this.banner[_this.giftListNum]);
      },
      drawCanvas(bb_imgs) {
        console.log(bb_imgs);
        let _this = this;
        if (this.isClose) return;
        uni.showLoading({
          title: '生成中'
        });

        uni.getImageInfo({
          src: bb_imgs,
          success(res) {
            console.log(res);
            console.log('--------------------------------------');
            _this.posterbg = res.path;
            uni.getImageInfo({
              src: _this.headimg || '',
              success(res) {
                console.log('getImageInfo', res);
                _this.head_s = res.path;
                uni.getImageInfo({
                  src: _this.code,
                  success(res) {
                    console.log(res.path);
                    console.log('-------------------------------------------');
                    _this.code_s = res.path;
                    console.log(_this.code_s);
                    ctx.beginPath();
                    ctx.setFillStyle('#fff');
                    ctx.fillRect(0, 0, 375, 667);
                    ctx.restore();
                    ctx.beginPath();
                    ctx.drawImage(_this.posterbg, 0, 0, 750, 1334);
                    ctx.restore();
                    // 	// 用户头像
                    // ctx.beginPath()
                    // ctx.arc(70, 770, 30, 0, 2 * Math.PI);
                    // ctx.clip();
                    ctx.drawImage(_this.head_s, 40, 40, 80, 80);
                    // ctx.drawImage(_this.head_s, 40, 1214, 80, 80);
                    ctx.restore();
                    // 	// 用户昵称
                    const text_size = 68;
                    ctx.setFontSize(uni.upx2px(text_size));
                    ctx.setFillStyle('#ffffff');
                    ctx.fillText(_this.shopname, 140, 90);

                    ctx.drawImage(_this.code_s, 550, 1170, 140, 140);
                    ctx.restore();
                    // debugger
                    ctx.fillStyle = '#10131c';
                    ctx.draw(true, () => {
                      // canvas画布转成图片并返回图片地址
                      // setTimeout(() => {
                      uni.canvasToTempFilePath(
                        {
                          canvasId: 'poster',
                          width: 750 * 2,
                          height: 1334 * 2,
                          success: (result) => {
                            _this.canvasimg = result.tempFilePath;
                            // console.log(_this.canvasimg);
                            let arr = _this.bbimg;
                            arr[_this.currentSwiperIndex] = _this.canvasimg;
                            _this.bbimg = arr;
                            // _this.bbimg = arr.concat(_this.canvasimg);
                            if (_this.bbimg.length != _this.banner.length) {
                              let img = ''; // 添加空数据方便滑动
                              _this.bbimg.push(img);
                              // console.log(_this.bbimg);
                              // _this.currentSwiperIndex++;
                            }

                            uni.hideLoading();
                            return;
                            // setTimeout(() => {
                            //     _this.giftListNum++;
                            //     _this.getDetail();
                            // }, 1000)
                          },
                          fail: (err) => {
                            uni.hideLoading();
                            console.log('海报制作失败！', err);
                            reject();
                          }
                        },
                        _this
                      );
                      // }, 800)
                    });
                  },
                  fail: function (err) {
                    console.log(err);
                  }
                });
              },
              fail: function (err) {
                console.log(err, 'getImageInfo');
              }
            });
          },
          fail: function (err) {
            uni.hideLoading();
            console.log(err);
            console.log('yyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyyy');
          }
        });
      },
      swiperBindchange(e) {
        console.log('走到这了');
        let _this = this;
        _this.currentSwiperIndex = e.detail.current;
        if (_this.bbimg.length - _this.banner.length <= 0) {
          for (let i = 0; i < _this.bbimg.length; i++) {
            if (_this.bbimg[i] == '' && _this.bbimg.length <= this.banner.length) {
              _this.canvasall();
            }
          }
        }
      },

      // 保存海报
      save() {
        let _this = this;
        let img = _this.bbimg[_this.currentSwiperIndex];
        console.log(img);
        console.log('..................................................');
        uni.getImageInfo({
          src: img,
          success: function (res1) {
            uni.saveImageToPhotosAlbum({
              filePath: res1.path,
              success: function (res) {
                uni.showToast({
                  title: '保存成功',
                  icon: 'success'
                });
              },
              fail: function (res) {
                if (
                  res.errMsg === 'saveImageToPhotosAlbum:fail:auth denied' ||
                  res.errMsg === 'saveImageToPhotosAlbum:fail auth deny' ||
                  res.errMsg === 'saveImageToPhotosAlbum:fail authorize no response'
                ) {
                  // 这边微信做过调整，必须要在按钮中触发，因此需要在弹框回调中进行调用
                  uni.showModal({
                    title: '提示',
                    content: '需要您授权保存相册',
                    showCancel: false,
                    success: (modalSuccess) => {
                      uni.openSetting({
                        success(settingdata) {
                          if (settingdata.authSetting['scope.writePhotosAlbum']) {
                            uni.showModal({
                              title: '提示',
                              content: '获取权限成功,再次点击图片即可保存',
                              showCancel: false
                            });
                          } else {
                            uni.showModal({
                              title: '提示',
                              content: '获取权限失败，将无法保存到相册哦~',
                              showCancel: false
                            });
                          }
                        },
                        fail(failData) {
                          console.log('failData', failData);
                        },
                        complete(finishData) {
                          console.log('finishData', finishData);
                        }
                      });
                    }
                  });
                }
              }
            });
          }
        });
      },
      // 复制文案
      copy() {
        uni.setClipboardData({
          data: this.slogan_text,
          success: function (res) {
            uni.getClipboardData({
              success: function (res) {
                uni.showToast({
                  title: '已复制到剪贴板'
                });
              }
            });
          }
        });
      },

      //分享好友
      share() {
        if (this.bbimg.length == 0) {
          uni.showToast({
            icon: 'none',
            title: '海报生成中'
          });
          return;
        }
        uni.share({
          provider: 'weixin',
          scene: 'WXSceneSession',
          type: 2,
          imageUrl: this.bbimg[this.currentSwiperIndex],
          success: function (res) {
            console.log('success:' + JSON.stringify(res));
            uni.showToast({
              icon: 'none',
              title: '分享成功'
            });
          },
          fail: function (err) {
            console.log('fail:' + JSON.stringify(err));
            uni.showToast({
              icon: 'none',
              title: '分享失败'
            });
          }
        });
      },
      //分享朋友圈
      shareBig() {
        if (this.bbimg.length == 0) {
          uni.showToast({
            icon: 'none',
            title: '海报生成中'
          });
          return;
        }
        uni.share({
          provider: 'weixin',
          scene: 'WXSceneTimeline',
          type: 2,
          imageUrl: this.bbimg[this.currentSwiperIndex],
          success: function (res) {
            console.log('success:' + JSON.stringify(res));
            uni.showToast({
              icon: 'none',
              title: '分享成功'
            });
          },
          fail: function (err) {
            console.log('fail:' + JSON.stringify(err));
            uni.showToast({
              icon: 'none',
              title: '分享失败'
            });
          }
        });
      }
    },

    onUnload() {
      console.log('guanbi');

      console.log(this.isClose);
      this.isClose = true;
      uni.hideLoading();
    }
  };
</script>

<style>
  .zoom-out {
    transform: scale(0.95);
    transition: all 0.7s ease-out 0s;
  }

  .savebtn {
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
    width: 100%;
    height: 90upx;
    line-height: 90upx;
    border-radius: 45upx;
    margin: 0 50rpx;
  }

  .sharebtn {
    background-image: linear-gradient(to bottom, #42b983, #1aa034);
    width: 100%;
    height: 90upx;
    line-height: 90upx;
    border-radius: 45upx;
    margin: 0 50rpx;
  }

  .swiper_item {
    width: 600rpx;
  }

  .zoom-in {
    transform: scale(1);
    transition: all 0.7s ease-in 0s;
  }

  .swiper {
    height: 990upx;
  }

  .slide-image {
    width: 90%;
    height: 100%;
    border-radius: 16rpx;
  }

  .shareline {
    width: 10upx;
    height: 32upx;
    background-color: #006658;
  }

  .icon_hyh {
    width: 38upx;
  }

  .copy {
    width: 150upx;
    height: 60upx;
    border-radius: 30upx;
    color: #fff;
    line-height: 60upx;
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
  }

  .poster_canvas {
    width: 750px;
    height: 1334px;
    position: fixed;
    top: 2000upx;
  }

  /* .poster_canvas{
		position: fixed;
		top: 2000upx;
	} */

  /* .savebtn{
		margin: 0 60rpx;
	} */
  .wh160 {
    width: 160rpx;
  }

  .colour {
    color: #ea6031;
  }

  .marginTop100 {
    margin-top: 100rpx;
  }

  .orange {
    color: #f17427;
  }
</style>
