<template>
  <view class="pt-140">
    <u-empty mode="car" textSize="24" width="200" height="200" :icon="imgHost + 'alading/correcting/no_data.png'" :text="emptyText || '暂无记录'"></u-empty>
  </view>
</template>

<script>
  export default {
    props: ['emptyText'],
    data() {
      return {
        imgHost: getApp().globalData.imgsomeHost
      };
    },
    methods: {}
  };
</script>

<style></style>
