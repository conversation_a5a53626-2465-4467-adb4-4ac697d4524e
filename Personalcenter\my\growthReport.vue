<template>
  <view class="growth_report_css plr-32">
    <view>
      <view class="content_top_css">
        <text class="c-ff content_text_css">鼎小校</text>
        <text class="f-24">再学习{{ studyInfo.nextCourseLevelStudyDurationMinutes }}分钟达到{{ studyInfo.nextCourseStudyLevel }}</text>
      </view>
      <view class="flex-self-s mt-16">
        <view class="button_left_css">
          <text class="f-24 c-33">累计学习时长</text>
          <text class="f-48 bold green_css mlr-10">{{ studyInfo.studyDurationMinutesTotal }}</text>
          <text class="f-24 c-8896">分钟</text>
        </view>
        <view class="button_right_css ml-15 c-ff f-24">
          {{ studyInfo.courseStudyLevel }}
        </view>
      </view>
      <view class="recent_study mt-10">
        <view class="study_time_recent flexbox">
          <view class="study_time_item">
            <view class="bold">
              <text class="f-40 green_css">{{ studyInfo.studyDurationMinutesTodaystr.split(':')[0] || 0 }}</text>
              <text class="f-32">时</text>
              <text class="f-40 green_css">{{ studyInfo.studyDurationMinutesTodaystr.split(':')[1] || 0 }}</text>
              <text class="f-32">分</text>
            </view>
            <view class="f-24 c-55 mt-8">今日学习</view>
          </view>
          <view class="study_time_item">
            <view class="bold">
              <text class="f-40 green_css">{{ studyInfo.studyDurationMinutesLastWeekstr.split(':')[0] || 0 }}</text>
              <text class="f-32">时</text>
              <text class="f-40 green_css">{{ studyInfo.studyDurationMinutesLastWeekstr.split(':')[1] || 0 }}</text>
              <text class="f-32">分</text>
            </view>
            <view class="f-24 c-55 mt-8">本周学习</view>
          </view>
        </view>
        <view class="flexbox echarts_css">
          <view class="f-28">
            <text class="time_css">4月</text>
            <text>最近7天学习时长</text>
          </view>
          <view class="f-24">单位：分钟</view>
        </view>
        <uCharts
          v-if="arr.length > 0"
          :marginLeft="20"
          class="uCharts_css"
          :scroll="arr[0].opts.enableScroll"
          :show="true"
          :canvasId="arr[0].id"
          :chartType="arr[0].chartType"
          :extraType="arr[0].extraType"
          :cWidth="cWidth"
          :cHeight="cHeight"
          :opts="arr[0].opts"
          :ref="arr[0].id"
        />
      </view>
      <view class="Learning_situation">
        <view class="content_situation">
          <text class="f-32 c-33 text_css bold">学习情况总数据</text>
          <view class="situation_list">
            <view class="flexbox situation_list_item" v-for="(item, index) in studyList" :key="index">
              <view>{{ item.text }}</view>
              <view>
                <text>{{ item.number }}</text>
                <text>{{ item.unit }}</text>
                <text v-if="item.time">{{ item.time }}</text>
                <text v-if="item.timeUnit">{{ item.timeUnit }}</text>
              </view>
            </view>
          </view>
          <!-- https://document.dxznjy.com/course/1794471d0b2349bd8b9bb7f75486af2f.png -->
          <view class="learning_growth">
            <view class="learning_growth_content">
              <view class="scroll_y_css">
                <view v-for="i in 12" class="growth_content_item">
                  <view class="growth_content_box">
                    <view class="c-6a ml-25 f-24 mt-16 time_css">2024-4-18 12:26</view>
                    <view class="c-33 bold f-28 mt-8 ml-25 lh-40">鼎学能</view>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view class="data_ranking">
            <view class="data_ranking_content">
              <view class="table_ranking_css">
                <view class="table_header c-ff f-28 lh-40 pt-20 pb-12 table_item flexbox">
                  <view>数据名称</view>
                  <view>当前排名</view>
                </view>
                <view class="table_td table_item flexbox">
                  <view>本周学习（0时25分）</view>
                  <view>
                    第
                    <text class="green_css">100+</text>
                    名
                  </view>
                </view>
                <view class="table_td table_item flexbox">
                  <view>本周学习（0时25分）</view>
                  <view>
                    第
                    <text class="green_css">100+</text>
                    名
                  </view>
                </view>
                <view class="table_td table_item flexbox">
                  <view>本周学习（0时25分）</view>
                  <view>
                    第
                    <text class="green_css">100+</text>
                    名
                  </view>
                </view>
              </view>
            </view>
          </view>
          <button class="button_css_content f-28 c-ff" hover-class="none" open-type="share">分享我的学习报告</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  const { $http } = require('@/util/methods.js');
  import uCharts from '@/components/u-charts/u-charts.vue';
  let chart = null;
  export default {
    components: {
      uCharts
    },
    data() {
      return {
        ec: {
          lazyLoad: true
        },
        times: [],
        studyList: [
          { text: '累计时长', number: 0, unit: '时', time: 25, timeUnit: '分' },
          { text: '累计天数', number: 0, unit: '天' },
          { text: '连续天数', number: 0, unit: '天' },
          { text: '完成计划数', number: 0, unit: '次' },
          { text: '参与课程数', number: 0, unit: '节' },
          { text: '收到的鼎', number: 0, unit: '次' }
        ],
        arr: [],
        growthInfo: {},
        studyInfo: {},
        data: {
          LineA: {
            categories: this.times,
            series: [
              {
                name: '学习',
                data: [0, 0, 0, 0, 0, 0, 0, 0],
                color: '#d2efe1'
              }
            ],
            yAxis: {
              min: 0
            }
          }
        },
        cWidth: '',
        cHeight: ''
      };
    },
    onShareAppMessage() {
      return {
        title: '学习成长报告',
        // imageUrl: this.shopdetail.shareCardUrl?this.shopdetail.shareCardUrl:this.shopdetail.bannerImages[0], //分享封面
        //如果有参数的情况可以写path
        path: '/Personalcenter/my/growthReport?courseId=' + this.growthInfo.courseId + '&studentCode=' + this.growthInfo.studentCode
      };
    },
    onLoad(e) {
      this.getTime(e);
    },
    methods: {
      ChangeHourMinutestr(str) {
        if (str !== '0' && str !== '' && str !== null) {
          return Math.floor(str / 60).toString() + ':' + (str % 60).toString();
        } else {
          return '';
        }
      },
      async getDetailList(e) {
        this.growthInfo = e;
        let parm = {
          userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '',
          studentCode: e.studentCode
        };
        if (e.courseId) {
          parm.courseId = e.courseId;
        }
        let userId = uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : '';
        const res = await $http({
          url: 'zx/wap/course/user/record/growth/track/query',
          data: parm
        });
        console.log(res);
        if (res) {
          this.studyInfo = res.data[0];
          this.studyInfo.courseGrowthRecordDailyVoLastSevenDayList.forEach((item) => {
            let date = new Date(item.studyDate);
            let day = ('0' + date.getDate()).slice(-2) + '日';
            let index = this.times.findIndex((item) => item == day);
            this.data.LineA.series[0].data[index] = item.studyDurationMinutes;
          });
          this.studyInfo.studyDurationMinutesTodaystr = this.ChangeHourMinutestr(this.studyInfo.studyDurationMinutesToday);
          this.studyInfo.studyDurationMinutesLastWeekstr = this.ChangeHourMinutestr(this.studyInfo.studyDurationMinutesLastWeek);
          this.studyInfo.studyDurationMinutesTotalstr = this.ChangeHourMinutestr(this.studyInfo.studyDurationMinutesTotal);
          this.cWidth = uni.upx2px(630);
          this.cHeight = uni.upx2px(400);
          this.studyList[1].number = this.studyInfo.studyDaysTotal;
          this.studyList[2].number = this.studyInfo.consecutiveDays;
          this.studyList[0].number = this.studyInfo.studyDurationMinutesTotalstr.split(':')[0];
          this.studyList[0].time = this.studyInfo.studyDurationMinutesTotalstr.split(':')[1];
          this.studyList[4].number = this.studyInfo.attendedCourseTotal;
          this.getServerData();
        }
      },
      getServerData() {
        var LineA = {
          enableScroll: false,
          unit: ''
        };
        LineA.categories = this.times; //result.data.LineA.categories
        LineA.series = this.data.LineA.series; //result.data.LineA.series
        LineA.yAxis = this.data.LineA.yAxis;
        LineA.legend = false;
        var serverData = [
          {
            group: '关卡报告',
            title: '分组正确率',
            opts: LineA,
            chartType: 'line',
            extraType: 'curve',
            id: 'c01'
          }
        ];
        this.arr = serverData;
      },
      getTime(e) {
        // 创建一个空数组来存储日期
        let dates = [];
        // 获取当前日期
        let now = new Date();
        // 循环获取最近 7 天的日期
        for (let i = 0; i < 7; i++) {
          // 获取当前日期的时间戳
          let timestamp = now.getTime();
          // 计算 i 天前的时间戳
          let dayTimestamp = 24 * 60 * 60 * 1000; // 一天的毫秒数
          let iDayAgoTimestamp = timestamp - i * dayTimestamp;
          // 转换为日期对象
          let date = new Date(iDayAgoTimestamp);
          // 格式化日期为 "yyyy-MM-dd" 的字符串并存入数组
          let year = date.getFullYear();
          let month = ('0' + (date.getMonth() + 1)).slice(-2);
          let day = ('0' + date.getDate()).slice(-2);
          dates.push(day + '日');
        }
        this.times = dates.reverse();
        this.getDetailList(e);
      }
    }
  };
</script>

<style lang="scss" scoped>
  .growth_report_css {
    background: url(https://document.dxznjy.com/course/1f8b0c94f7e84734b433a53f82eec2f4.png) no-repeat #f2f9fa;
    width: 686rpx;
    background-size: 100%;
    .content_top_css {
      line-height: 44rpx;
      padding-top: 70rpx;
      color: #026549;
      .content_text_css {
        color: #fff;
        font-size: 32rpx;
        margin-right: 16rpx;
      }
    }
    .button_right_css {
      width: 132rpx;
      height: 56rpx;
      line-height: 56rpx;
      text-align: center;
      border-radius: 28rpx;
      background-color: #339378;
    }
    .button_left_css {
      min-width: 296rpx;
      height: 56rpx;
      line-height: 50rpx;
      border-radius: 28rpx;
      background-color: #f6fff8;
      text-align: center;
      padding: 0 24rpx;
      text {
        vertical-align: middle;
      }
    }
    .green_css {
      color: #339378;
      line-height: 40rpx;
      display: inline-block;
    }
    .recent_study {
      background: url('https://document.dxznjy.com/course/a900e033253949acab6edaec03e521b1.png') no-repeat;
      background-size: 100%;
      width: 696rpx;
      padding-top: 164rpx;
      margin-left: -10rpx;
      .study_time_recent {
        background-color: #f6fcff;
        border-radius: 16rpx;
        padding: 24rpx 0;
        width: 636rpx;
        margin-left: 38rpx;
        .study_time_item {
          width: 50%;
          text-align: center;
        }
      }
      .time_css {
        color: #339378;
        display: inline-block;
        margin-right: 8rpx;
      }
      .echarts_css {
        padding-left: 38rpx;
        padding-right: 22rpx;
        margin-top: 38rpx;
        margin-bottom: 32rpx;
      }
      .uni-ec-canvas {
        width: 100%;
        height: 448rpx;
        display: block;
      }
      padding-bottom: 30rpx;
    }
    .Learning_situation {
      padding-top: 124rpx;
      background: url('https://document.dxznjy.com/course/5ff4c4e46d714b4db650e2d9b1e87424.png') no-repeat #fff;
      background-size: 101%;
      position: relative;
      .text_css {
        position: absolute;
        top: 28rpx;
        left: 29rpx;
      }
      .content_situation {
        width: 686rpx;
        background: url('https://document.dxznjy.com/course/bf7a2773d9b0446a8b076f94f94dfc96.png') no-repeat #afd5cc;
        background-size: 100%;
        padding-bottom: 40rpx;
        .situation_list {
          width: 596rpx;
          margin-left: 46rpx;
          padding-top: 5rpx;
          padding-bottom: 44rpx;
          .situation_list_item {
            border-bottom: 2rpx solid #e3f5ec;
            padding-top: 34rpx;
            padding-bottom: 42rpx;
            padding-left: 2rpx;
          }
        }
        .learning_growth {
          background: url('https://document.dxznjy.com/course/1794471d0b2349bd8b9bb7f75486af2f.png') no-repeat;
          background-size: 97%;
          padding-top: 122rpx;
          .learning_growth_content {
            width: 660rpx;
            height: 968rpx;
            background: url('https://document.dxznjy.com/course/29658e9114d4461bb405f6883e7ccaac.png') no-repeat;
            background-size: 100%;
            margin: 0 auto;
            padding-top: 40rpx;
            padding-bottom: 60rpx;
            .scroll_y_css {
              overflow-y: scroll;
              height: 965rpx;
            }
            .growth_content_item {
              margin-left: 44rpx;
              padding-bottom: 48rpx;
              position: relative;
              .growth_content_box {
                position: relative;
                margin-left: 40rpx;
                border-radius: 0rpx 24rpx 24rpx 24rpx;
                border: 1rpx solid #dee6e8;
                background-color: #fff;
                width: 532rpx;
                height: 114rpx;
                .time_css {
                  line-height: 34rpx;
                }
              }
              .growth_content_box::after {
                content: ' ';
                position: absolute;
                display: inline-block;
                border: 4rpx solid #06c385;
                border-radius: 50%;
                width: 24rpx;
                height: 24rpx;
                top: 0;
                left: -58rpx;
              }
            }
            .growth_content_item::after {
              content: ' ';
              position: absolute;
              display: inline-block;
              border-left: 1px dashed #06c385;
              height: 142rpx;
              left: 0;
              top: 30rpx;
            }
          }
        }
        .data_ranking {
          background: url('https://document.dxznjy.com/course/f7d6b289210a4cb3b009899771d4f084.png') no-repeat;
          background-size: 97%;
          padding-top: 122rpx;
          .data_ranking_content {
            width: 660rpx;
            height: 490rpx;
            margin: 0 auto;
            background: url('https://document.dxznjy.com/course/765d36fa5d9d4befb17ae73ebe0ace3b.png') no-repeat;
            background-size: 100%;
            .table_ranking_css {
              width: 592rpx;
              margin: 0 auto;
              border-radius: 16rpx;
              overflow: hidden;
              .table_item {
                padding-left: 32rpx;
                padding-right: 40rpx;
                font-size: 28rpx;
              }
              .table_header {
                margin-top: 56rpx;
                background: linear-gradient(270deg, #06c385 0%, #339378 100%);
              }
              .table_td {
                color: #555;
                padding: 24rpx 0;
                border-bottom: 2rpx solid #e3f5ec;
                .green_css {
                  color: #27a07c;
                }
              }
            }
          }
        }
        .button_css_content {
          width: 660rpx;
          height: 74rpx;
          line-height: 74rpx;
          background-color: #339378;
          border-radius: 38rpx;
          margin: 30rpx auto;
          text-align: center;
        }
      }
    }
  }
  /* https://document.dxznjy.com/course/1f8b0c94f7e84734b433a53f82eec2f4.png */
</style>
