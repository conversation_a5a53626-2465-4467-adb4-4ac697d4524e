<template>
  <view class="bgColor" :style="{ height: screenHeight + 'rpx' }">
    <view class="box-bg flex-x">
      <uni-icons type="left" size="22" color="#fff" @click="back"></uni-icons>
      <view style="margin-left: 35%" class="c-ff f-34">学时详情</view>
    </view>
    <view class="plr-30 students-style">
      <view class="plr-30 flex-s flex-y-c bg-ff radius-15">
        <view>学员姓名：</view>
        <view class="flex-a-c">
          <picker @change="bindPickerChange" disabled :value="selectShow ? studentIndex : studentIndex - 1" :range="studentArray" range-key="realName">
            <view class="mr-20">{{ studentNameStudy }}</view>
          </picker>
        </view>
      </view>
      <view class="plr-30 flex-s flex-y-c bg-ff radius-15 mt-30">
        <view>选择门店：</view>
        <view class="flex-a-c">
          <picker disabled @change="storeChange" :value="storeShow ? storeIndex : storeIndex - 1" :range="storeList" range-key="merchantName">
            <view class="mr-20">{{ merchantName }}</view>
          </picker>
        </view>
      </view>
    </view>

    <view class="back-color plr-30" style="width: 92%" :class="!show ? 'marginT265' : ''">
      <view class="recharge_detail f-30" style="width: 92%" :class="!show ? 'top460' : ''">
        <view class="bg-ff radius-15 plr-30 ptb-40">
          <view class="f-32 bold">{{ studentNameStudy }}学时详情</view>
          <view class="flex-s flex-x-b mt-40">
            <view class="purchased">
              <view class="pl-30 pt-20">
                <view class="c-2e8 f-32">{{ lessonDetails.totalCourseHours || 0 }}节</view>
                <view class="c-f9c f-26 mt-10">已购学时</view>
              </view>
            </view>
            <view class="residue">
              <view class="pl-30 pt-20">
                <view class="c-f1a f-32">{{ lessonDetails.haveCourseHours || 0 }}节</view>
                <view class="c-f1a f-26 mt-10">剩余学时</view>
              </view>
            </view>
          </view>

          <view class="flex-s flex-x-b mt-30">
            <view class="pay-deliver">
              <view class="pl-30 pt-20">
                <view class="c-f76 f-32">{{ lessonDetails.haveDeliverHours || 0 }}节</view>
                <view class="c-fba f-26 mt-10">剩余集中交付学时</view>
              </view>
            </view>
            <view class="residue-deliver">
              <view class="pl-30 pt-20">
                <view class="c-fe9 f-32">{{ lessonDetails.deliverSelfCourse || 0 }}节</view>
                <view class="c-ff4 f-26 mt-10">剩余自行交付学时</view>
              </view>
            </view>
          </view>
          <view class="flex-s flex-x-b mt-30">
            <view class="residue-kcb">
              <view class="pl-30 pt-20">
                <view class="c-fd3 f-32">{{ packagelength || 0 }}节</view>
                <view class="c-fd3 f-26 mt-10">课程包交付学时</view>
              </view>
            </view>
          </view>
        </view>

        <view class="bg-ff radius-15 plr-30 ptb-40 mt-20" v-if="coursePackage != null && coursePackage != ''">
          <view class="flex-s">
            <view class="f-32 bold">课程包详情</view>
            <view class="f-28 c-66">不限制学时</view>
          </view>

          <view v-for="(item, index) in coursePackage" :key="index">
            <view class="c-f1d f-30 mt-20 bold" v-if="item.courseNames.length > 0">{{ item.skuName }}</view>
            <view class="c-99 course" v-for="(val, index) in item.courseNames" :key="index">{{ val }}</view>
          </view>
        </view>

        <view class="bg-ff radius-15 plr-30 ptb-40 mt-20" v-if="orderList.data != undefined && orderList.data.length > 0">
          <view class="f-32 bold">充值明细</view>

          <view v-for="(item, index) in orderList.data" :key="index">
            <view class="c-33 f-30 mt-40 border-t pt-30" v-if="item.type == 1">
              <view class="flex-s">
                <view>学时：{{ item.courseLength || 0 }}节</view>
                <view v-if="item.deliverLength > 0 && item.deliverLength != ''">交付学时：{{ item.deliverLength }}节</view>
                <view class="c-fe5"></view>
              </view>
              <view class="c-99 mt-20">{{ item.createTime }}</view>
            </view>

            <view class="c-33 f-30 mt-40 border-t pt-30" v-if="item.type == 2">
              <view class="flex-s">
                <view>课程包：{{ item.packageName }}</view>
                <view class="c-fe5"></view>
              </view>
              <view class="c-99 mt-20">{{ item.createTime }}</view>
            </view>
          </view>
        </view>

        <view v-if="(orderList.data != undefined && orderList.data.length == 0) || !rechargeShow" class="bg-ff radius-15 mt-30" :style="{ height: useHeight + 'rpx' }">
          <view class="f-32 bold ml-30 mt-30">充值明细</view>
          <view class="t-c flex-col" :style="{ height: useHeight - 60 + 'rpx' }">
            <image :src="imgHost + 'alading/correcting/no_data.png'" class="mb-20 img_s"></image>
            <view style="color: #bdbdbd">暂无充值明细</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  const { $navigationTo, $http } = require('@/util/methods.js');
  const { httpUser } = require('@/util/luch-request/indexUser.js');
  export default {
    data() {
      return {
        index: 0,
        screenHeight: 0,
        imgHost: getApp().globalData.imgsomeHost,
        studentValue: 0, // 学员
        selectShow: true, // 是否展示请选择字符
        studentArray: [],
        studentIndex: 0,
        studentCode: '',
        studentNameStudy: '',
        useHeight: 0,
        lessonDetails: {}, // 学时详情
        coursePackage: '',
        orderList: {},
        // merchantCode:'',
        packagelength: 0, // 学时包交付学时
        page: 1,
        no_more: false,
        rechargeShow: false, //

        widHeight: 0,
        studentName: '',

        storeList: [], // 门店
        storeIndex: 0,
        storeShow: true,
        show: false,
        merchantCode: '',
        merchantName: ''
      };
    },
    onLoad(e) {
      this.studentNameStudy = e.studentName;
      this.studentCode = e.studentCode;
      this.merchantCode = e.merchantCode;
      this.getHeight();
      this.getJudgePurchasedEnglishCourse();
      this.getSchoolList();
      // this.getStudentCode();
    },

    onReady() {
      uni.getSystemInfo({
        success: (res) => {
          // 可使用窗口高度，将px转换rpx
          let h = res.windowHeight * (750 / res.windowWidth);
          this.useHeight = h - 830;
          this.widHeight = h - 200;
        }
      });
    },
    onReachBottom() {
      if (this.studentArray.length > 0) {
        if (this.page >= this.orderList.totalPage) {
          this.no_more = true;
          return false;
        }
        if (this.studentCode != '') {
          this.getOrderPage(true, ++this.page);
        }
      }
    },
    methods: {
      back() {
        uni.navigateBack();
      },

      getHeight() {
        // 获取系统信息
        const systemInfo = uni.getSystemInfoSync();
        // 获取屏幕高度
        console.log(systemInfo);
        this.screenHeight = systemInfo.screenHeight - 200;
      },

      change(e) {
        console.log('e:', e);
      },

      bindPickerChange(e) {
        this.studentIndex = Number(e.detail.value) + 1;
        this.selectShow = false;
        this.studentCode = this.studentArray[this.studentIndex - 1].studentCode;
        this.studentName = this.studentArray[this.studentIndex - 1].realName;
        this.getJudgePurchasedEnglishCourse();
      },
      async getJudgePurchasedEnglishCourse() {
        let res = await $http({
          url: 'zx/wap/course/student/course/judgePurchasedEnglishCourse',
          data: {
            studentCode: this.studentCode
          }
        });
        if (res) {
          this.coursePackage = [];
          this.storeList = [];
          this.lessonDetails = {};
          this.orderList = [];
          if (res.data) {
            this.getSchoolList();
          }
        }
      },
      // /
      storeChange(e) {
        console.log('picker发送选择改变，携带值为', e.detail.value);
        this.show = true;
        this.storeIndex = Number(e.detail.value) + 1;
        this.storeShow = false;
        this.merchantCode = this.storeList[this.storeIndex - 1].merchantCode;
        console.log(this.storeList);
        this.merchantName = this.storeList[this.storeIndex - 1].merchantName;
        this.getCourse();
        this.getCoursePackage();
        this.getOrderPage();
      },

      clearChange() {
        this.studentIndex = 0;
        this.selectShow = true;
        this.studentCode = '';
        this.studentName = '';
        this.coursePackage = [];
        this.storeList = [];
        this.lessonDetails = {};
        this.orderList = [];
      },
      clearStore() {
        this.storeIndex = 0;
        this.storeShow = true;
        this.merchantCode = '';
        this.merchantName = '';
      },
      // 查询studentCode
      async getStudentCode() {
        let result = await this.$httpUser.get('znyy/review/query/my/student');
        if (result != undefined && result != '') {
          if (result.data.data != null) {
            if (result.data.data.length > 0) {
              if (result.data.data.length == 1) {
                this.studentArray = [];
                this.studentArray = this.studentArray.concat(result.data.data);
                this.studentCode = this.studentArray[0].studentCode;
                this.studentName = this.studentArray[0].realName;
              } else {
                var that = this;
                that.studentArray = that.studentArray.concat(result.data.data);
              }
            }
          }
        }
      },

      // 学员门店列表 111111111
      async getSchoolList() {
        let res = await $http({
          url: 'znyy/school/recharge/getStudentSchoolList',
          data: {
            studentCode: this.studentCode
          }
        });
        if (res) {
          this.storeList = res.data;
          const store = this.storeList.find((item) => item.merchantCode == this.merchantCode);
          this.merchantName = store.merchantName;
          console.log(this.merchantName, 'this.merchantName');
          this.getCourse();
          this.getCoursePackage();
          this.getOrderPage();
        }
      },

      async getCourse() {
        let res = await $http({
          url: 'znyy/school/recharge/student/course',
          data: {
            studentCode: this.studentCode,
            merchantCode: this.merchantCode
          }
        });
        if (res) {
          this.lessonDetails = res.data;
          this.packagelength = (this.lessonDetails.haveDeliverNum - this.lessonDetails.haveDeliverHours).toFixed(2);
        }
      },

      // 课程包详细课程
      async getCoursePackage() {
        let res = await $http({
          url: 'znyy/course/student/getStudentCoursePackage',
          data: {
            studentCode: this.studentCode,
            merchantCode: this.merchantCode
          }
        });
        if (res) {
          this.coursePackage = res.data;
        }
      },

      // 充值明细
      async getOrderPage(isPage, page) {
        let that = this;
        let res = await $http({
          url: 'znyy/school/recharge/getStudentRechargeOrderPage',
          data: {
            pageNum: that.page,
            pageSize: 10,
            studentCode: that.studentCode,
            merchantCode: that.merchantCode
          }
        });
        that.rechargeShow = true;
        if (res) {
          if (isPage) {
            let old = that.orderList.data;
            that.orderList.data = [...old, ...res.data.data];
          } else {
            that.orderList = res.data;
          }
          console.log(that.orderList);
        }
      }
    }
  };
</script>
<style>
  page {
    background-color: #f3f8fc;
  }
</style>
<style lang="scss" scoped>
  .bgColor {
    background: linear-gradient(to bottom, #2f8c70, #fff);
  }

  .box-bg {
    width: 100%;
    z-index: 9;
    position: fixed;
    padding-top: 80rpx;
    background: linear-gradient(to bottom, #55a18a, #59a38d);
  }

  /deep/ .uni-nav-bar-text {
    font-size: 34rpx !important;
  }

  /deep/ .uni-navbar--border {
    border-bottom-color: #2f8c70 !important;
  }

  .class_details {
    height: 60rpx;
    width: 160rpx;
    color: #f2f2f2;
    font-size: 30rpx;
    margin-top: 100rpx;
    line-height: 60rpx;
    text-align: center;
    border-radius: 30rpx;
    background-color: rgba(255, 255, 255, 0.2);
  }

  .back-color {
    height: 80%;
    margin-top: 165rpx;
    background-color: #f2f8fd;
    border-top-left-radius: 15rpx;
    border-top-right-radius: 15rpx;
  }

  .recharge_detail {
    position: absolute;
    top: 300rpx;
    padding-bottom: 130rpx;
  }

  .top460 {
    top: 460rpx;
  }

  .marginT265 {
    margin-top: 265rpx;
  }

  .add {
    width: 100%;
    height: 80rpx;
    color: #2e896f;
    font-size: 30rpx;
    line-height: 80rpx;
    text-align: center;
    border-radius: 45rpx;
    border: 1px solid #2e896f;
  }

  .search-icon {
    width: 38rpx;
    height: 38rpx;
  }

  .flex-x {
    display: flex;
    align-items: center;
  }

  .border-b {
    border-bottom: 1px solid #efefef;
  }

  /deep/.uni-select {
    border: none !important;
  }

  /deep/ .uni-select__input-text {
    font-size: 30rpx;
    margin-right: 40rpx !important;
  }

  .amount {
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 9;
    width: 92%;
    height: 110rpx;
    padding: 0 30rpx;
    font-size: 28rpx;
    box-shadow: 0rpx 2rpx 30rpx #e3e3e3;
  }

  .pay {
    color: #fff;
    width: 200rpx;
    height: 70rpx;
    font-size: 32rpx;
    text-align: center;
    line-height: 70rpx;
    border-radius: 35rpx;
    background-image: linear-gradient(to bottom, #88cfba, #1d755c);
  }

  .course {
    color: #666;
    height: 60rpx;
    width: 500rpx;
    font-size: 26rpx;
    margin-top: 35rpx;
    line-height: 60rpx;
    padding-left: 24rpx;
    border-radius: 6rpx;
    background-color: #f5f5f5;
  }

  .students-style {
    padding-top: 170rpx;
    height: 100rpx;
    line-height: 100rpx;
  }

  .purchased {
    width: 300rpx;
    height: 135rpx;
    background-image: url('https://document.dxznjy.com/applet/4.1.5/class-ygks.png');
    background-repeat: no-repeat;
    background-size: 300rpx 135rpx;
  }

  .residue {
    width: 300rpx;
    height: 135rpx;
    background-image: url('https://document.dxznjy.com/applet/4.1.5/class-syks.png');
    background-repeat: no-repeat;
    background-size: 300rpx 135rpx;
  }

  .pay-deliver {
    width: 300rpx;
    height: 140rpx;
    background-image: url('https://document.dxznjy.com/applet/4.1.5/class-ygjfks.png');
    background-repeat: no-repeat;
    background-size: 300rpx 135rpx;
  }

  .residue-deliver {
    width: 300rpx;
    height: 135rpx;
    background-image: url('https://document.dxznjy.com/applet/4.1.5/class-syjfks.png');
    background-repeat: no-repeat;
    background-size: 300rpx 135rpx;
  }

  .residue-kcb {
    width: 300rpx;
    height: 135rpx;
    background-image: url('https://document.dxznjy.com/applet/4.1.5/class-kcb.png');
    background-repeat: no-repeat;
    background-size: 300rpx 135rpx;
  }

  .border-t {
    border-top: 1px solid #efefef;
  }

  .img_s {
    width: 160rpx;
    height: 160rpx;
  }
</style>
