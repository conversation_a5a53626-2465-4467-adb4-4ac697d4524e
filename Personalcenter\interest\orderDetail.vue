<template>
  <view class="purchasedetail">
    <uni-section title="订单详情"></uni-section>
    <view class="intresetingList">
      <text class="inListTitle">趣味复习</text>
      <view class="inList">
        <text>识词冲关</text>
        <text>听音辨意</text>
        <text>连连看</text>
        <text>拼拼乐</text>
      </view>
    </view>

    <view class="interprice">
      <uni-list>
        <uni-list-item :title="'商品总额:'" :rightText="'￥' + orderInfo.price" :show-arrow="false" />
      </uni-list>
    </view>

    <view>
      <uni-section title="使用时间"></uni-section>
      <uni-list>
        <uni-list-item :title="orderInfo.validTime" :show-arrow="false" />
      </uni-list>
    </view>
    <view>
      <uni-section title="学员编号"></uni-section>
      <uni-list v-if="!isShare">
        <uni-list-item :title="studentInfo.studentCode" :show-arrow="false" />
      </uni-list>

      <view v-if="isShare">
        <uni-list v-if="studentArr.length == 0">
          <uni-list-item title="请选择学员编号" :show-arrow="false" @click="bindPickerChange($event, false)" />
        </uni-list>
        <view class="uni-list-cell-db chooselistTitle" v-if="studentArr.length != 0">
          <picker
            @change="bindPickerChange($event, true)"
            :value="index"
            :style="studentInfo.studentCode == null ? 'color:#999999' : ''"
            :range="studentArr"
            :range-key="'studentListInfo'"
          >
            {{ studentInfo.studentCode == null ? '请选择学员编号' : studentInfo.studentCode }}
          </picker>
          <uni-icons class="chooseRight" type="arrowright" size="20" color="#999999"></uni-icons>
        </view>
      </view>
    </view>

    <uni-section title="支付方式"></uni-section>
    <uni-list>
      <uni-list-item
        :show-extra-icon="false"
        :show-arrow="false"
        :show-leftradio="true"
        :radiocolor="radiocolor"
        title=""
        :radioLeftdata="radioLeftdata"
        @radioLeftChange="radioLeftChange"
      />
    </uni-list>

    <uni-bottom :payprice="'￥' + orderInfo.price" @confirmpay="confirmpay"></uni-bottom>
  </view>
</template>

<script>
  import uniSection from '../components/uni-section/uni-section.vue';
  import uniList from '../components/uni-list/uni-list.vue';
  import uniListItem from '../components/uni-list-item/uni-list-item.vue';
  import uniBottom from '../components/uni-bottom/uni-bottom.vue';
  export default {
    components: {
      uniSection,
      uniList,
      uniListItem,
      uniBottom
    },
    data() {
      return {
        payInfo1: {},
        flag1: false,
        radioLeftdata: [
          {
            value: 'vx',
            check: true,
            name: '微信支付',
            iconurl: 'https://document.dxznjy.com/dxSelect/image/weiimg.png'
          }
        ],
        orderInfo: {},
        studentArr: [], //获取学员列表
        studentInfo: {}, //选中学员的学员信息
        payInfo: {}, //支付信息
        merchantCode: '', // 校区
        isShare: false, //是否是分享进入
        buyInterestClassData: '', //分享进入的data
        radiocolor: ''
      };
    },
    onLoad(option) {
      this.studentInfo.studentCode = option.studentCode;
      this.onloadFun(option);
    },
    onShow() {
      if (this.flag1) {
        uni.$tlpayResult(this.sucees, this.fail, this.payInfo1.orderId);
      }
      // this.getUserAddress()
      this.onloadFun('');
      this.merchantCode = uni.getStorageSync('merchantCode');
    },
    methods: {
      sucees() {
        this.flag1 = false;
        this.paysuccess(this.payInfo1.bizOrderNo);
      },
      fail() {
        this.flag1 = false;
      },
      fails() {
        uni.showToast({
          title: '支付失败',
          icon: 'none',
          duration: 2000
        });
        this.flag1 = false;
      },
      onloadFun(option) {
        this.buyInterestClassData = uni.getStorageSync('buyInterestClassData');
        if (decodeURIComponent(option.q) != 'undefined') {
          //分享进入
          this.isShare = true;
          if (this.buyInterestClassData == '') {
            // 存储是为了没有登陆重新进入
            uni.setStorageSync('buyInterestClassData', decodeURIComponent(option.q).split('qrcode/')[1]);
          }
        }
        this.fetchData();
        this.getStudent();
      },

      // 页面初始化数据
      async fetchData() {
        await this.$httpUser.get('v2/mall/funReview/orderChart').then((res) => {
          if (res.data.success) {
            this.orderInfo = res.data.data;
          } else {
            uni.showToast({
              title: res.data.message,
              icon: 'none'
            });
          }
        });
      },

      // 获取学员列表
      async getStudent() {
        let that = this;
        var loginMobile = uni.getStorageSync('LoginMobile');
        that.StudentCodeKey = 'review_' + loginMobile;
        let result = await that.$httpUser.get('znyy/review/query/my/student');
        if (result != undefined && result != '') {
          if (result.data.data != null) {
            that.studentArr = result.data.data;
            that.studentArr.forEach((item) => {
              that.$set(item, 'studentListInfo', item.studentCode + '(' + item.realName + ')');
            });
          }
        }
      },
      bindPickerChange(e, isChange) {
        if (this.studentArr.length == 0) {
          this.$util.alter('当前您还没有学员，请添加学员后开通正式课程再次扫码进入');
          return;
        }
        if (isChange) {
          this.studentInfo = this.studentArr[e.target.value];
        }
      },

      // 选择支付方式
      radioLeftChange(e) {
        // console.log(e)
      },
      // 支付失败提示
      failetip() {
        uni.showModal({
          content: '支付失败，请重试！',
          showCancel: false
        });
        return;
      },
      // 确认支付
      confirmpay() {
        var that = this;
        if (that.studentInfo.studentCode == null) {
          that.$util.alter('请先选择学员编号');
          return;
        }

        let urlRequest;
        if (that.isShare) {
          urlRequest = `?studentCode=${that.studentInfo.studentCode}&isCodeBuy=1&memberCode=${that.buyInterestClassData.split(',')[1]}&merchantCode=${that.merchantCode}`;
        } else {
          urlRequest = `?studentCode=${that.studentInfo.studentCode}&isCodeBuy=0&merchantCode=${that.merchantCode}`;
        }

        // 先下单
        that.$httpUser.post('v2/mall/buy/funReview' + urlRequest).then((res) => {
          if (res.data.success) {
            // 获取订单信息
            that.payInfo = res.data.data;
            that.submitPay();
          } else {
            that.failetip();
          }
        });
      },
      // 提交支付
      submitPay() {
        var that = this;
        uni.showLoading({
          title: '支付中，请稍后'
        });
        // that.$httpUser.config.header['dx-source'] = 'MALL##WX##MINIAPP';
        console.log(that.$httpUser.config.header['dx-source']);
        that.$httpUser.post('mps/line/collect/order/unified/collect', that.payInfo).then((res) => {
          // if(res.data.success) {
          // that.$httpUser.config.header['dx-source'] = 'ALADING##WX##MINIAPP';
          that.paySuccessFun(res);
          // }
        });
      },

      // 支付成功执行
      paySuccessFun(res) {
        var that = this;
        uni.hideLoading();
        if (!res.data.success) {
          that.failetip();
        }
        if (res.data.success) {
          if (res.data.data.openAllinPayMini) {
            this.flag1 = true;
            res = res.data.data;
            uni.$payTlian(res);
            this.payInfo1 = res;
          } else {
            let paymentData = res.data.data.payInfo;
            let payId = res.data.data.bizOrderNo;
            //#ifndef MP-WEIXIN
            uni.requestPayment({
              provider: 'wxpay',
              orderInfo: paymentData,
              success: (res) => {
                // console.log('success:' + JSON.stringify(res));
              },
              fail: (err) => {
                // console.log('fail:' + JSON.stringify(err));
                that.$util.alter('支付失败');
              },
              complete: () => {
                console.log('payment结束');
              }
            });
            //#endif
            //#ifdef MP-WEIXIN
            var payData = paymentData;
            uni.requestPayment({
              provider: 'wxpay',
              timeStamp: payData.timeStamp,
              nonceStr: payData.nonceStr,
              package: payData.packageX,
              signType: payData.signType,
              paySign: payData.paySign,
              success: function (res) {
                that.paysuccess(payId);
              },
              fail: function (err) {
                console.log('fail:' + JSON.stringify(err));
              }
            });
            //#endif
          }
        } else {
          that.$util.alter(res.data.message);
        }
      },

      // 支付成功回调
      paysuccess(orderid) {
        this.clearBuyInterestData();
        // 返回首页
        uni.switchTab({
          url: '/pages/index/index'
        });
      },

      // 清除分享缓存
      clearBuyInterestData() {
        if (this.isShare && this.buyInterestClassData != '') {
          uni.removeStorageSync('buyInterestClassData');
        }
      }
    },
    onBackPress() {
      this.clearBuyInterestData();
    },

    onUnload() {
      // 页面关闭后清空缓存
      console.log(8989898);
      this.clearBuyInterestData();
    }
  };
</script>

<style lang="scss" scoped>
  /* 头条小程序组件内不能引入字体 */

  /* #ifdef MP-TOUTIAO */
  @font-face {
    font-family: uniicons;
    font-weight: normal;
    font-style: normal;
    src: url('~@/static/uni.ttf') format('truetype');
  }

  /* #endif */

  /* #ifndef APP-NVUE */
  page {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    background-color: #efeff4;
    min-height: 100%;
    height: auto;
  }

  view {
    font-size: 14px;
    line-height: inherit;
  }

  .listIcon {
    display: block;
    position: absolute;
    left: 20upx;
  }

  /* #endif */
  .chooselistTitle {
    padding: 0rpx 30rpx;
    width: 100%;
    background: #ffffff;
    height: 90rpx;
    box-sizing: border-box;
    line-height: 90rpx;
    position: relative;
  }

  .chooseRight {
    position: absolute;
    right: 30rpx;
    top: 0rpx;
  }

  .intresetingList {
    padding: 30rpx;
    background: #ffffff;
    border-bottom: 1rpx solid #dddddd;
  }

  .intresetingList .inListTitle {
    font-size: 30rpx;
    line-height: 40rpx;
  }

  .intresetingList .inList text {
    display: inline-block;
    width: 130rpx;
    text-align: center;
    color: #999999;
    border: 2rpx solid #999999;
    border-radius: 24rpx;
    margin: 20rpx 16rpx 10rpx 0;
  }

  /deep/.list-between > view {
    align-items: center;
  }
</style>
