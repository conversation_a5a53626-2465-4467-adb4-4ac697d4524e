<template>
	<view class="plr-30 pb-30 positionRelative">
		<!-- 顶部文字和返回箭头 -->
		<!-- <view class="top-title">
			<view class="icon_img" @click="goBack">
				<uni-icons type="left" size="20" color="#000"></uni-icons>
			</view>
			<view class="page_title">付款</view>
		</view> -->


		<!-- 中间白色卡片 -->
		<view class="center-card positionRelative" :style="{height:useHeight+'rpx'}">
			<view class="logobox pb-55">
				<image src="https://document.dxznjy.com/dxSelect/image/logo.png" class="logo-img"></image>
				<view>充值金额</view>
				<view class="price" v-if="!IsCode">¥{{paylist.price || 0}}</view>
				<view class="price" v-if="IsCode">¥{{paylist.amount/100 || 0}}</view>
			</view>
			<view class="studentinfo" v-if="!IsCode">
				<view class="studentinfo-text">
					<text>学员姓名：</text>
					<text>{{paylist.studentName || ''}}</text>
				</view>
				<view class="studentinfo-text">
					<text>充值类型：</text>
					<text>{{paylist.type==1?'充值软件使用费':'充值课程包'}}</text>
				</view>
				<view class="studentinfo-text" v-if="paylist.type==2">
					<text>充值课程包：</text>
					<text>{{paylist.packageName || 0}}</text>
				</view>
				<view class="studentinfo-text" v-if="paylist.type==1">
					<text>充值学时：</text>
					<text>{{paylist.courseNum || 0}}节</text>
				</view>
				<view class="studentinfo-text" v-if="paylist.deliverNum!=0 && schoolType==3">
					<text>充值交付学时：</text>
					<text>{{paylist.deliverNum || 0}}节</text>
				</view>

				<view class="studentinfo-text" v-if="paylist.selfDeliverNum !=0">
					<text>自行交付学时：</text>
					<text>{{paylist.selfDeliverNum || 0}}节</text>
				</view>

				<view class="studentinfo-text">
					<text>充值说明：</text>
					<text class="lh-50">{{paylist.description || ''}}</text>
				</view>
			</view>
			<view class="studentinfo t-l" style="padding-left: 0;" v-if="IsCode">
				<view class="studentinfo-textcode">
					<text>充值名称：</text>
					<text>{{paylist.remark || ''}}</text>
				</view>
			</view>
			<!-- 收款方 -->
			<view class="Payee">
				<view class="Payee-left">
					收款方：
				</view>
				<view class="Payee-right" v-if="!IsCode">
					{{paylist.merchantName}}
				</view>
				<view class="Payee-right" v-if="IsCode">
					鼎校甄选平台
				</view>
			</view>

			<view class="buttonFn" @click="getOrderCreate">
				<text>支付</text>
			</view>

		</view>


	</view>
</template>

<script>
	import {
		$tlpayResult
	} from '@/util/methods/common.js'
	const {
		$navigationTo,
		$http
	} = require("@/util/methods.js")
	const {
		httpUser
	} = require('@/util/luch-request/indexUser.js')
	export default {
		data() {
			return {
				payInfo: {},
				flag1: false,
				useHeight: 0,
				type: 2, // 1未付款 2已付款 
				imgHost: getApp().globalData.imgsomeHost,
				level: false,
				screenHeight: '', // 屏幕高度
				screenWidth: '', // 屏幕高度
				tempFilePath: '', // 
				show: false, // 是否保存付款记录按钮
				payImg: "",
				saveImg: "",
				orderId: "", // 订单id
				sharePay: false,
				paylist: {}, // 支付信息
				orderlist: {}, // 订单支付信息
				flag: false, // 防止重复点击
				schoolType: '',
				IsCode: false,
				codeToken: ''
			};
		},
		onReady() {
			uni.getSystemInfo({
				success: (res) => {
					// 可使用窗口高度，将px转换rpx
					let h = (res.windowHeight * (750 / res.windowWidth));
					if (this.screenHeight <= 688 && this.screenHeight > 619) {
						this.useHeight = h + 80;
					} else if (this.screenHeight <= 619) {
						this.useHeight = h + 80;
					} else if (this.screenHeight > 688 && this.screenHeight <= 736) {
						this.level = true;
						this.useHeight = h - 120;
					} else {
						this.level = true;
						this.useHeight = h - 100;
					}
				}
			})
		},
		onShow() {
			if (this.flag1) {
				uni.$tlpayResult(this.sucees, this.fail, this.payInfo.orderId);
			}
			this.getHeight();
			uni.setStorageSync('wxpay', true);
			uni.removeStorage({
				key: 'payToken',
				success: (res) => {
					console.log('删除payToken');
				}
			})
			let token = uni.getStorageSync('token');
			if (!token) {
				uni.navigateTo({
					url: '/Personalcenter/login/login'
				})
			} else {
				// this.saveCanvas();
				if (this.IsCode) {
					this.getCodeOrderInfo()
				} else {
					this.getOrderInfo();
				}

			}
		},
		onLoad(optinos) {
			if (optinos.scene) {
				let data = optinos.scene;
				const q = decodeURIComponent(data)
				const params = {};
				if (q) {
					q.split('&').forEach(param => {
						const [key, value] = param.split('=');
						params[decodeURIComponent(key)] = decodeURIComponent(value);
					});
				}
				this.IsCode = params['a'];
				this.orderId = params['orderId'];
				return
			}
			this.orderId = optinos.orderId;
			// 链接分享

			if (optinos.type == 4) {
				this.type = 4;
				this.sharePay = true;
			}
			if (optinos.id != undefined) {
				this.orderId = optinos.id;
			} else {
				// 二维码分享
				this.sharePay = true;
				// let d = uni.getLaunchOptionsSync();
				// let list = d.query;
				let data = optinos.q;
				const q = decodeURIComponent(data) // 获取到二维码原始链接内容
				this.orderId = q.split('=')[1];
			}

		},
		methods: {
			sucees() {
				this.flag1 = false
				if(this.IsCode){
					uni.switchTab({
						url:'/pages/home/<USER>/index'
					})
				}else {
					uni.redirectTo({
						url: `/Recharge/paySuccess?orderId=${this.orderId}&schoolType=${this.schoolType}&type=${this.type}&studentCode=${this.paylist.studentCode}&deliverLen=${this.paylist.deliverNum}`
					})
				}
				
			},
			fail() {
				
				if(this.IsCode){
					uni.showModal({
					  title: "温馨提示",
					  content:  '请上级重新生成二维码,再进行支付',
					  showCancel: false,
					});
				}
				
				this.flag1 = false
			},
			fails() {
				uni.showToast({
					title: '支付失败',
					icon: 'none',
					duration: 2000
				})
				this.flag1 = false
			},
			goBack() {
				if (!this.sharePay) {
					uni.navigateBack();
				} else {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				}
			},

			getHeight() {
				// 获取系统信息
				const systemInfo = uni.getSystemInfoSync();
				// 获取屏幕高度
				this.screenHeight = systemInfo.windowHeight;
				this.screenWidth = systemInfo.windowWidth - 60;
				// 打印屏幕高度
				console.log(this.screenHeight);
			},

			// 获取订单信息
			async getCodeOrderInfo() {
				let res = await $http({
					url: 'zxAdminCourse/web/piMerchant/getLineOrder',
					method: 'get',
					data: {
						sourceOrderId: this.orderId,
					}
				})
				if (res) {
					this.paylist = res.data.data;
				}
			},
			async getOrderInfo() {
				let res = await $http({
					url: 'znyy/school/recharge/getRechargeOrderInfo',
					method: 'get',
					data: {
						orderId: this.orderId,
					}
				})
				if (res) {
					this.paylist = res.data;
				}
			},

			// 获取订单支付信息
			async getOrderCreate() {

				// uni.showLoading();
				if (this.flag) {
					return;
				}
				this.flag = true;
				if (this.IsCode) {
					let res = await $http({
						url: 'zxAdminCourse/web/piMerchant/getLineOrder',
						method: 'get',
						data: {
							sourceOrderId: this.orderId,
						}
					})
					// uni.hideLoading();
					console.log(res)
					if (res) {
						this.orderlist = res.data.data;
						this.payCode(this.orderlist);
						this.flag = false;
					} else {
						this.flag = false;
					}
				} else {
					let res = await $http({
						url: 'znyy/school/recharge/getRechargeLineOrderCreateDto',
						method: 'get',
						data: {
							orderId: this.orderId,
						}
					})
					// uni.hideLoading();
					console.log(res)
					if (res) {
						this.orderlist = res.data;
						uni.setStorageSync('bussinessTag', this.orderlist.businessTag);
						this.payBtn(this.orderlist);
						this.flag = false;
					} else {
						this.flag = false;
					}
				}
			},

			async payBtn(data) {
				data.collectUserCodes=JSON.parse(data.userCollectDetail);
				let _this = this;
				uni.showLoading();
				let resdata = await httpUser.post('mps/line/collect/order/unified/multi/collect/check', data)
				let res = resdata.data.data;
				_this.disabled = false;

				uni.hideLoading();
				if (res) {
					if (res.openAllinPayMini) {
						this.flag1 = true
						this.payInfo = res;
						uni.$payTlian(res)
					} else {
						uni.requestPayment({
							provider: 'wxpay',
							timeStamp: res.payInfo.timeStamp,
							nonceStr: res.payInfo.nonceStr,
							package: res.payInfo.packageX,
							signType: res.payInfo.signType,
							paySign: res.payInfo.paySign,
							success: function(res) {
								console.log('支付成功');
								this.flag = false;
								uni.redirectTo({
									url: `/Recharge/paySuccess?orderId=${_this.orderId}&schoolType=${_this.schoolType}&type=${_this.type}&studentCode=${_this.paylist.studentCode}&deliverLen=${_this.paylist.deliverNum}`
								})
							},
							fail: function(err) {
								uni.showToast({
									title: '支付失败'
								})

								this.flag = false;
							}
						})
					}

				}
			},
			async payCode(data) {
				let _this = this;
				uni.showLoading();
				let resdata = await $http({
					url: 'mps/line/collect/order/unified/collect',
					method: 'post',
					data: data
				})
				let res = resdata.data;
				_this.disabled = false;
				uni.hideLoading();
				if (res) {
					if (res.openAllinPayMini) {
						this.flag1 = true
						this.payInfo = res;
						uni.$payTlian(res)
					} else {
						uni.requestPayment({
							provider: 'wxpay',
							timeStamp: res.payInfo.timeStamp,
							nonceStr: res.payInfo.nonceStr,
							package: res.payInfo.packageX,
							signType: res.payInfo.signType,
							paySign: res.payInfo.paySign,
							success: function(res) {
								console.log('支付成功');
								this.flag = false;
								uni.redirectTo({
									url: `/Recharge/paySuccess?orderId=${_this.orderId}&schoolType=${_this.schoolType}&type=${_this.type}&studentCode=${_this.paylist.studentCode}&deliverLen=${_this.paylist.deliverNum}`
								})
							},
							fail: function(err) {
								uni.showToast({
									title: '支付失败'
								})

								this.flag = false;
							}
						})
					}

				}
			},
			// 生成海报
			longPress() { //长按保存
				uni.showLoading();
				if (this.path !== "") {
					uni.hideLoading();
					// #ifdef MP-WEIXIN
					uni.authorize({
						scope: "scope.writePhotosAlbum",
						success: () => {
							uni.saveImageToPhotosAlbum({
								filePath: this.path,
								success: () => {
									uni.showModal({
										title: "保存成功",
										content: "图片已成功保存到相册，快去分享到您的圈子吧",
										showCancel: false
									})
								}
							});
						},
						fail() {
							uni.showModal({
								title: '保存失败',
								content: "您没有授权，无法保存到相册",
								showCancel: false
							})
						}
					})
					// #endif


					// #ifdef APP-PLUS
					this.openAppOption()
					// #endif
					uni.hideLoading();
				} else {
					uni.showModal({
						title: '提示',
						content: "生成海报失败,请重试",
						showCancel: false,
					})
				}
			},

			saveCanvas() {
				let that = this;
				uni.getImageInfo({
					src: 'https://document.dxznjy.com/dxSelect/recharge/pay-success.png',
					success: function(image) {
						console.log(image);
						that.payImg = image.path;
						uni.getImageInfo({
							src: 'https://document.dxznjy.com/dxSelect/image/download.png',
							success: function(res) {
								console.log(res);
								that.saveImg = res.path;
							},
							fail(error) {
								console.log(error)
							}
						});
					},
					fail(err) {
						console.log(err)
					}
				});
			},

			closeSave() {
				this.$refs.popup.close();
			},

			save() {
				this.$refs.popup.open();
				this.capture()
			},

			capture() {
				this.show = true;
				// canvas绘制文本
				const ctx = uni.createCanvasContext('secondCanvas', this)
				// canvas布局

				ctx.drawImage(this.payImg, 150, 40, 50, 50);
				ctx.restore()

				ctx.setFontSize(16)
				ctx.setTextAlign('center')
				ctx.fillText('付款成功', 175, 115)

				// 开启一条新的路径
				ctx.beginPath()
				// 设置开始起点
				ctx.moveTo(10, 150)
				// 设置结束位置
				ctx.lineTo(500, 150)
				// 设置具体的颜色
				ctx.setStrokeStyle("#EFEFEF")
				// 设置线宽
				ctx.lineWidth = 1;
				//设置上颜色——在设置着色之前设置线宽和颜色
				ctx.stroke()
				// 结束路径
				ctx.closePath()

				ctx.setTextAlign('left')
				ctx.setFontSize(14)
				ctx.setFillStyle('#333333')
				ctx.fillText('学员姓名：' + this.paylist.studentName, 20, 200)

				ctx.setFontSize(14)
				ctx.setTextAlign('left')
				ctx.setFillStyle('#333333')
				if (this.paylist.type == 1) {
					ctx.fillText('充值类型：充值软件使用费', 20, 235)
				} else {
					ctx.fillText('充值类型：充值课程包', 20, 235)
				}

				if (this.paylist.type == 1) {
					ctx.setFontSize(14)
					ctx.setTextAlign('left')
					ctx.fillText('充值学时：' + this.paylist.courseNum + '节', 20, 270)

					if (this.paylist.deliverNum != 0 && this.paylist.deliverNum != undefined) {

						if (this.paylist.selfDeliverNum != 0 && this.paylist.selfDeliverNum != undefined) {
							ctx.setFontSize(14)
							ctx.setTextAlign('left')
							ctx.fillText('充值交付学时：' + this.paylist.deliverNum + '节', 20, 305)

							ctx.setFontSize(14)
							ctx.setTextAlign('left')
							ctx.fillText('自行交付学时：' + this.paylist.selfDeliverNum + '节', 20, 340)

							ctx.setFontSize(14)
							ctx.setTextAlign('left')
							ctx.fillText('充值金额：￥' + this.paylist.price, 20, 375)

							ctx.setFontSize(14)
							ctx.setTextAlign('left')
							ctx.setFillStyle('#333333')
							ctx.fillText('支付说明：' + this.paylist.description, 20, 410)
						} else {
							ctx.setFontSize(14)
							ctx.setTextAlign('left')
							ctx.fillText('充值交付学时：' + this.paylist.deliverNum + '节', 20, 305)

							ctx.setFontSize(14)
							ctx.setTextAlign('left')
							ctx.fillText('充值金额：￥' + this.paylist.price, 20, 340)

							ctx.setFontSize(14)
							ctx.setTextAlign('left')
							ctx.setFillStyle('#333333')
							ctx.fillText('支付说明：' + this.paylist.description, 20, 375)
						}

					} else if (this.paylist.selfDeliverNum != 0 && this.paylist.selfDeliverNum != undefined) {
						if (this.paylist.deliverNum != 0) {
							ctx.setFontSize(14)
							ctx.setTextAlign('left')
							ctx.fillText('充值交付学时：' + this.paylist.deliverNum + '节', 20, 305)

							ctx.setFontSize(14)
							ctx.setTextAlign('left')
							ctx.fillText('自行交付学时：' + this.paylist.selfDeliverNum + '节', 20, 340)

							ctx.setFontSize(14)
							ctx.setTextAlign('left')
							ctx.fillText('充值金额：￥' + this.paylist.price, 20, 375)

							ctx.setFontSize(14)
							ctx.setTextAlign('left')
							ctx.setFillStyle('#333333')
							ctx.fillText('支付说明：' + this.paylist.description, 20, 410)
						} else {
							ctx.setFontSize(14)
							ctx.setTextAlign('left')
							ctx.fillText('自行交付学时：' + this.paylist.selfDeliverNum + '节', 20, 305)

							ctx.setFontSize(14)
							ctx.setTextAlign('left')
							ctx.fillText('充值金额：￥' + this.paylist.price, 20, 340)

							ctx.setFontSize(14)
							ctx.setTextAlign('left')
							ctx.setFillStyle('#333333')
							ctx.fillText('支付说明：' + this.paylist.description, 20, 375)
						}

					} else {
						ctx.setFontSize(14)
						ctx.setTextAlign('left')
						ctx.fillText('充值金额：￥' + this.paylist.price, 20, 305)

						ctx.setFontSize(14)
						ctx.setTextAlign('left')
						ctx.setFillStyle('#333333')
						ctx.fillText('支付说明：' + this.paylist.description, 20, 340)
					}
				} else {
					ctx.setFontSize(14)
					ctx.setTextAlign('left')
					ctx.fillText('充值课程包：' + this.paylist.packageName, 45, 270)

					ctx.setFontSize(14)
					ctx.setTextAlign('left')
					ctx.fillText('充值金额：￥' + this.paylist.price, 20, 305)

					ctx.setFontSize(14)
					ctx.setTextAlign('left')
					ctx.setFillStyle('#333333')
					ctx.fillText('支付说明：' + this.paylist.description, 20, 340)
				}

				// 开启一条新的路径
				ctx.beginPath()
				// 设置开始起点
				ctx.moveTo(10, 510)
				// 设置结束位置
				ctx.lineTo(500, 510)
				// 设置具体的颜色
				ctx.setStrokeStyle("#EFEFEF")
				// 设置线宽
				ctx.lineWidth = 1;
				//设置上颜色——在设置着色之前设置线宽和颜色
				ctx.stroke()
				// 结束路径
				ctx.closePath()

				ctx.drawImage(this.saveImg, 20, 540, 20, 20);
				ctx.restore()


				ctx.setFontSize(13)
				ctx.setTextAlign('left')
				ctx.setFillStyle('#666666')
				ctx.fillText('关注鼎校公众号，高效学习倡导者，帮助', 65, 540)

				ctx.setFontSize(13)
				ctx.setTextAlign('left')
				ctx.setFillStyle('#666666')
				ctx.fillText('K12学员优化学习策略，让学习更简单！', 65, 560)
				// canvas画布转为图片 ，有时draw调用不成功，写了个定时器
				ctx.draw(setTimeout(() => {
					uni.canvasToTempFilePath({
						x: 0,
						y: 0,
						width: 325,
						height: 600,
						destWidth: 325,
						destHeight: 600,
						fileType: 'jpg',
						canvasId: 'secondCanvas',
						success: (res) => {
							uni.hideLoading()
							// // 保存当前绘制图片
							this.tempFilePath = res.tempFilePath;
							// this.show = false;
							// this.savePic();
						},
						fail: function(err) {
							console.log(err, '图片生成失败');
						}
					})
				}, 500))
			},

			// 控制绘制文本换行,百度CV的
			drawText: function(ctx, str, leftWidth, initHeight, titleHeight, canvasWidth) {
				var lineWidth = 0;
				var lastSubStrIndex = 0; //每次开始截取的字符串的索引
				for (let i = 0; i < str.length; i++) {
					lineWidth += ctx.measureText(str[i]).width;
					if (lineWidth > canvasWidth) {
						//因为我这个在矩形中的文本进行的换行，用的ctx.strokeText，不行用在矩形中添加文本的用ctx.fillText
						ctx.strokeText(str.substring(lastSubStrIndex, i), leftWidth, initHeight); //绘制截取部分
						initHeight += 11; //11为字体的高度
						lineWidth = 0;
						lastSubStrIndex = i;
						titleHeight += 30;
					}
					if (i == str.length - 1) { //绘制剩余部分
						ctx.strokeText(str.substring(lastSubStrIndex, i + 1), leftWidth, initHeight);
					}
				}
				// 标题border-bottom 线距顶部距离
				titleHeight = titleHeight + 10;
				return titleHeight
			},
			// 保存图片到本地，下面保存到手机百度CV的
			savePic() {
				uni.getSetting({
					//获取用户的当前设置
					success: res => {
						if (res.authSetting['scope.writePhotosAlbum']) {
							//验证用户是否授权可以访问相册
							uni.saveImageToPhotosAlbum({
								filePath: this.tempFilePath,
								success: function(res2) {
									uni.hideLoading();
									uni.showToast({
										title: '保存成功',
										icon: 'none',
										duration: 2000
									});

								},
								fail: function(err) {
									uni.hideLoading();
									uni.showToast({
										title: '保存失败',
										icon: 'none',
										duration: 2000
									});
								},
							});
						} else {
							uni.authorize({
								//如果没有授权，向用户发起请求
								scope: 'scope.writePhotosAlbum',
								success: (res) => {
									console.log(res)
									uni.saveImageToPhotosAlbum({
										filePath: this.tempFilePath,
										success: function(res2) {
											uni.hideLoading();
											uni.showToast({
												title: '保存成功',
												icon: 'none',
												duration: 2000
											});

										},
										fail: function(err) {
											uni.hideLoading();
											uni.showToast({
												title: '保存失败',
												icon: 'none',
												duration: 2000
											});
										},
									});
									// this.saveImageToPhotosAlbum();
								},
								fail: () => {
									uni.showToast({
										title: '请打开保存相册权限，再点击保存相册分享',
										icon: 'none',
										duration: 2000
									});
									setTimeout(() => {
										uni.openSetting({
											//调起客户端小程序设置界面,让用户开启访问相册
											success: res2 => {
												// console.log(res2.authSetting)
											}
										});
									}, 2000);
								}
							});

						}
					}
				});
			},


		}


	}
</script>

<style lang="scss" scoped>
	page {
		padding: 60rpx 30rpx 30rpx 30rpx;

		// 顶部文字和返回箭头
		.top-title {
			position: fixed;
			top: 80rpx;
			margin-top: 80rpx;
			margin-bottom: 25rpx;
			display: flex;
			align-items: center;
			// padding-left: 30rpx;

			.page_title {
				font-size: 34rpx;
				font-family: AlibabaPuHuiTiM;
				color: #000000;
				margin-left: 38.5%;
			}

		}

		.marginT290 {
			margin-top: 290rpx;
		}

		.marginT190 {
			margin-top: 190rpx;
		}

		// 中间白色卡片
		.center-card {
			// width: 630rpx;
			padding: 30rpx;
			background: #FFFFFF;
			border-radius: 14rpx;
			font-size: 30rpx;
			font-family: AlibabaPuHuiTiR;
			color: #000000;

			// 顶部logo部分
			.logobox {
				text-align: center;

				.logo-img {
					margin-top: 130rpx;
					margin-bottom: 140rpx;
					width: 130rpx;
					height: 58rpx;
				}

				.price {
					font-size: 38rpx;
					font-weight: bold;
					margin-top: 15rpx;
				}
			}
		}

		// 付款成功
		.Paysuccess {
			text-align: center;
			height: 400rpx;

			.success-img {
				width: 100rpx;
				height: 100rpx;
				margin-top: 100rpx;
				margin-bottom: 20rpx;
			}

		}

		// 学员信息部分
		.studentinfo {
			padding-top: 20rpx;
			padding-bottom: 60rpx;
			padding-left: 100rpx;
			// height: 356rpx;
			border-top: 1rpx solid #EFEFEF;

			.studentinfo-text {
				margin-top: 30rpx;
				color: #333333;
			}
		}

		// 收款方
		.Payee {
			border-top: 1rpx solid #EFEFEF;
			border-bottom: 1rpx solid #EFEFEF;
			height: 110rpx;
			display: flex;
			justify-content: space-between;
			line-height: 110rpx;
		}

		// 付款成功保存付款记录按钮
		.suceesave {
			position: absolute;
			top: 600rpx;
			let: 50%;
			margin: 0 auto;
			margin-bottom: 244rpx;
			border-radius: 45rpx;
			width: 290rpx;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			font-size: 30rpx;
			font-family: AlibabaPuHuiTiR;
			border: 1rpx solid #2E896F;
			color: #2E896F;
			display: flex;
			align-items: center;
			justify-content: center;

			.link-img {
				width: 42rpx;
				height: 42rpx;
				margin-right: 14rpx;
			}
		}

		// 底部支付按钮
		.buttonFn {
			position: absolute;
			width: 91%;
			bottom: 30rpx;
			right: 30rpx;
			margin: 0 auto;
			border-radius: 45rpx;
			height: 80rpx;
			line-height: 80rpx;
			text-align: center;
			font-size: 30rpx;
			background: linear-gradient(180deg, #88CFBA 0%, #1D755C 100%);
			color: #FFFFFF;

		}

		// 付款成功底部关注公众号二维码
		.footercode {
			height: 180rpx;
			border-top: 1rpx solid #EFEFEF;
			display: flex;
			align-items: center;

			.footercode-left {
				width: 80rpx;
				height: 80rpx;
				background-color: pink;
				margin-right: 20rpx;
			}

			.footercode-right {
				flex: 1;
				font-size: 28rpx;
				font-family: AlibabaPuHuiTiR;
				color: #666666;
			}
		}
	}



	.img_s {
		width: 160rpx;
		height: 160rpx;
	}

	.notify {
		box-shadow: 0rpx 0rpx 20rpx #e0e0e0;
	}

	.hint-icon {
		width: 38rpx;
		height: 38rpx;
	}

	.pay-success {
		width: 100rpx;
		height: 100rpx;
		padding-top: 90rpx;
	}

	.marginB120 {
		margin-bottom: 120rpx;
	}

	.pay-save {
		position: absolute;
		top: 50%;
		z-index: 9;
	}

	.review_btn {
		width: 250upx;
		height: 80upx;
		background: linear-gradient(180deg, #88CFBA 0%, #1D755C 100%);
		border-radius: 45upx;
		font-size: 28upx;
		color: #FFFFFF;
		line-height: 80upx;
		justify-content: center;
		text-align: center;
	}

	.close_btn {
		width: 250upx;
		height: 80upx;
		color: #2E896F;
		font-size: 28upx;
		line-height: 80upx;
		text-align: center;
		border-radius: 45upx;
		box-sizing: border-box;
		border: 1px solid #2E896F;
	}
</style>