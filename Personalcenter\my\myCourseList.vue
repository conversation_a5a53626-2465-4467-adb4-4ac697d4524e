<template>
  <view class="plr-30">
    <view class="radius-20">
      <view class="uni-margin-wrap">
        <u-empty v-if="list.length < 1" mode="history" icon="http://cdn.uviewui.com/uview/empty/history.png"></u-empty>
        <view class="swiper-item uni-bg-red mb-30 bg-ff plr-30" v-for="item in list" :key="item.id" v-else>
          <view class="subcard m-b">
            <view class="card-content">
              <view class="f-3 f-30">
                <view class="myClassList flex_s">
                  <view class="col-4">课程内容：</view>
                  <view class="col-4 t-c">{{ item.curriculumName }}</view>
                  <view class="col-4 t-r" v-if="!item.experience">
                    {{ '正式课程' }}
                  </view>
                  <view class="col-4 t-r" v-else>
                    <!-- <image :src="imgHost + 'dxSelect/shike.png'" class="shike-img" mode="widthFix"></image> -->
                    <text>试课课程</text>
                  </view>
                </view>
                <view class="myClassList displayflexbetween border-t">
                  <view class="">教练</view>
                  <view>
                    {{ item.teacher || '' }}
                  </view>
                </view>
                <view class="myClassList displayflexbetween border-t">
                  <view class="">上课时间</view>
                  <view class="">
                    {{ item.date || '' }}
                    {{ item.startTime || '' }}-{{ item.endTime || '' }}
                  </view>
                </view>
              </view>
              <view class="mb-40 t-r">
                <block v-if="item.isFeedback">
                  <button @click="$noMultipleClicks(goLastback, item)" class="btn btn_orange" type="default" plain="true">查看反馈</button>
                </block>
                <block v-else>
                  <button @click="$noMultipleClicks(gotoNextleave, item)" class="btn common_btn_orange_active btn_orange" type="default" plain="true">请假</button>
                </block>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
const { $showMsg } = require('@/util/methods.js');
import dayjs from 'dayjs';
export default {
  data() {
    return {
      noClick: true,
      page: 1,
      pageSize: 10,
      studentName: '',
      studentCode: '',
      list: [],
      total: 0,
      // 课程列表
      myClassList: {
        lastStudyCourse: {},
        nextStudyCourse: {}
      },
      imgHost: getApp().globalData.imgsomeHost
    };
  },
  onShow() {
    this.getSubject();
  },
  onLoad(e) {
    this.studentName = e.studentName;
    this.studentCode = e.studentCode;
  },
  onReachBottom() {
    if (this.total == this.list.length) {
      // console.log(1111);
      $showMsg('没有更多数据了！');
    } else {
      this.page++;
      this.getSubject();
    }
  },

  methods: {
    //去重
    uniqueObjectsByPropertyMap(arr, property) {
      console.log(arr, '2222222222222');
      const map = new Map();
      arr.forEach((item) => {
        const key = JSON.stringify(item[property]);
        map.set(key, item);
      });
      return Array.from(map.values());
    },
    // 时间判断
    checkNextTime(time) {
      if (time != undefined) {
        let datetime = time;
        let nowTime = Date.now();
        let setTime = dayjs(datetime).unix() * 1000;
        return nowTime < setTime;
      }
    },
    // 我的课程列表
    async getSubject() {
      console.log(111111);
      let res = await this.$httpUser.get('deliver/app/parent/getCourseList', { pageNum: this.page, pageSize: this.pageSize });
      if (res.data.success) {
        this.list = [...this.list, ...res.data.data.data];
        // const newList = this.list.length ? this.list.find((item) => item.studentName === this.studentName) : [];
        // console.log('课程列表 TODO', newList);
        // this.list = newList;
        this.list = this.uniqueObjectsByPropertyMap(this.list, 'id');
        this.total = res.data.data.totalItems;
      } else {
        this.$util.alter(res.data.message);
      }
    },
    // 查看上次反馈
    goLastback(e) {
      console.log(e);
      uni.navigateTo({
        url: '/Coursedetails/feedback/newIndex?data=' + JSON.stringify(e)
      });
    },

    // 查看下次反馈
    goNextback() {
      uni.navigateTo({
        url: '/Coursedetails/feedback/newIndex?data=' + JSON.stringify(this.myClassList.nextStudyCourse)
      });
    },

    // 下次课程请假跳转
    gotoNextleave(e) {
      uni.navigateTo({
        url: '/Coursedetails/leave/leave?data=' + JSON.stringify(e)
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.myClassList {
  height: 100upx;
  // border-bottom:1upx dashed #EEEEEE;
  box-sizing: border-box;
  line-height: 100upx;
}

.flex_s {
  display: flex;
}

.border-t {
  border-top: 1px dashed #eee;
}

.btn {
  width: 160rpx;
  box-sizing: border-box;
  border-radius: 30upx;
  font-size: 30upx;
  display: inline-block;
}

.btn_orange {
  color: #fff !important;
  height: 60rpx;
  line-height: 60rpx;
  border: none !important;
  background: linear-gradient(to bottom, #88cfba, #1d755c);
}

.uni-bg-red {
  position: relative;
}

.shike-img {
  position: absolute;
  width: 45rpx;
  top: 30rpx;
  right: 140rpx;
  margin-right: 20rpx;
}
</style>
