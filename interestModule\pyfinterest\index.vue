<template>
  <page-meta :page-style="'overflow:' + (show ? 'hidden' : 'visible')"></page-meta>
  <view class="funContent">
    <!-- 趣味复习标题 -->
    <interesting-head :title="titleText" @backPage="backPage()" :hasTitleBg="true" :closeWhite="false" :hasRight="false"></interesting-head>
    <!-- 选择课程按钮 -->
    <view class="interesting_surplus interesting_surplusMargin" @click="openLesson()">
      <view>
        <text>{{ courseName ? courseName : '请选择课程' }}</text>
        <image
          style="height: 24rpx; width: 14rpx; transform: scaleX(-1); margin-left: 20rpx"
          src="https://document.dxznjy.com/applet/newInteresting/interesting_back_white.png"
        ></image>
      </view>
    </view>
    <!-- <view class="" style="margin-bottom: 40px" @click="openLesson">{{ courseName ? courseName : '请选择课程' }}</view> -->
    <!-- <view v-for="(item, index) in list" :key="index" @click="go(item)">{{ item.text }}</view> -->
    <!-- 选择课程弹窗 -->
    <uni-popup ref="popopPower" type="center" @change="change">
      <view class="lessonPopor">
        <view class="review_close_icon" @click="cancel"></view>
        <view class="grade_choose" v-if="isGrade">
          <text>低年级</text>
          <image
            @click="gradeChoose"
            :src="courseLevel === '2' ? 'https://document.dxznjy.com/dxSelect/interest/high.png' : 'https://document.dxznjy.com/dxSelect/interest/low.png'"
          ></image>
          <text>高年级</text>
        </view>
        <view class="lessonitme_title">请选择课程</view>
        <view class="lessonlist">
          <view class="lessonitme" v-for="item in lesssonList" :class="item.funReviewStatus ? '' : 'disabled'" :key="item.id" @click="checkLesson(item)">
            {{ item.courseName }}
          </view>
        </view>
      </view>
    </uni-popup>
    <!-- 四种玩法列表 -->
    <view class="interesting_list">
      <view :style="item.isShow ? 'display: block' : 'display: none'" v-for="(item, index) in interestingList" :key="index" class="interesting_listContent">
        <view class="interesting_listContent_top_tap">{{ item.text }}</view>
        <view
          @touchstart="bindTouchStart"
          @touchend="bindTouchEnd"
          @longpress="bingLongTap"
          @click="isContinue(item)"
          :class="
            isLoading
              ? 'interesting_listContent_button info'
              : item.studyStatus == 0
              ? 'interesting_listContent_button start'
              : item.studyStatus == 1
              ? 'interesting_listContent_button continue'
              : 'interesting_listContent_button again'
          "
        >
          {{ isLoading ? '加载中...' : '' }}
        </view>
      </view>
    </view>
    <!-- 提示请选择选择课程弹窗 -->
    <uni-popup ref="popopPowerNotLesson" type="center" @change="change" :is-mask-click="false">
      <view class="notLessonPopor">
        <view class="review_close_icon" @click="cancel"></view>
        <view class="lessonitme_title">请先选择课程才能开始游戏哦！</view>
      </view>
    </uni-popup>
    <!-- 提示是否继续游戏弹窗 -->
    <uni-popup ref="popopPowerIsContinue" type="center" @change="change">
      <view class="popopPower_isContinue">
        <view class="review_close_icon" @click="cancel"></view>
        <view class="lessonitme_title"></view>
        <view>是否继续上次闯关答题？</view>
        <view class="popopPower_isContinue_bot">
          <view class="popopPower_isContinue_cancel" @click="notContinue(nowInteresting)"></view>
          <view class="popopPower_isContinue_confirm" @click="go(nowInteresting)"></view>
        </view>
      </view>
    </uni-popup>
    <!-- 底部小鸡 -->
    <view class="fun_bottom">
      <view class="fun_bottom_tip">快来学习啊呀~</view>
      <image class="fun_bottom_chicken" src="https://document.dxznjy.com/course/bda2cc0dd22c417b9c627f18e9f0073c.png"></image>
    </view>
  </view>
</template>

<script>
  import interestingHead from '../components/interesting-head/pyfInterestingHead.vue';

  export default {
    components: {
      interestingHead
    },

    data() {
      return {
        titleText: '趣味复习', // 标题
        show: false,
        //需要展示的玩法
        interestingList: [
          {
            imgUrl: '',
            text: '听音识词',
            routerUrl: 'interestingTysc',
            studyStatus: 0, // 状态 // 0-开始学习；1-继续学习；2-再来一轮
            isShow: true // 是否展示
          },
          {
            imgUrl: '',
            text: '连连看',
            routerUrl: 'interestingLlk',
            studyStatus: 0,
            isShow: true
          },
          {
            imgUrl: '',
            text: '拼拼乐',
            routerUrl: 'interestingPpl',
            studyStatus: 0,
            isShow: true
          },
          {
            imgUrl: '',
            text: '规则大闯关',
            routerUrl: 'rulesBroken',
            studyStatus: 0,
            isShow: false // 是否展示 // 规则大闯关默认不展示
          }
        ],
        // 当前点击的玩法
        nowInteresting: {},
        isLoading: true, // 是否正在更新 课程学习进度
        isOpening: false, // 是否正在打开 页面

        studentCode: '',
        merchantCode: '',
        lesssonList: [], // 课程列表
        courseName: '', // 课程名称
        courseCode: null, // 课程编码
        isGrade: false, // 是否同时购买高低年级课程
        courseLevel: uni.getStorageSync('pyGrade') || '', //高低年级
        startTime: 0, // 触摸开始时间
        endTime: 0, // 触摸结束时间
        timeId: null, // 定时器ID
        isThrottling: false // 是否节流
      };
    },
    onLoad(e) {
      this.studentCode = e.studentCode;
      this.merchantCode = e.merchantCode;
      // this.init();
      this.getGrade(); // 查询是否同时购买高低年级课程
    },
    onShow() {
      // this.courseCode = uni.getStorageSync('pyfWordCourse'); // 小程序缓存的课程数据
      // this.courseName = uni.getStorageSync('pyfLesssonName'); // 小程序缓存的课程数据
      this.isLoading = true; // 禁用选项
      // this.getGrade(); // 查询是否同时购买高低年级课程
      this.updateStatus(); // 获取课程学习进度
      // this.$refs.popopPowerNotLesson.open();
      // this.$refs.popopPowerIsContinue.open();
    },
    methods: {
      // 禁止穿透滚动
      change(e) {
        console.log(e);
        this.show = e.show;
      },
      async init() {
        let that = this;
        // 获取课程列表
        // let { data } = await this.$httpUser.get(`znyy/pd/mobile/funReview/selectCourse?studentCode=${this.studentCode}&merchantCode=${this.merchantCode}`);
        let { data } = await this.$httpUser.get(
          `znyy/pd/mobile/funReview/selectCourse?studentCode=${this.studentCode}&merchantCode=${this.merchantCode}&courseLevel=${this.courseLevel}`
        );
        this.lesssonList = data.data;
        // // 获取课程学习进度
        // that.updateStatus(true);
      },
      /**
       * 更新课程学习进度
       * @param ifFirst 是否第一次更新/是否需要先获取课程数据
       */
      async updateStatus() {
        let that = this;
        // 先还原学习进度
        that.interestingList.forEach((i) => {
          i.studyStatus = 0;
        });
        let res;
        if (!this.courseCode) {
          // 获取最近的课程数据
          res = await that.$httpUser.get(`znyy/pd/mobile/funReview/exist?studentCode=${this.studentCode}&merchantCode=${this.merchantCode}`);
          if (!res.data.data || !res.data.data.courseCode) {
            return (that.isLoading = false);
          }
          this.courseCode = res.data.data.courseCode; // 更新课程数据
          this.courseName = res.data.data.courseName; // 同上
          if (this.courseLevel !== res.data.data.courseLevel) {
            this.courseLevel = res.data.data.courseLevel;
            if (this.courseLevel === '1' || !this.courseLevel) {
              this.courseLevel = '0';
            }
            uni.setStorageSync('pyGrade', this.courseLevel);
            this.init();
          }
        } else {
          // 获取当前课程的学习进度
          res = await that.$httpUser.get(
            `znyy/pd/mobile/funReview/findCourseStudyStatus?studentCode=${this.studentCode}&merchantCode=${this.merchantCode}&courseCode=${this.courseCode}`
          );
          if (res.data.data) {
            this.courseLevel = res.data.data.courseLevel;
            if (this.courseLevel === '1' || !this.courseLevel) {
              this.courseLevel = '0';
            }
            uni.setStorageSync('pyGrade', this.courseLevel);
            this.init();
          }
        }
        if (res.data.data) {
          // 是否显示规则大闯关
          if (res.data.data.ruleExistStatus * 1) {
            that.interestingList[3].isShow = true;
          } else {
            that.interestingList[3].isShow = false;
          }
          // 有数据更新学习进度
          // that.interestingList[0].studyStatus = res.data.data.tpdFunReviewStudyInfo ? res.data.data.tpdFunReviewStudyInfo.studyStatus : 0;
          that.interestingList[0].studyStatus = res.data.data.tpdFunReviewStudyInfo?.studyStatus || 0;
          that.interestingList[1].studyStatus = res.data.data.lpdFunReviewStudyInfo?.studyStatus || 0;
          that.interestingList[2].studyStatus = res.data.data.ppdFunReviewStudyInfo?.studyStatus || 0;
          that.interestingList[3].studyStatus = res.data.data.gpdFunReviewStudyInfo?.studyStatus || 0;
        }
        that.isLoading = false; // 加载完成
      },

      // 打开选择课程弹窗
      openLesson() {
        this.$refs.popopPower.open();
      },
      // 选择课程
      async checkLesson(e) {
        if (!e.funReviewStatus) return;
        let that = this;
        getApp().sensors.track('checkLesson', {
          name: e.courseName,
          courseCode: e.courseCode
        });
        this.courseCode = e.courseCode;
        this.courseName = e.courseName;
        this.$refs.popopPower.close();
        that.isLoading = true; // 加载中
        // uni.setStorageSync('pyfWordCourse', e.courseCode); // 保存课程信息
        // uni.setStorageSync('pyfLesssonName', e.courseName); // 保存课程信息
        // 更新学习进度
        that.updateStatus();
      },
      // 关闭选择课程弹窗
      cancel() {
        this.$refs.popopPower.close(); // 关闭 选择课程弹窗
        if (this.timeId) {
          this.$refs.popopPowerNotLesson.close(); // 关闭 请选择课程弹窗
          clearTimeout(this.timeId);
          this.timeId = null; // 清空定时器 ID
        }
        this.$refs.popopPowerIsContinue.close(); // 关闭 是否继续闯关弹窗
      },
      // 封装uni.showModal 为同步
      showModalSync(options) {
        return new Promise((resolve, reject) => {
          uni.showModal({
            ...options,
            success: (res) => {
              if (res.confirm) {
                resolve(true); // 用户点击确认
              } else if (res.cancel) {
                resolve(false); // 用户点击取消
              }
            },
            fail: (err) => {
              reject(err); // 调用失败
            }
          });
        });
      },

      bindTouchStart(e) {
        this.startTime = e.timeStamp;
      },
      bindTouchEnd(e) {
        this.endTime = e.timeStamp;
      },
      bingLongTap(e) {
        console.log('长按');
      },
      // 是否继续闯关
      async isContinue(i) {
        console.log('isContinue', this.startTime, this.endTime, this.isThrottling);
        if (this.isThrottling) return this.$util.alter('请勿频繁点击'); // 如果正在节流，直接返回
        this.isThrottling = true;
        try {
          if (this.timeId) {
            clearTimeout(this.timeId); // 清除之前的定时器
            this.timeId = null; // 重置定时器 ID
          }
          console.log('点击');
          let that = this;
          that.nowInteresting = i; // 当前点击的课程

          // 请先选择课程
          if (!that.courseName) {
            this.$refs.popopPowerNotLesson.open();
            this.timeId = setTimeout(() => {
              if (this.timeId) {
                this.$refs.popopPowerNotLesson.close();
                this.timeId = null; // 清空定时器 ID
              }
            }, 1500);
            return;
          }

          // 是否正在更新课程学习状态 或 是否正在打开页面
          if (that.isLoading || that.isOpening) {
            return that.$util.alter('加载中，请稍等~');
          }

          // 判断是否继续上次闯关
          if (i.studyStatus == 1) {
            // 是否打开是否继续闯关弹窗
            that.$refs.popopPowerIsContinue.open();
          } else {
            that.isOpening = true; // 节流
            that.cancel(); // 关闭弹窗
            that.go(that.nowInteresting);
          }
        } catch (error) {
          console.error('Error in isContinue:', error);
        } finally {
          this.isThrottling = false; // 重置节流状态
          if (this.timeoutId) {
            clearTimeout(this.timeoutId); // 清理定时器
          }
        }
      },
      // 不继续闯关
      async notContinue(i) {
        let that = this;

        i.studyStatus = 0; //重置状态
        that.go(i);
      },
      // 跳转对应页面
      async go(i) {
        let that = this;
        this.$refs.popopPowerIsContinue.close(); // 关闭 是否继续闯关弹窗
        getApp().sensors.track('goStudyModal', {
          name: i.text
        });
        // 加载中.../异步执行避免和uni.showToast冲突
        setTimeout(() => {
          uni.showLoading({
            title: '加载中',
            mask: true
          });
        }, 0);
        setTimeout(() => {
          uni.hideLoading();
        }, 1000);
        // 跳转对应页面
        uni.navigateTo({
          url: `/interestModule/pyfinterest/${i.routerUrl}${uni.$u.queryParams({
            studentCode: that.studentCode,
            courseCode: that.courseCode,
            merchantCode: that.merchantCode,
            studyStatus: i.studyStatus
          })}`
        });
        that.isOpening = false; // 关闭节流
      },
      // 返回上一页
      backPage() {
        //返回按钮
        uni.navigateBack({
          delta: 1
        });
      },
      // 年级选择
      gradeChoose() {
        if (this.courseLevel === '0') {
          this.courseLevel = '2';
        } else if (this.courseLevel === '2') {
          this.courseLevel = '0';
        }
        getApp().sensors.track('checkLesson', {
          name: this.courseLevel === '2' ? '高年级' : '低年级'
        });
        this.lesssonList = []; // 清空课程列表
        this.courseName = ''; // 清空课程名称
        this.courseCode = null;
        this.interestingList[0].studyStatus = 0; // 清空课程学习状态
        this.interestingList[1].studyStatus = 0; // 清空课程学习状态
        this.interestingList[2].studyStatus = 0; // 清空课程学习状态
        this.interestingList[3].studyStatus = 0; // 清空课程学习状态
        this.init(); // 获取课程列表
        uni.setStorageSync('pyGrade', this.courseLevel);
      },
      // 查询是否同时购买高低年级课程
      getGrade() {
        this.$httpUser.get(`znyy/pd/mobile/funReview/existLowerAndSenior?studentCode=${this.studentCode}&merchantCode=${this.merchantCode}`).then((res) => {
          if (!res.data.success) return this.$util.alter(res.data.message);
          this.isGrade = res.data.data.existBuyLowerCourse && res.data.data.existBuySeniorCourse; // 是否同时购买高低年级课程
          if (this.isGrade) {
            this.courseLevel = res.data.data.defaultCourseLevel; // 0-低年级 2-高年级
          } else if (res.data.data.existBuyLowerCourse) {
            this.courseLevel = '0'; // 0-低年级
          } else if (res.data.data.existBuySeniorCourse) {
            this.courseLevel = '2'; // 2-高年级
          } else {
            this.courseLevel = ''; // ''-全部
          }
          uni.setStorageSync('pyGrade', this.courseLevel);
          this.init();
          // this.updateStatus(); // 获取课程学习进度
          console.log('是否同时购买高低年级课程', res, this.isGrade);
        });
      }
    }
  };
</script>
<style>
  page {
    height: 100vh;
    padding: 0;
  }
</style>
<style scoped lang="scss">
  $font-size: 28rpx;

  .funContent {
    width: 100%;
    padding: 0 30rpx;
    box-sizing: border-box;
    height: 100%;
    min-height: 1624rpx;
    background: url('https://document.dxznjy.com/course/d3684a6aa1294ce181ba6ea2fe3c986b.png') 100% 100% no-repeat;
    background-size: 100% 100%;
    overflow: hidden;
    position: relative;
  }

  .review_close_icon {
    position: absolute;
    bottom: -95rpx;
    right: 50%;

    width: 64rpx;
    height: 64rpx;
    transform: translateX(50%);
    z-index: 1;
    background: url('https://document.dxznjy.com/course/97005b06bf3549c9922dc6310bdc036d.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  .notLessonPopor {
    position: relative;
    width: 632rpx;
    height: 258rpx;
    text-align: center;
    font-size: $font-size;

    background: url('https://document.dxznjy.com/course/d93a455bdb2c44edaa39b47450093989.png') 100% 100% no-repeat;
    background-size: 100% 100%;

    // 还是小鸟
    .lessonitme_title {
      color: #555555;
      line-height: 258rpx;
    }
  }

  .popopPower_isContinue {
    position: relative;
    width: 632rpx;
    height: 364rpx;
    line-height: 324rpx;
    font-size: $font-size;

    text-align: center;
    background: url('https://document.dxznjy.com/course/2322c002582f430b806ca1a4781122f6.png') 100% 100% no-repeat;
    background-size: 100% 100%;

    // 小鸟
    .lessonitme_title {
      position: absolute;
      top: -70rpx;
      right: 40rpx;
      width: 70rpx;
      height: 70rpx;
      color: #555555;
      line-height: 334rpx;
      background: url('https://document.dxznjy.com/course/d0b884bc62b84fbbbceba1c1a9be96c8.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }

    .popopPower_isContinue_bot {
      position: absolute;
      bottom: 40rpx;
      right: 50%;
      transform: translateX(50%);
      width: 530rpx;
      height: 84rpx;

      display: flex;
      justify-content: space-between;
    }

    .popopPower_isContinue_confirm {
      width: 252rpx;
      height: 84rpx;
      background: url('https://document.dxznjy.com/course/f065a5323c7b4e7eaea1c556a4777dc0.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }

    .popopPower_isContinue_cancel {
      width: 252rpx;
      height: 84rpx;
      background: url('https://document.dxznjy.com/course/25eb8f70457d4691823b09f185fc5e2f.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }
  }

  .lessonPopor {
    position: relative;
    width: 686rpx;
    height: 1042rpx;
    // background-color: #fff;
    background: url('https://document.dxznjy.com/course/c9120c4bc19a443280fa6e67421993ba.png') 100% 100% no-repeat;
    background-size: 100% 100%;
    border-radius: 20rpx;
    box-sizing: border-box;
    padding: 20rpx 56rpx 56rpx 56rpx;

    transform: translateY(40rpx);

    .lessonitme_title {
      width: 572rpx;
      height: 138rpx;
      margin-bottom: 43rpx;
      border-radius: 20rpx;
      line-height: 138rpx;
      font-size: 40rpx;
      color: #8c6b0f;
      font-weight: bold;
      text-align: center;
      background: url('https://document.dxznjy.com/course/3b688e289e594069b18398885e8656ef.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }

    .grade_choose {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 50rpx;
      margin: 0rpx 28rpx 32rpx 0rpx;
      font-size: 28rpx;
      color: #555555;
      font-weight: 700;

      image {
        width: 84rpx;
        height: 50rpx;
        margin: 0 16rpx;
      }
    }
  }

  .lessonlist {
    height: 734rpx;
    overflow-y: auto;

    .lessonitme {
      width: 555rpx;
      height: 96rpx;
      margin-bottom: 30rpx;
      border-radius: 20rpx;
      line-height: 90rpx;
      font-size: $font-size;
      color: #555555;
      text-align: center;
      background: url('https://document.dxznjy.com/course/f27f510ad59a44cda5a5d6b75da413ae.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }

    .disabled {
      color: #888;
      opacity: 0.5;
    }
  }

  .interesting_surplus {
    margin: 10rpx auto;
    text-align: center;
    height: 84rpx;
    width: 600rpx;
    line-height: 84rpx;
    background: url('https://document.dxznjy.com/course/bfe8498393644ae8a1fdb92ef8479069.png') 100% 100% no-repeat;
    background-size: 100% 100%;
  }

  .interesting_surplusMargin {
    // margin-top: 75rpx;
    margin-top: 30rpx;
    margin-bottom: 50rpx;
  }

  .interesting_surplus > view {
    text-align: center;
    height: 84rpx;
    width: 100%;
    line-height: 84rpx;
    color: #fff;
    font-size: 34rpx;
  }

  .interesting_list {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding-left: 20rpx;
  }

  .interesting_listContent {
    width: 47%;
    height: 432rpx;
    margin-top: 40rpx;
    /* margin-right: 49rpx; */
    background: #9ecb9c;
    border-radius: 20rpx;
    position: relative;
    background: url('https://document.dxznjy.com/course/88bb0d5d7fa5436db6e9307f7604ba2b.png') 100% 100% no-repeat;
    background-size: 100% 100%;

    &.interesting_listContent:nth-child(1) {
      background: url('https://document.dxznjy.com/course/017101741e7843bfb4f11746a4a9248a.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }

    &.interesting_listContent:nth-child(2) {
      background: url('https://document.dxznjy.com/course/7cb1e1ebfd9640c39b7f257b6861c379.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }

    &.interesting_listContent:nth-child(3) {
      background: url('https://document.dxznjy.com/course/4c03c38476d04549be0d7e440d7845b6.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }

    &.interesting_listContent:nth-child(4) {
      background: url('https://document.dxznjy.com/course/77b9f96a7c7a45559dd6bcb73dcfa9d8.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }
  }

  .interesting_listContent_top_tap {
    position: absolute;
    left: 50%;
    /* 水平居中 */
    top: 0rpx;
    /* 距离底部 30rpx */
    transform: translateX(-50%);
    /* 使元素水平居中 */
    width: 180rpx;
    height: 70rpx;
    text-align: center;
    line-height: 60rpx;
    font-weight: bold;
    color: #444;
    border-radius: 20rpx;
    font-size: 32rpx;
    // background-color: #d8f5e9;
  }

  .interesting_listContent_button {
    position: absolute;
    left: 50%;
    /* 水平居中 */
    bottom: 30rpx;
    /* 距离底部 30rpx */
    transform: translateX(-50%);
    /* 使元素水平居中 */
    width: 254rpx;
    height: 70rpx;
    /* background: url('https://document.dxznjy.com/applet/newInteresting/interesting_start.png') 100% 100% no-repeat; */
    text-align: center;
    color: #fff;
    background-size: 100% 100%;
    line-height: 70rpx;
    border-radius: 60rpx;
    font-weight: bold;
    font-size: 30rpx;

    &.start {
      background: url('https://document.dxznjy.com/course/b23327296d5e4215a8347b8251680227.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }

    &.again {
      background: url('https://document.dxznjy.com/course/5dd624ec61364c5cb5fe4af606bc2104.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }

    &.continue {
      background: url('https://document.dxznjy.com/course/05a6aa6bf4c4457e8290f631178293f2.png') 100% 100% no-repeat;
      background-size: 100% 100%;
    }

    &.info {
      background-color: #cccccc;
    }
  }

  .fun_bottom {
    position: absolute;
    bottom: 0;
    right: 0;

    .fun_bottom_tip {
      position: absolute;
      bottom: 222rpx;
      right: 134rpx;
      width: 282rpx;
      height: 84rpx;
      background: url('https://document.dxznjy.com/course/b55bad80ba234e80b6d0a393c3b94af5.png') 100% 100% no-repeat;
      background-size: 100% 100%;

      font-size: $font-size;
      color: #3b1e0e;
      line-height: 64rpx;
      text-align: center;
    }

    .fun_bottom_chicken {
      width: 216rpx;
      height: 270rpx;
      position: absolute;
      bottom: 0;
      right: 0;
    }
  }
</style>
