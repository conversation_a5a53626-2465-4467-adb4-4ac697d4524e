<template>
  <view @click="getlist()" class="plr-32 pt-35">
    <scroll-view
      v-if="leftList.length > 0"
      @scrolltolower="scrolltolower"
      :show-scrollbar="false"
      bounces
      :throttle="false"
      scroll-with-animation
      scroll-anchoring
      scroll-y
      enhanced
    >
      <view class="courseList mt-12">
        <view class="waterfall-box h-flex-x h-flex-2">
          <view>
            <helang-waterfall
              v-for="(item, index) in leftList"
              :key="index"
              :item="item"
              tag="left"
              :index="index"
              @shareVip="shareVip"
              :identityType="identityType"
              @click="skintap('Coursedetails/productDetils?id=' + item.goodsId, item)"
            ></helang-waterfall>
          </view>
          <view style="margin-left: 24rpx">
            <helang-waterfall
              v-for="(item, index) in rightList"
              :key="index"
              :item="item"
              :identityType="identityType"
              @shareVip="shareVip"
              @click="skintap('Coursedetails/productDetils?id=' + item.goodsId)"
              tag="right"
              :index="index"
            ></helang-waterfall>
          </view>
        </view>
      </view>
    </scroll-view>
    <view v-else class="t-c flex-col" :style="{ height: 500 + 'rpx' }">
      <image :src="imgHost + 'alading/correcting/no_data.png'" style="width: 160rpx" class="mb-20 img_s" mode="widthFix"></image>
      <view style="color: #bdbdbd">暂无数据</view>
    </view>
    <sharePopup ref="sharePopups"></sharePopup>
  </view>
</template>
<script>
  const { $http, $navigationTo } = require('@/util/methods.js');
  import Config from '@/util/config.js';
  import sharePopup from '@/components/sharePopup.vue';
  export default {
    components: {
      sharePopup
    },
    data() {
      return {
        leftList: [],
        rightList: [],
        shareContent: {},
        page: 1,
        imgHost: getApp().globalData.imgsomeHost,
        identityType: '',
        couponId: '',
        no_more: false
      };
    },
    onLoad(e) {
      this.couponId = e.id;
      this.identityType = uni.getStorageSync('identityType');
      this.getlist();
    },
    methods: {
      scrolltolower() {
        console.log(this.page, this.pageNo, '下拉加载更多');
        if (this.page >= Number(this.pageNo)) {
          this.no_more = true;
          return false;
        }
        this.getlist(true, ++this.page);
      },
      shareVip(type, item) {
        // if (!uni.getStorageSync('token')) {
        //   // uni.navigateTo({
        //   //   url: '/Personalcenter/login/login'
        //   // });          return;
        // }
        // this.shareContent.type = type;
        // let id = '',
        //   imgurl = '';
        // //type 1课程  2学习超人（会员） 3超人俱乐部
        // if (type == '1') {
        //   id = item.courseId;
        //   imgurl = item.courseImage;
        // } else {
        //   id = item.mealId;
        //   type == '2' ? (imgurl = Config.supermanShareImage) : (imgurl = Config.supermanClubShareImage);
        // }
        // this.shareContent.id = item.goodsId;
        // if (type != 6) {
        //   this.shareContent.imgurl = imgurl;
        // } else {
        //   this.shareContent.imgurl = item.goodsSharePoster;
        //   this.shareContent.title = item.goodsShareTextList[0] ? item.goodsShareTextList[0].shareText : null;
        // }
        // let shareInfo = {};
        // // #ifdef APP-PLUS
        // shareInfo = {
        //   title: this.shareContent.title || '叮，你的好友敲了你一下，赶紧过来看看',
        //   imageUrl: this.shareContent.imgurl,
        //   path: `/pages/beingShared/index?scene=${uni.getStorageSync('user_id')}&type=${this.shareContent.type}&id=${this.shareContent.id}&source=app`
        // };
        // // #endif
        // this.$refs.sharePopups.open(true, this.shareContent, shareInfo);
        let shareContent = {
          type: '6',
          id: item.goodsId,
          bannerId: ''
        };
        shareContent.imgurl = item.goodsSharePoster;
        shareContent.title = item.goodsShareTextList[0] ? item.goodsShareTextList[0].shareText : null;
        let shareInfo = {
          title: item.goodsShareTextList[0].shareText,
          imageUrl: item.goodsSharePoster, //分享封面
          path: '/pages/beingShared/index?id=' + item.goodsId + '&type=6&scene=' + uni.getStorageSync('user_id') + '&source=app'
        };
        let shareType = uni.getStorageSync('appShareType') || '';
        this.$refs.sharePopups.open(true, shareContent, shareInfo, shareType);
      },
      skintap(url) {
        console.log('xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx');
        if (!uni.getStorageSync('token')) {
          return;
        } else {
          $navigationTo(url);
        }
      },
      async getlist(isPage, page) {
        uni.showLoading({
          title: '加载中'
        });
        const res = await $http({
          url: 'zx/wap/goods/findByCouponId/list',

          data: {
            pageSize: 20,
            pageNum: page || 1,
            couponId: this.couponId,
            userId: uni.getStorageSync('user_id') ? uni.getStorageSync('user_id') : ''
          }
        });
        if (res) {
          this.infoLists = res.data;
          // 初始化左右列表的数据
          let i = this.leftHeight > this.rightHeight ? 1 : 0;
          let [left, right] = [[], []];
          // 左右列表高度的差
          let differ = this.leftHeight - this.rightHeight;
          // 将数据源分为左右两个列表，容错差值请自行根据项目中的数据情况调节
          let list = res.data.data;
          list.forEach((item, index) => {
            /* 左侧高度大于右侧超过 600px 时，则前3条数据都插入到右边 */
            if (differ >= 600 && index < 3) {
              right.push(item);
              return;
            }

            /* 右侧高度大于左侧超过 600px 时，则前3条数据都插入到左边 */
            if (differ <= -600 && index < 3) {
              left.push(item);
              return;
            }

            /* 左侧高度大于右侧超过 350px 时，则前2条数据都插入到右边 */
            if (differ >= 350 && index < 2) {
              right.push(item);
              return;
            }
            /* 右侧高度大于左侧超过 350px 时，则前2条数据都插入到左边 */
            if (differ <= -350 && index < 2) {
              left.push(item);
              return;
            }

            /* 当前数据序号为偶数时，则插入到左边 */
            if (i % 2 == 0) {
              left.push(item);
            } else {
              /* 当前数据序号为奇数时，则插入到右边 */
              right.push(item);
            }
            i++;
          });
          if (isPage) {
            this.leftList = [...this.leftList, ...left];
            this.rightList = [...this.rightList, ...right];
          } else {
            this.leftList = left;
            this.rightList = right;
          }
        }
      }
    }
  };
</script>
