<template>
	<view class="bg-ff labour_main_css">
		<view class="w100 labour_banner_main plr-32">
			<swiper :autoplay="true" indicator-color="rgba(227, 231, 230, 1)" indicator-active-color="#04614B" class="swiper_css"  :indicator-dots="indicatorDots"  :current="bannerIndex" :interval="3000"  :duration="1000">
				<block v-for="(item,index) in bannerList" :key="index">
					<swiper-item class="flex-c radius-16 swiper_css_item">
						<image :src="item.bannerPicUrl" mode="aspectFill" class="wh100" lazy-load="true"
							@tap="bannerTab(item)"></image>
					</swiper-item>
					<!-- #04614B -->
				</block>
			</swiper>
		</view>
		<view class="mt-24">
			<view  class="plr-32">
				<u-tabs :list="labourList" :current="labourCurrent"  keyName="name"  lineWidth="40" lineHeight="11"  :activeStyle="{  color: '#333333', fontWeight: 'bold',fontSize:'28rpx' }"
				 :inactiveStyle="{color: '#5A5A5A ', transform: 'scale(1)', fontSize:'28rpx' }"
				itemStyle="padding-left:1px; padding-right: 18px; height: 42px;"
				:lineColor="`url(${lineBg}) 100% 110%`" @click="labourClick"></u-tabs>
			</view>
			<scroll-view :scroll-top="scrollTop"  class="labour_content_style"
			  @scrolltolower="scrolltolower"
			  @scroll="scroll"
			  :show-scrollbar="false"
			  bounces
			  :throttle="false"
			  scroll-with-animation
			  scroll-anchoring
			  scroll-y
			  enhanced>
				<view v-if="labourCurrent==0">
					<view class="pb-70">
						<view   v-for="item in releaseSelecte" :key="item.id" class="plr-32 pb-20 pt-24 border_color">
							<releaseItem @showImage="showImage" releaseType="3"  :showLike="true"   :releaseStyle="releaseStyle" :releaseInfo="item"></releaseItem>
						</view>
						<suspensionBtn @release="release" :showIcon="true" :fixedText="fixedText"></suspensionBtn>
					</view>
				</view>
				<view v-if="releaseSelecte.length==0">
					<emptyPage></emptyPage>
				</view>
			</scroll-view>
		</view>
		<tipsContentPopup ref="tipsContentPopupRefs" :tipsType="tipsType"></tipsContentPopup>
	</view>
</template>

<script>
	import releaseItem from "../components/releaseItem.vue"
	import suspensionBtn from "../components/suspensionBtn.vue"
	import tipsContentPopup from "../components/tipsContentPopup.vue"
	import emptyPage from "../components/emptyPage.vue"
	const { $navigationTo,$http } = require("@/util/methods.js")
	export default {
		components:{releaseItem,suspensionBtn,tipsContentPopup,emptyPage},
		data() {
			return {
				labourList:[
					{name:'勤耕纪实',key:1},
				],
				tipsType:3,
				lineBg:'https://document.dxznjy.com/course/01c96465d9ea46f69aa97465b1201aef.png',
				fixedText:'上传',
				labourCurrent:0,
				indicatorDots:true,
				scrollTop:0,
				//勤耕纪实
				releaseSelecte:[],
				bannerList:[],
				releaseStyle:{
					leftImageWidth:'100rpx',
				},
				identityType:uni.getStorageSync('identityType'),
				videoInfo:{},
				infoLists:{},
				scrollTopNum:0,
				showImageType:false,
				page:1,
			}
		},
		onLoad() {
			this.banner()
		},
		onShow() {
			this.page=1
			if(this.showImageType){
				this.showImageType=false
				return
			}
			this.getCultureCircle(-1)
		},
		methods: {
			scrolltolower(){
				if (this.page * 10 >= this.infoLists.totalItems) {
					return false;
				}
				this.getCultureCircle(true, ++this.page);
			},
			scroll(e){
				this.scrollTopNum=e.detail.scrollTop
			},
			async banner() {
				let _this = this
				const res = await $http({
					url: 'zx/wap/layout/banner/list',
					showLoading:true,
					data: {
						bannerPosition: 5,
					}
				})
				if (res) {
					_this.bannerList = res.data
				}
			},
			async getCultureCircle(isPage,page){
			    let _this = this
			    const res = await $http({
			    	url: 'zx/wap/CultureCircle',
					showLoading:true,
			    	data: {
						topicType:'WORK_CULTURE',
						pageNum:page||1,
						pageSize:10,
						userId:uni.getStorageSync('user_id')?uni.getStorageSync('user_id'):'',
					}
			    })
				if(res){
					this.infoLists = res.data;
					if(isPage==-1){
						_this.scrollTop=this.scrollTopNum
						this.$nextTick(() =>{
							 _this.scrollTop = 0
						})
					}
					if (isPage&&isPage!=-1) {
						this.releaseSelecte = [...this.releaseSelecte, ...res.data.data];
					} else {
						this.releaseSelecte = res.data.data || [];
					}
				}
			},
			bannerTab(item){
				if(item.needLogin==0&&!uni.getStorageSync('token')){
					uni.navigateTo({
						url: '/Personalcenter/login/login'
					})
				}else{
					if(item.goodsId){
						$navigationTo('Coursedetails/productDetils?id='+item.goodsId)
						
					}else{
						$navigationTo(item.bannerLinkUrl)
					}
				}
				
			},
			labourClick(e){
				this.labourCurrent=e.index
			},
			showImage(list,index){
				this.showImageType=true
				let photoList = list.map(item => {
					return item.url;
				});
				uni.previewImage({
					urls: photoList,
					current:index,
					indicator:'default',
				});
			},
			release(){
				if(this.identityType==4){
					uni.navigateTo({
						url: '/memberCenter/releaseIndex?type=3'
					})
				}else{
					this.$refs.tipsContentPopupRefs.open()
					this.tipsType=3
				}
			},
			openMembership(){
				$navigationTo('Personalcenter/my/nomyEquity?type=2')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.labour_content_style{
		height: calc(100vh - 480rpx);
	}
.labour_main_css{
	overflow-y: scroll;
	overflow-x: hidden;
	height: 100vh;
	.imagele{
		width: 100rpx;
		height: 100rpx;
	}
	.labour_banner_main{
		padding-top: 24rpx;
		.swiper_css{
			height: 300rpx;
			width: 686rpx;
			.swiper_css_item{
				height: 240rpx !important;
			}
		}
	}
	.border_color{
		border-bottom:1rpx solid #ECF0F4;
	}
	.tips_content_css{
		width: 560rpx;
		text-align: center;
	}
}

</style>
